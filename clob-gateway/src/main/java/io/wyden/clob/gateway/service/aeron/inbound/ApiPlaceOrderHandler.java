package io.wyden.clob.gateway.service.aeron.inbound;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.fsm.OrderService;
import io.wyden.clob.gateway.utils.SbeUtils;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.sbe.ApiPlaceOrderDecoder;
import io.wyden.sbe.CommandResultCode;
import io.wyden.sbe.MessageHeaderDecoder;
import org.agrona.DirectBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static io.wyden.clob.gateway.infrastructure.telemetry.TelemetryConfiguration.COUNT_INBOUND_AERON_EVENT;
import static io.wyden.clob.gateway.infrastructure.telemetry.TelemetryConfiguration.MATCHING_ENGINE_SYMBOL;

@Component
class ApiPlaceOrderHandler implements AeronMessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiPlaceOrderHandler.class);

    private final ApiPlaceOrderDecoder placeOrder = new ApiPlaceOrderDecoder();
    private final OrderService orderService;
    private final MeterRegistry meterRegistry;

    public ApiPlaceOrderHandler(@Lazy OrderService orderService,
                                Telemetry telemetry) {
        this.orderService = orderService;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public int templateId() {
        return placeOrder.sbeTemplateId();
    }

    @Override
    public void handle(DirectBuffer buffer, int offset, MessageHeaderDecoder headerDecoder) {
        placeOrder.wrapAndApplyHeader(buffer, offset, headerDecoder);
        updateMetrics(placeOrder);
        String orderUuid = SbeUtils.toUuidString(placeOrder.orderUuid());
        LOGGER.info("Received ApiPlaceOrder {}, orderUuid={}", placeOrder, orderUuid);

        boolean isSuccess = placeOrder.result() == CommandResultCode.SUCCESS;
        orderService.onPlaceOrder(new MatchingEngineEvent.PlaceOrder(
            placeOrder.symbol(),
            placeOrder.orderId(),
            orderUuid,
            isSuccess,
            placeOrder.result(),
            placeOrder.size(),
            placeOrder.amount()
        ));
    }

    private void updateMetrics(ApiPlaceOrderDecoder apiPlaceOrderDecoder) {
        try {
            this.meterRegistry.counter(COUNT_INBOUND_AERON_EVENT,
                MATCHING_ENGINE_SYMBOL, String.valueOf(apiPlaceOrderDecoder.symbol()),
                "execType", "new"
            ).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}