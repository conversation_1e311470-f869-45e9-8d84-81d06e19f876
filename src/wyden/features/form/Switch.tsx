import { useTsController } from '@ts-react/form';
import { FormGroup } from '@mui/material';
import { Switch as UISwitch } from '@ui/Switch';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { getSpacing } from '@wyden/utils/styles';
import { Paragraph } from '@ui/Typography/Paragraph';
import { useWatch } from 'react-hook-form';

export interface SwitchProps {
  label: string;
  disabled?: boolean;
}

export const Switch = (props: SwitchProps) => {
  const { field } = useTsController();

  const fieldValue = useWatch({
    name: field.name,
  });

  return (
    <FormGroup>
      <SwitchContainer>
        <UISwitch
          id={field.name}
          value={!!fieldValue}
          checked={!!fieldValue}
          onChange={field.onChange}
          disabled={props.disabled}
        />
        <label htmlFor={field.name}>
          <StyledLabel variant="small">{props.label}</StyledLabel>
        </label>
      </SwitchContainer>
    </FormGroup>
  );
};

const StyledLabel = styled(Paragraph)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
`;

const SwitchContainer = styled('div')`
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: ${getSpacing(2)};
`;
