import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { getSpacing } from '@wyden/utils/styles';
import { ReactComponent as PlusSignIcon } from '@wyden/assets/plus-sign.svg';
import { ReactComponent as CheckCircleIcon } from '@wyden/assets/check-circle.svg';
import { useCallback, useMemo } from 'react';
import { ClickAwayListener } from '@mui/material';
import { Label } from '@ui/Typography/Label';
import { useTranslation } from 'react-i18next';
import { InstrumentResponse, PortfolioResponse } from '@wyden/services/graphql/generated/graphql';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';

interface WatchlistInstrumentAccountRowBoxProps {
  instrument: InstrumentResponse;
  accountsForVenue: VenueAccountWithVenue[];
  setAnchorEl: (value: HTMLElement | null) => void;
  isAlreadyInWatchlist: (params: {
    account: VenueAccountWithVenue | null;
    instrument: InstrumentResponse;
    portfolio: PortfolioResponse | null;
  }) => boolean;
  handleVenueAccountClick: (
    event: React.MouseEvent<HTMLElement>,
    account: VenueAccountWithVenue | null,
    isPresentInWatchlist: boolean,
  ) => void;
}

export const WatchlistInstrumentAccountRowBox = ({
  instrument,
  accountsForVenue,
  setAnchorEl,
  isAlreadyInWatchlist,
  handleVenueAccountClick,
}: WatchlistInstrumentAccountRowBoxProps) => {
  const { t } = useTranslation();

  const checkWatchlistPresence = useCallback(
    (account) =>
      isAlreadyInWatchlist({
        account,
        instrument,
        portfolio: null,
      }),
    [instrument, isAlreadyInWatchlist],
  );

  const sortedAccounts = useMemo(
    () => accountsForVenue.sort((account) => (checkWatchlistPresence(account) ? -1 : 1)),
    [accountsForVenue, checkWatchlistPresence],
  );

  const accountsPresentInWatchlist = sortedAccounts.filter((account) =>
    checkWatchlistPresence(account),
  );
  const accountsNotPresentInWatchlist = sortedAccounts.filter(
    (account) => !checkWatchlistPresence(account),
  );

  const handleClosePopper = () => {
    setAnchorEl(null);
  };

  const renderBoxItem = (account: VenueAccountWithVenue | null) => {
    const isPresentInWatchlist = account ? checkWatchlistPresence(account) : false;

    return (
      <ClickAwayListener onClickAway={handleClosePopper}>
        <AddingRow
          key={account?.venueAccountId}
          onClick={(event) => handleVenueAccountClick(event, account, isPresentInWatchlist)}
        >
          <div>{account?.venueAccountName}</div>
          <div>
            {isPresentInWatchlist ? (
              <CheckCircleIcon />
            ) : (
              <PlusSignIcon data-testid="watchlist-add-venue-account-row" />
            )}
          </div>
        </AddingRow>
      </ClickAwayListener>
    );
  };

  const isAnyAccountSelected = accountsPresentInWatchlist.length > 0;
  const thereIsNoAccountToSelect = accountsNotPresentInWatchlist.length === 0;

  return (
    <StyledBoxContainer>
      {isAnyAccountSelected && <StyledLabel>{t('watchlist.alreadyAddedTo')}</StyledLabel>}
      {accountsPresentInWatchlist.map((account) => renderBoxItem(account))}
      {!thereIsNoAccountToSelect && <StyledLabel>{t('watchlist.selectAnAccount')}</StyledLabel>}
      {accountsNotPresentInWatchlist.map((account) => renderBoxItem(account))}
      {accountsPresentInWatchlist.length === 0 &&
        accountsNotPresentInWatchlist.length === 0 &&
        renderBoxItem(null)}
    </StyledBoxContainer>
  );
};

const AddingRow = styled('div')`
  display: flex;
  justify-content: space-between;
  min-width: 200px;
  gap: ${getSpacing(3)};
  padding: 6px ${getSpacing(3)};
  cursor: pointer;
  padding: ${getSpacing(2)} ${getSpacing(3)};
`;

const StyledLabel = styled(Label)`
  padding: 0 ${getSpacing(3)};
  font-size: 11px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;

const StyledBoxContainer = styled('div')`
  padding: ${getSpacing(3)};
`;
