import { Search, StyledSearchColumnWrapper } from '@ui/Search';
import { usePermissions } from '@wyden/hooks/usePermissions';
import { GetRowIdParams } from 'ag-grid-community';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { WydenGrid } from '../grid/WydenGrid';
import { LoadingDataContainer } from '../LoadingDataContainer';
import { staticPermissionsColumnDefinitions } from './staticPermissionsColDef';
import { useGridApiContext } from '@wyden/hooks/useGridApi';

const getRowId = (params: GetRowIdParams) => params.data.resource;

export function StaticPermissions() {
  const { t } = useTranslation();
  const [searchVal, setSearchVal] = useState<string | undefined>('');
  const { permissions, error, loading, refetch } = usePermissions();
  const api = useGridApiContext();
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearchVal(e.target.value);

  return (
    <>
      <StyledSearchColumnWrapper>
        <Search
          fullWidth
          onChange={handleSearch}
          autoFocus={false}
          placeholder={t('common.search')}
        />
      </StyledSearchColumnWrapper>
      <LoadingDataContainer loading={loading} data={permissions} refetch={refetch} error={error}>
        <WydenGrid
          data-testid="venue-account-data-grid"
          getRowId={getRowId}
          rowData={permissions}
          onGridReady={(params) => {
            if (api) {
              api.current = params;
            }
          }}
          columnDefs={staticPermissionsColumnDefinitions}
          quickFilterText={searchVal}
        />
      </LoadingDataContainer>
    </>
  );
}
