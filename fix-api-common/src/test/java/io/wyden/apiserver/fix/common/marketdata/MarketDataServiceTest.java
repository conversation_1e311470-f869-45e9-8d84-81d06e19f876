package io.wyden.apiserver.fix.common.marketdata;

import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.apiserver.fix.common.marketdata.model.SessionScopedMDRequest;
import io.wyden.apiserver.fix.common.referencedata.InstrumentsRepository;
import io.wyden.apiserver.fix.common.security.AccessService;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import quickfix.FieldNotFound;
import quickfix.IncorrectTagValue;
import quickfix.SessionID;
import quickfix.field.MDEntryType;
import quickfix.field.Text;
import quickfix.fix44.MarketDataRequest;
import quickfix.fix44.MarketDataRequestReject;

import java.util.Set;
import java.util.UUID;

import static io.wyden.apiserver.fix.common.marketdata.MarketData.defaultSubscribeRequest;
import static io.wyden.apiserver.fix.common.marketdata.MarketData.defaultUnsubscribeRequest;
import static io.wyden.apiserver.fix.common.marketdata.MarketData.incrementalSubscribeRequest;
import static io.wyden.apiserver.fix.common.marketdata.MarketData.snapshotSubscribeRequest;
import static io.wyden.apiserver.fix.common.marketdata.MarketData.unknownSubscriptionRequestTypeRequest;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.assertArg;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MarketDataServiceTest {

    private static final String BTC_USD = "BTCUSD";
    private static final String VENUE_NAME = "TestVenue";

    MarketDataService marketDataService;
    TestMarketDataRequestHandler testMarketDataRequestHandler;
    @Mock
    MarketDataSessionService marketDataSessionService;
    @Mock
    FixSessionWrapper fixSessionWrapper;
    @Mock
    InstrumentsRepository instrumentsRepository;
    @Mock
    AccessService accessService;

    SessionID session = new SessionID("FIX.4.4", "CLIENT1", "SIMULATOR");

    @BeforeEach
    void setup() {
        marketDataService = new MarketDataService(marketDataSessionService, fixSessionWrapper, instrumentsRepository, accessService);
        testMarketDataRequestHandler = new TestMarketDataRequestHandler(marketDataService);
    }

    @Test
    void shouldCallMarketDataSessionServiceSubscribeMethodForFullRefreshSnapshotUpdateRequest() throws IncorrectTagValue, FieldNotFound {
        // given
        when(instrumentsRepository.find(any())).thenReturn(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setVenueName(VENUE_NAME)
                .setVenueType(VenueType.STREET)
                .build())
            .build());
        when(accessService.hasStreetSideMarketDataAccess(any(), any())).thenReturn(true);
        MarketDataRequest subscribeRequest = defaultSubscribeRequest(1, Set.of(MDEntryType.BID), Set.of(BTC_USD), VENUE_NAME);
        // when
        testMarketDataRequestHandler.onMessage(subscribeRequest, session);
        // then
        ArgumentCaptor<SessionScopedMDRequest> reqCaptor = ArgumentCaptor.forClass(SessionScopedMDRequest.class);
        verify(marketDataSessionService, times(1)).subscribe(reqCaptor.capture(), anyList(), eq(Set.of(MarketDataType.BID)), any());
        assertThat(reqCaptor.getValue().sessionID()).isEqualTo(session);
    }

    @Test
    void shouldThrowErrorForSnapshotRequest() throws IncorrectTagValue, FieldNotFound {
        // given
        MarketDataRequest subscribeRequest = snapshotSubscribeRequest(1, Set.of(MDEntryType.BID), Set.of(BTC_USD), VENUE_NAME);
        // when
        testMarketDataRequestHandler.onMessage(subscribeRequest, session);
        // then
        verify(fixSessionWrapper, timeout(5000).times(1)).send(eq(session), any(MarketDataRequestReject.class));
    }

    @Test
    void shouldRejectForIncrementalRefreshSnapshotUpdateRequest() throws IncorrectTagValue, FieldNotFound {
        // given
        MarketDataRequest subscribeRequest = incrementalSubscribeRequest(1, Set.of(MDEntryType.BID), Set.of(BTC_USD), VENUE_NAME);
        // when
        testMarketDataRequestHandler.onMessage(subscribeRequest, session);
        // then
        verify(fixSessionWrapper, timeout(5000).times(1)).send(eq(session), any(MarketDataRequestReject.class));
    }

    @Test
    void shouldCallMarketDataSessionServiceUnsubscribeMethodForProperRequest() throws FieldNotFound {
        // given
        String mdReqId = UUID.randomUUID().toString();
        MarketDataRequest unsubscribeRequest = defaultUnsubscribeRequest(mdReqId);
        // when
        testMarketDataRequestHandler.onMessage(unsubscribeRequest, session);
        // then
        ArgumentCaptor<SessionScopedMDRequest> reqCaptor = ArgumentCaptor.forClass(SessionScopedMDRequest.class);
        verify(marketDataSessionService, times(1)).unsubscribe(reqCaptor.capture());
        assertThat(reqCaptor.getValue().sessionID()).isEqualTo(session);
        assertThat(reqCaptor.getValue().requestId()).isEqualTo(mdReqId);
    }

    @Test
    void shouldRejectForUnknownSubscriptionRequestType() throws IncorrectTagValue, FieldNotFound {
        // given
        MarketDataRequest unsubscribeRequest = unknownSubscriptionRequestTypeRequest(1, Set.of(MDEntryType.BID), Set.of(BTC_USD), VENUE_NAME);
        // when
        testMarketDataRequestHandler.onMessage(unsubscribeRequest, session);
        // then
        verify(fixSessionWrapper, timeout(5000).times(1)).send(eq(session), any(MarketDataRequestReject.class));
    }

    @Test
    void shouldThrowErrorForNotSupportedSymbol() throws IncorrectTagValue, FieldNotFound {
        // given
        MarketDataRequest subscribeRequest = defaultSubscribeRequest(1, Set.of(MDEntryType.BID), Set.of("UNSUPPORTED_SYMBOL"), VENUE_NAME);
        when(accessService.hasStreetSideMarketDataAccess(any(), any())).thenReturn(true);
        // when
        testMarketDataRequestHandler.onMessage(subscribeRequest, session);
        // then
        verify(fixSessionWrapper, timeout(5000).times(1)).send(eq(session), any(MarketDataRequestReject.class));
    }

    @Test
    void shouldThrowError_ifNoPermissions() {
        // given
        MarketDataRequest subscribeRequest = defaultSubscribeRequest(1, Set.of(MDEntryType.BID), Set.of(BTC_USD), VENUE_NAME);
        when(accessService.hasStreetSideMarketDataAccess(any(), any())).thenReturn(false);
        // when
        testMarketDataRequestHandler.onMessage(subscribeRequest, session);
        // then
        verify(fixSessionWrapper, timeout(5000).times(1))
            .send(eq(session), assertArg(m -> {
                try {
                    assertThat(m.getField(new Text()).getValue())
                        .contains("no access");
                } catch (FieldNotFound e) {
                    throw new RuntimeException(e);
                }
            }));
    }

}
