import ManageSearchIcon from '@mui/icons-material/ManageSearch';
import { ContextDropdown, ContextDropdownItem } from '@ui/ContextDropdown';
import { Label } from '@ui/Typography/Label';
import { styled } from '@ui/styled';
import {
  PortfolioPredicateType,
  PortfolioSearchType,
  TransactionResponse,
} from '@wyden/services/graphql/generated/graphql';
import { ICellRendererParams } from 'ag-grid-community';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { usePortfolioSearch } from '../focus/usePortfolioSearch';
import { useOrderHistoryFilters } from '../orders-history/useOrderHistoryFilters';
import { usePositionFilters } from '../positions/usePositionsFilters';
import { useWorkspaces } from '../useWorkspaces';
import { useTransactionHistoryFilters } from './useTransactionHistoryFilters';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import { logger } from '@wyden/utils/logger';

export const TRANSACTION_HISTORY_ACTIONS_RENDERER_TEST_ID = 'transaction-history-actions-renderer';

enum WidgetType {
  Positions = 'history-positions',
  OrderHistory = 'history-order-history',
}

export function ActionsRenderer({ data }: ICellRendererParams<TransactionResponse>) {
  const { t } = useTranslation();
  const { currentWorkspace } = useWorkspaces();
  const {
    setOrderId: setOrderHistoryOrderId,
    setPortfolios: setOrderHistoryPortfolios,
    clear: clearOrderHistoryFilters,
  } = useOrderHistoryFilters();
  const {
    setCurrencies: setPositionCurrency,
    setPortfolios: setPositionsPortfolios,
    setAccounts: setPositionsAccounts,
    resetWidgetFilters: clearPositionFilters,
  } = usePositionFilters();
  const { setRootExecutionId, clear } = useTransactionHistoryFilters();
  const [searchedPortfolio, setSearchedPortfolio] = useState<
    { portfolioId: string; widget: string } | undefined
  >(undefined);

  usePortfolioSearch({
    skip: !searchedPortfolio?.portfolioId,
    search: {
      portfolioPredicate: {
        method: PortfolioPredicateType.Contains,
        value: searchedPortfolio?.portfolioId || '',
        searchType: PortfolioSearchType.Id,
      },
    },
    fetchPolicy: 'network-only',
    onDataFetched: (portfolioResults) => {
      // portfolioSearch returns all results containing the search string, exact matching must be done manually
      const portfolio = portfolioResults.find(
        (portfolio) => portfolio.id === searchedPortfolio?.portfolioId,
      );
      if (portfolio === undefined) {
        logger.warn(`No portfolio with the search id: ${portfolio} was found`);
      } else {
        if (searchedPortfolio?.widget === WidgetType.OrderHistory) {
          setOrderHistoryPortfolios(WidgetType.OrderHistory)([portfolio]);
        }
        if (searchedPortfolio?.widget === WidgetType.Positions) {
          setPositionsPortfolios(WidgetType.Positions)([portfolio]);
        }
        setSearchedPortfolio(undefined);
      }
    },
  });

  const handleFindAllPositionsClick = () => {
    clearPositionFilters(WidgetType.Positions)();
    /** dirty type checking and casting because the new Transactions type (Settlment) do not have currency property
     * to execute: setPositionCurrency
     */
    if (data && Object.hasOwn(data, 'currency')) {
      const currencies: string[] = [
        (data as { currency: string })?.currency,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (data as any)?.baseCurrency,
      ].filter(Boolean);
      setPositionCurrency(WidgetType.Positions)(currencies as string[]);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((data as any)?.venueAccountName) {
        setPositionsAccounts(WidgetType.Positions)([
          {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            venueAccountId: (data as any)?.venueAccount,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            venueAccountName: (data as any)?.venueAccountName,
          } as VenueAccountWithVenue,
        ]);
      }
    }

    /** dirty type checking and casting because the new Transactions types (Withdrawal and Deposit)do not have portfolio and orderId properties
     */
    if (data && Object.hasOwn(data, 'portfolioId')) {
      setSearchedPortfolio({
        portfolioId: (data as { portfolioId: string })?.portfolioId,
        widget: 'history-positions',
      });
    }
  };

  const handleFindOrderClick = () => {
    clearOrderHistoryFilters(WidgetType.OrderHistory)();
    /** dirty type checking and casting because the new Transactions types (Withdrawal and Deposit)do not have portfolio and orderId properties
     */
    if (data && Object.hasOwn(data, 'orderId')) {
      (data as { orderId: string })?.orderId &&
        setOrderHistoryOrderId(WidgetType.OrderHistory)((data as { orderId: string })?.orderId);
    }
    /** dirty type checking and casting because the new Transactions types (Withdrawal and Deposit)do not have portfolio and orderId properties
     */
    if (data && Object.hasOwn(data, 'portfolioId')) {
      setSearchedPortfolio({
        portfolioId: (data as { portfolioId: string })?.portfolioId,
        widget: WidgetType.OrderHistory,
      });
    }
  };

  const handleShowRelatedTransactionsClick = () => {
    clear();
    setRootExecutionId(
      (data as { rootExecution: { executionId: string } })?.rootExecution?.executionId,
    );
  };

  if (currentWorkspace?.isTrading) {
    return null;
  }

  return (
    <ActionsContainer data-testid={TRANSACTION_HISTORY_ACTIONS_RENDERER_TEST_ID}>
      <ContextDropdown>
        <ContextDropdownItem onClick={handleFindAllPositionsClick}>
          <ManageSearchIcon />
          <Label>{t('transactionsHistory.findAllPositions')}</Label>
        </ContextDropdownItem>

        <ContextDropdownItem onClick={handleFindOrderClick}>
          <ManageSearchIcon />
          <Label>{t('transactionsHistory.findOrder')}</Label>
        </ContextDropdownItem>

        {data && 'rootExecution' in data && (
          <ContextDropdownItem onClick={handleShowRelatedTransactionsClick}>
            <ManageSearchIcon />
            <Label>{t('transactionsHistory.showRelatedTransactions')}</Label>
          </ContextDropdownItem>
        )}
      </ContextDropdown>
    </ActionsContainer>
  );
}

const ActionsContainer = styled('div')`
  display: flex;
  justify-content: flex-end;
`;
