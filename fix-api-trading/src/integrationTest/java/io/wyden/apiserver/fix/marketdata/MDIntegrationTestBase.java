package io.wyden.apiserver.fix.marketdata;

import com.github.javafaker.Faker;
import com.hazelcast.config.AdvancedNetworkConfig;
import com.hazelcast.config.Config;
import com.hazelcast.config.JoinConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.apiserver.fix.common.referencedata.InstrumentsRepository;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.audit.EventLogEvent;
import io.wyden.published.marketdata.MarketDataRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils;

import java.util.Map;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Testcontainers
@ContextConfiguration(initializers = MDIntegrationTestBase.Initializer.class)
@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true", "logging.level.root=debug"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
public abstract class MDIntegrationTestBase {

    public static final String TEST_CLIENT_ID = "test-client";
    public static final String FIX_API_SERVER = "fix-api-trading";
    public static final String VENUE = RandomStringUtils.randomAlphanumeric(4);
    public static final String TEST_SYMBOL = "BTCUSD@FOREX@" + VENUE;

    public static final Network NETWORK = Network.newNetwork();
    static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer("rabbitmq:3.12-management")
        .withNetwork(NETWORK)
        .withReuse(true);

    protected static PostgreSQLContainer<?> POSTGRES = new PostgreSQLContainer<>("postgres")
        .withReuse(true)
        .withDatabaseName("fix_api_trading")
        .withUsername("fix_api_trading")
        .withPassword("password");

    static {
        RABBIT_MQ.start();
        POSTGRES.start();
    }

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    protected RabbitIntegrator rabbitIntegrator;

    protected RabbitExchange<EventLogEvent> eventLogExchange;
    protected RabbitQueue<MarketDataRequest> marketDataRequestQueue;
    protected RabbitQueue<EventLogEvent> eventLogEventQueue1;
    protected RabbitQueue<EventLogEvent> eventLogEventQueue2;

    @Mock
    protected MeterRegistry mockMeterRegistry;
    @Mock
    protected Counter mockCounter;
    @MockBean
    protected InstrumentsRepository instrumentsRepository;

    protected static int randomAcceptorPort;

    @BeforeEach
    void setUpBase() {
        when(instrumentsRepository.find(anyString())).thenReturn(null);

        marketDataRequestQueue = new RabbitQueueBuilder<MarketDataRequest>(rabbitIntegrator).setQueueName("temporary-market-data-request-queue").declare();
        eventLogEventQueue1 = new RabbitQueueBuilder<EventLogEvent>(rabbitIntegrator).setQueueName("temporary-event-log-queue-1").declare();
        eventLogEventQueue2 = new RabbitQueueBuilder<EventLogEvent>(rabbitIntegrator).setQueueName("temporary-event-log-queue-2").declare();
        eventLogExchange = OemsExchange.eventLogExchange(rabbitIntegrator);
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {

            randomAcceptorPort = randomize();

            String urlSuffix = POSTGRES.getHost() + ":" + POSTGRES.getFirstMappedPort() + "/" + POSTGRES.getDatabaseName();

            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword(),

                "mdm-heartbeat-interval=" + "1",

                "fix.socketAccept.port=" + randomAcceptorPort,

                "spring.datasource.url=jdbc:postgresql://" + urlSuffix,
                "spring.flyway.enabled=true"
            );

            values.applyTo(applicationContext);
        }

    }

    private static Integer randomize() {
        return Faker.instance().random().nextInt(9000, 9999);
    }

    @TestConfiguration
    public static class TestConfig {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            AdvancedNetworkConfig advancedNetworkConfig = config.getAdvancedNetworkConfig();
            JoinConfig join = advancedNetworkConfig.getJoin();
            join.getMulticastConfig().setEnabled(false);
            join.getAutoDetectionConfig().setEnabled(false);
            join.getKubernetesConfig().setEnabled(false);
            join.getTcpIpConfig().setEnabled(false);
            join.getDiscoveryConfig().setDiscoveryStrategyConfigs(null);
            config.setClusterName(UUID.randomUUID().toString());
            config.getJetConfig().setEnabled(true);
            config.getJetConfig().setBackupCount(0);
            return Hazelcast.newHazelcastInstance(config);
        }
    }

    public void publishEventLogEvent(EventLogEvent eventLogEvent, String clientId) {
        Map<String, String> headers = Map.of(OemsHeader.CLIENT_ID.getHeaderName(), clientId);
        eventLogExchange.publishWithHeaders(eventLogEvent, headers);
    }
}
