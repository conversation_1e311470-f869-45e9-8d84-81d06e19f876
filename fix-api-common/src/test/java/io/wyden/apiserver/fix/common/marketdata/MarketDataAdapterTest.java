package io.wyden.apiserver.fix.common.marketdata;

import io.wyden.apiserver.fix.common.marketdata.infrastructure.MarketDataEventConsumers;
import io.wyden.apiserver.fix.common.marketdata.model.L1Event;
import io.wyden.apiserver.fix.common.marketdata.model.L2Event;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataDepth;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataIdentifierDTO;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataSubscription;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataSubscriptionKey;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataTypeDTO;
import io.wyden.apiserver.fix.common.marketdata.model.SessionScopedMDRequest;
import io.wyden.cloud.utils.test.TelemetryMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.client.ClientSide;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import quickfix.SessionID;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.UUID;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class MarketDataAdapterTest {
    
    private static final String VENUE = "testVenue";
    private static final String VENUE_ACCOUNT = "testVenueAccount";
    private static final String SYMBOL = "testSymbol";

    @Mock
    SessionID sessionID;
    private MarketDataSubscription marketDataSubscription;

    Telemetry telemetryMock = TelemetryMock.createMock();
    @Mock
    MarketDataEventConsumers marketDataEventConsumers = new MarketDataEventConsumers(telemetryMock);
    MarketDataAdapter marketDataAdapter;
    Sinks.Many<L1Event> l1Sink = Sinks.many().multicast().directBestEffort();
    Sinks.Many<L2Event> l2Sink = Sinks.many().multicast().directBestEffort();
    MarketDataIdentifierDTO marketDataIdentifierDTO;

    @BeforeEach
    void setup() {
        Mockito.when(marketDataEventConsumers.getL1Flux()).thenReturn(l1Sink.asFlux().log());
        Mockito.when(marketDataEventConsumers.getL2Flux()).thenReturn(l2Sink.asFlux().log());
        marketDataAdapter = new MarketDataAdapter(marketDataEventConsumers, null, null, "testApp");
        marketDataIdentifierDTO = new MarketDataIdentifierDTO(SYMBOL, VENUE, VENUE_ACCOUNT, ZonedDateTime.now());
        marketDataSubscription = new MarketDataSubscription(
            new MarketDataSubscriptionKey(SYMBOL, VENUE_ACCOUNT, MarketDataDepth.L1),
            new SessionScopedMDRequest(sessionID, UUID.randomUUID().toString()));
    }

    @Test
    void shouldGetAskEvent() {
        L1Event l1Event = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            BigDecimal.valueOf(100.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.ASK);

        Flux<L1Event> marketDataDTOFlux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);

        StepVerifier
            .create(marketDataDTOFlux)
            .then(() -> l1Sink.tryEmitNext(l1Event))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                marketDataDTO.identifier().instrumentId().equals(SYMBOL) &&
                marketDataDTO.askPrice().equals(BigDecimal.valueOf(100.0)))
            .verifyTimeout(Duration.ofSeconds(1));
    }

    @Test
    void shouldGetBidAndAskEvents() {
        L1Event l1Event1 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(105.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BID);

        L1Event l1Event2 = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            BigDecimal.valueOf(100.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.ASK);
        

        Flux<L1Event> marketDataDTOFlux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);

        StepVerifier
            .create(marketDataDTOFlux)
            .then(() -> l1Sink.tryEmitNext(l1Event1))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(105.0)) &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.bidSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.askSize() == null)
            .then(() -> l1Sink.tryEmitNext(l1Event2))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice() == null &&
                    marketDataDTO.askPrice().equals(BigDecimal.valueOf(100.0)) &&
                    marketDataDTO.bidSize() == null &&
                    marketDataDTO.askSize().equals(BigDecimal.valueOf(1.0)))
            .verifyTimeout(Duration.ofSeconds(1));
    }

    @Test
    void shouldGetBidAndAskAndBidEvents() {
        L1Event bid1 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(105.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BID);

        L1Event ask = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            BigDecimal.valueOf(100.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.ASK);

        L1Event bid2 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(108.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BID);

        Flux<L1Event> marketDataDTOFlux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);

        StepVerifier
            .create(marketDataDTOFlux)
            .then(() -> l1Sink.tryEmitNext(bid1))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(105.0)) &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.bidSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.askSize() == null)
            .then(() -> l1Sink.tryEmitNext(ask))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice() == null &&
                    marketDataDTO.askPrice().equals(BigDecimal.valueOf(100.0)) &&
                    marketDataDTO.bidSize() == null &&
                    marketDataDTO.askSize().equals(BigDecimal.valueOf(1.0)))
            .then(() -> l1Sink.tryEmitNext(bid2))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(108.0)) &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.bidSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.askSize() == null)
            .verifyTimeout(Duration.ofSeconds(1));
    }

    @Test
    void shouldGetBidAndBidAndAskEvents() {
        L1Event bid1 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(105.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BID);
        
        L1Event bid2 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(108.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BID);
        
        L1Event ask = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            BigDecimal.valueOf(100.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.ASK);

        Flux<L1Event> marketDataDTOFlux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);

        StepVerifier
            .create(marketDataDTOFlux)
            .then(() -> l1Sink.tryEmitNext(bid1))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(105.0)) &&
                    marketDataDTO.askPrice() == null)
            .then(() -> l1Sink.tryEmitNext(bid2))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(108.0)) &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.bidSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.askSize() == null)
            .then(() -> l1Sink.tryEmitNext(ask))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice() == null &&
                    marketDataDTO.askPrice().equals(BigDecimal.valueOf(100.0)) &&
                    marketDataDTO.bidSize() == null &&
                    marketDataDTO.askSize().equals(BigDecimal.valueOf(1.0)))
            .verifyTimeout(Duration.ofSeconds(1));
    }

    @Test
    void shouldGetBidAskAndTradeEvents() {
        L1Event bidAsk = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(105.0),
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(103.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BIDASK);

        L1Event trade = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            null,
            null,
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(104.0),
            BigDecimal.valueOf(1.0),
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.TRADE);

        Flux<L1Event> marketDataDTOFlux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);

        StepVerifier
            .create(marketDataDTOFlux)
            .then(() -> l1Sink.tryEmitNext(bidAsk))
            .expectNextMatches(marketDataDTO ->
                    marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(105.0)) &&
                    marketDataDTO.askPrice().equals(BigDecimal.valueOf(103.0)) &&
                    marketDataDTO.lastPrice() == null &&
                    marketDataDTO.lastSize() == null &&
                    marketDataDTO.vol() == null)
            .then(() -> l1Sink.tryEmitNext(trade))
            .expectNextMatches(marketDataDTO ->
                    marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice() == null &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.lastPrice().equals(BigDecimal.valueOf(104.0)) &&
                    marketDataDTO.lastSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.vol().equals(BigDecimal.valueOf(1.0)))
            .verifyTimeout(Duration.ofSeconds(1));
    }

    @Test
    void shouldGetBidAskAndTradeAndTradeAndBidAskEvents() {
        L1Event bidAsk1 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(105.0),
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(103.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BIDASK);

        L1Event trade1 = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            null,
            null,
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(104.0),
            BigDecimal.valueOf(1.0),
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.TRADE);

        L1Event bidAsk2 = new L1Event(
            marketDataIdentifierDTO,
            BigDecimal.valueOf(111.0),
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(103.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BIDASK);

        L1Event trade2 = new L1Event(
            marketDataIdentifierDTO,
            null,
            null,
            null,
            null,
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(110.0),
            BigDecimal.valueOf(1.0),
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.TRADE);

        Flux<L1Event> marketDataDTOFlux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);

        StepVerifier
            .create(marketDataDTOFlux)
            .then(() -> l1Sink.tryEmitNext(bidAsk1))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(105.0)) &&
                    marketDataDTO.askPrice().equals(BigDecimal.valueOf(103.0)) &&
                    marketDataDTO.lastPrice() == null &&
                    marketDataDTO.lastSize() == null &&
                    marketDataDTO.vol() == null)
            .then(() -> l1Sink.tryEmitNext(trade1))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice() == null &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.lastPrice().equals(BigDecimal.valueOf(104.0)) &&
                    marketDataDTO.lastSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.vol().equals(BigDecimal.valueOf(1.0)))
            .then(() -> l1Sink.tryEmitNext(trade2))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice() == null &&
                    marketDataDTO.askPrice() == null &&
                    marketDataDTO.lastPrice().equals(BigDecimal.valueOf(110.0)) &&
                    marketDataDTO.lastSize().equals(BigDecimal.valueOf(1.0)) &&
                    marketDataDTO.vol().equals(BigDecimal.valueOf(1.0)))
            .then(() -> l1Sink.tryEmitNext(bidAsk2))
            .expectNextMatches(marketDataDTO ->
                marketDataDTO.identifier().venueAccount().equals(VENUE_ACCOUNT) &&
                    marketDataDTO.bidPrice().equals(BigDecimal.valueOf(111.0)) &&
                    marketDataDTO.askPrice().equals(BigDecimal.valueOf(103.0)) &&
                    marketDataDTO.lastPrice() == null &&
                    marketDataDTO.lastSize() == null &&
                    marketDataDTO.vol() == null)
            .verifyTimeout(Duration.ofSeconds(1));
    }
}
