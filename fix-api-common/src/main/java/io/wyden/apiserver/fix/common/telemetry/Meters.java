package io.wyden.apiserver.fix.common.telemetry;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientResponseType;

import java.time.Duration;

import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.createMeterFilter;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.createTimer;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.registerMeterFilter;
import static io.wyden.cloudutils.telemetry.metrics.MetricsConfigUtils.toLowerCamel;

public final class Meters {
    public static final String TRADING_REQUEST_INCOMING_LATENCY = "wyden.trading.request.incoming.latency";
    public static final String TRADING_REQUEST_OUTGOING_LATENCY = "wyden.trading.request.outgoing.latency";
    public static final String TRADING_RESPONSE_INCOMING_LATENCY = "wyden.trading.response.incoming.latency";
    public static final String TRADING_RESPONSE_OUTGOING_LATENCY = "wyden.trading.response.outgoing.latency";
    public static final String SECURITY_CHECK_LATENCY = "wyden.security.check.latency";
    public static final String STORAGE_QUERY_LATENCY = "wyden.storage.query.latency";

    public static final String MESSAGE_TYPE_TAG = "messageType";
    public static final String ACCESS_TYPE_TAG = "accessType";
    public static final String QUERY_TYPE_TAG = "queryType";

    public enum AccessType {TRADE, MD, READ}
    public enum QueryType {
        FIX_SESSION_LOCK_ACQUIRE, FIX_SESSION_LOCK_RELEASE, FIX_SESSION_LOCK_REFRESH,
        ORDER_ID_REGISTER, ORDER_ID_FIND, ORDER_ID_EVICT,
        CLIENT_REQUEST_REGISTER, CLIENT_REQUEST_FIND, CLIENT_REQUEST_EVICT,
        MD_TRACKER_ADD, MD_TRACKER_DELETE, MD_TRACKER_FIND, MD_TRACKER_FIND_KEYS, MD_TRACKER_SIZE
    }

    private Meters() {
        // Empty
    }

    public static void configure(MeterRegistry meterRegistry) {
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_REQUEST_INCOMING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(200)));
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_REQUEST_OUTGOING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(5)));
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_RESPONSE_INCOMING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(5)));
        registerMeterFilter(meterRegistry, createMeterFilter(TRADING_RESPONSE_OUTGOING_LATENCY, Duration.ofMillis(0), Duration.ofMillis(5)));
        registerMeterFilter(meterRegistry, createMeterFilter(SECURITY_CHECK_LATENCY, Duration.ofMillis(0), Duration.ofMillis(100)));
        registerMeterFilter(meterRegistry, createMeterFilter(STORAGE_QUERY_LATENCY, Duration.ofMillis(0), Duration.ofMillis(5)));
    }

    public static Timer tradingRequestIncomingLatencyTimer(MeterRegistry meterRegistry, ClientRequestType requestType) {
        return createTimer(meterRegistry, TRADING_REQUEST_INCOMING_LATENCY,
            MESSAGE_TYPE_TAG, requestType.name());
    }

    public static Timer tradingRequestOutgoingLatencyTimer(MeterRegistry meterRegistry, ClientRequestType requestType) {
        return createTimer(meterRegistry, TRADING_REQUEST_OUTGOING_LATENCY,
            MESSAGE_TYPE_TAG, requestType.name());
    }

    public static Timer tradingResponseIncomingLatencyTimer(MeterRegistry meterRegistry, ClientResponseType responseType) {
        return createTimer(meterRegistry,TRADING_RESPONSE_INCOMING_LATENCY,
            MESSAGE_TYPE_TAG, responseType.name());
    }

    public static Timer tradingResponseOutgoingLatencyTimer(MeterRegistry meterRegistry, ClientResponseType responseType) {
        return createTimer(meterRegistry,TRADING_RESPONSE_OUTGOING_LATENCY,
            MESSAGE_TYPE_TAG, responseType.name());
    }

    public static Timer securityCheckLatencyTimer(MeterRegistry meterRegistry, AccessType accessType) {
        return createTimer(meterRegistry, SECURITY_CHECK_LATENCY,
            ACCESS_TYPE_TAG, toLowerCamel(accessType.name()));
    }

    public static Timer storageQueryLatencyTimer(MeterRegistry meterRegistry, QueryType queryType) {
        return createTimer(meterRegistry, STORAGE_QUERY_LATENCY,
            QUERY_TYPE_TAG, toLowerCamel(queryType.name()));
    }

}
