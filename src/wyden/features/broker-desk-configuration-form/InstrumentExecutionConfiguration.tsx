import { useTranslation } from 'react-i18next';
import {
  ConfigurationType,
  CurrencyType,
  ExecutionMode,
  InstrumentConfiguration,
  InstrumentGroupConfiguration,
  PortfolioConfiguration,
  PortfolioGroupConfiguration,
  PortfolioResponse,
  TradingMode,
} from '@wyden/services/graphql/generated/graphql';
import { useBrokerDeskStore } from './useBrokerDeskStore';
import { useBrokerDeskConfig } from './useBrokerDeskConfig';
import {
  ExecutionConfigurationForm,
  ExecutionConfigurationSchema,
  getExecutionConfigurationFormProps,
} from './FormConfigs';
import { InstrumentsExecutionConfigurationFormContent } from './InstrumentsExecutionConfigurationFormContent';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import { z } from 'zod';
import {
  convertInstrumentPricingConfigReadToWrite,
  getBooleanFieldToSend,
  getCurrencyTypeToSend,
  getFieldToSend,
  getInstrumentExecutionConfigurationDefaultValues,
  getNumberFieldToSend,
  getPercentageFieldToSend,
  isFulfilledValue,
} from './utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { ConfigurationFormHeader } from './CommonItems';
import { LoadingDataContainer } from '../LoadingDataContainer';

type Props = {
  instrumentConfiguration?: InstrumentConfiguration | InstrumentGroupConfiguration | null;
  parentInstrumentConfiguration?: InstrumentConfiguration | InstrumentGroupConfiguration | null;
  portfolioOrGroupConfig: PortfolioConfiguration | PortfolioGroupConfiguration | undefined | null;
  parentConfiguration?: PortfolioConfiguration | PortfolioGroupConfiguration | null;
  portfolios: PortfolioResponse[];
  getVenueForAccountId: (id: string) => string | null;
  getAccountForId: (id: string | undefined) => VenueAccountWithVenue | undefined;
};

export function InstrumentExecutionConfiguration({
  instrumentConfiguration,
  parentInstrumentConfiguration,
  portfolioOrGroupConfig,
  portfolios,
  getAccountForId,
  parentConfiguration,
}: Props) {
  const { t } = useTranslation();
  const editingInstrument = useBrokerDeskStore((state) => state.editingInstrument);
  const editingInstrumentId =
    editingInstrument && editingInstrument?.instrumentIdentifiers?.instrumentId;
  const tradable = useBrokerDeskStore((state) => state.tradable);
  const tradableTouched = useBrokerDeskStore((state) => state.tradableTouched);

  const executionConfiguration = instrumentConfiguration?.executionConfiguration;
  const pricingConfiguration = instrumentConfiguration?.pricingConfiguration;
  const parentInstrumentExecutionConfiguration =
    parentInstrumentConfiguration?.executionConfiguration;

  const defaultValues = getInstrumentExecutionConfigurationDefaultValues({
    instrumentConfig: executionConfiguration,
    portfolioConfig: portfolioOrGroupConfig?.executionConfiguration,
    parentInstrumentConfig: parentConfiguration?.instrumentConfiguration?.find(
      (config) => config.instrumentId === editingInstrumentId,
    )?.executionConfiguration,
    portfolioGroupConfig: parentConfiguration?.executionConfiguration,
    getAccountForId,
    portfolios,
  });

  const form = useForm<z.infer<typeof ExecutionConfigurationSchema>>({
    resolver: zodResolver(ExecutionConfigurationSchema),
    defaultValues,
  });
  const {
    updateInstrumentConfiguration,
    resetInstrumentConfiguration,
    resource,
    configurationLoading,
  } = useBrokerDeskConfig({
    resetInstrumentExecutionForm: form.reset,
  });
  const { currencies, shouldSendInstrumentExecutionField } = useBrokerDeskConfig();

  const parentExecutionMode =
    portfolioOrGroupConfig?.executionConfiguration?.executionMode ||
    parentInstrumentExecutionConfiguration?.executionMode ||
    parentConfiguration?.executionConfiguration?.executionMode;

  const handleExecutionConfigurationSubmit = (
    values: z.infer<typeof ExecutionConfigurationSchema>,
  ) => {
    if (editingInstrumentId) {
      const shouldSendPercentageBase =
        shouldSendInstrumentExecutionField('percentageFeeCurrency') &&
        getFieldToSend(values.percentageFeeCurrency?.id) === CurrencyType.BaseCurrency;
      const shouldSendPercentageQuote =
        shouldSendInstrumentExecutionField('percentageFeeCurrency') &&
        getFieldToSend(values.percentageFeeCurrency?.id) === CurrencyType.QuoteCurrency;
      const shouldSendPercentageSpecific =
        shouldSendInstrumentExecutionField('percentageFeeCurrency') &&
        Boolean(getFieldToSend(values.percentageFeeCurrency?.id));

      const percentageFeeCurrencyType = getCurrencyTypeToSend(
        shouldSendPercentageBase,
        shouldSendPercentageQuote,
        shouldSendPercentageSpecific,
      );

      updateInstrumentConfiguration({
        instrumentId: editingInstrumentId,
        tradeable:
          isFulfilledValue(instrumentConfiguration?.tradeable) || tradableTouched
            ? tradable
            : undefined,
        pricingConfiguration: convertInstrumentPricingConfigReadToWrite(pricingConfiguration),
        executionConfiguration: {
          agencyTradingAccount: shouldSendInstrumentExecutionField('agencyTradingAccount')
            ? getFieldToSend(values.agencyTradingAccount?.id)
            : undefined,
          chargeExchangeFee: shouldSendInstrumentExecutionField('chargeExchangeFee')
            ? getBooleanFieldToSend(values.chargeExchangeFee)
            : undefined,
          counterPortfolioId: shouldSendInstrumentExecutionField('counterPortfolioId')
            ? getFieldToSend(values.counterPortfolioId?.id)
            : undefined,
          discloseTradingVenue: shouldSendInstrumentExecutionField('discloseTradingVenue')
            ? getBooleanFieldToSend(values.discloseTradingVenue)
            : undefined,
          tradingMode: TradingMode.Agency,
          fixedFee: shouldSendInstrumentExecutionField('fixedFee')
            ? getNumberFieldToSend(values.fixedFee)
            : undefined,
          fixedFeeCurrency: shouldSendInstrumentExecutionField('fixedFeeCurrency')
            ? getFieldToSend(values.fixedFeeCurrency?.id)
            : undefined,
          percentageFeeCurrencyType: percentageFeeCurrencyType,
          minFee: shouldSendInstrumentExecutionField('minFee')
            ? getNumberFieldToSend(values.minFee)
            : undefined,
          minFeeCurrency: shouldSendInstrumentExecutionField('minFeeCurrency')
            ? getFieldToSend(values.minFeeCurrency?.id)
            : undefined,
          agencyTargetInstrumentId: shouldSendInstrumentExecutionField('agencyTargetInstrument')
            ? getFieldToSend(values.agencyTargetInstrument?.instrumentIdentifiers?.instrumentId)
            : undefined,
          percentageFee: shouldSendInstrumentExecutionField('percentageFee')
            ? getPercentageFieldToSend(values.percentageFee)
            : undefined,
          percentageFeeCurrency: shouldSendInstrumentExecutionField('percentageFeeCurrency')
            ? getFieldToSend(values.percentageFeeCurrency?.id)
            : undefined,
          executionMode: shouldSendInstrumentExecutionField('executionMode')
            ? values.executionMode
              ? ExecutionMode.Sor
              : ExecutionMode.Simple
            : !parentExecutionMode
            ? ExecutionMode.Simple
            : undefined,
          sorTradingAccounts: shouldSendInstrumentExecutionField('sorTradingAccountDescs')
            ? values.sorTradingAccountDescs?.map(({ venueAccountId }) => venueAccountId)
            : undefined,
          sorTarget: shouldSendInstrumentExecutionField('sorTarget') ? values.sorTarget : undefined,
        },
      });
    }
  };

  const resetConfig = () =>
    editingInstrumentId &&
    resetInstrumentConfiguration(ConfigurationType.Execution, editingInstrumentId);

  return (
    <LoadingDataContainer loading={configurationLoading} data={configurationLoading ? false : true}>
      <ConfigurationFormHeader onReset={resetConfig} title={t('brokerDesk.configuration')} />
      <ExecutionConfigurationForm
        onSubmit={handleExecutionConfigurationSubmit}
        props={getExecutionConfigurationFormProps(
          t,
          currencies,
          portfolios,
          Boolean(!resource.managable),
          {
            configName: 'execution',
            scope: resource.type === 'portfolio' ? 'Instrument' : 'PortfolioGroupInstrument',
          },
        )}
        form={form}
        schema={ExecutionConfigurationSchema}
      >
        {(fields) => <InstrumentsExecutionConfigurationFormContent fields={fields} />}
      </ExecutionConfigurationForm>
    </LoadingDataContainer>
  );
}
