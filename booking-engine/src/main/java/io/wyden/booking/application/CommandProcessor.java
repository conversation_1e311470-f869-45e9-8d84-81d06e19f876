package io.wyden.booking.application;

import com.google.protobuf.Message;
import io.wyden.booking.application.state.StateInput;
import io.wyden.booking.application.state.StateOutput;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.interfaces.rabbit.CommandResultEmitter;
import io.wyden.published.common.Error;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.Result;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;

import java.util.stream.Collectors;

import static io.wyden.booking.utils.StringUtils.toHumanReadableString;
import static io.wyden.published.common.MetadataUtil.asResponseTo;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * Responsibilities:
 * - preprocessAndSave (should work on dedicated single thread, sequenced processing)
 *      -> receive the Command (Rabbit msg - OemsRequest/Response, ReservationRequest, TransactionRequest, SettlementRequest etc)
 *      -> preprocess - map, enrich with rates, create Transaction with LedgerEntries
 *      -> save -> save:
 *          - original Request,
 *          - Transaction,
 *          - LedgerEntry rows (LEs grouped by Portfolio/Account for parallel processing),
 *          - TransactionalOutbox entry (Transaction id + processed LE tracking + original Request reference)
 * - book (should work in parallel with dedicated thread per Portfolio/Account so we avoid any locking on writes (we should still be able to read immutable Transaction rows though))
 *      -> get LEs of Portfolio/Account since last update
 *      -> apply & commit updates to DB
 *      -> emit PositionUpdate events
 *      -> update TransactionalOutbox tracking table
 * - postProcess
 *      In general, we want to fire an event that will announce the Command as processed (successfully or not).
 *      For now, it will happen in the same process, but eventually it should be decoupled with TransactionalOtbox pattern.
 *      -> periodically check TransactionalOutbox tracking table for ProcessedTransactions
 *      -> post process, by firing a strategy (use different strategies per Transaction type)
 *      -> delete TransactionalOutbox row
 *
 */
public interface CommandProcessor<C extends Message, I extends StateInput> {

    Logger LOGGER = getLogger(CommandProcessor.class);

    Class<C> getSupportedMessageClass();

    void tryMarkCompleted(C command);

    /**
     * Consumes an incoming Command message for further processing.
     * Responsibilities:
     * - Detect duplicates and discard them
     * - Evaluate Command if applicable for processing by Booking engine and discard if not applicable
     *      - Some Commands require a report event to be sent, even if Booking engine processing is not necessary
     *      - Such events will be sent via CommandProcessor.onSkip()
     * - Durably enqueue Command in DB
     *      - Some Commands require preprocessing to be able to be processed later in a queue
     *          - For example, a current rate (at the time of receiving the event) may be required
     *              - in that case, source the rate or discard the Command if rate not found
     * - Mark Command as enqueued (needed for deduplication of next input Commands)
     */
    @Transactional
    default void enqueue(C command) {
        if (isCompletedOrEnqueued(command)) {
            LOGGER.warn("Duplicate message enqueue attempt - skipping: {}", command);
            return;
        }

        if (shouldProcess(command)) {
            preProcessAndSave(command);
        } else {
            onSkip(command);
        }

        tryMarkEnqueued(command); // or rollback on duplicate
    }

    void tryMarkEnqueued(C command);

    boolean isCompletedOrEnqueued(C command);

    default boolean shouldProcess(C message) {
        return true;
    }

    /**
     * Preprocess the Command before it can be put on a durable queue.
     * E.g. collect and set current market rates
     * (has to happen at the time of receiving the event so that archived events can be replayed any time and produce the same, deterministic results)
     */
    @Transactional
    C preProcessAndSave(C message);

    /**
     * Collect the state view of Booking application that will be needed for Command processing
     */
    @Transactional
    I fetchStateInput(C command);

    /**
     * Stateless.
     * Produces a result in the form of a StateOutput to be applied on cache and/or the booking DB.
     */
    @Transactional
    StateOutput book(C persistedCommand, I stateInput);

    /**
     * Triggers side effects after successful processing, e.g. emit Position changes, etc.
     */
    void onSuccess(C command, StateOutput stateOutput);

    /**
     * Triggers side effects after failed processing, e.g. Reservation reject events.
     */
    void onError(C command, Exception processingError);

    /**
     * Triggers side effects after skipped processing if required, e.g., current Position state
     * (There are Commands that optionally require to mutate Position state, and always require to emit current Position state after processing)
     */
    default void onSkip(C command) {
        // implement side effects of skipped processing in concrete classes if needed
    }

    default void debug(StateOutput stateOutput) {
        String ledgerEntriesDescription = stateOutput.processedLedgerEntries().stream()
            .map(LedgerEntry::toString)
            .collect(Collectors.joining(System.lineSeparator()));

        LOGGER.info("Booking Engine finished processing of type {} and applied the following ledger entries to positions: \n{}", stateOutput.getProcessingType(), ledgerEntriesDescription);

        String positionsDescription = stateOutput.processedPositions().values().stream()
            .map(position -> "Position: %s %s qty=%s pending=%s available=%s realized=%s".formatted(position.getReference(), position.getInstrument(),
                toHumanReadableString(position.getQuantity()),
                toHumanReadableString(position.getPendingQuantity()),
                toHumanReadableString(position.getAvailableForTradingQuantity()),
                toHumanReadableString(position.getNetRealizedPnl())))
            .collect(Collectors.joining(System.lineSeparator()));

        LOGGER.info("Affected positions: \n{}", positionsDescription);
    }

    default void emitErrorResponse(CommandResultEmitter commandResultEmitter, Exception processingError, Metadata originalRequestMetadata) {
        commandResultEmitter.emit(Result.newBuilder()
            .setMetadata(asResponseTo(originalRequestMetadata))
            .setError(Error.newBuilder()
                .setException(processingError.getClass().getName())
                .setMessage(processingError.getMessage())
                .build()
            )
            .build());
    }
}
