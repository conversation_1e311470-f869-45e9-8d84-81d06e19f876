import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface InitiateNewSettlementStore {
  initiateNewSettlementDialogOpen: boolean;
  initiateNewSettlementSnackbarOpen: boolean;
  toggleInitiateNewSettlementDialog: () => void;
  toggleInitiateNewSettlementSnackbar: () => void;
  isDataLoading: boolean;
  dataLoaded: () => void;
  dataLoading: () => void;
}

const INITIATE_NEW_SETTLEMENT_ACTIONS = {
  TOGGLE_INITIATE_NEW_SETTLEMENT_DIALOG: 'toggleInitiateNewSettlementDialog',
  TOGGLE_INITIATE_NEW_SETTLEMENT_SNACKBAR: 'toggleInitiateNewSettlementSnackbar',
  DATA_LOADED: 'dataLoaded',
  DATA_LOADING: 'dataLoading',
} as const;

export const useInitiateNewSettlementStore = create<InitiateNewSettlementStore>()(
  devtools(
    (set) => ({
      initiateNewSettlementDialogOpen: false,
      initiateNewSettlementSnackbarOpen: false,
      isDataLoading: false,
      dataLoaded: () =>
        set(
          () => ({
            isDataLoading: false,
          }),
          false,
          INITIATE_NEW_SETTLEMENT_ACTIONS.DATA_LOADED,
        ),
      dataLoading: () =>
        set(
          () => ({
            isDataLoading: true,
          }),
          false,
          INITIATE_NEW_SETTLEMENT_ACTIONS.DATA_LOADING,
        ),
      toggleInitiateNewSettlementSnackbar: () =>
        set(
          (state) => ({
            initiateNewSettlementSnackbarOpen: !state.initiateNewSettlementSnackbarOpen,
          }),
          false,
          INITIATE_NEW_SETTLEMENT_ACTIONS.TOGGLE_INITIATE_NEW_SETTLEMENT_SNACKBAR,
        ),
      toggleInitiateNewSettlementDialog: () =>
        set(
          (state) => ({
            initiateNewSettlementDialogOpen: !state.initiateNewSettlementDialogOpen,
          }),
          false,
          INITIATE_NEW_SETTLEMENT_ACTIONS.TOGGLE_INITIATE_NEW_SETTLEMENT_DIALOG,
        ),
    }),
    { name: 'InitiateNewSettlementStore' },
  ),
);
