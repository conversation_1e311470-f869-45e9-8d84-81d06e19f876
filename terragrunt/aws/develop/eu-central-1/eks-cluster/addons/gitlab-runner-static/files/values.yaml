unregisterRunners: false

runners:
  tags: "kubernetes-static"
  runUntagged: true
  config: |
    [[runners]]
      [runners.kubernetes]
        namespace = "{{.Release.Namespace}}"
        service_account = "{{ include "gitlab-runner.fullname" . }}"
        image = "alpine"
        privileged = true
        poll_interval = 3
        poll_timeout = 300

        cpu_limit = "1"
        cpu_request = "0.5"
        memory_limit = "1024Mi"
        memory_request = "512Mi"

        # service containers
        service_cpu_request = "0.2"
        service_memory_request = "256Mi"
        service_cpu_limit = "1"
        service_memory_limit = "4Gi"

        # helper container
        helper_cpu_request = "0.3"
        helper_memory_request = "512Mi"
        helper_cpu_limit = "0.6"
        helper_memory_limit = "2048Mi"

        image_pull_secrets = ["regcred"]
        helper_image_autoset_arch_and_os = true

      [runners.kubernetes.pod_annotations]
        "karpenter.sh/do-not-disrupt" = "true"
      [runners.kubernetes.node_selector]
        environment = "dev"
        kubernetes.io/arch: "arm64"
        kubernetes.io/os: "linux"
      [runners.kubernetes.node_tolerations]
        "dev=true" = "NoSchedule"

rbac:
  create: true
  clusterWideAccess: true
  rules:
    - apiGroups:
        [
          ""
        ]
      resources: ["*"]
      verbs: ["*"]

nodeSelector:
  environment: dev
  kubernetes.io/arch: "arm64"
  kubernetes.io/os: "linux"

tolerations:
  - key: "dev"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
