import '../../../../../cypress/support/component';
import { FormPO } from '../../form/__test__/FormPO.cy';

export class PricingConfigurationPO {
  private formPO = new FormPO();

  selectMultiPriceSource(value: string[]) {
    this.formPO.selectMultiByLabelText('Price source accounts', value);
  }

  selectPriceSource(value: string) {
    this.formPO.selectByLabelText('Account price source', value);
  }

  insertMarkup(value: string) {
    this.formPO.insertByLabelText('Markup [%]', value);
  }

  expectSuccessMessage() {
    return cy.findByText('Saving configuration was successful');
  }

  save() {
    cy.findByTestId('save-pricing-configuration').click({ force: true });
  }
}
