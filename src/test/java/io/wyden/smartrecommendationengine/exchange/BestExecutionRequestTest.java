package io.wyden.smartrecommendationengine.exchange;

import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.published.smartrecommendationengine.Strategy;
import io.wyden.smartrecommendationengine.assertions.RankedCandidatesAssertions;
import io.wyden.smartrecommendationengine.domain.RecommendationSubscription;
import io.wyden.smartrecommendationengine.service.SmartRecommendationEngine;
import io.wyden.smartrecommendationengine.service.audit.AuditService;
import io.wyden.smartrecommendationengine.service.connectorstate.ConnectorStateService;
import io.wyden.smartrecommendationengine.service.marketdata.MarketDataCache;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static io.wyden.published.oems.OemsSide.BUY;
import static io.wyden.published.oems.OemsSide.SELL;
import static io.wyden.smartrecommendationengine.assertions.RankedCandidatesAssertions.assertThat;
import static java.util.Optional.empty;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BestExecutionRequestTest extends BestExecutionRequestTestBase {
    @Mock
    private MarketDataCache marketDataCache;
    @Mock
    private ConnectorStateService connectorStateService;
    @Mock
    private SmartRecommendationEngine smartRecommendationEngine;

    @Mock
    private RecommendationSubscription recommendationSubscription;

    @Mock
    private AuditService auditService;

    private final int stopOrderTriggerRequiredVenuesPercentage = 100;
    private final int maxMarketDataAge = 6000;

    public BestExecutionRequestTest() {
        marketDataCache = mock(MarketDataCache.class);
        connectorStateService = mock(ConnectorStateService.class);
        auditService = mock(AuditService.class);
        doNothing().when(auditService).sendAuditEventWithOrderBooks(anyList(), anyString(), anyString());

        when(connectorStateService.isTradingAlive(BITMEX.getVenueAccount())).thenReturn(true);
        when(connectorStateService.isTradingAlive(BINANCE.getVenueAccount())).thenReturn(true);
    }
    
    @Test
    public void testBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "1100", "5"),
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2200", "5")
        );
    }

    @Test
    public void testSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "8800", "10"),
            new RankedCandidatesAssertions.RankingResult(BINANCE, "4400", "10")
        );
    }

    @Test
    public void testOBNotDeepEnoughForOneCandidateSellOrder() {
        String quantity = "10";

        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockNotDeepEnoughOrderBook(BINANCE, quantity));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity(quantity)
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "8800", "10"),
            new RankedCandidatesAssertions.RankingResult(BINANCE, "4100", "9")
        );
    }

    @Test
    public void testOBNotDeepEnoughForOneCandidateBuyOrder() {
        String quantity = "5";

        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockNotDeepEnoughOrderBook(BINANCE, quantity));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity(quantity)
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1000", "3"),
            new RankedCandidatesAssertions.RankingResult(BITMEX, "1100", "5")
        );
    }

    @Test
    public void testOBNotDeepEnoughForBothCandidatesSellOrder() {
        String quantity = "10";

        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockNotDeepEnoughOrderBook(BITMEX, quantity));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockNotDeepEnoughOrderBook(BINANCE, quantity));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity(quantity)
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "4100", "9"),
            new RankedCandidatesAssertions.RankingResult(BINANCE, "4100", "9")
        );
    }

    @Test
    public void testOBNotDeepEnoughForBothCandidatesBuyOrder() {
        String quantity = "5";

        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockNotDeepEnoughOrderBook(BITMEX, quantity));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockNotDeepEnoughOrderBook(BINANCE, quantity));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity(quantity)
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "1000", "3"),
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1000", "3")
        );
    }

    @Test
    public void testOBNotPresentForOneCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "1100", "5")
        );
    }

    @Test
    public void testOBNotPresentForOneCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "8800", "10")
        );
    }

    @Test
    public void testOBNotPresentForBothCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

    @Test
    public void testOBNotPresentForBothCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

    @Test
    public void testOBTooOldForOneCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "8800", "10")
        );
    }

    @Test
    public void testOBTooOldForOneCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockBestOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BITMEX, "3000", "10")
        );
    }

    @Test
    public void testOBTooOldForTwoCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockTooOldOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

    @Test
    public void testOBTooOldForTwoCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(BITMEX)).thenReturn(mockTooOldOrderBook(BITMEX));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(BITMEX, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

}
