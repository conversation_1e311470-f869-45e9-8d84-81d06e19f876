package io.wyden.agencytrading.it;

import io.wyden.published.brokerdesk.TernaryBool;
import io.wyden.published.oems.FeeBasis;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsExecRestatementReason;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

class StandardFlowIntegrationTest extends AgencyTradingIntegrationTestBase {

    @Test
    void shouldBeFilledAllAtOnce() {
        //OemsRequest is sent from OG. Order gets consumed in ATS and creates street order. It is sent to the OC
        OemsRequest clientOrder = orderGateway.emitsOemsRequest(testingData.clientSingleOrderRequest(OemsOrderType.MARKET, null, null));

        OemsRequest streetOrder = collider.awaitNewChildOrderRequest(clientOrder.getOrderId());
        assertThat(streetOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(CHILD_ORDER_AGENCY_INSTRUMENT);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CHILD_ORDER_AGENCY_ACCOUNT);
            assertThat(request.getQuantity()).isEqualTo(clientOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(clientOrder.getOrderType());
            assertThat(request.getSide()).isEqualTo(clientOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(clientOrder.getOrderId());
        });

        OemsResponse parentPendingNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_NEW);
        assertExecReportPendingNewFor(parentPendingNew, clientOrder);

        collider.emitExecutionReportNew(streetOrder);

        //OC sends back street order execution report with status NEW. ATS processes child's execution report and emits execution report for parent order with status NEW back to OG
        OemsResponse parentExecNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);
        assertThat(parentExecNew).satisfies(response -> {
            assertExecReportFor(response, clientOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.NEW);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_NEW);
            assertThat(response.getOrderQty()).isEqualTo(clientOrder.getQuantity());
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(clientOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });

        //OC emits execution report for street order with FILLED status. ATS consumes execution report and emits another with FILLED status for parent order back to OG
        collider.emitExecutionReportFilled(streetOrder);

        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, clientOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getOrderQty()).isEqualTo(clientOrder.getQuantity());
            assertThat(response.getCumQty()).isEqualTo(clientOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
            // underlyingVenueAccount is only populated if `discloseTradingVenue` is set to true (tested by another test case)
            assertThat(response.getUnderlyingVenueAccount()).isBlank();
        });
    }

    @Test
    void shouldDiscloseTradingVenue() {
        //OemsRequest is sent from OG. Order gets consumed in ATS and creates street order. It is sent to the OC
        OemsRequest oemsRequest = testingData.clientSingleOrderRequest(OemsOrderType.MARKET, null, null);
        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(oemsRequest.getExecutionConfig().toBuilder()
                .setDiscloseTradingVenue(TernaryBool.TRUE))
            .build();

        OemsRequest clientOrder = orderGateway.emitsOemsRequest(oemsRequest);

        OemsRequest streetOrder = collider.awaitNewChildOrderRequest(clientOrder.getOrderId());

        OemsResponse pendingNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_NEW);
        assertExecReportPendingNewFor(pendingNew, clientOrder);

        collider.emitExecutionReportNew(streetOrder);

        //OC sends back street order execution report with status NEW. ATS processes child's execution report and emits execution report for parent order with status NEW back to OG
        orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);

        //OC emits execution report for street order with FILLED status. ATS consumes execution report and emits another with FILLED status for parent order back to OG
        collider.emitExecutionReportFilled(streetOrder);

        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, clientOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getOrderQty()).isEqualTo(clientOrder.getQuantity());
            assertThat(response.getUnderlyingVenueAccount()).isEqualTo(streetOrder.getVenueAccount());
        });
    }

    @Test
    void shouldPropagateExchangeFee() {
        //OemsRequest is sent from OG. Order gets consumed in ATS and creates street order. It is sent to the OC
        OemsRequest oemsRequest = testingData.clientSingleOrderRequest(OemsOrderType.MARKET, null, null);
        oemsRequest = oemsRequest.toBuilder()
            .setExecutionConfig(oemsRequest.getExecutionConfig().toBuilder()
                .setChargeExchangeFee(TernaryBool.TRUE)
            )
            .build();

        OemsRequest agencyOrder = orderGateway.emitsOemsRequest(oemsRequest);

        OemsRequest streetOrder = collider.awaitNewChildOrderRequest(agencyOrder.getOrderId());

        OemsResponse parentPendingNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(agencyOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_NEW);
        assertExecReportPendingNewFor(parentPendingNew, agencyOrder);

        collider.emitExecutionReportNew(streetOrder);

        //OC sends back street order execution report with status NEW. ATS processes child's execution report and emits execution report for parent order with status NEW back to OG
        orderGateway.awaitExecutionReportWithOrderStatusReceived(agencyOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);

        //OC emits execution report for street order with CALCULATED status which contains only fee. ATS consumes execution report and emits another with FILLED status for parent order back to OG
        FeeData exchangeFee = FeeData.newBuilder()
            .setBasis(FeeBasis.ABSOLUTE)
            .setType(FeeType.EXCHANGE_FEE)
            .setCurrency("USD")
            .setAmount("2")
            .build();
        collider.emitExecutionReportCalculated(streetOrder, exchangeFee);

        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithExecTypeReceived(agencyOrder.getOrderId(), OemsExecType.CALCULATED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, agencyOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CALCULATED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_NEW);
            assertThat(response.getOrderQty()).isEqualTo(agencyOrder.getQuantity());
            assertThat(response.getFeeDataList()).containsExactly(exchangeFee);
        });
    }

    @Test
    void shouldReduceOrderQty() {
        //OemsRequest is sent from OG. Order gets consumed in ATS and creates street order. It is sent to the OC
        OemsRequest clientOrder = orderGateway.emitsOemsRequest(testingData.clientSingleOrderRequest(OemsOrderType.MARKET, null, null));

        OemsRequest streetOrder = collider.awaitNewChildOrderRequest(clientOrder.getOrderId());

        OemsResponse clientPendingNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_NEW);
        assertExecReportPendingNewFor(clientPendingNew, clientOrder);

        collider.emitExecutionReportNew(streetOrder);

        //OC sends back street order execution report with status NEW. ATS processes child's execution report and emits execution report for parent order with status NEW back to OG
        orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);

        collider.emitExecutionReportRestated(streetOrder, OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);

        OemsResponse parentExecRestated = orderGateway.awaitExecutionReportWithExecTypeReceived(clientOrder.getOrderId(), OemsExecType.RESTATED);
        assertThat(parentExecRestated).satisfies(response -> {
            assertExecReportFor(response, clientOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.RESTATED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_NEW);
            assertThat(response.getOrderQty()).isEqualTo(testingData.quantityRestated);
        });

        collider.emitExecutionReportFilled(streetOrder, testingData.quantityRestated, testingData.quantityRestated);

        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(clientOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, clientOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getOrderQty()).isEqualTo(testingData.quantityRestated);
            assertThat(response.getCumQty()).isEqualTo(testingData.quantityRestated);
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    public void shouldBeFilledIn2PartialFills() {
        //OemsRequest is sent from OG. Order gets consumed in ATS and creates child order. It is sent to the OC
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSingleOrderRequest(OemsOrderType.MARKET, null, null));

        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId());
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(CHILD_ORDER_AGENCY_INSTRUMENT);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CHILD_ORDER_AGENCY_ACCOUNT);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(smartOrder.getOrderType());
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        OemsResponse parentPendingNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_NEW);
        assertExecReportPendingNewFor(parentPendingNew, smartOrder);

        collider.emitExecutionReportNew(childOrder);

        //OC sends back child order execution report with status NEW. ATS processes child's execution report and emits execution report for parent order with status NEW back to OG
        OemsResponse parentExecNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);
        assertThat(parentExecNew).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.NEW);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_NEW);
            assertThat(response.getOrderQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });

        //OC emits execution report for child order with PARTIALLY_FILLED status
        collider.emitExecutionReportPartiallyFilled(childOrder, "5", "5", "5");

        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getOrderQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getCumQty()).isEqualTo("5");
            assertThat(response.getLeavesQty()).isEqualTo("5");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        //OC emits execution report for child order with FILLED status. ATS consumes execution report and emits another with FILLED status for parent order back to OG
        collider.emitExecutionReportFilled(childOrder, "5", "10");

        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getOrderQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }
}
