package io.wyden.booking.snapshotter.application.wal;

import io.wyden.booking.snapshotter.application.transaction.TransactionToProtoMapper;
import io.wyden.booking.snapshotter.domain.CommandResultEmitter;
import io.wyden.booking.snapshotter.domain.state.StateOutput;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.common.Error;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.Result;
import io.wyden.published.common.Success;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.UUID;

import static io.wyden.booking.snapshotter.infrastructure.telemetry.TelemetryConfig.SERVICE;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
public class TransactionPostprocessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionPostprocessor.class);

    private final CommandResultEmitter commandResultEmitter;

    public TransactionPostprocessor(CommandResultEmitter commandResultEmitter) {
        this.commandResultEmitter = commandResultEmitter;
    }

    public void postProcess(TransactionSnapshot transactionSnapshot, StateOutput stateOutput) {
        if (stateOutput.isSuccessful()) {
            emitSuccess(transactionSnapshot, stateOutput);
        } else {
            emitFailure(transactionSnapshot, stateOutput);
        }
    }

    private void emitFailure(TransactionSnapshot transactionSnapshot, StateOutput stateOutput) {
        Result.Builder result = Result.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(SERVICE)
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .setError(Error.newBuilder()
                .setMessage(stateOutput.reason())
                .build());

        if (transactionSnapshot.hasRequestId() && isNotBlank(transactionSnapshot.getRequestId())) {
            result.mergeMetadata(Metadata.newBuilder()
                .setInResponseToRequestId(transactionSnapshot.getRequestId())
                .build()
            );
        }

        commandResultEmitter.emit(result.build());
        LOGGER.info("{} - Emitted CommandResult event: {}", stateOutput.sequenceNumber(), result);
    }

    private void emitSuccess(TransactionSnapshot command, StateOutput stateOutput) {
        Transaction transaction = stateOutput.processedTransactions().stream().findFirst()
            .orElseThrow();
        TransactionSnapshot resultTransaction = TransactionToProtoMapper.map(transaction).toBuilder()
            .setRequestId(command.getRequestId())
            .build();

        Result.Builder result = Result.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setSource(SERVICE)
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .setSuccess(Success.newBuilder()
                .setTransaction(resultTransaction)
                .build());

        if (command.hasRequestId() && isNotBlank(command.getRequestId())) {
            result.mergeMetadata(Metadata.newBuilder()
                .setInResponseToRequestId(command.getRequestId())
                .build()
            );
        }

        commandResultEmitter.emit(result.build());
        LOGGER.info("{} - Emitted CommandResult event: {}", stateOutput.sequenceNumber(), result);
    }
}
