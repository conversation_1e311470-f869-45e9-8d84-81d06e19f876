package io.wyden.executionengine.service.autohedger;

import io.wyden.executionengine.domain.InitialExposure;
import io.wyden.executionengine.domain.StreetSideInstrument;
import io.wyden.executionengine.service.booking.BookingEngineService;
import io.wyden.executionengine.service.booking.PositionSnapshotConsumer;
import io.wyden.executionengine.service.referencedata.InstrumentsRepository;
import io.wyden.executionengine.service.referencedata.ReferenceDataHttpClient;
import io.wyden.executionengine.service.utils.ProtobufUtils;
import io.wyden.published.brokerdesk.AutoHedgerConfig;
import io.wyden.published.brokerdesk.AutoHedgerConfigKey;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioListSnapshot;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static io.wyden.executionengine.service.autohedger.AutoHedgerConfigMapper.map;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class AutoHedgerFactory {

    private final BookingEngineService bookingEngineService;
    private final AutoHedgerService autoHedgerService;
    private final InstrumentsRepository instrumentsRepository;
    private final AutoHedgerConfigService autoHedgerConfigService;
    private final ReferenceDataHttpClient referenceDataHttpClient;
    private final PositionSnapshotConsumer positionSnapshotConsumer;
    private final Map<String, Boolean> bankPortfolioRegistry;
    private static final Logger LOGGER = getLogger(AutoHedgerFactory.class);

    public AutoHedgerFactory(BookingEngineService bookingEngineService,
                             AutoHedgerService autoHedgerService,
                             InstrumentsRepository instrumentsRepository,
                             AutoHedgerConfigService autoHedgerConfigService,
                             ReferenceDataHttpClient referenceDataHttpClient,
                             PositionSnapshotConsumer positionSnapshotConsumer) {
        this.bookingEngineService = bookingEngineService;
        this.autoHedgerService = autoHedgerService;
        this.instrumentsRepository = instrumentsRepository;
        this.autoHedgerConfigService = autoHedgerConfigService;
        this.referenceDataHttpClient = referenceDataHttpClient;
        this.positionSnapshotConsumer = positionSnapshotConsumer;
        this.bankPortfolioRegistry = new ConcurrentHashMap<>();
    }

    public void createAutoHedger(AutoHedgerConfig config) {
        Instrument instrument = instrumentsRepository.find(config.getStreetSideHedgeInstrument())
                .orElseThrow(() -> new IllegalArgumentException("Cannot create new auto-hedger. No street-side instrument to hedge with found for instrumentId: (%s)"
                        .formatted(config.getStreetSideHedgeInstrument())));

        StreetSideInstrument streetSideInstrument = new StreetSideInstrument(instrument);

        setupBankPortfolio(config.getPortfolioId());

        // save auto-hedger with user-provided config
        createAutoHedger(streetSideInstrument, map(config));

        // save configuration to hz
        autoHedgerConfigService.updateAutoHedgerConfig(config);
    }

    private void createAutoHedger(StreetSideInstrument streetSideInstrument, AutoHedgerConfigDto config) {
        try {
            InitialExposure initialExposure = bookingEngineService.getInitialExposure(config.asset(), config.portfolioId());

            autoHedgerService.configure(streetSideInstrument, initialExposure, config);
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to create auto-hedger for asset: " + config.asset(), e);
        }
    }

    public void deleteAutoHedger(AutoHedgerConfigKey configKey) {
        AutoHedger deletedAutoHedger = autoHedgerService.delete(configKey);
        if (deletedAutoHedger == null) {
            LOGGER.warn("There was no auto hedger to remove. Expected to find an auto hedger configured for ({})", configKey);
        }

        autoHedgerConfigService.deleteAutoHedgerConfig(configKey);
    }

    public void setupBankPortfolio(String portfolioId) {
        if (bankPortfolioRegistry.containsKey(portfolioId)) {
            LOGGER.info("Bank portfolio is already configured: {}", portfolioId);
            return;
        }

        PortfolioListSnapshot portfolioListSnapshot = referenceDataHttpClient.getPortfolioByIdFlat(List.of(portfolioId));

        List<Portfolio> portfolioList = portfolioListSnapshot.getPortfolioList();

        if (portfolioList.isEmpty()) {
            LOGGER.info("No portfolios found for portfolioId: {} therefore the bank portfolio is not set", portfolioId);
        } else if (portfolioList.size() == 1) {
            Portfolio portfolio = portfolioList.get(0);
            LOGGER.info("Configuring bank portfolio, id: {}", ProtobufUtils.shortDebugString(portfolio));
            bankPortfolioRegistry.put(portfolioId, true);
            positionSnapshotConsumer.registerConsumer(portfolio.getId());
        } else {
            LOGGER.info("Multiple portfolios found for portfolioId: {} therefore the bank portfolio is not set", portfolioId);
        }
    }
}
