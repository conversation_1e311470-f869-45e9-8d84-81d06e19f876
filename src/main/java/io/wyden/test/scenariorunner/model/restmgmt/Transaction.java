package io.wyden.test.scenariorunner.model.restmgmt;

import io.wyden.rest.management.domain.TransactionModel;

import java.io.Serializable;

//TODO represent rest mgmt transactions, rest-mgmt-domain should be used to provide proper parseable structure
public record Transaction(String id, TransactionModel.TransactionType transactionType, String description, Long dateTime, String executionId, String venueExecutionId) implements Serializable {
}
