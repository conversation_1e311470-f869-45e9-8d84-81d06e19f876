package io.wyden.settlement.client.transaction;

import io.wyden.settlement.client.run.SortingOrder;

import java.util.LinkedList;
import java.util.List;

import static java.util.Objects.requireNonNullElse;

public record TransactionSearchInput(
    String uuid,
    List<String> currency,
    List<String> accountId,
    List<String> portfolioId,
    String orderId,
    String parentOrderId,
    String rootOrderId,
    String underlyingExecutionId,
    String executionId,
    String venueExecutionId,
    String rootExecutionId,
    String settlementRunId,
    <PERSON><PERSON><PERSON> settled,
    <PERSON><PERSON><PERSON> selected,
    String from,
    String to,
    Integer first,
    String after,
    SortingOrder sortingOrder
) {
    public TransactionSearchInput {
        currency = currency == null ? List.of() : new LinkedList<>(currency);
        accountId = accountId == null ? List.of() : new LinkedList<>(accountId);
        portfolioId = portfolioId == null ? List.of() : new LinkedList<>(portfolioId);
        sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
    }

    @Override
    public List<String> accountId() {
        return accountId;
    }

    public Builder toBuilder() {
        return new Builder()
            .uuid(uuid)
            .currency(currency)
            .accountId(accountId)
            .portfolioId(portfolioId)
            .orderId(orderId)
            .parentOrderId(parentOrderId)
            .rootOrderId(rootOrderId)
            .underlyingExecutionId(underlyingExecutionId)
            .executionId(executionId)
            .venueExecutionId(venueExecutionId)
            .rootExecutionId(rootExecutionId)
            .settlementRunId(settlementRunId)
            .isSettled(settled)
            .selected(selected)
            .from(from)
            .to(to)
            .first(first)
            .after(after)
            .sortingOrder(sortingOrder);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String uuid;
        private List<String> currency = List.of();
        private List<String> accountId = List.of();
        private List<String> portfolioId = List.of();
        private List<String> transactionType = List.of();
        private String orderId;
        private String parentOrderId;
        private String rootOrderId;
        private String underlyingExecutionId;
        private String executionId;
        private String venueExecutionId;
        private String rootExecutionId;
        private String settlementRunId;
        private Boolean selected;
        private Boolean isSettled;
        private String from;
        private String to;
        private Integer first;
        private String after;
        private SortingOrder sortingOrder = SortingOrder.DESC;

        public Builder uuid(final String uuid) {
            this.uuid = uuid;
            return this;
        }

        public Builder currency(List<String> currency) {
            this.currency = currency;
            return this;
        }

        public Builder currency(String currency) {
            this.currency = List.of(currency);
            return this;
        }

        public Builder accountId(List<String> accountId) {
            this.accountId = accountId;
            return this;
        }

        public Builder accountId(String accountId) {
            this.accountId = List.of(accountId);
            return this;
        }

        public Builder portfolioId(List<String> portfolioId) {
            this.portfolioId = portfolioId;
            return this;
        }

        public Builder portfolioId(String portfolioId) {
            this.portfolioId = List.of(portfolioId);
            return this;
        }

        public Builder transactionType(List<String> transactionType) {
            this.transactionType = transactionType;
            return this;
        }

        public Builder transactionType(String transactionType) {
            this.transactionType = List.of(transactionType);
            return this;
        }

        public Builder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder parentOrderId(String parentOrderId) {
            this.parentOrderId = parentOrderId;
            return this;
        }

        public Builder rootOrderId(String rootOrderId) {
            this.rootOrderId = rootOrderId;
            return this;
        }

        public Builder underlyingExecutionId(String underlyingExecutionId) {
            this.underlyingExecutionId = underlyingExecutionId;
            return this;
        }

        public Builder executionId(String executionId) {
            this.executionId = executionId;
            return this;
        }

        public Builder venueExecutionId(String venueExecutionId) {
            this.venueExecutionId = venueExecutionId;
            return this;
        }

        public Builder rootExecutionId(String rootExecutionId) {
            this.rootExecutionId = rootExecutionId;
            return this;
        }

        public Builder settlementRunId(String settlementId) {
            this.settlementRunId = settlementId;
            return this;
        }

        public Builder selected(Boolean selected) {
            this.selected = selected;
            return this;
        }

        public Builder isSettled(Boolean isSettled) {
            this.isSettled = isSettled;
            return this;
        }

        public Builder from(String from) {
            this.from = from;
            return this;
        }

        public Builder to(String to) {
            this.to = to;
            return this;
        }

        public Builder first(Integer first) {
            this.first = first;
            return this;
        }

        public Builder after(String after) {
            this.after = after;
            return this;
        }

        public Builder sortingOrder(SortingOrder sortingOrder) {
            this.sortingOrder = sortingOrder;
            return this;
        }

        public TransactionSearchInput build() {
            return new TransactionSearchInput(
                uuid,
                currency,
                accountId,
                portfolioId,
                orderId,
                parentOrderId,
                rootOrderId,
                underlyingExecutionId,
                executionId,
                venueExecutionId,
                rootExecutionId,
                settlementRunId,
                isSettled,
                selected,
                from,
                to,
                first,
                after,
                sortingOrder
            );
        }
    }
}