package io.wyden.target.registry.k8s;

import org.springframework.core.env.AbstractEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Component
class ConnectorPropertiesConfiguration {

    private static final String CONNECTOR_PROPERTIES_PREFIX = "connector.properties.";

    private final Environment env;

    ConnectorPropertiesConfiguration(Environment env) {
        this.env = env;
    }

    Map<String, String> getConnectorProperties() {
        return getAllSpringProperties().entrySet().stream()
            .filter(entry -> entry.getKey().startsWith(CONNECTOR_PROPERTIES_PREFIX))
            .map(entry -> Map.entry(formatAsEnvVariables(entry.getKey()), entry.getValue()))
            .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toString()));
    }

    private static String formatAsEnvVariables(String key) {
        return key.replace(CONNECTOR_PROPERTIES_PREFIX, "")
            .replace(".", "_")
            .toUpperCase();
    }

    // TODO: Why not regular ConfigurationProperties?
    private Map<String,Object> getAllSpringProperties() {
        var map = new HashMap<String, Object>();
        MutablePropertySources propSrcs = ((AbstractEnvironment) env).getPropertySources();
        StreamSupport.stream(propSrcs.spliterator(), false)
            .filter(ps -> ps instanceof EnumerablePropertySource)
            .map(ps -> ((EnumerablePropertySource<?>) ps).getPropertyNames())
            .flatMap(Arrays::stream)
            .filter(propName -> propName.startsWith(CONNECTOR_PROPERTIES_PREFIX))
            .forEach(propName -> map.put(propName, env.getProperty(propName)));
        return map;
    }
}
