package io.wyden.test.scenariorunner.integration.keycloak;

import io.wyden.test.scenariorunner.data.user.Administrator;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;

import static io.wyden.test.scenariorunner.data.user.Administrator.ADMIN;

/**
 * Wraps {@link KeycloakClient} and keeps track of the created data.
 */
public class KeycloakDataManager {

    private final Set<String> managedUsers = new HashSet<>();
    private final Set<String> managedGroups = new HashSet<>();
    private final Set<KeycloakClient> managedUserKeycloakClients = new HashSet<>();
    private final KeycloakClient adminKeycloakClient = new KeycloakClient();

    public static Predicate<String> isNotManualUser() {
        return user -> !ADMIN.equals(user);
    }

    /**
     * Creates Key<PERSON><PERSON>ak user, creates Keycloak group, makes the user participant of the group,
     * retrieves {@link AccessTokenResponse} for the user
     *
     * @param username  name of the user to be created
     * @param groupName name of the group to be created
     * @return access token of the newly created user
     */
    // TODO: Should probably return Keycloak, but KeycloakClient is managing Keycloak (closing it), so it needs to be resolved first.
    public KeycloakClient createUserBelongingToGroup(String username, String groupName) {
        Optional<GroupRepresentation> group = adminKeycloakClient.getGroups().stream().filter(g -> g.getName().equals(groupName)).findFirst();
        if (group.isEmpty()) {
            group = adminKeycloakClient.createGroup(groupName);
            group.ifPresent(gr -> managedGroups.add(gr.getName()));
        }

        Optional<UserRepresentation> user = adminKeycloakClient.getUser(username);
        if (user.isEmpty()) {
            user = adminKeycloakClient.createUser(username, username);
            user.ifPresent(u -> managedUsers.add(u.getUsername()));
        }

        if (!groupName.isBlank()) {
            adminKeycloakClient.addUserToGroup(username, groupName);
        }

        return managedUserKeycloakClients.stream()
            .filter(client -> client.username().equals(username))
            .findFirst()
            .orElseGet(() -> {
                KeycloakClient client = createKeycloakClient(username, username);
                managedUserKeycloakClients.add(client);
                return client;
            });
    }

    public KeycloakClient createUser(String username) {
        Optional<UserRepresentation> user = adminKeycloakClient.getUser(username);
        if (user.isEmpty()) {
            user = adminKeycloakClient.createUser(username, username);
            user.ifPresent(u -> managedUsers.add(u.getUsername()));
        }

        return managedUserKeycloakClients.stream()
            .filter(client -> client.username().equals(username))
            .findFirst()
            .orElseGet(() -> {
                KeycloakClient client = createKeycloakClient(username, username);
                managedUserKeycloakClients.add(client);
                return client;
            });
    }

    private KeycloakClient createKeycloakClient(String username, String password) {
        if (ADMIN.equals(username)) {
            return adminKeycloakClient;
        }
        return new KeycloakClient(username, password);
    }

    /**
     * Removes all managed users and groups, and closes underlying Keycloak client
     */
    public void cleanTestUsersAndGroups() {
        removeAllManagedGroups();
        removeAllManagedUsers();
        managedUserKeycloakClients
            .stream()
            .filter(client -> !ADMIN.equals(client.username()))
            .forEach(KeycloakClient::close);
        managedUsers.clear();
        adminKeycloakClient.close();
    }

    private void removeAllManagedGroups() {
        managedGroups
            .stream()
            .filter(group -> !Administrator.GROUP_NAME.equals(group))
            .forEach(adminKeycloakClient::removeGroup);
    }

    private void removeAllManagedUsers() {
        managedUsers
            .stream()
            .filter(isNotManualUser())
            .forEach(adminKeycloakClient::removeUser);
    }

}
