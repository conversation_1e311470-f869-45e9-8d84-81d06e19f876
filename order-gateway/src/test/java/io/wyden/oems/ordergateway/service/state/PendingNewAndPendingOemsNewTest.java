package io.wyden.oems.ordergateway.service.state;

import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.referencedata.InstrumentsRepository;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static io.wyden.published.client.ClientRequestType.CANCEL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
class PendingNewAndPendingOemsNewTest {

    private static final String VENUE = "Test";

    private State underTest;

    private ClientRequest originalClientRequest;

    private ClientOrderContext ctx;

    private ClientOrderCache clientOrderCache;

    private InstrumentsRepository instrumentsRepository;

    @BeforeEach
    void setUp() {
        originalClientRequest = ClientRequest.newBuilder()
            .setRequestType(ClientRequestType.ORDER_SINGLE)
            .setInstrumentId("BTCUSD")
            .setQuantity("10")
            .setOrderId("1")
            .setClientId("client")
            .setClOrderId("c1")
            .setOrderType(ClientOrderType.MARKET)
            .build();

        underTest = new PendingNewAndPendingOemsNew();

        ClientOrderState persistentState = PendingNew.createPersistentState(originalClientRequest, InstrumentsRepository.buildInstrument("BTC", "USD", VENUE));

        clientOrderCache = mock(ClientOrderCache.class);
        instrumentsRepository = mock(InstrumentsRepository.class);
        lenient().doReturn(InstrumentsRepository.buildInstrument("BTC", "USD", VENUE)).when(instrumentsRepository).find(any());

        ctx = ClientOrderContext.rebuild(underTest, clientOrderCache, persistentState, instrumentsRepository);
    }

    @Nested
    class OnOemsRequestSendingFailure {
        @Test
        void shouldMoveToRejected() {
            underTest.onOemsRequestSendingFailure(ctx);

            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.REJECTED);
        }
    }

    @Nested
    class OnOemsRequestSent {

        @Test
        void shouldThrow() { // not supported operation - already send
            assertThatThrownBy(() -> underTest.onOemsRequestSent(OemsRequest.newBuilder()
                    .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
                    .setOrderId("oems-1")
                    .build(), ctx))
                    .isInstanceOf(IllegalStateException.class);

        }


    }

    @Nested
    class OnOemsResponse {

        private OemsResponse.Builder oemsResponse;

        @BeforeEach
        void setup() {
            oemsResponse = OemsResponse.newBuilder();
        }

        @Test
        void oemsNewMovesToNew() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.NEW)
                .setOrderStatus(OemsOrderStatus.STATUS_NEW)
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);


            // act
            underTest.onOemsResponse(oemsResponse.build(), ctx);

            // assert
            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.NEW);

        }

        @Test
        void oemsFillMovesToFilled() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.FILL)
                .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
                .setAvgPrice("1")
                .setLastPrice("1")
                .setLastQty(ctx.getPersistentState().getRemainingQuantity())
                .setCumQty(ctx.getPersistentState().getQuantity())
                .setLeavesQty("0")
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act
            underTest.onOemsResponse(oemsResponse.build(), ctx);

            // assert
            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.FILLED);
        }

        @Test
        void oemsPartialFillMovesToPartiallyFilled() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.PARTIAL_FILL)
                .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
                .setAvgPrice("1")
                .setLastPrice("1")
                .setLastQty("2")
                .setOrderQty("10")
                .setCumQty("2")
                .setLeavesQty("8")
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act
            underTest.onOemsResponse(oemsResponse.build(), ctx);

            // assert
            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.PARTIALLY_FILLED);
        }

        @Test
        void lastOemsPartialFillMovesToFilled() {
            // arrange
            oemsResponse
                    .setExecType(OemsExecType.PARTIAL_FILL)
                    .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
                    .setAvgPrice("1")
                    .setLastPrice("1")
                    .setLastQty(ctx.getPersistentState().getRemainingQuantity())
                    .setCumQty(ctx.getPersistentState().getQuantity())
                    .setLeavesQty("0")
                    .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act
            underTest.onOemsResponse(oemsResponse.build(), ctx);

            // assert
            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.FILLED);
        }

        @Test
        void oemsRejectMovesToRejected() {
            // arrange
            oemsResponse
                .setExecType(OemsExecType.REJECTED)
                .setOrderStatus(OemsOrderStatus.STATUS_REJECTED)
                .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT);

            // act
            underTest.onOemsResponse(oemsResponse.build(), ctx);

            // assert
            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.REJECTED);

        }
    }

    @Nested
    class WhenCheckingIfHasToBeBackedByNewOemsOrder {
        @Test
        void returnsTrue() {
            assertThat(underTest.hasToBeBackedByNewOemsOrder(ctx)).isFalse();
        }
    }

    @Nested
    class OnClientCancelRequest {
        @Test
        void shouldMoveToCancel() {
            // act, assert
            assertThatThrownBy(() -> underTest.onClientCancelRequest(ClientRequest.newBuilder()
                    .setRequestType(ClientRequestType.CANCEL)
                    .build(), ctx));
        }
    }

    @Nested
    class OnClientCancelReplaceRequest {
        @Test
        void shouldMoveToCancel() {
            // act, assert
            assertThatThrownBy(() -> underTest.onClientCancelReplaceRequest(ClientRequest.newBuilder()
                    .setRequestType(ClientRequestType.CANCEL_REPLACE)
                    .build(), ctx));
        }
    }

    @Nested
    class onClientForceCancelRequest {
        @Test
        void shouldAllowCancel() {
            underTest.onClientCancelRequest(ClientRequest.newBuilder()
                .setRequestType(CANCEL)
                .setForceCancel(true)
                .setOrderId("2")
                .build(), ctx);

            // assert
            assertThat(ctx.getClientOrderStatus()).isEqualTo(ClientOrderStatus.CANCELED);
        }
    }
}
