package io.wyden.booking.snapshotter.acceptance.datatable;

import io.cucumber.java.DataTableType;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Map;

import static io.wyden.booking.snapshotter.acceptance.TestUtils.parseBigDecimal;
import static org.apache.commons.lang3.StringUtils.EMPTY;

public class TransactionMapping {

    public record TransactionTableType(
        String uuid,
        String executionId,
        String reservationRef,
        BigDecimal quantity,
        @Nullable BigDecimal price,
        String currency,
        String baseCurrency,
        String instrument,
        String portfolioId,
        String counterPortfolioId,
        String sourcePortfolioId,
        String targetPortfolioId,
        String feePortfolioId,
        String accountId,
        String sourceAccountId,
        String targetAccountId,
        String feeAccountId,
        BigDecimal fee,
        String feeCurrency
    ) {
    }

    @DataTableType
    public TransactionTableType convertTransaction(Map<String, String> entry) {

        String uuid = entry.getOrDefault("uuid", EMPTY);
        String executionId = entry.getOrDefault("executionId", EMPTY);
        String reservationRef = entry.getOrDefault("reservationRef", EMPTY);
        String quantity = entry.getOrDefault("quantity", EMPTY);
        String price = entry.getOrDefault("price", EMPTY);
        String currency = entry.getOrDefault("currency", EMPTY);
        String baseCurrency = entry.getOrDefault("baseCurrency", EMPTY);
        String instrument = entry.getOrDefault("instrument", EMPTY);
        String portfolioId = entry.getOrDefault("portfolioId", EMPTY);
        String counterPortfolioId = entry.getOrDefault("counterPortfolioId", EMPTY);
        String sourcePortfolioId = entry.getOrDefault("sourcePortfolioId", EMPTY);
        String targetPortfolioId = entry.getOrDefault("targetPortfolioId", EMPTY);
        String feePortfolioId = entry.getOrDefault("feePortfolioId", EMPTY);
        String accountId = entry.getOrDefault("accountId", EMPTY);
        String sourceAccountId = entry.getOrDefault("sourceAccountId", EMPTY);
        String targetAccountId = entry.getOrDefault("targetAccountId", EMPTY);
        String feeAccountId = entry.getOrDefault("feeAccountId", EMPTY);
        String fee = entry.getOrDefault("fee", EMPTY);
        String feeCurrency = entry.getOrDefault("feeCurrency", EMPTY);

        return new TransactionTableType(
            uuid,
            executionId,
            reservationRef,
            parseBigDecimal(quantity),
            parseBigDecimal(price),
            currency,
            baseCurrency,
            instrument,
            portfolioId,
            counterPortfolioId,
            sourcePortfolioId,
            targetPortfolioId,
            feePortfolioId,
            accountId,
            sourceAccountId,
            targetAccountId,
            feeAccountId,
            parseBigDecimal(fee),
            feeCurrency
        );
    }
}
