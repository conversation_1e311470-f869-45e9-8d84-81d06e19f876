package io.wyden.oems.ordercollider.collider.service.tracking;

import io.wyden.published.oems.OemsEnvironmentType;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.venue.VenueEnvironmentType;
import io.wyden.published.venue.VenueInstrumentType;
import io.wyden.published.venue.VenueOrderStatus;
import io.wyden.published.venue.VenueOrderType;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueRequestType;
import io.wyden.published.venue.VenueSide;
import io.wyden.published.venue.VenueTIF;

public class VenueOemsMapper {

    private VenueOemsMapper() {
        // Empty
    }

    public static VenueRequest toVenueOrder(OemsRequest request, InstrumentIdentifiers instrumentIdentifiers) {
        return VenueRequest.newBuilder()
            .setRequestType(VenueRequestType.VENUE_ORDER)
            .setOrderId(request.getOrderId())
            .setVenueAccount(request.getVenueAccount())
            .setInstrumentType(toVenue(request.getInstrumentType()))
            .setInstrumentIdentifiers(instrumentIdentifiers)
            .setOrderType(toVenue(request.getOrderType()))
            .setSide(toVenue(request.getSide()))
            .setQuantity(request.getQuantity())
            .setPrice(request.getPrice())
            .setStopPrice(request.getStopPrice())
            .setTif(toVenue(request.getTif()))
            .setExpireTime(request.getExpireTime())
            .setPostOnly(request.getPostOnly())
            .build();
    }

    public static OemsOrderStatus toOems(VenueOrderStatus status) {
        return OemsOrderStatus.forNumber(status.getNumber());
    }

    public static VenueInstrumentType toVenue(OemsInstrumentType instrumentType) {
        return VenueInstrumentType.forNumber(instrumentType.getNumber());
    }

    public static VenueOrderType toVenue(OemsOrderType oemsOrderType) {
        return VenueOrderType.forNumber(oemsOrderType.getNumber());
    }

    public static VenueSide toVenue(OemsSide oemsSide) {
        return VenueSide.forNumber(oemsSide.getNumber());
    }

    public static OemsSide toOems(VenueSide venueSide) {
        return OemsSide.forNumber(venueSide.getNumber());
    }

    public static VenueTIF toVenue(OemsTIF oemsTIF) {
        return VenueTIF.forNumber(oemsTIF.getNumber());
    }

    public static OemsEnvironmentType toOemsEnvironmentType(VenueEnvironmentType venueEnvironmentType) {
        return switch (venueEnvironmentType) {
            case UNSPECIFIED  -> OemsEnvironmentType.UNSPECIFIED;
            case LIVE  -> OemsEnvironmentType.LIVE;
            case NOT_LIVE  -> OemsEnvironmentType.NOT_LIVE;
            case UNRECOGNIZED -> OemsEnvironmentType.UNRECOGNIZED;
        };
    }

}
