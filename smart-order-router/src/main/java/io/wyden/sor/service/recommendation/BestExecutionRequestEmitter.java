package io.wyden.sor.service.recommendation;

import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.published.smartrecommendationengine.Strategy;
import io.wyden.sor.infrastructure.rabbit.RabbitDestinations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.UUID;

@Component
public class BestExecutionRequestEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(BestExecutionRequestEmitter.class);
    public static final String SOR_CLIENT_ID = "SOR";

    public static final String REQUESTER = "smart-order-router";

    private final String broadcastHeader;

    private final RabbitExchange<BestExecutionRequest> bestExecutionRequestExchange;
    private final Tracing otlTracing;

    public BestExecutionRequestEmitter(RabbitDestinations rabbitDestinations,
                                       @Value("${rabbitmq.best-execution-request-broadcast-header}") String broadcastHeader,
                                       Tracing otlTracing) {
        this.bestExecutionRequestExchange = rabbitDestinations.getBestExecutionRequestExchange();
        this.broadcastHeader = broadcastHeader;
        this.otlTracing = otlTracing;
    }

    public void createAndEmitBestExecutionBroadcastRequest(String recommendationSubscriptionId, Collection<InstrumentKey> instrumentKeys, OemsRequest request) {
        try (var ignored = otlTracing.createSpan("bestexecutionrequest.broadcast.emit", SpanKind.PRODUCER)) {
            BestExecutionRequest bestExecutionRequest = createBestExecutionRequest(recommendationSubscriptionId, instrumentKeys, request);
            emitBestExecutionRequest(bestExecutionRequest, broadcastHeader);
            LOGGER.info("Emitting best execution broadcast request: {}, order id {}", recommendationSubscriptionId, request.getOrderId());
        }
    }

    public void createAndEmitBestExecutionPrivateRequest(String recommendationSubscriptionId, Collection<InstrumentKey> instrumentKeys, OemsRequest request, String destination) {
        try (var ignored = otlTracing.createSpan("bestexecutionrequest.private.emit", SpanKind.PRODUCER)) {
            BestExecutionRequest bestExecutionRequest = createBestExecutionRequest(recommendationSubscriptionId, instrumentKeys, request);
            emitBestExecutionRequest(bestExecutionRequest, destination);
            LOGGER.info("Emitting best execution private request: {}, order id {}", recommendationSubscriptionId, request.getOrderId());
        }
    }

    private void emitBestExecutionRequest(BestExecutionRequest bestExecutionRequest, String destination) {
        Map<String, String> routingHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), BestExecutionRequest.class.getSimpleName(),
            OemsHeader.SRE_DESTINATION.getHeaderName(), destination,
            OemsHeader.CLIENT_ID.getHeaderName(), SOR_CLIENT_ID
        );

        Map<String, String> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(routingHeaders), RabbitHeadersPropagator.setter())
            .getHeaders();

        bestExecutionRequestExchange.publishWithHeaders(bestExecutionRequest, headers);
    }

    private BestExecutionRequest createBestExecutionRequest(String recommendationSubscriptionId, Collection<InstrumentKey> instrumentKeys, OemsRequest request) {
        Metadata metadata = Metadata.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setRequesterId(REQUESTER)
            .setCreatedAt(ZonedDateTime.now().toString())
            .build();

        BestExecutionRequest.Builder bestExecutionRequest = BestExecutionRequest.newBuilder()
            .setMetadata(metadata)
            .setQuantity(request.getQuantity())
            .setSide(request.getSide())
            .setStrategy(Strategy.SIMPLE) // TODO abac change strategy when splitting is introduced
            .addAllInstruments(instrumentKeys)
            .setRecommendationSubscriptionId(recommendationSubscriptionId)
            .setOrderId(request.getOrderId());

        if (request.getOrderType() == OemsOrderType.LIMIT || request.getOrderType() == OemsOrderType.STOP_LIMIT) {
            bestExecutionRequest.setLimit(request.getPrice());
        }

        if (request.getOrderType() == OemsOrderType.STOP || request.getOrderType() == OemsOrderType.STOP_LIMIT) {
            bestExecutionRequest.setStop(request.getStopPrice());
        }

        return bestExecutionRequest.build();
    }
}
