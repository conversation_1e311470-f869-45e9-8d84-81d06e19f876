package io.wyden.booking.snapshotter.domain.transaction.payment;

import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.snapshotter.domain.transaction.TransactionFee;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.stream.Stream;

import static io.wyden.booking.snapshotter.domain.transaction.TransactionType.Values.DEPOSIT;

/**
 * Represents an incoming payment from an external system, e.g., a deposit from a Metamask wallet onto an exchange account.
 */
@Entity
@DiscriminatorValue(DEPOSIT)
public class Deposit extends Payment {

    public Deposit(String uuid,
                   String reservationRef,
                   ZonedDateTime dateTime,
                   String executionId,
                   String venueExecutionId,
                   Collection<TransactionFee> fees,
                   String description,
                   boolean isLive,
                   String currency,
                   BigDecimal quantity,
                   String portfolioId,
                   String accountId,
                   String feeAccountId,
                   String feePortfolioId) {
        super(uuid, reservationRef, dateTime, executionId, venueExecutionId, fees, description, isLive, currency, quantity, portfolioId, accountId, feeAccountId, feePortfolioId);
    }

    protected Deposit() {
        // JPA
    }

    @Transient
    @Override
    public Collection<LedgerEntry> getLedgerEntries() {
        Stream<LedgerEntry> ledgerEntries = Stream.of(
            portfolioLedgerEntry(LedgerEntryType.DEPOSIT, quantity, null, fees, portfolioId, currency),
            accountLedgerEntry(LedgerEntryType.DEPOSIT, quantity, null, fees, accountId, currency),

            portfolioLedgerEntry(LedgerEntryType.RESERVATION_RELEASE, quantity.negate(), portfolioId, currency),
            accountLedgerEntry(LedgerEntryType.RESERVATION_RELEASE, quantity.negate(), accountId, currency)
        );

        Stream<LedgerEntry> fees = this.fees.stream()
            .filter(fee -> fee.getAmount() != null)
            .flatMap(fee -> Stream.of(
                portfolioLedgerEntry(LedgerEntryType.DEPOSIT_FEE, fee.getAmount().negate(), feePortfolioId, fee.getCurrency()),
                accountLedgerEntry(LedgerEntryType.DEPOSIT_FEE, fee.getAmount().negate(), feeAccountId, fee.getCurrency()),

                portfolioLedgerEntry(LedgerEntryType.RESERVATION_RELEASE, fee.getAmount(), feePortfolioId, fee.getCurrency()),
                accountLedgerEntry(LedgerEntryType.RESERVATION_RELEASE, fee.getAmount(), feeAccountId, fee.getCurrency())
            ));

        return Stream.concat(ledgerEntries, fees).toList();
    }

    @Override
    public Deposit doSnapshot() {
        return new Deposit(uuid, reservationRef, dateTime, executionId, venueExecutionId, fees, description, isLive, currency, quantity, portfolioId, accountId, feeAccountId, feePortfolioId);
    }

    @Override
    public String toString() {
        return "Deposit{" +
            "currency=" + currency +
            ", quantity=" + quantity +
            ", portfolioId='" + portfolioId + '\'' +
            ", accountId='" + accountId + '\'' +
            ", feeAccountId='" + feeAccountId + '\'' +
            ", feePortfolioId='" + feePortfolioId + '\'' +
            ", uuid='" + uuid + '\'' +
            ", reservationRef='" + reservationRef + '\'' +
            ", dateTime=" + dateTime +
            ", executionId='" + executionId + '\'' +
            ", venueExecutionId='" + venueExecutionId + '\'' +
            ", description='" + description + '\'' +
            ", isLive=" + isLive +
            ", transactionType=" + transactionType +
            ", fees=" + fees +
            '}';
    }
}
