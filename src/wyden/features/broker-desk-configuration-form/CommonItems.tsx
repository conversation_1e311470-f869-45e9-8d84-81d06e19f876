import { Header } from '@ui/Typography/Header';
import { Paragraph } from '@ui/Typography/Paragraph';
import { styled } from '@ui/styled';
import { getSpacing } from '@wyden/utils/styles';
import { useTranslation } from 'react-i18next';
import { Button } from '@ui/Button';
import { ReactComponent as ResetIcon } from '@wyden/assets/restart-alt.svg';
import { IconButton } from '@ui/IconButton';
import { useContext } from 'react';
import { ConfigContext } from './useBrokerDeskConfig';

type ConfigurationFormHeaderProps = {
  title: string;
  onReset?: () => void;
};

export const ConfigurationFormHeader = (props: ConfigurationFormHeaderProps) => {
  const { t } = useTranslation();
  const { resource } = useContext(ConfigContext);

  return (
    <StyledHeader>
      <Header variant="h5">{props.title}</Header>
      <StyledParagraph variant="small">
        {props.onReset && (
          <>
            {t('brokerDesk.inheritInfo')}
            <StyledIconButton disabled={!resource.managable} onClick={props.onReset} size="small">
              <ResetIcon />
              <Paragraph variant="small">{t('brokerDesk.resetOverrides')}</Paragraph>
            </StyledIconButton>
          </>
        )}
      </StyledParagraph>
    </StyledHeader>
  );
};

const StyledParagraph = styled(Paragraph)`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const FormContainer = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(5)};
  min-height: 100%;
`;

export const StyledSaveButton = styled(Button)`
  width: fit-content;
`;

const StyledHeader = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(3)};
`;

export const ConfigContainer = styled('div')`
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: ${getSpacing(6)};
`;

export const InstrumentFormButtons = styled('div')`
  margin-top: auto;
  display: flex;
  margin-top: ${getSpacing(4)};
  gap: ${getSpacing(1)};
`;

export const StyledIconButton = styled(IconButton)`
  border-radius: 8px;
`;
