import { FormContainer, StyledSaveButton } from './CommonItems';
import { useTranslation } from 'react-i18next';
import { PricingConfigurationSchema } from './FormConfigs';
import { z } from 'zod';
import { useContext, useEffect } from 'react';
import { useBrokerDeskStore } from './useBrokerDeskStore';
import { useFormContext, useWatch } from 'react-hook-form';
import { ConfigContext } from './useBrokerDeskConfig';

type Props = {
  fields: { [key in keyof z.infer<typeof PricingConfigurationSchema>]: JSX.Element };
};

export function PricingConfigurationFormContent({ fields }: Props) {
  const { t } = useTranslation();
  const { formState } = useFormContext();
  const setDirtyFields = useBrokerDeskStore((state) => state.setPricingDirtyFields);
  const formFields = useWatch();
  const { resource } = useContext(ConfigContext);

  useEffect(() => {
    setDirtyFields(Object.keys(formState.dirtyFields));

    return () => {
      setDirtyFields([]);
    };
  }, [formFields, formState.dirtyFields, setDirtyFields]);

  return (
    <FormContainer>
      {fields.markup}
      {fields.venueAccounts}
      <StyledSaveButton
        data-testid="save-pricing-configuration"
        size="lg"
        disabled={!resource.managable}
        variant="primary"
        type="submit"
      >
        {t('common.saveChanges')}
      </StyledSaveButton>
    </FormContainer>
  );
}
