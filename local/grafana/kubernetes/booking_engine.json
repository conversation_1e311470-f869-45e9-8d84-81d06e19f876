{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3592, "links": [], "liveNow": false, "panels": [{"datasource": {"default": true, "type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by(commandType) (rate(wyden_booking_engine_command_incoming_total{namespace=\"$namespace\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "CommandType: {{commandType}}", "range": true, "refId": "Commands found", "useBackend": false}], "title": "Commands received", "type": "timeseries"}, {"datasource": {"default": true, "type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "builder", "expr": "rate(wyden_booking_engine_commands_found_total{namespace=\"$namespace\"}[$__rate_interval])", "hide": false, "legendFormat": "Request: {{venueType}}", "range": true, "refId": "Commands found"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "group by(messageType) (rate(wyden_booking_engine_command_processed_total{namespace=\"$namespace\"}[$__rate_interval]))", "hide": true, "legendFormat": "Response: {{venueType}}", "range": true, "refId": "Commands processed - types"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "group by(resultType) (rate(wyden_booking_engine_command_processed_total{namespace=\"$namespace\"}[$__rate_interval]))", "hide": true, "legendFormat": "Response: {{venueType}}", "range": true, "refId": "Command processed - results"}], "title": "Commands found", "type": "timeseries"}, {"datasource": {"default": true, "type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum by(messageType) (rate(wyden_booking_engine_command_processed_total{namespace=\"$namespace\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "MessageType: {{messageType}}", "range": true, "refId": "Commands processed - types", "useBackend": false}], "title": "Commands processed - by type", "type": "timeseries"}, {"datasource": {"default": true, "type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(resultType) (rate(wyden_booking_engine_command_processed_total{namespace=\"$namespace\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "Result: {{resultType}}", "range": true, "refId": "Command processed - results", "useBackend": false}], "title": "Commands processed - by result", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.consume\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Request", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.consume\"}[$__rate_interval])", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "Response", "range": true, "refId": "B", "useBackend": false}], "title": "Processing Rate", "type": "timeseries"}, {"datasource": {"default": true, "type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(resultType) (rate(cache_local_ledgerentry_reservation_miss_total{namespace=\"$namespace\"}[$__rate_interval]))", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "legendFormat": "Result: {{resultType}}", "range": true, "refId": "Command processed - results", "useBackend": false}], "title": "Local cache miss", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.consume\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.consume\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Consume", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.calculatevalue\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.calculatevalue\"}[$__rate_interval])", "hide": false, "legendFormat": "Calculate Value", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.save\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.pendingorder.save\"}[$__rate_interval])", "hide": false, "legendFormat": "Save", "range": true, "refId": "C"}], "title": "Order Processing Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by(venueType) (rate(wyden_booking_engine_request_incoming_total{namespace=\"$namespace\"}[$__rate_interval]))", "legendFormat": "Request: {{venueType}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "editorMode": "code", "expr": "sum by(venueType) (rate(wyden_booking_engine_response_incoming_total{namespace=\"$namespace\"}[$__rate_interval]))", "hide": false, "legendFormat": "Response: {{venueType}}", "range": true, "refId": "B"}], "title": "Incoming Trading Messages", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.position.emitpositionchangedhandling\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.position.emitpositionchangedhandling\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Emit", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.position.marketdatahandling\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.position.marketdatahandling\"}[$__rate_interval])", "hide": false, "legendFormat": "Market Data", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.position.calculatevalue\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.position.calculatevalue\"}[$__rate_interval])", "hide": false, "legendFormat": "Calculate Value", "range": true, "refId": "C"}], "title": "Position Processing Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "builder", "expr": "sum by(instrument) (rate(wyden_booking_engine_position_outgoing_total{namespace=\"$namespace\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Outgoing Position Snapshots", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by(queue) (rabbitmq_queue_messages{namespace=\"$namespace\", queue=~\"booking-engine-queue.trading.ALL\"})", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "<PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.consume\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.consume\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Consume", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.split\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.split\"}[$__rate_interval])", "hide": false, "legendFormat": "Split", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.process\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.process\"}[$__rate_interval])", "hide": false, "legendFormat": "Process", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{namespace=\"$namespace\", span_name=\"booking.processing.apply\"}[$__rate_interval]) / on(namespace) rate(traces_spanmetrics_latency_count{namespace=\"$namespace\", span_name=\"booking.processing.apply\"}[$__rate_interval])", "hide": false, "legendFormat": "Apply", "range": true, "refId": "D"}], "title": "Transaction Processing Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.transaction.marketdatahandling\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.processing.transaction.marketdatahandling\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Handling", "range": true, "refId": "A", "useBackend": false}], "title": "Market Data Processing Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 64}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.ledgerentry.findbyproperties\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.ledgerentry.findbyproperties\"}[$__rate_interval])", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Ledger Entry Find", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.ledgerentry.createifmissing\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.ledgerentry.createifmissing\"}[$__rate_interval])", "hide": false, "legendFormat": "Ledger Entry Create", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.position.findbyproperties\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.position.findbyproperties\"}[$__rate_interval])", "hide": false, "legendFormat": "Position Find", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.position.createifmissing\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.position.createifmissing\"}[$__rate_interval])", "hide": false, "legendFormat": "Position Create", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.transaction.findbyproperties\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.transaction.findbyproperties\"}[$__rate_interval])", "hide": false, "legendFormat": "Transaction Find", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.transaction.createifmissing\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.transaction.createifmissing\"}[$__rate_interval])", "hide": false, "legendFormat": "Transaction Create", "range": true, "refId": "F"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.instrument.createifmissing\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.instrument.createifmissing\"}[$__rate_interval])", "hide": false, "legendFormat": "Instrument Create", "range": true, "refId": "G"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traces_spanmetrics_latency_sum{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.reference.createifmissing\"}[$__rate_interval]) / on(k8s_namespace_name) rate(traces_spanmetrics_latency_count{k8s_namespace_name=\"$namespace\", span_name=\"booking.lookup.reference.createifmissing\"}[$__rate_interval])", "hide": false, "legendFormat": "Reference Create", "range": true, "refId": "H"}], "title": "Lookup Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 72}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(pod) (hikaricp_connections{namespace=\"$namespace\", wyden_service=\"booking-engine\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Database connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 72}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(pod) (hikaricp_connections_active{namespace=\"$namespace\", wyden_service=\"booking-engine\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Database connections - active", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 72}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(pod) (hikaricp_connections_idle{namespace=\"$namespace\", wyden_service=\"booking-engine\"})", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Database connections - idle", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 80}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(pod) (rate(hikaricp_connections_usage_seconds_sum{namespace=\"$namespace\", wyden_service=\"booking-engine\"}[$__rate_interval])) / sum by(pod) (rate(hikaricp_connections_usage_seconds_count{namespace=\"$namespace\", wyden_service=\"booking-engine\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Database connections - usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 80}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "disableTextWrap": false, "editorMode": "builder", "expr": "sum by(pod) (rate(hikaricp_connections_acquire_seconds_sum{namespace=\"$namespace\", wyden_service=\"booking-engine\"}[$__rate_interval])) / sum by(pod) (rate(hikaricp_connections_acquire_seconds_count{namespace=\"$namespace\", wyden_service=\"booking-engine\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Database connections - acquire", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "algotrader-performance", "value": "algotrader-performance"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(namespace)", "hide": 0, "includeAll": false, "label": "Namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(namespace)", "refId": "prometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Booking Engine", "uid": "b8727bc7-4d61-4b32-acee-d3f61869375d", "version": 30, "weekStart": ""}