data "aws_caller_identity" "current" {}

data "http" "crds_yaml" {
  url = "https://raw.githubusercontent.com/aws/eks-charts/v${var.crd_version}/stable/aws-load-balancer-controller/crds/crds.yaml"
}

locals {
  namespace            = "kube-system"
  account_id           = data.aws_caller_identity.current.account_id
  oidc_provider_arn    = replace("arn:aws:iam::${local.account_id}:oidc-provider/${data.aws_eks_cluster.eks.identity[0].oidc[0].issuer}", "https://", "")
  service_account_name = "aws-load-balancer-controller-sa"
  crds_list            = split("\n---\n", data.http.crds_yaml.response_body)
}

module "lb_controller_sa" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-role-for-service-accounts-eks"
  version = "5.48.0"

  allow_self_assume_role                 = false
  role_name                              = "${var.cluster_name}-lb-controller"
  role_description                       = "IRSA for aws-load-balancer-controller"
  attach_load_balancer_controller_policy = true
  oidc_providers = {
    main = {
      provider_arn               = local.oidc_provider_arn
      namespace_service_accounts = ["${local.namespace}:${local.service_account_name}"]
    }
  }
  tags = var.tags
}

# resource "kubectl_manifest" "alb_controller_crds" {
#   for_each = { for idx, crd in local.crds_list : idx => yamldecode(crd) }

#   yaml_body = jsonencode(each.value)

#   force_new = true
#   wait      = true
# }
resource "kubectl_manifest" "alb_controller_crds" {
  for_each = { for idx, crd in local.crds_list : idx => crd }

  yaml_body = each.value
  force_new = true
  wait      = true
}

resource "helm_release" "lb_controller" {
  name             = "aws-load-balancer-controller"
  chart            = "aws-load-balancer-controller"
  version          = var.chart_version
  repository       = "https://aws.github.io/eks-charts"
  namespace        = local.namespace
  create_namespace = true
  atomic           = true
  cleanup_on_fail  = true
  depends_on       = [module.lb_controller_sa, kubectl_manifest.alb_controller_crds]
  set {
    name  = "clusterName"
    value = data.aws_eks_cluster.eks.name
  }
  set {
    name  = "vpcId"
    value = data.aws_eks_cluster.eks.vpc_config[0].vpc_id
  }
  set {
    name  = "podDisruptionBudget.maxUnavailable"
    value = 1
  }
  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = module.lb_controller_sa.iam_role_arn
  }
  set {
    name  = "serviceAccount.name"
    value = local.service_account_name
  }
}
