package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.test.scenariorunner.data.user.Administrator;
import io.wyden.test.scenariorunner.data.user.Manager;
import io.wyden.test.scenariorunner.extension.ExtensionUtils;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.extension.common.InvalidExtensionConfigException;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.fixclient.FixTradingActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import io.wyden.test.scenariorunner.integration.restclient.RestActor;
import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.ParameterContext;
import org.junit.jupiter.api.extension.ParameterResolver;
import org.junit.platform.commons.util.AnnotationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.actorKey;
import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.gqlActorKey;

/**
 * This extension:
 * <ul>
 *  <li>creates {@link ClientActor} instance before all tests</li>
 *  <li>connects to streams of data before all tests</li>
 *  <li>waits logon on streams of data before all tests</li>
 *  <li>tear down user after all tests</li>
 * </ul>
 * <p>
 * Current limitation: cannot be used to resolve parameter with several registered extensions with 1 clientId<br>
 * Extension resolves actors based on clientIds.<br>
 * Issue is solved by simply retrieving actor from extension like extension.actor().
 * </p>
 * <pre>{@code
 * @RegisterExtension
 * GraphQLActorExtension nonAccountCreatorGqlExtension = new GraphQLActorExtension(Manager.GROUP_NAME, true);
 * private GraphQLActor nonAccountCreatorGql;
 * @BeforeAll
 * void setupNonAccountCreator() {
 *     nonAccountCreatorGql = nonAccountCreatorGqlExtension.actor();
 * }
 * }
 * </pre>
 *
 * @param <T> type of actor (extends {@link ClientActor})
 */
public abstract class AbstractClientActorExtension<T extends ClientActor> implements BeforeAllCallback, AfterAllCallback, ParameterResolver {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractClientActorExtension.class);

    private static final String ACTORS_KEY = "actors";

    protected final String username;
    private final boolean requireLogon;
    private final String groupName;

    private ClientActor actor;
    private KeycloakClient keycloakClient;

    private GraphQLActor configGqlActor;

    public AbstractClientActorExtension(String username, String groupName, boolean requireLogon) {
        this.requireLogon = requireLogon;
        this.username = username;
        this.groupName = groupName;
        LOGGER.debug("Initialized with username={}, groupName={}), requireLogon={}", username, groupName, requireLogon);
    }

    public AbstractClientActorExtension(String groupName, boolean requireLogon) {
        this(ClientActor.generateClientId(), groupName, requireLogon);
    }

    public AbstractClientActorExtension(String username, String groupName) {
        this(username, groupName, true);
    }

    public AbstractClientActorExtension(String clientId) {
        this(clientId, Administrator.GROUP_NAME, true);
    }

    public AbstractClientActorExtension() {
        this(Administrator.GROUP_NAME, false);
    }

    protected abstract T createClientActor(ExtensionContext context, SecurityIntegrator securityIntegrator, KeycloakClient keycloakClient);

    @Override
    public void beforeAll(ExtensionContext context) throws IOException {
        SecurityIntegrator securityIntegrator = ExtensionUtils.getGlobalStore(context).get(SecurityIntegratorExtension.class, SecurityIntegrator.class);

        if (securityIntegrator == null) {
            throw new InvalidExtensionConfigException("SecurityIntegrator not present in context. Check if corresponding SecurityIntegratorExtension registered");
        }

        this.keycloakClient = getKeycloakClient(securityIntegrator, username, groupName);

        this.actor = createClientActor(context, securityIntegrator, keycloakClient);

        String actorKey = actorKey(username);
        ExtensionUtils.getGlobalStore(context).put(actorKey(username), actor);
        LOGGER.debug("Stored by key [{}] actor [{}] in storage of GLOBAL namespace", actorKey, actor);

        // store additionally list of actors to resolve several client actor extensions
        storeExistingActors(context);

        storeConfigurationGraphQLActor(context, keycloakClient);

        if (actor != null) {
            actor.connectToWyden();
            if (requireLogon) actor.awaitLogon();
        }
    }

    private void storeConfigurationGraphQLActor(ExtensionContext context, KeycloakClient keycloakClient) {
        configGqlActor = actor instanceof GraphQLActor ? (GraphQLActor) actor : new GraphQLActor(keycloakClient);
        ExtensionUtils.getGlobalStore(context).put(gqlActorKey(username), configGqlActor);
        LOGGER.debug("Stored additional GQL actor [{}] for management (account, portfolio creation) in storage of GLOBAL namespace", actor.getClientId());
    }

    private void storeExistingActors(ExtensionContext context) {
        Actors existingActors = ExtensionUtils.getGlobalStore(context).get(ACTORS_KEY, Actors.class);
        if (existingActors == null) {
            List<ClientActor> actorsList = new ArrayList<>();
            actorsList.add(actor);
            existingActors = new Actors(actorsList);
        } else {
            existingActors.actors().add(actor);
        }
        ExtensionUtils.getGlobalStore(context).put(ACTORS_KEY, existingActors);
        LOGGER.debug("Stored by key [{}] actors [{}] in storage of GLOBAL namespace", ACTORS_KEY, existingActors);
    }

    private KeycloakClient getKeycloakClient(SecurityIntegrator securityIntegrator, String username, String groupName) {
        if (groupName.equals(Manager.GROUP_NAME)) {
            return securityIntegrator.setupManager(username);
        } else if (groupName.equals(Administrator.GROUP_NAME)) {
            return securityIntegrator.setupAdministrator(username);
        }
        return securityIntegrator.setupManager();
    }

    @Override
    public void afterAll(ExtensionContext context) throws Exception {
        ClientActor actor = ExtensionUtils.getGlobalStore(context).get(actorKey(username), ClientActor.class);
        if (actor != null) {
            actor.disconnectFromWyden();
            if (requireLogon) actor.awaitLogoff();
            actor.close();
        }
        GraphQLActor graphQLActor = ExtensionUtils.getGlobalStore(context).get(gqlActorKey(username), GraphQLActor.class);
        if (graphQLActor != null) {
            graphQLActor.close();
        }
    }

    @Override
    public boolean supportsParameter(ParameterContext parameterContext, ExtensionContext extensionContext) {
        return Actors.class.equals(parameterContext.getParameter().getType())
            || ClientActor.class.equals(parameterContext.getParameter().getType())
            || GraphQLActor.class.equals(parameterContext.getParameter().getType())
            || FixTradingActor.class.equals(parameterContext.getParameter().getType())
            || RestActor.class.equals(parameterContext.getParameter().getType());
    }

    @Override
    public Object resolveParameter(ParameterContext parameterContext, ExtensionContext extensionContext) {
        if (Actors.class.equals(parameterContext.getParameter().getType())) {
            return ExtensionUtils.getGlobalStore(extensionContext).get(ACTORS_KEY, Actors.class);
        }
        if (AnnotationUtils.isAnnotated(extensionContext.getTestClass(), GraphQL.class)) {
            return ExtensionUtils.getGlobalStore(extensionContext).get(actorKey(username), GraphQLActor.class);
        }
        if (AnnotationUtils.isAnnotated(extensionContext.getTestClass(), Fix.class)) {
            return ExtensionUtils.getGlobalStore(extensionContext).get(actorKey(username), FixTradingActor.class);
        }
        if (AnnotationUtils.isAnnotated(extensionContext.getTestClass(), Rest.class)) {
            return ExtensionUtils.getGlobalStore(extensionContext).get(actorKey(username), RestActor.class);
        }

        return ExtensionUtils.getGlobalStore(extensionContext).get(actorKey(username), ClientActor.class);
    }

    public String clientId() {
        return username;
    }

    @SuppressWarnings("unchecked")
    public T actor() {
        if (actor == null) {
            LOGGER.error("Actor was not initialized. Please check if correct user credentials were used");
        }
        return (T) actor;
    }

    public KeycloakClient getKeycloakClient() {
        if (keycloakClient == null) {
            LOGGER.error("Keycloak client was not created. Please check if correct user credentials were used");
        }
        return keycloakClient;
    }

    public GraphQLActor configGqlActor() {
        return configGqlActor;
    }

    public record Actors(List<ClientActor> actors) {

        public ClientActor getActor(String clientId) {
            return actors.stream()
                .filter(actor -> actor.getClientId().equals(clientId))
                .findFirst()
                .orElseThrow();
        }

        public ClientActor getActor(String clientId, Class<?> cls) {
            return actors.stream()
                .filter(actor -> cls.isInstance(actor) && actor.getClientId().equals(clientId))
                .findFirst()
                .orElseThrow();
        }

    }

}
