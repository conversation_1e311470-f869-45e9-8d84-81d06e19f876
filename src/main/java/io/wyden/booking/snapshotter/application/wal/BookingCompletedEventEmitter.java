package io.wyden.booking.snapshotter.application.wal;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.booking.snapshotter.application.ledgerentry.LedgerEntryToProtoMapper;
import io.wyden.booking.snapshotter.application.position.PositionToProtoMapper;
import io.wyden.booking.snapshotter.application.reservation.ReservationToProtoMapper;
import io.wyden.booking.snapshotter.application.transaction.TransactionToProtoMapper;
import io.wyden.booking.snapshotter.domain.reservation.Reservation;
import io.wyden.booking.snapshotter.domain.reservation.trade.ClientCashTradeReservation;
import io.wyden.booking.snapshotter.domain.reservation.trade.StreetCashTradeReservation;
import io.wyden.booking.snapshotter.domain.state.StateOutput;
import io.wyden.booking.snapshotter.domain.transaction.ExecType;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.booking.snapshotter.domain.transaction.trade.Trade;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.booking.BookingCompleted;
import io.wyden.published.booking.LedgerEntrySnapshot;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsOrderStatus;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static io.wyden.booking.snapshotter.infrastructure.telemetry.Meters.BOOKING_SNAPSHOTTER_BOOKING_COMPLETED_OUTGOING;
import static io.wyden.booking.snapshotter.infrastructure.telemetry.TelemetryConfig.SERVICE;
import static org.slf4j.LoggerFactory.getLogger;

@Component
public class BookingCompletedEventEmitter {

    private static final Logger LOGGER = getLogger(BookingCompletedEventEmitter.class);

    private final RabbitExchange<BookingCompleted> bookingCompletedExchange;
    private final MeterRegistry meterRegistry;

    public BookingCompletedEventEmitter(RabbitExchange<BookingCompleted> bookingCompletedExchange,
                                        Telemetry telemetry) {
        this.bookingCompletedExchange = bookingCompletedExchange;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    public void emit(StateOutput stateOutput) {
        updateMetrics(stateOutput);
        if (!stateOutput.isSuccessful()) {
            return;
        }

        // This condition can be true both in error cases and during processing of subsequent WalEvents,
        // for example when multiple pre-trade checks are related to single reservationRef
        if (stateOutput.isEmpty()) {
            LOGGER.info("Skipping emitting booking completed event as processed transaction or reservation cannot be found in stateOutput: " + stateOutput);
            return;
        }

        List<BalanceSnapshot> balanceSnapshots = stateOutput.processedPositions().values().stream()
            .map(PositionToProtoMapper::calculateBalanceSnapshot)
            .toList();

        List<LedgerEntrySnapshot> ledgerEntrySnapshots = stateOutput.processedLedgerEntries().stream()
            .map(LedgerEntryToProtoMapper::map)
            .toList();

        Optional<OrderInfo> transactionOrderInfo = stateOutput.processedTransactions().stream()
            .map(this::toOrderInfo)
            .findFirst();

        Optional<OrderInfo> reservationOrderInfo = stateOutput.processedReservations().stream()
            .map(this::toOrderInfo)
            .findFirst();

        Optional<OrderInfo> reservationReleaseOrderInfo = stateOutput.reservationRelease().stream()
            .map(this::toOrderInfo)
            .findFirst();

        OrderInfo orderInfo = transactionOrderInfo
            .or(() -> reservationOrderInfo)
            .or(() -> reservationReleaseOrderInfo)
            .orElse(null);

        BookingCompleted.Builder bookingCompleted = BookingCompleted.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .setSequenceNumber(stateOutput.sequenceNumber())
            .addAllBalanceSnapshot(balanceSnapshots)
            .addAllLedgerEntrySnapshot(ledgerEntrySnapshots);

        if (orderInfo != null) {
            bookingCompleted
                .setOrderId(orderInfo.orderId())
                .setOrderStatus(orderInfo.orderStatus())
                .setPortfolioId(orderInfo.portfolioId());
        }

        Optional<TransactionSnapshot> transactionSnapshot = stateOutput.processedTransactions().stream()
            .map(TransactionToProtoMapper::map)
            .findFirst();

        Optional<ReservationSnapshot> reservationSnapshot = stateOutput.processedReservations().stream()
            .map(ReservationToProtoMapper::map)
            .findFirst();

        if (transactionSnapshot.isPresent() && reservationSnapshot.isPresent()) {
            throw new IllegalArgumentException("Cannot emit booking completed event for transaction and reservation at the same time");
        }

        transactionSnapshot.ifPresent(bookingCompleted::setTransactionSnapshot);
        reservationSnapshot.ifPresent(bookingCompleted::setReservationSnapshot);

        Map<String, String> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), BookingCompleted.class.getSimpleName(),
            OemsHeader.SOURCE.getHeaderName(), SERVICE
        );

        LOGGER.info("Emitting booking completed event to {}: \n{}", bookingCompletedExchange.getName(), bookingCompleted);
        bookingCompletedExchange.tryPublishWithHeaders(bookingCompleted.build(), headers);
    }

    private record OrderInfo(String orderId, OemsOrderStatus orderStatus, String portfolioId) {
        public static OrderInfo unknown() {
            return new OrderInfo("", OemsOrderStatus.ORDER_STATUS_UNSPECIFIED, "");
        }
    }

    private OrderInfo toOrderInfo(Transaction transaction) {
        if (transaction instanceof Trade trade) {
            return new OrderInfo(trade.getOrderId(), map(trade.getExecType()), trade.getPortfolioId());
        }

        return OrderInfo.unknown();
    }

    private OrderInfo toOrderInfo(Reservation reservation) {
        if (reservation instanceof ClientCashTradeReservation trade) {
            return new OrderInfo(trade.getReservationRef(), OemsOrderStatus.STATUS_NEW, trade.getPortfolioId());
        }

        if (reservation instanceof StreetCashTradeReservation trade) {
            return new OrderInfo(trade.getReservationRef(), OemsOrderStatus.STATUS_NEW, trade.getPortfolioId());
        }

        return OrderInfo.unknown();
    }

    private OrderInfo toOrderInfo(String reservationRef) {
        if (reservationRef != null) {
            return new OrderInfo(reservationRef, OemsOrderStatus.ORDER_STATUS_UNSPECIFIED, "");
        }

        return OrderInfo.unknown();
    }

    private OemsOrderStatus map(ExecType execType) {
        if (execType == null) {
            return OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
        }

        return switch (execType) {
            case NEW -> OemsOrderStatus.STATUS_NEW;
            case PARTIAL_FILL -> OemsOrderStatus.STATUS_PARTIALLY_FILLED;
            case FILL -> OemsOrderStatus.STATUS_FILLED;
            case EXPIRED -> OemsOrderStatus.STATUS_EXPIRED;
            case CANCELED -> OemsOrderStatus.STATUS_CANCELED;
            case PENDING_CANCEL -> OemsOrderStatus.STATUS_PENDING_CANCEL;
            case REJECTED -> OemsOrderStatus.STATUS_REJECTED;
            case PENDING_NEW -> OemsOrderStatus.STATUS_PENDING_NEW;
            default -> OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
        };
    }

    private void updateMetrics(StateOutput stateOutput) {
        try {
            Tags tags = Tags.of(
                "resultType", stateOutput.isSuccessful() ? "SUCCESS" : "ERROR",
                "failureType", stateOutput.reason()
            );
            meterRegistry.counter(BOOKING_SNAPSHOTTER_BOOKING_COMPLETED_OUTGOING, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}