package io.wyden.rest.management.booking;

import io.wyden.published.booking.BalanceSnapshot;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.rest.management.domain.PositionModel;

import static io.wyden.rest.management.common.ProtoMappingUtils.bd;
import static io.wyden.rest.management.common.ProtoMappingUtils.dt;

public class PositionFromProtoMapper {

    public static PositionModel.Position map(PositionSnapshot position) {
        if (position == null) {
            return null;
        }

        return new PositionModel.Position(
            dt(position.getMetadata().getUpdatedAt()),
            position.getCurrency(),
            position.getSymbol(),
            position.getPortfolio(),
            position.getAccount(),
            position.getLastAppliedLedgerEntryId(),
            bd(position.getQuantity()),
            bd(position.getPendingQuantity()),
            bd(position.getAvailableForTradingQuantity()),
            bd(position.getAvailableForWithdrawalQuantity()),
            bd(position.getSettledQuantity()),
            bd(position.getUnsettledQuantity()),
            bd(position.getNotionalQuantity()),
            bd(position.getMarketValue()),
            bd(position.getNetRealizedPnl()),
            bd(position.getGrossRealizedPnl()),
            bd(position.getNetUnrealizedPnl()),
            bd(position.getGrossUnrealizedPnl()),
            bd(position.getNetCost()),
            bd(position.getGrossCost()),
            bd(position.getNetAveragePrice()),
            bd(position.getGrossAveragePrice()));
    }

    public static PositionModel.Balance map(BalanceSnapshot balance) {
        if (balance == null) {
            return null;
        }

        return new PositionModel.Balance(
            dt(balance.getMetadata().getUpdatedAt()),
            balance.getCurrency(),
            balance.getSymbol(),
            balance.getPortfolioId(),
            balance.getAccountId(),
            balance.getLastAppliedLedgerEntryId(),
            bd(balance.getQuantity()),
            bd(balance.getPendingQuantity()),
            bd(balance.getAvailableForTradingQuantity()),
            bd(balance.getAvailableForWithdrawalQuantity()),
            bd(balance.getSettledQuantity()),
            bd(balance.getUnsettledQuantity()));
    }
}
