include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: maven:3.8.********************

variables:
  IMAGE_NAME: quoting-order-service
  RELEASE_NAME: quoting-order-service

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification
  stage: version

changes-verify:
  extends: .verify-locked-files
  stage: version

version-check-domain:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - quoting-order-service-domain/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - quoting-order-service-domain/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  stage: test
  interruptible: true
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  script:
    - ./gradlew sonarqube --stacktrace
  allow_failure: true
  only:
    - main
  except:
    - schedules

.unit-test:
  stage: test
  extends: .unit_test
  variables:
    REPORTS_BASE_DIRECTORY: "quoting-order-service/"
  except:
    - tags
    - schedules

.integration-test:
  stage: test
  extends: .integration_test
  variables:
    REPORTS_BASE_DIRECTORY: "quoting-order-service/"
  except:
    - tags
    - schedules

build:
  stage: build
  interruptible: true
  script:
    - ./gradlew assemble
  artifacts:
    paths:
      - quoting-order-service/build/libs/
      - quoting-order-service-domain/build/libs/
    expire_in: 1 days
  except:
    - tags
    - schedules

publish-domain-lib:
  stage: publish
  interruptible: true
  only:
    - main
  script:
    - cd quoting-order-service-domain
    - ../gradlew publish
  dependencies:
    - build
  tags:
    - image-builder
  except:
    - tags
    - schedules

publish:
  extends:
    - .publish_image
  variables:
    DOCKER_BUILD_CONTEXT: quoting-order-service/.
    DOCKERFILE_PATH: quoting-order-service/Dockerfile
  dependencies:
    - build
  only:
    - main
  except:
    - schedules

container-scan:
  extends:
    - .docker_scan
  dependencies: []
  artifacts:
    when: always
    paths:
      - ./results-container.html
    expire_in: 1 days
  allow_failure: true
  only:
    - main

retag:
  extends:
    - .retag_image
  stage: publish
  dependencies: []
  only:
    - tags
  except:
    - schedules

audit:
  extends:
    - .audit_trails
  stage: publish
  only:
    - tags

deploy-dev:
  extends:
    - .deploy_helm
  only:
    - main
  except:
    - schedules

tag_rc:
  extends: .tag_repository
  stage: tag
  only:
    - main

tag_rc_scheduled:
  extends: .tag_repository_scheduled
  stage: tag

