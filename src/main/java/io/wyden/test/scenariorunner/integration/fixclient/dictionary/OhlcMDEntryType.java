package io.wyden.test.scenariorunner.integration.fixclient.dictionary;

import quickfix.field.MDEntryType;

public enum OhlcMDEntryType {

    TRADE_OPEN('o'),
    TRADE_HI('h'),
    TRADE_LOW('l'),
    TRADE_CLOSE('c'),

    MID_OPEN('w'),
    MID_HI('x'),
    MID_LOW('y'),
    MID_CLOSE('z');

    private final MDEntryType mdEntryType;

    OhlcMDEntryType(char val) {
        this.mdEntryType = new MDEntryType(val);
    }

    public MDEntryType getMdEntryType() {
        return mdEntryType;
    }

}
