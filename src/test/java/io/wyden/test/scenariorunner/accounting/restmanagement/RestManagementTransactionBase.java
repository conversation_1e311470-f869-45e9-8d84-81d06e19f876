package io.wyden.test.scenariorunner.accounting.restmanagement;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.rest.management.domain.PositionModel.Balance;
import io.wyden.rest.management.domain.PositionModel.Position;
import io.wyden.rest.management.domain.TransactionModel.StreetCashTradeRequest;
import io.wyden.test.scenariorunner.assertion.accounting.restmgmt.PositionSoftAssert;
import io.wyden.test.scenariorunner.data.refdata.Currency;
import io.wyden.test.scenariorunner.extension.eachtest.GraphQLActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.RestManagementActorExtension;
import io.wyden.test.scenariorunner.integration.restmgmtclient.RestManagementActor;
import io.wyden.test.scenariorunner.integration.service.RateServiceClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.extension.RegisterExtension;

import java.math.BigDecimal;
import java.util.NoSuchElementException;

import static io.wyden.test.scenariorunner.data.infra.Epics.REST_MANAGEMENT;
import static io.wyden.test.scenariorunner.data.infra.TestTags.BPCE;

@Epic(REST_MANAGEMENT)
@Tag(BPCE)
public abstract class RestManagementTransactionBase {

    protected static final Currency CRYPTO_CURRENCY = Currency.PEPE;
    protected static final Currency FIAT_CURRENCY = Currency.USD;
    protected long beforeCreationTime;
    protected RestManagementActor restMgmtActor;
    protected BigDecimal price;
    protected RateServiceClient rateServiceClient;

    @RegisterExtension
    @Order(0)
    GraphQLActorExtension graphQlActorExtension = new GraphQLActorExtension();
    @RegisterExtension
    @Order(1)
    RestManagementActorExtension restManagementActorExtension = new RestManagementActorExtension(graphQlActorExtension.clientId());

    @BeforeEach
    void actorSetup() {
        rateServiceClient = new RateServiceClient();
        restMgmtActor = restManagementActorExtension.actor();
    }

    protected Balance getPortfolioBalance(String portfolioName, Currency currency) {
        return restMgmtActor
            .portfolios()
            .getPortfolioBalances(portfolioName)
            .stream()
            .filter(e -> e.symbol().equals(currency.name()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Cannot find portfolio=%s balance with currency=%s".formatted(portfolioName, currency)));
    }

    protected Balance getAccountBalance(String accountName, Currency currency) {
        return restMgmtActor
            .accounts()
            .getAccountBalances(accountName)
            .stream()
            .filter(e -> e.symbol().equals(currency.name()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Cannot find account=%s balance with currency=%s".formatted(accountName, currency)));
    }

    protected Position getPortfolioPosition(String portfolioName, Currency currency) {
        return restMgmtActor
            .portfolios()
            .getPortfolioPositions(portfolioName)
            .stream()
            .filter(e -> e.symbol().equals(currency.name()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Cannot find portfolio=%s position with currency=%s".formatted(portfolioName, currency)));
    }

    protected Position getAccountPosition(String accountName, Currency currency) {
        return restMgmtActor
            .accounts()
            .getAccountPositions(accountName)
            .stream()
            .filter(e -> e.symbol().equals(currency.name()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Cannot find account=%s position with currency=%s".formatted(accountName, currency)));
    }

    @Step
    protected void verifyFiatPosition(StreetCashTradeRequest trade, Position position) {
        BigDecimal tradeAmount = trade.quantity().multiply(trade.price()).negate();
        PositionSoftAssert.assertThat(position)
            .quantityIs(tradeAmount)
            .availableForWithdrawalQuantityIs(tradeAmount)
            .pendingQuantityIs(tradeAmount)
            .settledQuantityIs(tradeAmount)
            .marketValueIs(tradeAmount)
            .netCostIs(tradeAmount)
            .grossCostIs(tradeAmount)
            .netAveragePriceIs(BigDecimal.ONE)
            .grossAveragePriceIs(BigDecimal.ONE)
            .assertAll();
    }

    @Step
    protected void verifyCryptoPosition(StreetCashTradeRequest trade, Position position) {
        price = getPrice();
        BigDecimal fullTradePrice = trade.quantity().multiply(trade.price());
        BigDecimal expectedUnrealizedPNL = price.subtract(fullTradePrice);
        PositionSoftAssert.assertThat(position)
            .quantityIs(trade.quantity())
            .availableForWithdrawalQuantityIs(trade.quantity())
            .pendingQuantityIs(trade.quantity())
            .settledQuantityIs(trade.quantity())
            .marketValueIs(price)
            .netUnrealizedPnlIs(expectedUnrealizedPNL)
            .grossUnrealizedPnlIs(expectedUnrealizedPNL)
            .netCostIs(fullTradePrice)
            .grossCostIs(fullTradePrice)
            .netAveragePriceIs(fullTradePrice)
            .grossAveragePriceIs(fullTradePrice)
            .assertAll();
    }

    protected BigDecimal getPrice() {
        return rateServiceClient.getPrice(CRYPTO_CURRENCY.name(), FIAT_CURRENCY.name());
    }

}
