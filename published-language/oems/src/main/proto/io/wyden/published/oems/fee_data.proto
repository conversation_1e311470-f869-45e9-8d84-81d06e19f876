syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.oems;

message FeeData {

  // The currency of the fee
  string currency = 1;

  // The amount of the fee charged
  // String representation of java.math.BigDecimal
  string amount = 2;

  // The type indicating the origin of the fee
  FeeType type = 3;

  FeeBasis basis = 4;

  // Generic description of the fee containing any extra information
  string description = 5;
}

enum FeeType {
  FEE_TYPE_UNSPECIFIED = 0;

  //The executing broker's commission
  EXCHANGE_FEE = 2;

  //The commission charged by the sales desk
  TRANSACTION_FEE = 5;

  //The fixed fee which is charged by the sales desk
  FIXED_FEE = 10;
}

enum FeeBasis {
  FEE_BASIS_UNSPECIFIED = 0;

  //Total monetary amount
  ABSOLUTE = 3;
}