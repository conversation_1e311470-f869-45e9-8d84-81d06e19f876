@startuml

actor Admin
actor Retail
participant Agency
participant CLOB_gw
participant CLOB
participant QuotingEngine
participant Bitstamp_conn
participant Goldman_conn

== Configuration ==
Admin -> CLOB_gw: Configure\nCLOB **uid 123** = Bank_portfolio
Admin -> QuotingEngine: Configure\nCLOB **uid 765** = Quoting_engine_portfolio
Admin -> QuotingEngine: Configure\nquoting source: **bitstamp_account**\nconversion_source: **goldman_account**\nBTCTRY@CLOB->BTCUSD@Bitstamp,TRYUSD@Goldman
'assumption: on Instrument level, source instrument is mandatory. conversion source instrument is cnditionally mandatory (when source instr symbol != CLOB Instr symbol)
'on global config level we do not allow heuristic/guessing, but we can offer auto-complete for the user (later phase)

== Quoting ==
QuotingEngine -> Bitstamp_conn: subscribe BTCUSD market data\naccount: bitstamp_account
QuotingEngine -> Goldman_conn: subscribe USDTRY market data\naccount: goldman_account
Bitstamp_conn -> QuotingEngine: market data\nask 1 BTCUSD@Bitstamp @ 60k\naccount: bitstamp_account
Goldman_conn -> QuotingEngine: market data\nask 1 TRYUSD@Goldman @ 30\naccount: goldman_account

QuotingEngine -> CLOB: **clob_Order3**\nSELL 1 BTCTRY @ 1.800k\nuid: 765

'given:
'BTCTRY@FOREX@Garanti instrument exists
'BTCTRY@FOREX@CLOB instrument exists (and book is open in CLOB)
'Agency is configured to use BTCTRY@FOREX@CLOB for BTCTRY@FOREX@Garanti Order

== Order placement ==
Retail -> Agency: **Order1**\nBUY 1 BTCTRY@Garanti (client)\nportfolio: retail1, counterPortfolio: Bank_portfolio
Agency -> CLOB_gw: **Order2**\nBUY 1 BTCTRY@CLOB (street)\nportfolio: Bank_portfolio, account: clob_account
CLOB_gw -> CLOB: **clob_Order2**\nBUY 1 BTCTRY\nuid: 123

== Match and hedge ==
CLOB -> CLOB_gw: **match1**\nclob_Order2 vs clob_Order3\n123 vs 765
CLOB_gw -> CLOB_gw: get details of both uids
note left: CLOB_gw triggers the hedging logic after reading details of uid 765
CLOB_gw -> CLOB_gw: Create OEMS **Order3**: BTCTRY@CLOB, SELL 1 @ 1.800k \nportfolio: Quoting_engine_portfolio, account: clob_account
note left: Order 3 is created upon a match to reflect the quoting engine order
CLOB_gw -> Bitstamp_conn: **Order4**: BTCUSD@Bitstamp, BUY 1 @ 60k+safety_margin\nportfolio: Quoting_engine_portfolio, account: bitstamp_account
note left: Hedge Order4 is created to get liquidity to cover Order3\nHedge Order4 only covers crypto part of the hedge, uid 765 is resolved to BTCUSD@Bitstamp

== Executions ==
Bitstamp_conn -> CLOB_gw: **Execution1**\nfor Order4\n\nLE: \n+1 BTC (Quoting_engine_portfolio), \n+1 BTC (bitstamp_account), \n-60k USD (Quoting_engine_portfolio), \n-60k USD (bitstamp_account)
CLOB_gw -> Agency: **Execution2**\nfor Order2\nBank_portfolio vs clob_account\n\nLE: \n+1 BTC (clob_account), \n+1 BTC (Bank_portfolio), \n-1800k TRY (clob_account), \n-1800k TRY (Bank_portfolio)
CLOB_gw -> Agency: **Execution3**\nfor Order3\nQuoting_engine_portfolio vs clob_account\n\nLE: \n-1 BTC (clob_account), \n-1 BTC (Quoting_engine_portfolio), \n+1800k TRY (clob_account), \n+1800k TRY (Quoting_engine_portfolio)
Agency -> Retail: **Execution4**\nfor Order1\nBank_portfolio vs retail1\n\nLE: \n-1 BTC (Bank_portfolio), \n+1 BTC (retail1), \n+1800k TRY (Bank_portfolio), \n-1800k TRY (retail1)\n (+/- fees and markup)

@enduml

TODO:
* Instrument overrides - BE and FE
* Extend quoting config with conversion Instrument, conversion source
* Extend quoting engine with triangulation
---
* CLOBgw - on external match, resolve source Instrument (ignore conversion source)
* Verify booking engine is correctly tracking all Orders/executions
// GBP-50