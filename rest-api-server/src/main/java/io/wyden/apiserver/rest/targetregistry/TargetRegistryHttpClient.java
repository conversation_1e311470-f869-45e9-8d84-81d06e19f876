package io.wyden.apiserver.rest.targetregistry;

import io.wyden.apiserver.rest.infrastructure.webclient.WebClientInstance;
import io.wyden.apiserver.rest.venueaccount.model.KeyValue;
import io.wyden.published.targetregistry.ConnectorTemplateField;
import io.wyden.published.targetregistry.VenueCapabilitiesSnapshot;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

@Component
public class TargetRegistryHttpClient {

    private final WebClient webClient;
    private final String targetStatesUrl;
    private final String targetTemplateUrl;
    private final String targetDetailPrefix;

    public TargetRegistryHttpClient(final WebClientInstance webClientInstance,
                                    final @Value("${target.registry.target.states.url}") String targetStatesUrl,
                                    final @Value("${target.registry.target.template.url}") String targetTemplateUrl,
                                    final @Value("${target.registry.target.details.url.prefix}") String targetDetailPrefix) {
        this.webClient = webClientInstance.getWebClient();
        this.targetStatesUrl = targetStatesUrl;
        this.targetTemplateUrl = targetTemplateUrl;
        this.targetDetailPrefix = targetDetailPrefix;
    }

    Flux<AggregatedTargetState> targetStatesSnapshot(Set<String> targets) {

        if (targets.isEmpty()) {
            return Flux.empty();
        }

        return webClient
            .post()
            .uri(targetStatesUrl)
            .accept(MediaType.APPLICATION_JSON)
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(Map.of("targets", targets))
            .retrieve()
            .bodyToFlux(AggregatedTargetState.class);
    }

    public Mono<VenueAccountDetailsResponseDto> fetchTargetDetails(String target) {
        return webClient
            .get()
            .uri(targetDetailPrefix, uriBuilder -> uriBuilder
                .path( target )
                .build()
            )
            .accept(MediaType.APPLICATION_JSON)
            .retrieve()
            .bodyToMono(VenueAccountDetailsResponseDto.class);
    }

    public record VenueAccountDetailsResponseDto(String venueAccountId, String venueName, Collection<KeyValue> keyValues) {
    }

    public Mono<VenueCapabilitiesSnapshot> getDetailedConnectorCapabilities() {
        return webClient
            .get()
            .uri(targetDetailPrefix, uriBuilder -> uriBuilder
                .path( "capabilities")
                .build()
            )
            .accept(MediaType.APPLICATION_PROTOBUF)
            .retrieve()
            .bodyToMono(VenueCapabilitiesSnapshot.class);

    }

    public Flux<ConnectorTemplateField> getConnectorTemplate(String venue) {
        return webClient
            .get()
            .uri(targetTemplateUrl, uriBuilder -> uriBuilder
                .path(venue)
                .build()
            )
            .accept(MediaType.APPLICATION_PROTOBUF)
            .retrieve()
            .bodyToFlux(ConnectorTemplateField.class);

    }
}
