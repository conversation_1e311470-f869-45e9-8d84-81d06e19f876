package io.wyden.sor.utils;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.DelayHeader;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.RawRabbitExchange;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.sor.infrastructure.rabbit.RabbitDestinations;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class MessageScheduler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageScheduler.class);

    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations destinations;
    private final Set<String> consumerTags = new CopyOnWriteArraySet<>();
    private final RabbitQueue<Message> delayedMessageQueue;

    public MessageScheduler(RabbitIntegrator rabbitIntegrator, RabbitDestinations destinations) {
        this.rabbitIntegrator = rabbitIntegrator;

        delayedMessageQueue = rabbitIntegrator.createQueue()
            .setQueueName("test-delayed-queue-%s".formatted(UUID.randomUUID()))
            .setConsumerName("sor-integration-test")
            .setSingleActiveConsumer(true)
            .declare();
        this.destinations = destinations;

        delayedMessageQueue.bindWithHeaders(destinations.getScheduledRabbitExchange().getScheduledMessagesExchange(), MatchingCondition.ANY, Map.of());
        String consumerTag = delayedMessageQueue.attachConsumer((bytes, s) -> Any.parseFrom(bytes), this::onDelayedMessage);
        consumerTags.add(consumerTag);
    }

    private ConsumptionResult onDelayedMessage(Message message, AMQP.BasicProperties basicProperties) {
        try {
            long scheduledTime = Long.valueOf(DateUtils.isoUtcTimeToEpochMillis(basicProperties.getHeaders().get(DelayHeader.SCHEDULED_TIME.getHeaderName()).toString()));
            long now = System.currentTimeMillis();
            long delay = Math.max(scheduledTime - now, 1);
            String targetExchange = basicProperties.getHeaders().get(DelayHeader.TARGET_EXCHANGE.getHeaderName()).toString();
            LOGGER.info("Scheduling delayed message to {} with delay={} ms", targetExchange, delay);
            scheduledExecutorService.schedule(() -> {
                LOGGER.info("Sending delayed message to {}", targetExchange);
                RawRabbitExchange.publishWithHeaders(message.toByteArray(), StringUtils.EMPTY, basicProperties.getHeaders(), message.getClass().getName(), rabbitIntegrator, targetExchange);
            }, delay, TimeUnit.MILLISECONDS);
            return ConsumptionResult.consumed();
        } catch (Exception e) {
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    public void cleanup() throws IOException {
        scheduledExecutorService.shutdownNow();
        delayedMessageQueue.unbindWithHeaders(destinations.getScheduledRabbitExchange().getScheduledMessagesExchange(), MatchingCondition.ANY, Map.of());
        for (String consumerTag : consumerTags) {
            delayedMessageQueue.cancelConsumer(consumerTag);
        }

        rabbitIntegrator.getDeclarationAndPublishChannel().queueDelete(delayedMessageQueue.getName());
    }
}
