import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { ApolloProvider } from '@apollo/client';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

import './i18n';
import { store } from './store';
import { AppWrapper } from './AppWrapper';
import { NestReact } from '@nest-react/NestReact';
import { apolloClient } from '@wyden/services/graphql/apollo-client';
import { AnalyticsProviders } from './AnalyticsProviders';
import { LicenseManager } from 'ag-grid-enterprise';

LicenseManager.setLicenseKey(
  'Using_this_{AG_Grid}_Enterprise_key_{AG-061126}_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_changing_this_key_please_contact_info@ag-grid.com___{Wyden}_is_granted_a_{Single_Application}_Developer_License_for_the_application_{Wyden}_only_for_{5}_Front-End_JavaScript_developers___All_Front-End_JavaScript_developers_working_on_{Wyden}_need_to_be_licensed___{Wyden}_has_been_granted_a_Deployment_License_Add-on_for_{3}_Production_Environments___This_key_works_with_{AG_Grid}_Enterprise_versions_released_before_{25_December_2025}____[v3]_[01]_MTc2NjYyMDgwMDAwMA==d1a0ecc8e7d55700aefaa6e3e3408b60',
);

// eslint-disable-next-line import/no-default-export
export default function AuthenticatedContainer() {
  return (
    <AnalyticsProviders>
      <Provider store={store}>
        <ApolloProvider client={apolloClient}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <BrowserRouter>
              <NestReact root="container" gap={8} suggestedHandlers={true} tabTargetHeight={36}>
                <AppWrapper />
              </NestReact>
            </BrowserRouter>
          </LocalizationProvider>
        </ApolloProvider>
      </Provider>
    </AnalyticsProviders>
  );
}

console.log(`
  ****************************                                                                                                                      
  ****************************                                                                  ****                                                
  *************  *************                                                                  ****                                                
  *******   *  **  *   *******              ****     *****     ****  ****      *****  **************    ************   **************               
  *************  ** **********               ****   *** ***   ****    ****    *****  ****       ****   ***       ****  *****      ****              
  *******  **  **  *   *******                ***  ***   ***  ***      ****  ****   ****        ****  ***************  ****       ,***              
  ********** ****** **********                *******     *******       ********     ****      *****   ****      .*    ****       ,***              
  *******  **  **  **  *******                 *****       *****          *****       **************    ************   ****       ,***              
  *********  ******  *********                                           *****                                                                      
  ****************************                                          ****                                                                        
  ****************************                                         ****                                                                                                                                                                                                                    
  
`);
