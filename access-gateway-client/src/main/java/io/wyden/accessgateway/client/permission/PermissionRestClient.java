package io.wyden.accessgateway.client.permission;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.RequestEntity;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Set;
import javax.annotation.Nullable;

public class PermissionRestClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionRestClient.class);

    private final RestTemplate restTemplate;
    private final String accessGatewayHost;

    public PermissionRestClient(RestTemplate restTemplate, String accessGatewayHost) {
        this.restTemplate = restTemplate;
        this.accessGatewayHost = accessGatewayHost;
    }

    @Nullable
    public void addGroupPermissions(String groupName, Set<PermissionDto> permissions) {
        LOGGER.info("Requesting grant to group {} permissions {}", groupName, permissions);
        restTemplate.exchange(
            RequestEntity.post(accessGatewayHost + "/permissions/groups/" + groupName).body(permissions),
            Void.class
        ).getBody();
    }

    public void addUserPermissions(String username, Set<PermissionDto> permissions) {
        LOGGER.info("Requesting grant to user {} permissions {}", username, permissions);
        restTemplate.exchange(
            RequestEntity.post(accessGatewayHost + "/permissions/users/" + username).body(permissions),
            Void.class
        );
    }

    public void removeGroupPermissions(String groupName, Set<PermissionDto> permissions) {
        LOGGER.info("Requesting revoke from group {} permissions {}", groupName, permissions);
        restTemplate.exchange(
            RequestEntity.post(accessGatewayHost + "/permissions/groups/" + groupName + "/remove").body(permissions),
            Void.class
        );
    }

    public void removeUserPermissions(String username, Set<PermissionDto> permissions) {
        LOGGER.info("Requesting revoke from user {} permissions {}", username, permissions);
        restTemplate.exchange(
            RequestEntity.post(accessGatewayHost + "/permissions/users/" + username + "/remove").body(permissions),
            Void.class
        );
    }

    @Nullable
    public List<String> getGroupNames() {
        LOGGER.info("Requesting group names");
        List<String> result = restTemplate.exchange(
                RequestEntity.get(accessGatewayHost + "/permissions/groups/names").build(),
                new ParameterizedTypeReference<List<String>>() {}
            ).getBody();
        LOGGER.info("Received {} group names", result != null ? String.valueOf(result.size()) : "null");
        return result;
    }

    @Nullable
    public List<String> getUsersNames() {
        LOGGER.info("Requesting user names");
        List<String> result = restTemplate.exchange(
                RequestEntity.get(accessGatewayHost + "/permissions/users/names").build(),
                new ParameterizedTypeReference<List<String>>() {}
            ).getBody();
        LOGGER.info("Received {} user names", result != null ? String.valueOf(result.size()) : "null");
        return result;
    }
}
