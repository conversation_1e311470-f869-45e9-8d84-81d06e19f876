package io.wyden.target.registry.connectorstate;

import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.target.registry.k8s.K8sIntegrationTest;
import org.junit.jupiter.api.Test;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertNull;

class VenueAccountDeactivateConsumerTest extends K8sIntegrationTest {

    @Test
    void after_connector_deactivation_target_registry_should_not_emit_any_events() {
        //given
        awaitConnectorIsNotAlive();
        requestDefaultConnector();
        awaitConnectorIsAlive();
        //when
        deactivateConnector();
        mockTargetStateConsumer.getSentCapabilityState().clear();
        awaitConnectorIsNotAlive();
        //then no state change emitted
        assertNull(mockTargetStateConsumer.getSentCapabilityState().get(Capability.MARKET_DATA));
    }

    @Test
    void after_deactivating_connector_should_be_possible_to_activate_it_again() {
        //given
        requestDefaultConnector();
        deactivateConnector();
        //when
        Awaitility.await().atMost(50, TimeUnit.SECONDS).until(() -> checkTargetRegistryStatus(CONNECTOR_ID, HealthStatus.HEALTH_STATUS_UNSPECIFIED));
        //then
        activateConnector();
        awaitConnectorIsAlive();
    }
}
