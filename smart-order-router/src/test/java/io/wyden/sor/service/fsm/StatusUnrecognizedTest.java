package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.sor.model.SmartOrderState;
import io.wyden.sor.service.tracking.FailureRequeueException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static io.wyden.published.oems.OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_CANCELED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_CANCEL;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED;
import static io.wyden.published.oems.OemsResponse.Result.OEMS_ORDER_REGISTRATION_ERROR_DUPLICATE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;

class StatusUnrecognizedTest extends OrderServiceBase {

    OrderState orderState;
    String requestId;

    @BeforeEach
    void beforeEach() {
        requestId = UUID.randomUUID().toString();
        SmartOrderState smartOrderState = SmartOrderState.newBuilder()
            .setRequest(order)
            .setClientId(order.getClientId())
            .setQuantity(order.getQuantity())
            .setFilledQuantity("0")
            .setRemainingQuantity(order.getQuantity())
            .setAvgPrice("0")
            .setClosed(false)
            .addCurrentStatus(STATUS_NEW)
            .addCurrentStatus(STATUS_CANCELED)
            .addCurrentStatus(ORDER_STATUS_UNSPECIFIED)
            .addCurrentStatus(STATUS_PENDING_CANCEL)
            .setPendingCancelRequestId(requestId)
            .build();
        orderCache.add(smartOrderState);
        orderState = new OrderState(smartOrderState);
    }

    @Test
    void whenNewOrderThenRejectWithDuplicateOrderId() {
        OrderStatus orderStatus = new StatusUnrecognized(OemsOrderStatus.STATUS_CANCELED_VALUE);
        OrderContext context = new OrderContext(orderService);
        context.setOrderState(orderState);
        orderStatus.onNewOrder(context, order);
        smartOrderRoutingService.updateSmartOrderState(orderState);
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getValue();
        assertThat(oemsResponse.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
        assertThat(oemsResponse.getExecType()).isEqualTo(OemsExecType.REJECTED);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_DUPLICATE);
        assertThat(oemsResponse.getReason()).isEqualTo("Duplicate orderId");
    }

    @Test
    void whenCancelRequestThenRequeue() {
        OemsRequest oemsCancel = defaultCancel();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsRequest(oemsCancel));
        assertThat(ex.getMessage()).isEqualTo("Received Cancel when in unrecognized state value=0");
    }

    @Test
    void whenNewThenRequeue() {
        OemsResponse executionReport = newExecutionReport();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsChildResponse(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received New when in unrecognized state value=0");
    }

    @Test
    void whenRejectedThenRequeue() {
        OemsResponse executionReport = rejectedExecutionReport("no reason");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsChildResponse(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Rejected when in unrecognized state value=0");
    }

    @Test
    void whenPartialFillThenRequeue() {
        OemsResponse executionReport = partialFillExecutionReport("3.0", "3.0", "30", "30");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsChildResponse(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Trade when in unrecognized state value=0");
    }

    @Test
    void whenFilledThenRequeue() {
        OemsResponse executionReport = filledExecutionReport("10.0", "10.0", "30", "30");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsChildResponse(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Trade when in unrecognized state value=0");
    }

    @Test
    void whenCancelledThenRequeue() {
        OemsResponse executionReport = cancelledExecutionReport("10.0", "30");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsChildResponse(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Canceled when in unrecognized state value=0");
    }

    @Test
    void whenUnrecognizedThenRequeue() {
        OemsResponse report = newExecutionReport().toBuilder()
            .setExecTypeValue(256)
            .build();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsChildResponse(report));
        assertThat(ex.getMessage()).isEqualTo("Received ExecType.256 when in unrecognized state 0");
    }

    @Test
    void whenConvertedToVenueOrderStatusThenReturnsStatusUnspecified() {
        OrderStatus orderStatus = StatusUnrecognized.create(127);
        assertThat(orderStatus.toOemsOrderStatus()).isEqualTo(ORDER_STATUS_UNSPECIFIED);
    }

    @Test
    void equalsOnlyWhenVenueOrderStatusEquals() {
        assertThat(StatusUnrecognized.create(500)).isEqualTo(StatusUnrecognized.create(500));
        assertThat(StatusUnrecognized.create(500)).isNotEqualTo(StatusUnrecognized.create(501));
    }

    @Test
    void toStringReturnsBothPrecedenceAndVenueOrderStatus() {
        OrderStatus orderStatus = StatusUnrecognized.create(303);
        assertThat(orderStatus).hasToString("StatusUnrecognized(303).100");
    }

    @Test
    void compareToComparesVenueOrderStatus() {
        assertThat(StatusUnrecognized.create(128)).isLessThan(StatusUnrecognized.create(129));
    }

    @Test
    void statusUnrecognizedIsGreaterThanOtherStates() {
        assertThat(StatusUnrecognized.create(1)).isGreaterThan(StatusPendingCancel.create());
    }
}
