import { useState } from 'react';
import { InputAdornment } from '@mui/material';
import { TextField, TextFieldProps } from './TextField';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { styled } from './styled';

export const PasswordField = (props: TextFieldProps) => {
  const [inputType, setInputType] = useState('password');
  const togglePasswordVisibility = () =>
    setInputType((prev) => (prev === 'password' ? 'text' : 'password'));

  return (
    <TextField
      {...props}
      type={inputType}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            {inputType === 'password' ? (
              <StyledVisibilityOffIcon onClick={togglePasswordVisibility} />
            ) : (
              <StyledVisibilityIcon onClick={togglePasswordVisibility} />
            )}
          </InputAdornment>
        ),
      }}
    />
  );
};

const StyledVisibilityIcon = styled(VisibilityIcon)`
  cursor: pointer;
`;

const StyledVisibilityOffIcon = styled(VisibilityOffIcon)`
  cursor: pointer;
`;
