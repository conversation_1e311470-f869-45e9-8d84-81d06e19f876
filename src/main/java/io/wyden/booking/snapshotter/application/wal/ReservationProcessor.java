package io.wyden.booking.snapshotter.application.wal;

import io.wyden.booking.snapshotter.application.state.StateProvider;
import io.wyden.booking.snapshotter.domain.BookingService;
import io.wyden.booking.snapshotter.domain.BuyingPowerPositionCheck;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.reservation.Reservation;
import io.wyden.booking.snapshotter.domain.state.ReservationStateInput;
import io.wyden.booking.snapshotter.domain.state.StateOutput;
import io.wyden.booking.snapshotter.domain.state.StateOutputBuilder;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.oems.OemsOrderType;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;

import static io.wyden.booking.snapshotter.application.wal.TransactionProcessor.debugLedgerEntries;
import static io.wyden.booking.snapshotter.application.wal.TransactionProcessor.debugPositions;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * Processor for reservation snapshots.
 * Handles the core booking logic and delegates additional processing to specialized services.
 */
@Service
public class ReservationProcessor {

    private static final Logger LOGGER = getLogger(ReservationProcessor.class);

    private final StateProvider stateProvider;
    private final BookingService bookingService;
    private final BuyingPowerPositionCheck buyingPowerPositionCheck;

    public ReservationProcessor(StateProvider stateProvider,
                                BookingService bookingService,
                                BuyingPowerPositionCheck buyingPowerPositionCheck) {
        this.stateProvider = stateProvider;
        this.bookingService = bookingService;
        this.buyingPowerPositionCheck = buyingPowerPositionCheck;
    }

    /**
     * Process a reservation snapshot.
     * This includes:
     * 1. Fetching the state input
     * 2. Booking the ledger entries
     * 3. Processing any additional fields in the snapshot
     * 4. Emitting the booking completed event
     *
     * @param sequenceNumber the sequence number
     * @param reservationSnapshot the reservation snapshot to process
     * @return the state output from processing
     */
    public StateOutput process(Long sequenceNumber, ReservationSnapshot reservationSnapshot) {
        // Fetch state and prepare for booking
        ReservationStateInput stateInput = stateProvider.fetchStateInput(reservationSnapshot);
        Reservation reservation = stateInput.getReservation();
        reservation.setSequenceNumber(sequenceNumber);
        LOGGER.info("{} - Reservation parsed from snapshot: ({})", sequenceNumber, reservation);

        if (stateInput.isReservationActive(reservation.getReservationRef())) {
            String reason = "Reservation with the same reference %s already exists. No booking will be performed".formatted(reservation.getReservationRef());
            LOGGER.info("{} - {}", sequenceNumber, reason);
            return StateOutput.emptySuccess(sequenceNumber, reason);
        }

        Collection<LedgerEntry> ledgerEntries = new ArrayList<>(reservation.getLedgerEntries());
        if (reservationSnapshot.hasVolatilityBuffer()) {
            includeVolatilityBufferEntries(reservationSnapshot, reservation, ledgerEntries);
        }
        debugLedgerEntries(ledgerEntries);

        // Book the ledger entries
        StateOutputBuilder stateOutput = bookingService.book(ledgerEntries, stateInput)
            .addSequenceNumber(sequenceNumber)
            .addReservation(reservation);

        if (reservationSnapshot.getHasBuyingPowerPolicy()) {
            Optional<String> checkResult = buyingPowerPositionCheck.check(reservation, stateOutput);
            if (checkResult.isPresent()) {
                String reason = "Reservation with buying power policy has failed Position check and will be rejected. " +
                    "No ledger entries will be applied.";
                LOGGER.info("{} - {}", reason, sequenceNumber);
                return StateOutput.failure(sequenceNumber, checkResult.get());
            }
        }

        StateOutput successful = stateOutput.buildSuccessful();
        debugPositions(successful.processedPositions().values());
        return successful;
    }

    private static void includeVolatilityBufferEntries(ReservationSnapshot reservationSnapshot, Reservation reservation, Collection<LedgerEntry> ledgerEntries) {
        BigDecimal volatilityBuffer = bd(reservationSnapshot.getVolatilityBuffer());
        BigDecimal marketPrice = bd(reservationSnapshot.getMarketPrice());
        OemsOrderType orderType = reservationSnapshot.getOrderType();
        Collection<LedgerEntry> reservationBufferLedgerEntries = reservation.getReservationBufferLedgerEntries(marketPrice, volatilityBuffer, orderType);
        LOGGER.debug("Adding volatility buffer ledger entries: {}", reservationBufferLedgerEntries);
        ledgerEntries.addAll(reservationBufferLedgerEntries);
    }
}
