# exchange collections
[![][license img]][license]

Exchange Collections is an **open source high performance Java collections** project. 
-[Adaptive Radix Tree](https://db.in.tum.de/~leis/papers/ART.pdf).

### Features
- low-latency - no need to re-balance or re-size
- objects pooling, no GC pressure
- keys are stored in sorted order

### Installation
1. Install library into your Maven's local repository by running `mvn install`
2. Add the following Maven dependency to your project's `pom.xml`:
```
<dependency>
    <groupId>exchange.core2</groupId>
    <artifactId>collections</artifactId>
    <version>0.5.1</version>
</dependency>
```

### Usage examples

TBD

### Testing

TBD

### Contributing
Exchange Collections is an open-source project and contributions are welcome!

[license]:LICENSE
[license img]:https://img.shields.io/badge/License-Apache%202-blue.svg
