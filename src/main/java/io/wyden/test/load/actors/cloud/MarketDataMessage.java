package io.wyden.test.load.actors.cloud;

import io.wyden.test.load.actors.Message;
import io.wyden.test.scenariorunner.model.marketdata.L1Event;
import io.wyden.test.scenariorunner.model.marketdata.L2Event;
import io.wyden.test.scenariorunner.model.marketdata.MarketDataType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import javax.annotation.ParametersAreNonnullByDefault;


@ParametersAreNonnullByDefault
public class MarketDataMessage extends Message {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataMessage.class);

    private MarketDataType marketDataType;
    private String instrumentId;
    private String venueAccount;
    private ZonedDateTime zonedDateTime;

    public MarketDataMessage(String id, L1Event event) {
        super(id);
        this.marketDataType = event.marketDataType();
        this.instrumentId = event.identifier().instrumentId();
        this.venueAccount = event.identifier().venueAccount();
        this.zonedDateTime = event.identifier().dateTime();
    }

    public MarketDataMessage(String id, L2Event event) {
        super(id);
        this.marketDataType = MarketDataType.ORDERBOOK;
        this.instrumentId = event.identifier().instrumentId();
        this.venueAccount = event.identifier().venueAccount();
        this.zonedDateTime = event.identifier().dateTime();
    }

    public MarketDataType getMarketDataType() {
        return marketDataType;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public String getVenueAccount() {
        return venueAccount;
    }

    public ZonedDateTime getZonedDateTime() {
        return zonedDateTime;
    }

    public long getTimestamp() {
        return zonedDateTime.toInstant().toEpochMilli();
    }
}
