version: "3.9"
services:
  db:
    image: "gvenzl/oracle-xe:18-slim-faststart"
    environment:
      ORACLE_RANDOM_PASSWORD: "true"
      ORACLE_DATABASE: "wyden"
      ORACLE_PDB: "wyden"
      APP_USER: "keycloak"
      APP_USER_PASSWORD: "password"
    volumes:
      - ./sql/oracle:/docker-entrypoint-initdb.d
    ports:
      - "1521:1521"
    healthcheck:
      test: [ "CMD-SHELL", "/opt/oracle/healthcheck.sh"]

  pgadmin:
    entrypoint: ["echo", "Service pgadmin disabled"]

  keycloak:
    environment:
      KC_DB_URL: *********************************
      KC_DB: oracle

  storage:
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_FLYWAY_LOCATIONS=classpath:oracle/migration/schema,classpath:oracle/migration/data
      - DB_ENGINE=oracle

  access-gateway-server:
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - DB_ENGINE=oracle

  rest-api-server:
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_FLYWAY_LOCATIONS=classpath:oracle/migration/schema,classpath:oracle/migration/data
      - DB_ENGINE=oracle

  booking-engine:
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_FLYWAY_LOCATIONS=classpath:oracle/migration/schema,classpath:oracle/migration/data
      - DB_ENGINE=oracle

  audit-server:
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_FLYWAY_LOCATIONS=classpath:oracle/migration/schema,classpath:oracle/migration/data
      - DB_ENGINE=oracle
