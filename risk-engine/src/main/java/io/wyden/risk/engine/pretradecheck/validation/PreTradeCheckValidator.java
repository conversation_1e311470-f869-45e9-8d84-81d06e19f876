package io.wyden.risk.engine.pretradecheck.validation;

import io.micrometer.common.util.StringUtils;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeCheckLevel;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import jakarta.validation.Validator;
import org.hibernate.validator.internal.engine.ConstraintViolationImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

import static io.wyden.published.risk.PreTradeCheckLevel.PRE_TRADE_CHECK_LEVEL_UNSPECIFIED;
import static io.wyden.published.risk.PreTradeCheckLevel.UNRECOGNIZED;

@Component
public class PreTradeCheckValidator {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreTradeCheckValidator.class);

    private final Validator validator;

    public PreTradeCheckValidator(Validator validator) {
        this.validator = validator;
    }

    public <P> void validate(P preTradeCheckProperties) {
        Set<ConstraintViolation<P>> violations = validator.validate(preTradeCheckProperties);
        if (!violations.isEmpty()) {
            ConstraintViolationException exception = new ConstraintViolationException(violations);
            LOGGER.warn("Validation failed for {}: {}", preTradeCheckProperties, exception.getMessage());
            throw exception;
        }
    }

    public void validateCheck(PreTradeCheck ptc) throws ValidationException {
        if (StringUtils.isBlank(ptc.getId())) {
            throw new ValidationException("id must not be null");
        }
        if (StringUtils.isBlank(ptc.getType())) {
            throw new ValidationException("type must not be null");
        }
        if (ptc.getLevel() == PRE_TRADE_CHECK_LEVEL_UNSPECIFIED) {
            throw new ValidationException("level must not be null");
        }
        if (ptc.getLevel() == UNRECOGNIZED) {
            throw new ValidationException("level not recognized");
        }
    }
}
