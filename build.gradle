import org.gradle.testing.jacoco.plugins.JacocoPlugin

plugins {
	id 'java'
	id 'idea'
	id 'jaco<PERSON>'
	alias dependencyCatalog.plugins.spring.boot
	alias dependencyCatalog.plugins.dependency.management
	alias dependencyCatalog.plugins.sonarqube
	alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
	buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
	repository_username = System.env.NEXUS_DEPLOY_USERNAME
	repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

group = 'io.wyden'
version = "0.3.0$buildVersion"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

repositories {
	mavenLocal()
	maven {
		name 'nexus-releases'
		url 'https://repo.wyden.io/nexus/repository/releases/'
		credentials {
			username repository_username
			password repository_password
		}
	}
	maven {
		name 'nexus-snapshot'
		url 'https://repo.wyden.io/nexus/repository/snapshots/'
		credentials {
			username repository_username
			password repository_password
		}
	}
	mavenCentral()
}

dependencies {
	// wyden
	implementation dependencyCatalog.cloud.utils.hazelcast
	implementation dependencyCatalog.cloud.utils.rabbitmq
	implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
	implementation dependencyCatalog.cloud.utils.rest
	implementation dependencyCatalog.cloud.utils.spring
	implementation dependencyCatalog.cloud.utils.telemetry
	implementation dependencyCatalog.cloud.utils.tools
	implementation dependencyCatalog.published.language.oems

	// wyden clients
	implementation dependencyCatalog.access.gateway.client
	implementation dependencyCatalog.rate.service.client
	implementation dependencyCatalog.reference.data.client

	// spring
	implementation dependencyCatalog.spring.boot.starter.actuator
	implementation dependencyCatalog.spring.boot.starter.web
	implementation dependencyCatalog.spring.boot.starter.data.jpa
	implementation dependencyCatalog.spring.retry

	// booking
	implementation dependencyCatalog.flyway.core
	implementation dependencyCatalog.postgresql
	implementation dependencyCatalog.hazelcast
	implementation dependencyCatalog.hazelcast.jet.protobuf
	implementation dependencyCatalog.hazelcast.sql

	// test
	testImplementation dependencyCatalog.awaitility
	testImplementation dependencyCatalog.assertj.core
	testImplementation dependencyCatalog.spring.boot.starter.test
	testImplementation dependencyCatalog.cloud.utils.test
	testImplementation dependencyCatalog.h2
	testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }

	// acceptance tests
	testImplementation dependencyCatalog.cucumber.java
	testImplementation dependencyCatalog.cucumber.junit
	testImplementation dependencyCatalog.cucumber.spring
	testImplementation dependencyCatalog.cucumber.junit.platform.engine
	testImplementation dependencyCatalog.junit.platform.suite

	// integration test
	testImplementation dependencyCatalog.testcontainers
	testImplementation dependencyCatalog.testcontainers.junit.jupiter
	testImplementation dependencyCatalog.testcontainers.postgresql
	testImplementation dependencyCatalog.testcontainers.rabbitmq
}

testing {
	suites {
		test {
			useJUnitJupiter()
		}

		integrationTest(JvmTestSuite) {
			// Use JUnit Jupiter for integration tests too
			useJUnitJupiter()

			dependencies {
				// Include the main project
				implementation project()

				// Copy test suite implementation dependencies
				implementation sourceSets.test.compileClasspath
				runtimeOnly sourceSets.test.runtimeClasspath
			}

			targets {
				all {
					testTask.configure {
						shouldRunAfter(test)
					}
				}
			}
		}

		acceptanceTest(JvmTestSuite) {
			// Use JUnit Jupiter for Cucumber tests
			useJUnitJupiter()

			dependencies {
				// Include the main project
				implementation project()

				// Copy test suite implementation dependencies
				implementation sourceSets.test.compileClasspath
				runtimeOnly sourceSets.test.runtimeClasspath
			}

			sources {
				java {
					srcDirs = ['src/test/java']
					include 'io/wyden/booking/snapshotter/acceptance/CucumberTestRunner.java'
				}
			}

			targets {
				all {
					testTask.configure {
						shouldRunAfter(test)
						shouldRunAfter(integrationTest)
					}
				}
			}
		}
	}
}

tasks.named('check') {
	dependsOn(testing.suites.integrationTest)
	dependsOn(testing.suites.acceptanceTest)
}

test {
	finalizedBy jacocoTestReport
}

jacocoTestReport {
	reports {
		xml.required = true
		csv.required = true
	}

	getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

plugins.withType(JacocoPlugin) {
	tasks["test"].finalizedBy 'jacocoTestReport'
	tasks["integrationTest"].finalizedBy 'jacocoTestReport'
	tasks["acceptanceTest"].finalizedBy 'jacocoTestReport'
}

jacocoToCobertura {
	inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
	outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
	tasks["test"].finalizedBy 'jacocoTestReport'
	tasks["integrationTest"].finalizedBy 'jacocoTestReport'
	tasks["acceptanceTest"].finalizedBy 'jacocoTestReport'
	tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
	tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

bootJar {
	manifest {
		attributes(
				"Implementation-Version": "${archiveVersion}"
		)
	}
}

bootRun {
	args = ["--tracing.collector.endpoint=http://localhost:4317"]
	jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9043"]
	environment([
			"FLUENTD_HOST": "localhost",
			"SPRING_PROFILES_ACTIVE": "dev"
	])
}

sonarqube {
	properties {
		property "sonar.projectKey", "booking-snapshotter"
		property "sonar.projectName", "Booking Snapshotter"
		property "sonar.qualitygate.wait", true
	}
}
