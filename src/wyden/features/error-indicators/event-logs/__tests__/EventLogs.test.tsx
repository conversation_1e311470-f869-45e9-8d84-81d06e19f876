import { EventLogsPO } from './EventLogsPO';
import { useEventLogs } from '../useEventLogs';
import { useEffect } from 'react';
import { beforeEach, describe, it } from 'vitest';
import { eventsMockJSON } from './Events.mock';

const TestEventTrigger = () => {
  const { addEventLog } = useEventLogs();

  useEffect(() => {
    eventsMockJSON.forEach((event) => {
      addEventLog(event);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <> </>;
};

const TestOneEventTrigger = () => {
  const { addEventLog } = useEventLogs();

  useEffect(() => {
    addEventLog(eventsMockJSON[0]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <> </>;
};

describe('<EventLogs />', () => {
  let eventLogsPO: EventLogsPO;

  beforeEach(() => {
    eventLogsPO = new EventLogsPO();
  });

  it('Should render event logs and filter out the data', async () => {
    await eventLogsPO.render(
      <>
        <TestEventTrigger />
      </>,
    );

    await eventLogsPO.expectTextToBeDisplay('Buy 1 BTC');
    await eventLogsPO.expectTextToBeDisplay('Unable to buy 5 LTC due to network error');
    await eventLogsPO.triggerMouseOver('Unable to buy 5 LTC due to network error');
  });

  it('should render empty state item when there is no events', async () => {
    await eventLogsPO.render();

    await eventLogsPO.expectTextToBeDisplay('No events');
  });

  it('should render empty state item when filters is selected and there is no events', async () => {
    await eventLogsPO.render(<TestOneEventTrigger />);

    await eventLogsPO.selectEventLogType('Send order failed');
    await eventLogsPO.expectTextToBeDisplay('No events');
  });
});
