subscription OrderStatesWithSnapshot(
  $portfolioId: String
  $venueAccount: String
  $isOpen: Boolean
  $historyCap: Int
) {
  orderStatesWithSnapshot(
    portfolioId: $portfolioId
    venueAccount: $venueAccount
    isOpen: $isOpen
    historyCap: $historyCap
  ) {
    orderType
    avgPrice
    clOrderId
    clientId
    extOrderId
    createdAt
    expirationDateTime
    filledQty
    lastPrice
    lastQty
    lastRequestResult
    limitPrice
    orderId
    orderQty
    currency
    rootOrderId
    orderStatus
    origClOrderId
    portfolioId
    portfolioName
    counterPortfolioId
    counterPortfolioName
    reason
    remainingQty
    side
    stopPrice
    instrument {
      ...InstrumentContent
    }
    symbol
    assetClass
    venue
    tif
    updatedAt
    venueTimestamp
    sequenceNumber
    orderCategory
    parentOrderId
    venueAccountDescs {
      id
      name
    }
  }
}
