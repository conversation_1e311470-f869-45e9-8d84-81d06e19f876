package io.wyden.oems.storage.config.fixapi;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.apiserver.fix.domain.FixMarketDataSessionLockMapConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.EphemeralMapConfigBean;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.web.StringStringMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FixMarketDataSessionLockMapConfiguration {

    @Bean
    HazelcastMapConfigBean fixMarketDataSessionLockMapConfig() {
        HazelcastMapConfig hazelcastMapConfig = new FixMarketDataSessionLockMapConfig();
        return new EphemeralMapConfigBean(hazelcastMapConfig);
    }

    @Bean
    WebAccess fixMarketDataSessionLockMapWebAccess(HazelcastInstance hazelcast) {
        HazelcastMapConfig hazelcastMapConfig = new FixMarketDataSessionLockMapConfig();
        return new StringStringMapWebAccess(hazelcast, hazelcastMapConfig, null);
    }
}
