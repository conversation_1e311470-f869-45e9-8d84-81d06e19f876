package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.test.scenariorunner.extension.common.AnnotationBasedClientActorCreator;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import org.junit.jupiter.api.extension.ExtensionContext;

public class ClientActorExtension extends AbstractClientActorExtension<ClientActor> {

    public ClientActorExtension() {
        super();
    }

    public ClientActorExtension(String groupName, boolean requiredLogin) {
        super(groupName, requiredLogin);
    }

    @Override
    protected ClientActor createClientActor(ExtensionContext context, SecurityIntegrator securityIntegrator, KeycloakClient keycloakClient) {
        AnnotationBasedClientActorCreator annotationBasedClientActorCreator = new AnnotationBasedClientActorCreator();
        return annotationBasedClientActorCreator.createActor(context, securityIntegrator, keycloakClient);
    }

}
