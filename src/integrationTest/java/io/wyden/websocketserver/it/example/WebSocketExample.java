package io.wyden.websocketserver.it.example;

import io.wyden.websocketserver.interfaces.ws.Command;
import io.wyden.websocketserver.it.WebSocketClient;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.locks.LockSupport;

public class WebSocketExample {
    private static final String URI = "ws://localhost:8400/ws";
    private static final String API_KEY = "25b3209a-b10e-40a6-8fb2-96a5c6bb0317";
    private static final String API_SECRET = "UU7E10cYi4D6IQYC";

    public static void main(String[] args) {
        try (WebSocketClient client = new WebSocketClient("ws://localhost:8400/ws")) {
            client.setMessageListener(System.out::println);
            client.connect();
            client.login(API_KEY, API_SECRET);
            client.send(new Command("subscribe", "position", UUID.randomUUID().toString(), Map.of("snapshot", true)));
            client.send(new Command("subscribe", "connector-state", UUID.randomUUID().toString(), Map.of("snapshot", true)));
            LockSupport.park();
        }
    }
}
