package io.wyden.apiserver.rest.apiui.permissions;

import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.booking.BookingEngineService;
import io.wyden.apiserver.rest.booking.BookingModel;
import io.wyden.apiserver.rest.booking.BookingModel.PositionSearchInput;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.mock.mockito.MockBean;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;
import java.util.Optional;

import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class BookingGraphQLPermissionsTest extends PermissionsIntegrationTestNoDb {

    @MockBean
    BookingEngineService bookingEngineService;

    @MockBean
    VenueAccountCacheFacade venueAccountCacheFacade;

    @BeforeEach
    void setup() {
        PaginationModel.CursorConnection<BookingModel.PositionResponse> responsePositions = new PaginationModel.CursorConnection<>(of(), new PaginationModel.PageInfo(false, null, 0, 0L));
        PaginationModel.CursorConnection<BookingModel.LedgerEntryResponse> responseLedger = new PaginationModel.CursorConnection<>(of(), new PaginationModel.PageInfo(false, null, 0, 0L));
        PaginationModel.CursorConnection<BookingModel.TransactionResponse> responseTransactions = new PaginationModel.CursorConnection<>(of(), new PaginationModel.PageInfo(false, null, 0, 0L));
        when(bookingEngineService.getPositions(any(PositionSearchInput.class), any())).thenReturn(Mono.just(responsePositions));
        when(bookingEngineService.getLedgerEntries(any(BookingModel.LedgerEntrySearchInput.class), any())).thenReturn(Mono.just(responseLedger));
        when(bookingEngineService.getTransactions(any(BookingModel.TransactionSearchInput.class), any())).thenReturn(Mono.just(responseTransactions));
        when(bookingEngineService.subscribePositionStream(any(PositionSearchInput.class), any())).thenReturn(Flux.empty());
    }

    @ParameterizedTest
    @ValueSource(strings = {"ledger-entries"})
    @WithMockCustomUser
    void ledger_entries_permissions(String queryFileName) {
        grant("venue.account", "read", "venueAccountName");
        grant("portfolio", "read", "portfolioId");
        //happy path
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, Map.of("search", new BookingModel.LedgerEntrySearchInput(
            of("BTCUSD@FOREX@Bank"),
            of(),
            of("venueAccountName"),
            of("portfolioId"),
            of(BookingModel.LedgerEntryType.FEE),
            null,
            null,
            null,
            null,
            100,
            null,
            null))));

        //no permissions
        AssertionError exception = assertThrows(AssertionError.class, () ->
            executeAndVerifyFile(queryFileName, Map.of("search", new BookingModel.LedgerEntrySearchInput(
                of("BTCUSD@FOREX@Bank"),
                of(),
                of("forbiddenVenueAccount"),
                of("forbiddenPortfolioId"),
                of(BookingModel.LedgerEntryType.FEE),
                null,
                null,
                null,
                null,
                100,
                null,
                null))));

        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @ParameterizedTest
    @ValueSource(strings = {"positions"})
    @WithMockCustomUser
    void positions_permissions(String queryFileName) {
        grant("venue.account", "read", "venueAccountName");
        grant("portfolio", "read", "portfolioId");
        //happy path
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, Map.of("search", new PositionSearchInput(
            of("BTCUSD@FOREX@Bank"),
            of(),
            of("venueAccountName"),
            of("portfolioId"),
            100,
            null,
            null))));

        //no permissions
        AssertionError exception = assertThrows(AssertionError.class, () ->
            executeAndVerifyFile(queryFileName, Map.of("search", new PositionSearchInput(
                of("BTCUSD@FOREX@Bank"),
                of(),
                of("forbiddenVenueAccount"),
                of("forbiddenPortfolioId"),
                100,
                null,
                null))));

        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @Test
    @WithMockCustomUser
    void transactions_permissions() {
        grant("venue.account", "read", "venueAccountName");
        grant("portfolio", "read", "portfolioId");
        //happy path
        assertDoesNotThrow(() -> executeAndVerifyFile("transactions", Map.of("search", new BookingModel.TransactionSearchInput(
            of("BTCUSD@FOREX@Bank"),
            of("venueAccountName"),
            of("venueAccountName"),
            of("portfolioId"),
            of(), null, null, null, null, null, null, null, null, null, 100, null, null, null))));

        //no permissions
        AssertionError exception = assertThrows(AssertionError.class, () -> executeAndVerifyFile("transactions", Map.of("search",
            new BookingModel.TransactionSearchInput(
                of("BTCUSD@FOREX@Bank"),
                of("forbiddenVenueAccount"),
                of("forbiddenVenueAccount"),
                of("forbiddenPortfolioId"),
                of(), null, null, null, null, null, null, null, null, null, 100, null, null, null))));

        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @Test
    @WithMockCustomUser
    void position_subscription_permissions() {
        grant("venue.account", "read", "venueAccountName");
        grant("portfolio", "read");
        //happy path
        StepVerifier.create(executeFileSubscription("position-changes", Map.of("search", new PositionSearchInput(
                of("BTCUSD@FOREX@Bank"),
                of(),
                of("venueAccountName"),
                of("portfolioId"),
                100,
                null,
                null))))
            .expectSubscription()
            .expectNextCount(0)
            .thenCancel()
            .verify();

        //no permissions
        StepVerifier.create(executeFileSubscription("position-changes", Map.of("search", new PositionSearchInput(
                of("BTCUSD@FOREX@Bank"),
                of(),
                of("forbiddenVenueAccountName"),
                of("portfolioId"),
                100,
                null,
                null))))
            .verifyErrorMatches(CONTAINS_ACCESS_DENIED);
    }

    @Test
    @WithMockCustomUser
    void transaction_subscription_permissions() {
        grant("venue.account", "read", "venueAccountName");
        grant("portfolio", "read");
        //happy path
        StepVerifier.create(executeFileSubscription("transaction-created", Map.of("search", new BookingModel.TransactionSearchInput(
                of("BTCUSD@FOREX@Bank"),
                of("venueAccountName"),
                of("venueAccountName"),
                of("portfolioId"),
                of(),
                null, null, null, null, null, null, null,
                null,
                null,
                100,
                null,
                null, null))))
            .expectSubscription()
            .expectNextCount(0)
            .thenCancel()
            .verify();

        //no permissions
        StepVerifier.create(executeFileSubscription("transaction-created", Map.of("search", new BookingModel.TransactionSearchInput(
                of("BTCUSD@FOREX@Bank"),
                of("forbiddenVenueAccountName"),
                of("forbiddenVenueAccountName"),
                of("portfolioId"),
                of(),
                null, null, null, null, null, null, null,
                null,
                null,
                100,
                null,
                null, null))))
            .verifyErrorMatches(CONTAINS_ACCESS_DENIED);
    }

    @ParameterizedTest
    @ValueSource(strings = {"positions"})
    @WithMockCustomUser
    void positions_permissions_by_wallet_filtering(String queryFileName) {
        grant("wallet", "read");

        when(venueAccountCacheFacade.find("w1"))
            .thenReturn(Optional.of(VenueAccount.newBuilder().setId("w1").setAccountType(AccountType.WALLET).build()));

        //happy path
        assertDoesNotThrow(() -> executeAndVerifyFile(queryFileName, Map.of("search", new PositionSearchInput(
            of(),
            of(),
            of("w1"),
            of(),
            100,
            null,
            null))));

    }
}
