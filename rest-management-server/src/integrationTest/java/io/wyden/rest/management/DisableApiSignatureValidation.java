package io.wyden.rest.management;

import io.wyden.rest.management.security.authentication.AuthenticationService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.net.ssl.SSLException;

public class DisableApiSignatureValidation extends AuthenticationService {

    public DisableApiSignatureValidation() throws SSLException {
        super(null,null,null,null,null, null, null);
    }

    @Override
    public Authentication getAuthentication(HttpServletRequest request) {
        return SecurityContextHolder.getContext().getAuthentication();
    }
}