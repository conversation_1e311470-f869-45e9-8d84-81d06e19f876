query Timeline($search: TimelineSearchInput) {
  timeline(search: $search) {
    edges {
      node {
        type
        data {
          ... on OrderStateResponse {
            ...OrderStateContent
          }
          ... on MatchResponse {
            id
            makers {
              ...CounterpartyContent
            }
            primarySymbolQuotes {
              ...MarketQuoteContent
            }
            secondarySymbolQuotes {
              ...MarketQuoteContent
            }
            taker {
              ...CounterpartyContent
            }
            timestamp
          }
          ... on HedgeResult {
            timestamp
            clobOrderId
            clobRootOrderId
            matchId
            estimatedPrice #matchPrice
            hedgeOrderId
            hedgeRootOrderId
            hedgingVenue
            hedgeOrderQuantity
            hedgeOrderLimitPrice
            hedgeOrderSide
            success
            executionPrice
            hedgeOrderAmount #value
            hedgeOrderAmountInClobQuoteCurrency #valueTc
            primarySymbolQuote {
              ...MarketQuoteContent
            } #top_of_book_on_venue
            secondarySymbolQuote {
              ...MarketQuoteContent
            } #fx_rate
            breakEvenFxRate
          }
        }
      }
      cursor
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}

fragment CounterpartyContent on Counterparty {
  portfolioId
  portfolioName
  venueAccount
  venueAccountName
  referenceType
  orderId
  price
  volume
}

fragment MarketQuoteContent on MarketQuote {
  instrument {
    ...InstrumentContent
  }
  marketAskPrice
  marketBidPrice
  marketAskSize
  marketBidSize
  markupAskPrice
  markupBidPrice
  timestamp
}
