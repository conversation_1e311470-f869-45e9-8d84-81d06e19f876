---
status: ACCEPTED
---

# Use Semantic Versioning for package version numbers

## Context and Problem Statement

When released packages have consecutive numbers, there is no space
in versioning schema for hotfixes.

## Considered Options

* Use MAJOR.MINOR.PATCH.HOTFIX version numbering schema with HOTFIX part not
  present for releases.
* Use MAJOR.MINOR.0 version numbering schema for regular releases and MAJOR.MINOR.PATCH
  for hotfixes.

## Decision Outcome

#### For packages

Use MAJOR.MINOR.0 version for regular releases:
- MAJOR version changes when there are incompatible API changes,
- MINOR version changes when changes are backward-compatible

To release a first hotfix for given release:
- find commit tagged with appropriate MAJOR.MINOR.0 tag
- create branch named 'hotfix/MAJOR.MINOR'
- create feature branch named 'hotfix/AC-XXXX-short-description' from this branch
- change version number to MAJOR.MINOR.1
- merge to branch 'hotfix/MAJOR.MINOR'

To release subsequent hotfixes:
- find branch named 'hotfix/MAJOR.MINOR'
- create feature branch named 'hotfix/AC-XXXX-short-description' from this branch
- change version number to MAJOR.MINOR.PATCH+1
- merge to branch 'hotfix/MAJOR.MINOR'

The 'version-verify' pipeline job will guard proper versioning schema.

#### For libraries and dependency-catalog

No changes needed, we can use X.Y.Z-SNAPSHOT for regular releases and
X.Y.Z.H-SNAPSHOT for hotfixes.
