package io.wyden.executionengine.service.brokerdesk;

import io.wyden.executionengine.domain.model.BookEntry;
import io.wyden.published.oems.OemsOrderType;

import java.io.Serializable;
import java.util.Comparator;

import static io.wyden.executionengine.domain.model.OrderUtils.isAwaitingOrder;
import static io.wyden.executionengine.domain.model.OrderUtils.isBuyOrder;

public class BookEntryComparator implements Comparator<BookEntry>, Serializable {

    @Override
    public int compare(BookEntry o1, BookEntry o2) {
        if (!isAwaitingOrder(o1.getOrder().getOrderType())
            || !isAwaitingOrder(o2.getOrder().getOrderType())
            || isBuyOrder(o1.getOrder().getSide()) != isBuyOrder(o2.getOrder().getSide())
            || !o1.getOrder().getInstrumentId().equals(o2.getOrder().getInstrumentId()))
        {
            throw new IllegalArgumentException("Book entries %s and %s are not comparable.".formatted(o1, o2));
        }

        return combine(compareType(o1, o2), comparePrice(o1, o2), compareTime(o1, o2), compareOrderId(o1, o2));
    }

    private int combine(int... values) {
        for (int v : values) {
            if (v != 0) return v;
        }
        return 0;
    }

    private int compareOrderId(BookEntry o1, BookEntry o2) {
        return o1.getOrder().getOrderId().compareTo(o2.getOrder().getOrderId());
    }

    private int compareTime(BookEntry o1, BookEntry o2) {
        return Long.compare(o1.getTimestamp(), o2.getTimestamp());
    }

    private int comparePrice(BookEntry o1, BookEntry o2) {
        OemsOrderType orderType = o1.getOrder().getOrderType();
        if (orderType != OemsOrderType.LIMIT) {
            return 0;
        } else if (isBuyOrder(o1.getOrder().getSide())) {
            return o2.getPrice().compareTo(o1.getPrice());
        } else {
            return o1.getPrice().compareTo(o2.getPrice());
        }
    }

    private int compareType(BookEntry o1, BookEntry o2) {
        int v1 = o1.getOrder().getOrderType().getNumber();
        int v2 = o2.getOrder().getOrderType().getNumber();
        return v1 - v2;
    }
}
