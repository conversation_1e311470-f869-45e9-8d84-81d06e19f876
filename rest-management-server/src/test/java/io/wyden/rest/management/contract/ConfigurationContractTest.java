package io.wyden.rest.management.contract;

import io.wyden.published.brokerdesk.AgencyTargetType;
import io.wyden.published.brokerdesk.CurrencyType;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.ExecutionMode;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.InstrumentConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfigList;
import io.wyden.published.brokerdesk.PricingConfig;
import io.wyden.published.brokerdesk.SorTarget;
import io.wyden.published.brokerdesk.TernaryBool;
import io.wyden.published.brokerdesk.ThresholdConfig;
import io.wyden.published.brokerdesk.TradingMode;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.rest.management.SpringTestBase;
import io.wyden.rest.management.common.Identifiers;
import io.wyden.rest.management.contract.client.ConfigurationManagementApi;
import io.wyden.rest.management.contract.model.PortfolioGroupConfiguration;
import io.wyden.rest.management.contract.model.WydenRole;
import io.wyden.rest.management.fee.BrokerDeskConfigHttpClient;
import io.wyden.rest.management.security.permission.AccessGatewayHttpClient;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.slf4j.LoggerFactory.getLogger;

@Tag( "contract-tests")
public class ConfigurationContractTest extends SpringTestBase {

    private static final Logger LOGGER = getLogger(ConfigurationContractTest.class);

    @MockBean
    private AccessGatewayHttpClient accessGatewayHttpClient;

    @MockBean
    private BrokerDeskConfigHttpClient brokerDeskConfigHttpClient;

    /**
     * Get list of available portfolio group configurations
     */
    @Test
    public void configurationsPortfolioGroupsGetTest() {
        String path = "/api/v1/configurations/portfolio-groups";
        ConfigurationManagementApi configurationApi = new ConfigurationManagementApi(configureApi(path, "GET", ""));

        ExecutionConfig executionConfig = createExecutionConfig();
        PricingConfig pricingConfig = createPricingConfig();
        HedgingConfig hedgingConfig = createHedgingConfig();

        InstrumentConfig instrumentConfig = InstrumentConfig.newBuilder()
            .setInstrumentId("instrument-id")
            .setTradeable(TernaryBool.TRUE)
            .setExecutionConfig(executionConfig)
            .setPricingConfig(pricingConfig)
            .build();

        PortfolioGroupConfig portfolioGroupConfig = PortfolioGroupConfig.newBuilder()
            .setId("portfolio-group-id")
            .setName("portfolio-group-name")
            .setPortfolioType(PortfolioType.VOSTRO)
            .setExecutionConfig(executionConfig)
            .setPricingConfig(pricingConfig)
            .setHedgingConfig(hedgingConfig)
            .addInstrument(instrumentConfig)
            .build();

        when(brokerDeskConfigHttpClient.getPortfolioGroupConfigurations())
            .thenReturn(PortfolioGroupConfigList.newBuilder()
                .addPortfolioGroupConfig(portfolioGroupConfig)
                .build());

        List<PortfolioGroupConfiguration> response = configurationApi.retrieveAvailablePortfolioGroupConfigurations();
        LOGGER.info("Response: {}", response);

        assertThat(response).hasSize(1);
        assertThat(response.get(0).getId()).isEqualTo(portfolioGroupConfig.getId());
        assertThat(response.get(0).getName()).isEqualTo(portfolioGroupConfig.getName());
        assertThat(response.get(0).getCounterPortfolio()).isEqualTo(executionConfig.getCounterPortfolio());
        assertThat(response.get(0).getTargetAccount()).isEqualTo(executionConfig.getAgencyTradingAccount());
        assertThat(response.get(0).getPriceSourceAccount()).isEqualTo(pricingConfig.getPricingSourceList().get(0).getVenueAccount());
        assertThat(bd(response.get(0).getPriceMarkup())).isEqualTo(bd(pricingConfig.getMarkup()));
        assertThat(bd(response.get(0).getFixedFee())).isEqualTo(bd(executionConfig.getFixedFee()));
        assertThat(response.get(0).getFixedFeeCurrency()).isEqualTo(executionConfig.getFixedFeeCurrency());
        assertThat(bd(response.get(0).getMinFee())).isEqualTo(bd(executionConfig.getMinFee()));
        assertThat(response.get(0).getMinFeeCurrency()).isEqualTo(executionConfig.getMinFeeCurrency());
        assertThat(bd(response.get(0).getPercentageFee())).isEqualTo(bd(executionConfig.getPercentageFee()));
        assertThat(response.get(0).getPercentageFeeCurrency()).isEqualTo(executionConfig.getPercentageFeeCurrency());
        assertThat(response.get(0).getPercentageFeeCurrencyType().name()).isEqualTo(executionConfig.getPercentageFeeCurrencyType().name());
    }

    /**
     * Add Keycloak Admin Role to user/group
     */
    @Test
    public void keycloakUsersUsernameGroupsGroupNameRolesPostTest() {
        String username = Identifiers.randomIdentifier();
        String groupName = Identifiers.randomIdentifier();

        String path = "/api/v1/keycloak/users/%s/groups/%s/roles".formatted(username, groupName);
        ConfigurationManagementApi configurationApi = new ConfigurationManagementApi(configureApi(path, "POST", ""));

        doNothing().when(accessGatewayHttpClient).attachAdminRoleToUserAndGroup(anyString(), anyString());

        configurationApi.addAKeycloakAdministratorRoleToAUserOrGroup(username, groupName);
    }

    /**
     * Add Wyden Role to user/group
     */
    @Test
    public void permissionsUsersUsernameGroupsGroupNameRolesPostTest() {
        String username = Identifiers.randomIdentifier();
        String groupName = Identifiers.randomIdentifier();
        List<WydenRole> roles = List.of(WydenRole.MANAGE_ALL);

        String path = "/api/v1/permissions/users/%s/groups/%s/roles?roles=%s".formatted(username, groupName, roles.get(0));
        ConfigurationManagementApi configurationApi = new ConfigurationManagementApi(configureApi(path, "POST", ""));

        configurationApi.thisEndpointLetsYouAddOneOrMoreWydenRolesToASpecificUserOrGroupYouNeedToSpecifyTheUserByUsernameAndTheGroupByGroupNameASuccessfulOperationAddsTheSpecifiedRoleSToTheUsersPermissions(username, groupName, roles);
    }

    private static @NotNull PricingConfig createPricingConfig() {
        return PricingConfig.newBuilder()
            .addPricingSource(InstrumentKey.newBuilder()
                .setInstrumentId("BTC")
                .setVenueAccount("venue-account-id")
                .build())
            .setMarkup("3.0")
            .setClientSideVenueName("client-side-venue-name")
            .setPricingEnabled(true)
            .build();
    }

    private static @NotNull HedgingConfig createHedgingConfig() {
        return HedgingConfig.newBuilder()
            .setAutoHedging(TernaryBool.TRUE)
            .setTargetAccountId("target-account-id")
            .addThreshold(ThresholdConfig.newBuilder()
                .setAsset("BTC")
                .setHighThreshold("1.0")
                .setLowThreshold("2.0")
                .setTargetExposure("3.0")
                .setHedgeInstrument("ETH")
                .build())
            .setTargetAccountName("target-account-name")
            .build();
    }

    private static @NotNull ExecutionConfig createExecutionConfig() {
        return ExecutionConfig.newBuilder()
            .setTradingMode(TradingMode.AGENCY)
            .setCounterPortfolio("counter-portfolio-id")
            .setAgencyTradingAccount("agency-trading-account-id")
            .setAgencyTargetInstrumentId("agency-target-instrument-id")
            .setChargeExchangeFee(TernaryBool.TRUE)
            .setDiscloseTradingVenue(TernaryBool.TRUE)
            .setPercentageFee("1.0")
            .setPercentageFeeCurrency("EUR")
            .setPercentageFeeCurrencyType(CurrencyType.BASE_CURRENCY)
            .setFixedFee("2.0")
            .setFixedFeeCurrency("USD")
            .setMinFee("3.0")
            .setMinFeeCurrency("GBP")
            .setExecutionMode(ExecutionMode.SIMPLE)
            .addSorTradingAccounts("sor-trading-account-id-1")
            .setSorTarget(SorTarget.newBuilder()
                .setSymbol("BTC")
                .setAssetClass(AssetClass.FOREX)
                .build())
            .setAgencyTargetType(AgencyTargetType.CLOB)
            .build();
    }
}
