syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.booking.settlement;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/common/sorting.proto";

message SettlementSearch {
  string transaction_execution_id = 1;
  string client_settlement_id = 2;
  string from = 3; // unix timestamp
  string to = 4; // unix timestamp
  string client_id = 5;
  int32 first = 6;
  string after = 7;
  io.wyden.published.common.SortingOrder sorting_order = 8;
}

message SettlementRequest {
  io.wyden.published.common.Metadata metadata = 1;

  reserved 2;
  string settlement_date_time = 3; // ISO 8601 format, UTC
  repeated string settled_transaction_execution_ids = 4;
  reserved 5;
  repeated SettlementLeg street_side_legs = 7;
  repeated SettlementLeg client_side_legs = 8;
  SettlementConfiguration settlement_configuration = 9;

  // If true, settlement will be created, even if requested legs do not match booked transactions.
  // If false, settlement will be rejected if requested legs do not match booked transactions
  bool force_settlement = 6;

  string description = 10;
  string client_settlement_id = 11;
}

message SettlementResponse {
  io.wyden.published.common.Metadata metadata = 1;

  string settlement_id = 2;
  string settlement_date_time = 3; // ISO 8601 format, UTC
  repeated string settled_transaction_execution_ids = 4;

  reserved 5, 6;
  repeated SettlementLeg requested_street_side_legs = 9;
  repeated SettlementLeg requested_client_side_legs = 10;
  repeated SettlementLeg calculated_street_side_legs = 11;
  repeated SettlementLeg calculated_client_side_legs = 12;

  SettlementConfiguration settlement_configuration = 13;

  // Depending on force_settlement flag in request:
  // SUCCESS -> settlement was executed and requested legs matched booked transactions
  // SUCCESS_WITH_WARNING -> settlement was forced, because requested legs did not match booked transactions
  // FAILURE -> settlement was not executed, because requested legs did not match booked transactions
  SettlementStatus settlement_status = 7;

  string settlement_status_reason = 8;

  // legs that were requested, but not calculated by the system
  repeated SettlementLeg not_found_street_side_legs = 14;
  repeated SettlementLeg not_found_client_side_legs = 15;

  // legs that were calculated by the system, but not requested
  repeated SettlementLeg not_expected_street_side_legs = 16;
  repeated SettlementLeg not_expected_client_side_legs = 17;

  // legs that had different requested quantity than calculated quantity
  repeated MismatchedSettlementLeg quantity_mismatch_street_side_legs = 18;
  repeated MismatchedSettlementLeg quantity_mismatch_client_side_legs = 19;

  // executionIds that do not match any transaction
  repeated string invalid_transaction_execution_ids = 20;

  string description = 21;
  string client_settlement_id = 22;
}

message SettlementLeg {
  reserved 1, 2;

  string asset = 3;
  string quantity = 4;
  string source_id = 5;
  string target_id = 6;
}

message MismatchedSettlementLeg {
  string requested_quantity = 1;
  string calculated_quantity = 2;
  string asset = 3;
  string source_id = 4;
  string target_id = 5;
}

message SettlementConfiguration {
  repeated ConfigurationMapping street_side_mapping = 1;
  repeated ConfigurationMapping client_side_mapping = 2;
}

message ConfigurationMapping {
  string source_id = 1;
  repeated ConfigurationTarget targets = 2;
}

message ConfigurationTarget {
  ConfigurationTargetType type = 1;
  string target_id = 2;
}

enum ConfigurationTargetType {
  CONFIGURATION_TYPE_UNSPECIFIED = 0;
  CONFIGURATION_TYPE_CRYPTO = 1;
  CONFIGURATION_TYPE_FIAT = 2;
}

enum SettlementStatus {
  SETTLEMENT_STATUS_UNSPECIFIED = 0;
  SETTLEMENT_STATUS_SUCCESS = 1;
  SETTLEMENT_STATUS_SUCCESS_WITH_WARNING = 2;
  SETTLEMENT_STATUS_FAILURE = 3;
}
