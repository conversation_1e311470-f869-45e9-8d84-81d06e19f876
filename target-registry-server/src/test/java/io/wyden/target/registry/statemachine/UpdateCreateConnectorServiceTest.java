package io.wyden.target.registry.statemachine;

import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.targetregistry.ConnectorRequest;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.target.registry.eventlog.EventLogEmitter;
import io.wyden.target.registry.k8s.DockerImagesConfiguration;
import io.wyden.target.registry.k8s.KubernetesService;
import io.wyden.target.registry.vault.ConnectorVaultRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class UpdateCreateConnectorServiceTest {

    @Mock
    private ConnectorVaultRepository connectorVaultRepository;

    @Mock
    private KubernetesService kubernetesService;

    @Mock
    private DockerImagesConfiguration dockerImagesConfig;

    @Mock
    private EventLogEmitter eventLogEmitter;

    @Mock
    private VenueAccountCacheFacade venueAccountCacheFacade;

    private UpdateConnectorService updateConnectorService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        updateConnectorService = new UpdateConnectorService(connectorVaultRepository, venueAccountCacheFacade, kubernetesService, new StatePromotionValidator(dockerImagesConfig), eventLogEmitter);
    }

    @Test
    void testUpdateE2EAccount() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("e2eAccount");

        RuntimeException exception = assertThrows(RuntimeException.class, () -> updateConnectorService.update(request));
        assertTrue(exception.getMessage().contains("e2e venue account"));

        verifyNoInteractions(kubernetesService);
    }

    @Test
    void testUpdateActivateDeactivatedAccount_redeploySetToTrue() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("deactivatedAccount");
        when(request.getVenueName()).thenReturn("venueName");
        when(request.getParametersCount()).thenReturn(1);
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn("dockerImage");
        when(venueAccountCacheFacade.find(eq("deactivatedAccount")))
            .thenReturn(Optional.of(VenueAccount.newBuilder().build()));

        updateConnectorService.update(request);

        verify(connectorVaultRepository).updateConnectorData(request);
        verify(kubernetesService).deleteDeployment("deactivatedAccount");
        verify(kubernetesService).recreateDeployment(request);
        verify(eventLogEmitter).emit(
            eq("deactivatedAccount"),
            contains("Connector deactivatedAccount updated."),
            eq(EventLogStatus.SUCCESS),
            eq("updateConnector"),
            eq("")
        );
    }

    @Test
    void testUpdateActivateDeactivatedAccount_redeploySetToFalse() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("deactivatedAccount");
        when(request.getVenueName()).thenReturn("venueName");
        when(request.getParametersCount()).thenReturn(1);
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn("dockerImage");

        updateConnectorService.update(request);

        verify(connectorVaultRepository).updateConnectorData(request);

        verifyNoInteractions(kubernetesService, eventLogEmitter);
    }

    @Test
    void testCreateUpdateActivateDeactivateAccountWithoutParameters() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("noParamsAccount");
        when(request.getParametersCount()).thenReturn(0);
        when(request.getVenueName()).thenReturn("venueName");
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn("dockerImage");

        RuntimeException exception = assertThrows(RuntimeException.class, () -> updateConnectorService.update(request));
        assertTrue(exception.getMessage().contains("empty parameters list"));

        verifyNoInteractions(kubernetesService);
    }

    @Test
    void testRemovingAllParametersFromExistingAccount() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("existingAccount");
        when(request.getParametersCount()).thenReturn(0);
        when(request.getVenueName()).thenReturn("venueName");
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn("dockerImage");

        RuntimeException exception = assertThrows(RuntimeException.class, () -> updateConnectorService.update(request));
        assertTrue(exception.getMessage().contains("empty parameters list"));

        verifyNoInteractions(kubernetesService);
    }

    @Test
    void testAddingParameterToExistingAccountWithoutParameters() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("existingAccount");
        when(request.getParametersCount()).thenReturn(1);
        when(request.getVenueName()).thenReturn("venueName");
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn("dockerImage");
        when(venueAccountCacheFacade.find(eq("existingAccount")))
            .thenReturn(Optional.of(VenueAccount.newBuilder().build()));


        updateConnectorService.update(request);

        verify(connectorVaultRepository).updateConnectorData(request);
        verify(kubernetesService).recreateDeployment(request);
        verify(eventLogEmitter).emit(
            eq("existingAccount"),
            contains("Connector existingAccount updated."),
            eq(EventLogStatus.SUCCESS),
            eq("updateConnector"),
            eq("")
        );
    }

    @Test
    void testUpdateAccountWhenNoImage() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("existingAccount");
        when(request.getParametersCount()).thenReturn(1);
        when(request.getVenueName()).thenReturn("venueName");
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn(null);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> updateConnectorService.update(request));
        assertTrue(exception.getMessage().contains("unable to parse docker image for venue account"));

        verifyNoInteractions(kubernetesService);
    }

    @Test
    void shouldUpdateValuesInVault_eventWhenNotPassingValidation() {
        ConnectorRequest request = mock(ConnectorRequest.class);
        when(request.getVenueAccountId()).thenReturn("existingAccount");
        when(request.getParametersCount()).thenReturn(1);
        when(request.getVenueName()).thenReturn("venueName");
        when(dockerImagesConfig.getDockerImage("venueName")).thenReturn(null);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> updateConnectorService.update(request));
        assertTrue(exception.getMessage().contains("unable to parse docker image for venue account"));

        verify(connectorVaultRepository).updateConnectorData(eq(request));
        verifyNoInteractions(kubernetesService);
    }

}