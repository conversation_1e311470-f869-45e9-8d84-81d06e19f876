package io.wyden.apiserver.rest.booking;

import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.published.booking.LedgerEntrySearch;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.booking.TransactionSearch;
import io.wyden.published.booking.TransactionType;
import org.slf4j.Logger;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

import static java.util.Objects.requireNonNullElse;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.slf4j.LoggerFactory.getLogger;

public final class BookingModel {

    private static final Logger LOGGER = getLogger(BookingModel.class);

    public enum LedgerEntryType {
        ASSET_TRADE_BUY,
        ASSET_TRADE_SELL,
        CASH_TRADE_CREDIT,
        CASH_TRADE_DEBIT,
        ASSET_TRADE_PROCEEDS,
        DEPOSIT,
        WITHDRAWAL,
        TRANSFER,
        FEE,
        TRADING_FEE,
        DEPOSIT_FEE,
        WITHDRAWAL_FEE,
        TRANSFER_FEE,
        RESERVATION,
        RESERVATION_RELEASE,
        RESERVATION_RELEASE_REMAINING,
        WITHDRAWAL_RESERVATION,
        UNKNOWN,
    }

    public record PositionSearchInput(
        Collection<String> symbol,
        Collection<String> currency,
        Collection<String> venueAccount,
        Collection<String> portfolio,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) {
        public PositionSearchInput {
            symbol = symbol == null ? List.of() : new LinkedList<>(symbol);
            currency = currency == null ? List.of() : new LinkedList<>(currency);
            venueAccount = venueAccount == null ? List.of() : new LinkedList<>(venueAccount);
            portfolio = portfolio == null ? List.of() : new LinkedList<>(portfolio);
            sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
        }
    }

    public record PositionBookingSearch(
        Collection<String> symbol,
        Collection<String> currency,
        Collection<String> accountId,
        Collection<String> portfolio,
        String clientId,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) {
        public PositionBookingSearch {
            symbol = symbol == null ? List.of() : new LinkedList<>(symbol);
            currency = currency == null ? List.of() : new LinkedList<>(currency);
            accountId = accountId == null ? List.of() : new LinkedList<>(accountId);
            portfolio = portfolio == null ? List.of() : new LinkedList<>(portfolio);
            sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
        }

        public static PositionBookingSearch wrap(PositionSearchInput searchInput, String clientId) {
            return new PositionBookingSearch(
                searchInput.symbol(),
                searchInput.currency(),
                searchInput.venueAccount(),
                searchInput.portfolio(),
                clientId,
                searchInput.first(),
                searchInput.after(),
                searchInput.sortingOrder()
            );
        }

        public static PositionSearch wrapProto(PositionSearchInput searchInput, String clientId) {
            PositionSearch.Builder builder = PositionSearch.newBuilder()
                .setClientId(clientId);

            if (isNotEmpty(searchInput.symbol())) {
                builder.addAllSymbol(searchInput.symbol());
            }

            if (isNotEmpty(searchInput.currency())) {
                builder.addAllCurrency(searchInput.currency());
            }

            if (isNotEmpty(searchInput.venueAccount())) {
                builder.addAllAccountId(searchInput.venueAccount());
            }

            if (isNotEmpty(searchInput.portfolio())) {
                builder.addAllPortfolioId(searchInput.portfolio());
            }

            if (searchInput.first() != null) {
                builder.setFirst(searchInput.first());
            }

            if (isNotBlank(searchInput.after())) {
                builder.setAfter(searchInput.after());
            }

            if (searchInput.sortingOrder() != null) {
                builder.setSortingOrder(SharedModel.map(searchInput.sortingOrder()));
            }

            return builder.build();
        }

    }

    public record LedgerEntrySearchInput(
        Collection<String> symbol,
        Collection<String> currency,
        Collection<String> accountName,
        Collection<String> portfolioId,
        Collection<LedgerEntryType> ledgerEntryType,
        String transactionId,
        String orderId,
        String from,
        String to,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) {
        public LedgerEntrySearchInput {
            symbol = symbol == null ? List.of() : new LinkedList<>(symbol);
            currency = currency == null ? List.of() : new LinkedList<>(currency);
            accountName = accountName == null ? List.of() : new LinkedList<>(accountName);
            portfolioId = portfolioId == null ? List.of() : new LinkedList<>(portfolioId);
            ledgerEntryType = ledgerEntryType == null ? List.of() : new LinkedList<>(ledgerEntryType);
            sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
        }
    }

    public record LedgerEntryBookingSearch(
        Collection<String> symbol,
        Collection<String> currency,
        Collection<String> accountId,
        Collection<String> portfolio,
        Collection<LedgerEntryType> ledgerEntryType,
        String transactionId,
        String orderId,
        String clientId,
        String from,
        String to,
        Integer first,
        String after,
        SortingOrder sortingOrder
    ) {
        public LedgerEntryBookingSearch {
            symbol = symbol == null ? List.of() : new LinkedList<>(symbol);
            currency = currency == null ? List.of() : new LinkedList<>(currency);
            accountId = accountId == null ? List.of() : new LinkedList<>(accountId);
            portfolio = portfolio == null ? List.of() : new LinkedList<>(portfolio);
            ledgerEntryType = ledgerEntryType == null ? List.of() : new LinkedList<>(ledgerEntryType);
            sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
        }

        public static LedgerEntryBookingSearch wrap(LedgerEntrySearchInput searchInput, String clientId) {
            return new LedgerEntryBookingSearch(
                searchInput.symbol(),
                searchInput.currency(),
                searchInput.accountName(),
                searchInput.portfolioId(),
                searchInput.ledgerEntryType(),
                searchInput.transactionId(),
                searchInput.orderId(),
                clientId,
                searchInput.from(),
                searchInput.to(),
                searchInput.first(),
                searchInput.after(),
                searchInput.sortingOrder()
            );
        }

        public static LedgerEntrySearch wrapProto(LedgerEntrySearchInput searchInput, String clientId) {
            LedgerEntrySearch.Builder builder = LedgerEntrySearch.newBuilder()
                .setClientId(clientId);

            if (isNotEmpty(searchInput.symbol())) {
                builder.addAllSymbol(searchInput.symbol());
            }

            if (isNotEmpty(searchInput.currency())) {
                builder.addAllCurrency(searchInput.currency());
            }

            if (isNotEmpty(searchInput.accountName())) {
                builder.addAllAccountId(searchInput.accountName());
            }

            if (isNotEmpty(searchInput.portfolioId())) {
                builder.addAllPortfolioId(searchInput.portfolioId());
            }

            if (isNotEmpty(searchInput.ledgerEntryType())) {
                builder.addAllLedgerEntryType(map(searchInput.ledgerEntryType()));
            }

            if (isNotBlank(searchInput.transactionId())) {
                builder.setTransactionId(searchInput.transactionId());
            }

            if (isNotBlank(searchInput.orderId())) {
                builder.setOrderId(searchInput.orderId());
            }

            if (isNotBlank(searchInput.from())) {
                builder.setFrom(searchInput.from());
            }

            if (isNotBlank(searchInput.to())) {
                builder.setTo(searchInput.to());
            }

            if (searchInput.first() != null) {
                builder.setFirst(searchInput.first());
            }

            if (isNotBlank(searchInput.after())) {
                builder.setAfter(searchInput.after());
            }

            if (searchInput.sortingOrder() != null) {
                builder.setSortingOrder(SharedModel.map(searchInput.sortingOrder()));
            }

            return builder.build();
        }

        private static Collection<? extends io.wyden.published.booking.LedgerEntryType> map(Collection<LedgerEntryType> ledgerEntryTypes) {
            if (isEmpty(ledgerEntryTypes)) {
                return List.of();
            }

            return ledgerEntryTypes.stream()
                .map(type -> switch (type) {
                    case ASSET_TRADE_BUY -> io.wyden.published.booking.LedgerEntryType.ASSET_TRADE_BUY;
                    case ASSET_TRADE_SELL -> io.wyden.published.booking.LedgerEntryType.ASSET_TRADE_SELL;
                    case ASSET_TRADE_PROCEEDS -> io.wyden.published.booking.LedgerEntryType.ASSET_TRADE_PROCEEDS;
                    case CASH_TRADE_CREDIT -> io.wyden.published.booking.LedgerEntryType.CASH_TRADE_CREDIT;
                    case CASH_TRADE_DEBIT -> io.wyden.published.booking.LedgerEntryType.CASH_TRADE_DEBIT;
                    case DEPOSIT -> io.wyden.published.booking.LedgerEntryType.DEPOSIT;
                    case WITHDRAWAL -> io.wyden.published.booking.LedgerEntryType.WITHDRAWAL;
                    case TRANSFER -> io.wyden.published.booking.LedgerEntryType.TRANSFER;
                    case FEE -> io.wyden.published.booking.LedgerEntryType.FEE;
                    case TRADING_FEE -> io.wyden.published.booking.LedgerEntryType.TRADING_FEE;
                    case DEPOSIT_FEE -> io.wyden.published.booking.LedgerEntryType.DEPOSIT_FEE;
                    case WITHDRAWAL_FEE -> io.wyden.published.booking.LedgerEntryType.WITHDRAWAL_FEE;
                    case TRANSFER_FEE -> io.wyden.published.booking.LedgerEntryType.TRANSFER_FEE;
                    case RESERVATION -> io.wyden.published.booking.LedgerEntryType.RESERVATION;
                    case RESERVATION_RELEASE -> io.wyden.published.booking.LedgerEntryType.RESERVATION_RELEASE;
                    case RESERVATION_RELEASE_REMAINING -> io.wyden.published.booking.LedgerEntryType.RESERVATION_RELEASE_REMAINING;
                    default -> io.wyden.published.booking.LedgerEntryType.UNSPECIFIED;
                })
                .toList();
        }
    }

    public record TransactionSearchInput(
        List<String> symbol,
        List<String> currency,
        List<String> accountId,
        List<String> portfolioId,
        List<String> transactionType,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String underlyingExecutionId,
        String rootExecutionId,
        String executionId,
        String venueExecutionId,
        String from,
        String to,
        Integer first,
        String after,
        SortingOrder sortingOrder,
        String uuid
    ) {
        public TransactionSearchInput {
            symbol = symbol == null ? List.of() : new LinkedList<>(symbol);
            currency = currency == null ? List.of() : new LinkedList<>(currency);
            accountId = accountId == null ? List.of() : new LinkedList<>(accountId);
            portfolioId = portfolioId == null ? List.of() : new LinkedList<>(portfolioId);
            transactionType = transactionType == null ? List.of() : new LinkedList<>(transactionType);
            sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
        }

        @Override
        public List<String> accountId() {
            return accountId;
        }
    }

    public record TransactionBookingSearch(
        List<String> symbol,
        List<String> currency,
        List<String> accountId,
        List<String> portfolio,
        List<String> transactionType,
        String orderId,
        String parentOrderId,
        String rootOrderId,
        String underlyingExecutionId,
        String rootExecutionId,
        String executionId,
        String venueExecutionId,
        String clientId,
        String from,
        String to,
        Integer first,
        String after,
        SortingOrder sortingOrder,
        String uuid
    ) {
        public TransactionBookingSearch {
            symbol = symbol == null ? List.of() : new LinkedList<>(symbol);
            currency = currency == null ? List.of() : new LinkedList<>(currency);
            accountId = accountId == null ? List.of() : new LinkedList<>(accountId);
            portfolio = portfolio == null ? List.of() : new LinkedList<>(portfolio);
            transactionType = transactionType == null ? List.of() : new LinkedList<>(transactionType);
            sortingOrder = requireNonNullElse(sortingOrder, SortingOrder.DESC);
        }

        public static TransactionBookingSearch wrap(TransactionSearchInput searchInput, String clientId) {
            return new TransactionBookingSearch(
                searchInput.symbol(),
                searchInput.currency(),
                searchInput.accountId(),
                searchInput.portfolioId(),
                searchInput.transactionType(),
                searchInput.orderId(),
                searchInput.parentOrderId(),
                searchInput.rootOrderId(),
                searchInput.underlyingExecutionId(),
                searchInput.rootExecutionId(),
                searchInput.executionId(),
                searchInput.venueExecutionId(),
                clientId,
                searchInput.from(),
                searchInput.to(),
                searchInput.first(),
                searchInput.after(),
                searchInput.sortingOrder(),
                    searchInput.uuid()
            );
        }

        public static TransactionSearch wrapToProto(TransactionSearchInput searchInput, String clientId) {
            TransactionSearch.Builder builder = TransactionSearch.newBuilder()
                .setClientId(clientId);

            if (isNotEmpty(searchInput.symbol())) {
                builder.addAllSymbol(searchInput.symbol());
            }

            if (isNotEmpty(searchInput.currency())) {
                builder.addAllCurrency(searchInput.currency());
            }

            if (isNotEmpty(searchInput.accountId())) {
                builder.addAllAccountId(searchInput.accountId());
            }

            if (isNotEmpty(searchInput.portfolioId())) {
                builder.addAllPortfolioId(searchInput.portfolioId());
            }

            if (isNotEmpty(searchInput.transactionType())) {
                builder.addAllTransactionType(map(searchInput.transactionType()));
            }

            if (isNotBlank(searchInput.orderId())) {
                builder.setOrderId(searchInput.orderId());
            }

            if (isNotBlank(searchInput.parentOrderId())) {
                builder.setParentOrderId(searchInput.parentOrderId());
            }

            if (isNotBlank(searchInput.executionId())) {
                builder.setExecutionId(searchInput.executionId());
            }

            if (isNotBlank(searchInput.venueExecutionId())) {
                builder.setVenueExecutionId(searchInput.venueExecutionId());
            }

            if (isNotBlank(searchInput.rootOrderId())) {
                builder.setRootOrderId(searchInput.rootOrderId());
            }

            if (isNotBlank(searchInput.underlyingExecutionId())) {
                builder.setUnderlyingExecutionId(searchInput.underlyingExecutionId());
            }

            if (isNotBlank(searchInput.rootExecutionId())) {
                builder.setRootExecutionId(searchInput.rootExecutionId());
            }

            if (isNotBlank(searchInput.from())) {
                builder.setFrom(searchInput.from());
            }

            if (isNotBlank(searchInput.to())) {
                builder.setTo(searchInput.to());
            }

            if (searchInput.first() != null) {
                builder.setFirst(searchInput.first());
            }

            if (isNotBlank(searchInput.after())) {
                builder.setAfter(searchInput.after());
            }

            if (searchInput.sortingOrder() != null) {
                builder.setSortingOrder(SharedModel.map(searchInput.sortingOrder()));
            }

            if (isNotBlank(searchInput.uuid())) {
                builder.addUuid(searchInput.uuid());
            }

            return builder.build();
        }

        private static Collection<TransactionType> map(Collection<String> transactionTypes) {
            if (isEmpty(transactionTypes)) {
                return List.of();
            }

            return transactionTypes.stream()
                .map(String::toUpperCase)
                .map(transactionType -> switch (transactionType) {
                    case "CLIENT_CASH_TRADE" -> TransactionType.TRANSACTION_TYPE_CLIENT_CASH_TRADE;
                    case "STREET_CASH_TRADE" -> TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE;
                    case "CLIENT_ASSET_TRADE" -> TransactionType.TRANSACTION_TYPE_CLIENT_ASSET_TRADE;
                    case "STREET_ASSET_TRADE" -> TransactionType.TRANSACTION_TYPE_STREET_ASSET_TRADE;
                    case "DEPOSIT" -> TransactionType.TRANSACTION_TYPE_DEPOSIT;
                    case "WITHDRAWAL" -> TransactionType.TRANSACTION_TYPE_WITHDRAWAL;
                    case "PORTFOLIO_CASH_TRANSFER" -> TransactionType.TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER;
                    case "ACCOUNT_CASH_TRANSFER" -> TransactionType.TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER;
                    case "SETTLEMENT" -> TransactionType.TRANSACTION_TYPE_SETTLEMENT;
                    default -> TransactionType.TRANSACTION_TYPE_UNSPECIFIED;
                })
                .toList();
        }
    }

    public record PositionResponse(
        long updatedAt,
        BigDecimal quantity,
        String bookingCurrency,
        BigDecimal netRealizedPnl,
        BigDecimal grossRealizedPnl,
        BigDecimal netCost,
        BigDecimal grossCost,
        String symbol,
        String currency,
        PortfolioResponseDto portfolio,
        String account,
        String accountName,
        BigDecimal notionalQuantity,
        BigDecimal marketValue,
        BigDecimal marketValuePc,
        BigDecimal marketValueSc,
        BigDecimal netRealizedPnlPc,
        BigDecimal netRealizedPnlSc,
        BigDecimal grossRealizedPnlPc,
        BigDecimal grossRealizedPnlSc,
        BigDecimal netCostPc,
        BigDecimal netCostSc,
        BigDecimal grossCostPc,
        BigDecimal grossCostSc,
        BigDecimal netAveragePrice,
        BigDecimal grossAveragePrice,
        BigDecimal netUnrealizedPnl,
        BigDecimal grossUnrealizedPnl,
        BigDecimal netUnrealizedPnlPc,
        BigDecimal netUnrealizedPnlSc,
        BigDecimal grossUnrealizedPnlPc,
        BigDecimal grossUnrealizedPnlSc,
        BigDecimal pendingQty,
        BigDecimal pendingQuantity,
        BigDecimal availableForTradingQuantity,
        BigDecimal availableForWithdrawalQuantity,
        BigDecimal settledQuantity,
        BigDecimal unsettledQuantity
    ) implements Serializable {
    }

    public record LedgerEntryResponse(
        String id,
        long updatedAt,
        BigDecimal quantity,
        BigDecimal price,
        BigDecimal fee,
        BigDecimal balanceBefore,
        BigDecimal balanceAfter,
        LedgerEntryType type,
        String currency,
        String symbol,
        String portfolioId,
        String portfolioName,
        String accountId,
        String accountName,
        String orderId,
        String transactionId
    ) implements Serializable {
    }

    public record RootExecution(String orderId, String executionId) {
    }

    public record ConversionRate(String baseCurrency, String quoteCurrency, String rate) {
    }

    public enum TransactionInputType {
        TRADE,
        TRANSFER,
        DEPOSIT,
        WITHDRAWAL
    }

    public record TransactionInput(
        TransactionInputType type,
        String dateTime,
        String portfolioId,
        String description,
        String venueAccountId,
        String sourceAccountId,
        String sourcePortfolioId,
        String fee,
        String feePortfolioId,
        String feeAccountId,
        String counterPortfolioId,
        String instrumentId,
        String currency,
        Collection<ConversionRate> conversionRates,
        String quantity,
        String price,
        boolean isSettled,
        String settlementDate,
        String externalOrderId,
        String executionId,
        String venueExecutionId
    ) {
    }

    public interface TransactionResponse extends Serializable {
    }

    public record ClientCashTrade(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        BigDecimal fee,
        String feeCurrency,
        String description,
        BigDecimal quantity,
        BigDecimal price,
        String currency,
        String intOrderId,
        String extOrderId,
        String orderId,
        String baseCurrency,
        String portfolioId,
        String portfolioName,
        String counterPortfolioId,
        String counterPortfolioName,
        RootExecution rootExecution,
        String rootOrderId,
        String underlyingExecutionId,
        String parentOrderId,
        Boolean settled,
        Long settledDateTime
    ) implements TransactionResponse {

    }

    public record StreetCashTrade(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        BigDecimal fee,
        String feeCurrency,
        String description,
        BigDecimal quantity,
        BigDecimal price,
        String currency,
        String intOrderId,
        String extOrderId,
        String orderId,
        String baseCurrency,
        String portfolioId,
        String portfolioName,
        String venueAccount,
        String venueAccountName,
        RootExecution rootExecution,
        String rootOrderId,
        String underlyingExecutionId,
        String parentOrderId,
        Boolean settled,
        Long settledDateTime
    ) implements TransactionResponse {
    }

    public record ClientAssetTrade(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        BigDecimal fee,
        String feeCurrency,
        String description,
        BigDecimal quantity,
        BigDecimal price,
        String currency,
        String intOrderId,
        String extOrderId,
        String orderId,
        InstrumentResponseDto instrument,
        String portfolioId,
        String portfolioName,
        String counterPortfolioId,
        String counterPortfolioName,
        RootExecution rootExecution,
        Boolean settled,
        Long settledDateTime
    ) implements TransactionResponse {
    }

    public record StreetAssetTrade(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        BigDecimal fee,
        String feeCurrency,
        String description,
        BigDecimal quantity,
        BigDecimal price,
        String currency,
        String intOrderId,
        String extOrderId,
        String orderId,
        InstrumentResponseDto instrument,
        String portfolioId,
        String portfolioName,
        String venueAccount,
        String venueAccountName,
        String rootOrderId,
        Boolean settled,
        Long settledDateTime
    ) implements TransactionResponse {
    }

    public record Withdrawal(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        String description,
        BigDecimal quantity,
        String currency,
        String portfolioId,
        String portfolioName,
        String account,
        String accountName,
        Boolean settled,
        Long settledDateTime,
        String feeAccountId,
        String feeAccountName,
        String feePortfolioId,
        String feePortfolioName
    ) implements TransactionResponse {
    }

    public record Deposit(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        String description,
        BigDecimal quantity,
        String currency,
        String portfolioId,
        String portfolioName,
        String account,
        String accountName,
        Boolean settled,
        Long settledDateTime,
        String feeAccountId,
        String feeAccountName,
        String feePortfolioId,
        String feePortfolioName
    ) implements TransactionResponse {
    }

    public record AccountCashTransfer(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        String description,
        BigDecimal quantity,
        String currency,
        String sourceAccountId,
        String sourceAccountName,
        String targetAccountId,
        String targetAccountName,
        Boolean settled,
        Long settledDateTime,
        String feeAccountId,
        String feeAccountName,
        String feePortfolioId,
        String feePortfolioName
    ) implements TransactionResponse {
    }

    public record PortfolioCashTransfer(
        String uuid,
        long updatedAt,
        String dateTime,
        String executionId,
        String venueExecutionId,
        String description,
        BigDecimal quantity,
        String currency,
        String sourcePortfolioId,
        String sourcePortfolioName,
        String targetPortfolioId,
        String targetPortfolioName,
        Boolean settled,
        Long settledDateTime,
        String feePortfolioId,
        String feePortfolioName
    ) implements TransactionResponse {
    }

    public record Settlement(
        String uuid,
        long updatedAt,
        String dateTime,
        String description,
        Collection<String> settledTransactionIds
    ) implements TransactionResponse {
    }
}
