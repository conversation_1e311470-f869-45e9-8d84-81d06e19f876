package io.wyden.clob.gateway.it.utils;

import io.wyden.clob.gateway.service.aeron.inbound.AeronMessageHandler;
import io.wyden.sbe.AllOrdersReportResultDecoder;
import io.wyden.sbe.MessageHeaderDecoder;
import org.agrona.DirectBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
class AllOrdersReportHandler implements AeronMessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AllOrdersReportHandler.class);

    private final AllOrdersReportResultDecoder allOrdersReport = new AllOrdersReportResultDecoder();

    @Autowired
    OrdersHolder ordersHolder;

    @Override
    public int templateId() {
        return allOrdersReport.sbeTemplateId();
    }

    @Override
    public void handle(DirectBuffer buffer, int offset, MessageHeaderDecoder headerDecoder) {
        allOrdersReport.wrapAndApplyHeader(buffer, offset, headerDecoder);
        List<MatchingEngine.Order> orders = new ArrayList<>();
        for (AllOrdersReportResultDecoder.OrdersDecoder o : allOrdersReport.orders()) {
            MatchingEngine.Order order = new MatchingEngine.Order(o.orderId(), o.uid(), o.symbol(), o.price(), o.size(), o.filled(), o.timestamp());
            orders.add(order);
        }
        ordersHolder.orders = orders;
    }
}