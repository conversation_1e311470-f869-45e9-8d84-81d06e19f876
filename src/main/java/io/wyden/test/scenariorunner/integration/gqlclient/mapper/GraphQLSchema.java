package io.wyden.test.scenariorunner.integration.gqlclient.mapper;

import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.test.scenariorunner.model.marketdata.MarketDataType;
import io.wyden.test.scenariorunner.model.marketdata.OrderBookLevelDTO;

import java.util.List;

/**
 * This class reflects published GraphQL schema, to be used for communication between REST API Server and UI.
 * Source: https://gitlab.wyden.io/atcloud/schema-graphql/-/blob/main/schema.graphql
 */
public class GraphQLSchema {

    public record BidResponse(String instrumentId,
                              String venue,
                              String venueAccount,
                              long dateTime,
                              Double price,
                              Double size) {}

    public record AskResponse(String instrumentId,
                              String venue,
                              String venueAccount,
                              long dateTime,
                              Double price,
                              Double size) {}

    public record BidAskQuoteResponse(String instrumentId,
                                      String venue,
                                      String venueAccount,
                                      long dateTime,
                                      Double bidPrice,
                                      Double bidSize,
                                      Double askPrice,
                                      Double askSize) {}

    public record TradeResponse(String instrumentId,
                                String venue,
                                String venueAccount,
                                long dateTime,
                                Double vol,
                                Double lastPrice,
                                Double lastSize,
                                Side side) {}

    public record MarketDataResponse(InstrumentResponseDto instrument,
                                     String venue,
                                     String venueAccount,
                                     long dateTime,
                                     Double bidPrice,
                                     Double bidSize,
                                     Double askPrice,
                                     Double askSize,
                                     Double vol,
                                     Double lastPrice,
                                     Double lastSize,
                                     Side side,
                                     MarketDataType marketDataType) {}

    public record OrderBookResponse(String instrumentId,
                                    String venue,
                                    String venueAccount,
                                    String dateTime,
                                    List<OrderBookLevelDTO> bids,
                                    List<OrderBookLevelDTO> asks) {}

    public enum Side {
        BUY,
        REDUCE_SHORT,
        SELL,
        SELL_SHORT,
        SIDE_UNDETERMINED;

        public io.wyden.published.client.ClientSide toClientSide() {
            return io.wyden.published.client.ClientSide.valueOf(this.name());
        }
    }

}
