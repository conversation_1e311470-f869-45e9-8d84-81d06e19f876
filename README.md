# StartHere

A project describing the current configuration and steps needed to setup local environment for developers.
Before start coding see also [Developer guide](doc/developer_guide.md).

## Access to dependencies
In Algotrader dependencies are hosted on a dedicated Nexus instance (https://repo.wyden.io/nexus/). Verify if you are able to access it by pressing the "Sign In" button and enter credentials you received in introduction email (in case of any issues reach for help). Write down the valid credentials as they will be needed in next steps.

### settings.xml
Copy settings.xml file which can be found in this repository to proper location. Standard location on Windows is **C:\Users\\<USER>\>\\.m2**. Fill in **username** and **password**.

### Environment variables
Before you can build anything, you'll need to set up environment variables to access Nexus repository:
```
NEXUS_DEPLOY_USERNAME=<username from maven settings.xml>
NEXUS_DEPLOY_PASSWORD=<password from maven settings.xml>
```

## Access to Git
### SSH access
Generate SSH key and add it to your GitLab account. Remember to also create a file "~/.ssh/config" and put the following text there:
```
Host gitlab.wyden.io
  Port 2222
```

To verify if everything was configured correctly run the following command:

```
ssh -T *******************
```

### Clone all projects from Git
In your working directory (e.g. `~/IdeaProjects`) create a folder `atcloud`. Inside clone the StartHere project using:

```
<NAME_EMAIL>:atcloud/starthere.git
```

Move into `starthere` and run the `clone_env.sh`. It will create directory structure in the parent directory and fill it with cloned projects.

## Docker compose

### Docker login

Docker needs access to Wyden customized base java image, so you will need to login to docker first. Use the same credentials as in maven settings.xml 
file.
```
docker login docker.wyden.io
```

### Running

First run will be a bit different from the next ones as we do not have docker images built yet. All starting scripts are stored in `starthere` project and should be run from its location.

1. Run script that will spin up all dependencies RabbitMQ, ELK stack (consolidated logging) and other Java services:

```
./rebuild_all_services.sh
```

Verify if all tools are running correctly in docker

To access elasticsearch, go to http://localhost:5601/. During first run, you should create an index pattern (can be as simple as `log*`).


### Cleanup

To put down all services use:

```
./env_down.sh
```

Rebuilding of a single service (both Gradle build and new docker image) can be done via:

```
./rebuild_service.sh {service_name}
```

## Kibana

1. Open:
   * for local development: http://localhost:5601/
   * for dev cloud: https://kibana.wyden.io/ (use Azure AD button to log in)
2. Type into search box: "Kibana / Index Patterns"
3. Define pattern "log*" with time field "@timestamp"
4. Type into search box: "Discover"
5. Select: "level", "app.name", "message"

## Rabbit web console

1. Open
   * for local development: http://localhost:15672/
   * for dev cloud: https://rabbitmq-dev.wyden.io/
2. Use login/password: "whiterabbit" / "follow"

## Hazelcast Management Center

1. Open
   * for local development: http://localhost:8087/
   * for dev cloud: https://hazelcast-dev.wyden.io/

## Keycloak

1. Open
   * for local development: http://localhost:8080/
   * for dev cloud: https://keycloak-dev.wyden.io/
2. Use login password: "admin" / "Z#c9YgatDgr\\,M}t"


### Export Kevcloak realm:
To export keycloak realm config first you need to make some changes in keycloak entry in docker-compose.yaml.

Add:

    volumes:
      - ./keycloak/realms/export:/tmp/export

Change:

    ports:
      - "8080:9999"
    command: start-dev --import-realm --http-port=9999

Rebuild docker image.

Realm config could be exported in two ways:
- Call "docker exec -it keycloak /opt/keycloak/bin/kc.sh export --dir /tmp/export/ --users realm_file --realm MVPRealm"

OR
- In keycloak container terminal go to /opt/keycloak/bin and call "kc.sh export --dir /tmp/export/ --users realm_file --realm MVPRealm"

As a result 'MVPRealm-realm.json' should appear under directory "./keycloak/realms/export" on machine where Keycloak docker image is created (eg. on dev local: "~/IntelliJProjects/atcloud/starthere/keycloak/realms/export/MVPRealm-realm.json"

## UI

1. Open
   * for local development: http://localhost:3000/
   * for dev cloud: https://wyden-dev.wyden.io/
2. Use login password: "admin" / "Z#c9YgatDgr\\,M}t" (config)

## Swagger

1. Open
   * for local development: http://localhost:8095/webjars/swagger-ui/index.html
   * for dev cloud: https://testing-dev.wyden.io/rest-api/webjars/swagger-ui/index.html

## Execution Engine

1. Open
   * for local development: http://localhost:8097/dashboard/
   * for dev cloud: https://testing-dev.wyden.io/engine/dashboard/

## Jaeger

1. Open:
   * for local development: http://localhost:16686/
   * for dev cloud: https://jtracing.wyden.io/search
2. For dev cloud use credentials from Psono "Zipkin dev kubernetes".

## Micrometer

1. Open:
   * for local development: http://localhost:PORT/actuator/prometheus
   * for dev cloud: port forward "rest" port of service and go for /actuator/prometheus

## OpenTelemetry Collector

1. Open:
   * for local development: http://localhost:8889/metrics
   * for dev cloud: port forward "tracing" port of otel-agent (each service has his own)
     and go for /metrics

## Prometheus

1. Open:
   * for local development: http://localhost:7300/
   * for dev cloud: look for pod "prometheus-prometheus-kube-prometheus" in "monitoring" namespace
     and forward port "http-web"
2. Go to "Targets" to check scrapping status
3. Go to "Graphs" to examine metrics

## Grafana

1. Open:
   * for local development: http://localhost:3100/
   * for dev cloud: https://monitoring-cloud.wyden.io/
2. For dev cloud use Azure AD button to log in

See also: [Metrics](doc/metrics.md)

## PgAdmin

1. Open:
   * for local development: http://localhost:5050/
2. <NAME_EMAIL>/admin credentials
3. Login to Wyden Cloud with password 'password'

## Access to cloud PostgreSQL

1. Get azure-dev ssh key from Psono
2. ssh <EMAIL> -i key_path
3. psql -h mvp-dev.postgres.database.azure.com -U wyden postgres

## Troubleshooting

If you encounter problems when running `"gradle build"` or other gradle tasks, check paths to `.m2` and `"gradle home"` directories:
If there are non-ASCII characters on the path to these directories, Gradle may not read classpath properly. In such case move the `.m2` and `"gradle home"` directories to a location that does not contain non-ASCII chars.
On how to move the dirs, see
- https://stackoverflow.com/questions/16649420/how-to-specify-an-alternate-location-for-the-m2-folder-or-settings-xml-permanen
- https://stackoverflow.com/questions/12016681/how-to-change-gradle-download-location

## Postman collection
In order to use Postman collection of endpoints in our application you need to import it to your Postman. To do so, please follow the steps below:
1. Open Postman
2. Click on "Import" button
3. Move the newest version of collection from `atcloud/starthere/postman` to Postman
4. Replace all existing collections with the ones from imported file

## Running a subset of scenario runner tests
There is a possibility to run selected tests from scenario runner. To do so, please follow the steps below:
1. Open scenario-runner repository in browser
2. Navigate to the CI/CD section of the repository
3. Click "Run pipeline"
4. Put "SELECTED_TEST" as a variable key and the name of the test you want to run as a variable value.

Some examples on how to use this feature:

* **Running a single test class:**
  `SELECTED_TEST=io.wyden.test.scenariorunner.scenario.functional.trading.FIXSpec_B_Cancel_Test`


* **Running only one test from test class:**
  `SELECTED_TEST=io.wyden.test.scenariorunner.scenario.functional.trading.FIXSpec_B_Cancel_Test.test_B1a_Cancel_request_issued_for_a_zero_filled_order_cancelled`


* **Running all tests under following directory:**
  `SELECTED_TEST=io.wyden.test.scenariorunner.scenario.functional.trading.*`

## Attach to cloud environment

Execute script `./cloud_attach.sh NAMESPACE` to forward local ports to given
cloud environment. After this tests from IntelliJ can be executed on cloud
environment (also step by step execution works). To detach execute `./cloud_detach.sh`

Example:
* `./cloud_attach.sh algotrader-dev`
* `./cloud_detach.sh`

## Download and view logs from cloud environment

First install tool 'lnav' and install format file `lnav -i kibana_lnav.json`.
Tool can parse our json format, displays correctly multiline logs, can
combine logs from many files (basically it is kibana-lite).

To download logs execute `./cloud_logs.sh NAMESPACE` - it will place logs
in `logs` directory. To view them execute `lnav logs`. Or, for subset
`lnav logs/execution-engine*`.
