import { useEffect, useMemo } from 'react';
import { z } from 'zod';
import { Dialog } from '@ui/Dialog';
import {
  DayOfTheWeek,
  SettlementConfigurationInput,
  VenueType,
} from '@wyden/services/graphql/generated/graphql';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import { useSettlementConfiguration } from '../useSettlementConfiguration';
import { useSettlementConfigurationUpdate } from '../useSettlementConfigurationUpdate';
import { defaultTime, ScheduleForm, ScheduleFormSchema } from './ScheduleForm';
import { useScheduleSettlementStore } from './useScheduleSettlementStore';

export const ScheduleSettlementDialog = () => {
  const { t } = useTranslation();
  const {
    scheduleSettlementDialogOpen,
    toggleScheduleSettlementDialog,
    selectedAccount,
    selectedDay,
    resetSelection,
  } = useScheduleSettlementStore();
  const { updateSettlementConfiguration } = useSettlementConfigurationUpdate();
  const { settlementConfiguration } = useSettlementConfiguration();
  const sortedDays = Object.values(DayOfTheWeek);

  const initialFormValues = useMemo(() => {
    const scheduleValues = sortedDays.reduce(
      (acc, day) => {
        const isSelectedDay = selectedDay === day;
        acc[day] = {
          active: isSelectedDay,
          times: [defaultTime],
        };
        return acc;
      },
      {} as Record<DayOfTheWeek, { active: boolean; times: string[] }>,
    );

    return {
      accounts: selectedAccount
        ? [selectedAccount]
        : ([] as unknown as z.infer<typeof ScheduleFormSchema._def.schema.shape.accounts>),
      schedule: scheduleValues as z.infer<typeof ScheduleFormSchema._def.schema.shape.schedule>,
    };
  }, [selectedAccount, selectedDay, sortedDays]);

  useEffect(() => {
    if (!scheduleSettlementDialogOpen) {
      resetSelection();
    }
  }, [scheduleSettlementDialogOpen, resetSelection]);

  const handleSubmit = (values: z.infer<typeof ScheduleFormSchema>) => {
    const input: SettlementConfigurationInput[] = values.accounts.map((account) => {
      const existing = settlementConfiguration.find(
        (cfg) => cfg.accountId === account.venueAccountId,
      );

      const transformedSchedule = Object.entries(values.schedule)
        .filter(([, val]) => val.active)
        .flatMap(([day, val]) =>
          val.times.map((time) => ({
            day: day as DayOfTheWeek,
            time,
          })),
        );

      return {
        accountId: account.venueAccountId,
        config: {
          ...(existing?.config ?? {}),
          schedule: [...(existing?.config?.schedule ?? []), ...transformedSchedule],
          scheduleTZid: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
      };
    });

    updateSettlementConfiguration(input);
    toggleScheduleSettlementDialog();
  };

  const getScheduleSettlementFormProps = (t: TFunction<'translation', undefined>) => ({
    accounts: {
      label: t('common.accounts'),
      required: true,
      id: 'accounts',
      venueTypes: [VenueType.Street],
      includeDeactivated: true,
      careAboutSettledCount: false,
    },
  });

  return (
    <Dialog
      open={scheduleSettlementDialogOpen}
      onClose={toggleScheduleSettlementDialog}
      maxWidth="sm"
      fullWidth
    >
      <ScheduleForm
        onSubmit={handleSubmit}
        schema={ScheduleFormSchema}
        defaultValues={initialFormValues}
        props={getScheduleSettlementFormProps(t)}
      />
    </Dialog>
  );
};
