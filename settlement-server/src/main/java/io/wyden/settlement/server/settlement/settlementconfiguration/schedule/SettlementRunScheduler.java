package io.wyden.settlement.server.settlement.settlementconfiguration.schedule;

import io.wyden.cloudutils.rabbitmq.ScheduledRabbitExchange;
import io.wyden.published.settlement.SettlementRunScheduled;
import io.wyden.settlement.client.settlementconfiguration.SettlementSchedulePoint;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.RabbitDestinations;
import io.wyden.settlement.server.settlement.settlementconfiguration.SettlementConfigurationRepository;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SettlementRunScheduler {

    private static final Logger LOGGER = LoggerFactory.getLogger(SettlementRunScheduler.class);

    private final ScheduledRabbitExchange scheduledRabbitExchange;
    private final RabbitDestinations rabbitDestinations;
    private final SchedulerMessageCreator messageCreator;
    private final SettlementConfigurationRepository configurationRepository;
    private final ScheduleRepository scheduleRepository;

    public SettlementRunScheduler(ScheduledRabbitExchange scheduledRabbitExchange,
                                  RabbitDestinations rabbitDestinations,
                                  SchedulerMessageCreator messageCreator,
                                  SettlementConfigurationRepository configurationRepository, ScheduleRepository scheduleRepository) {
        this.scheduledRabbitExchange = scheduledRabbitExchange;
        this.rabbitDestinations = rabbitDestinations;
        this.messageCreator = messageCreator;
        this.configurationRepository = configurationRepository;
        this.scheduleRepository = scheduleRepository;
    }

    public void scheduleAll() {
        getSchedulePointsWithAccountsMap().forEach((pointWithZoneId, acounts) -> {
            SchedulerMessage schedulerMessage = messageCreator.create(pointWithZoneId.point(), pointWithZoneId.zoneId(), acounts);
            schedule(schedulerMessage);
        });
    }


    public void cancelAll() {
        scheduleRepository.findAll()
            .forEach(schedule -> cancel(schedule.messageId()));
    }

    private void schedule(SchedulerMessage message) {
        LOGGER.info("Registering scheduled run: {} with cron expression {} and zoneId {} for accounts {}",
            message.messageId(), message.cronExpression(), message.cronZoneId(), message.settlementRunScheduled.getAccountIdsList());

        scheduleRepository.insert(new ScheduledMessageEntity(message.messageId, message.cronExpression.toString(), message.cronZoneId.getId()));
        scheduledRabbitExchange.scheduleRepeatedPublishWithHeadersAndZoneId(
            message.messageId(),
            message.settlementRunScheduled(),
            StringUtils.EMPTY,
            Map.of(),
            message.cronExpression.toString(),
            message.cronZoneId,
            rabbitDestinations.getSettlementRunScheduledExchange());
    }

    public void cancel(String messageId) {
        LOGGER.info("Canceling scheduled run by id: {}", messageId);
        scheduleRepository.deleteById(messageId);
        scheduledRabbitExchange.cancelPublish(messageId);
    }

    private @NotNull Map<SchedulePointWithZoneIdKey, List<String>> getSchedulePointsWithAccountsMap() {

        Map<SchedulePointWithZoneIdKey, List<String>> allAccountsBySchedulePoints = new HashMap<>();

        configurationRepository.findAll().forEach(cfg -> cfg.configuration().schedule().forEach(point ->
            allAccountsBySchedulePoints.computeIfAbsent(new SchedulePointWithZoneIdKey(point, ZoneId.of(cfg.configuration().scheduleTZid())), k -> new ArrayList<>())
                .add(cfg.accountId())));

        return allAccountsBySchedulePoints;
    }

    public record SchedulerMessage(String messageId,
                                   SettlementRunScheduled settlementRunScheduled,
                                   CronExpression cronExpression,
                                   ZoneId cronZoneId) {
    }

    private record SchedulePointWithZoneIdKey(SettlementSchedulePoint point, ZoneId zoneId) {
    }
}
