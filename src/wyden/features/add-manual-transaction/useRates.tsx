import { useTransactionActionsStore } from './useTransactionActionsStore';
import {
  CurrencyPair,
  getConversionPair,
} from '@wyden/features/transactions/ConventionalPairConstructor';
import { useCurrencies } from '../currencies/useCurrencies';

const STABLE_COINS = ['USDT', 'USDC', 'DAI', 'BUSD', 'TUSD', 'PAX', 'GUSD', 'sUSD', 'HUSD', 'LUSD'];

const isStableCoin = (currency: string) => STABLE_COINS.includes(currency);

type ConversionRateCurrencyWithFields = {
  quoteCurrency: string;
  baseCurrency: string;
  fields: string[];
};

export const useRates = () => {
  const { transactionSubject, transactionParticipants } = useTransactionActionsStore();
  const { currencies: allCurrencies } = useCurrencies();

  const isCrypto = (currency: string) =>
    allCurrencies
      .filter((currency) => !STABLE_COINS.includes(currency.symbol))
      .some((c) => c.symbol === currency && c.type === 'CRYPTO') || false;

  const conversionRates: ConversionRateCurrencyWithFields[] = transactionParticipants.reduce(
    (acc: ConversionRateCurrencyWithFields[], participant) => {
      const rate =
        transactionSubject &&
        getConversionPair(
          {
            baseCurrency: transactionSubject.baseCurrency,
            quoteCurrency: transactionSubject.quoteCurrency,
          },
          participant.currency,
          isStableCoin,
          isCrypto,
        );

      const hasRate = rate && !(rate === 'NO_PAIR');
      const rateAlreadyExists =
        hasRate &&
        acc.find(
          (r) => r.quoteCurrency === rate.quoteCurrency && r.baseCurrency === rate.baseCurrency,
        );

      if (hasRate && !rateAlreadyExists) {
        return [
          ...acc,
          {
            quoteCurrency: rate.quoteCurrency,
            baseCurrency: rate.baseCurrency,
            fields: [participant.name],
          },
        ];
      } else if (hasRate && rateAlreadyExists) {
        return acc.map((r) => {
          if (r.quoteCurrency === rate.quoteCurrency && r.baseCurrency === rate.baseCurrency) {
            return {
              ...r,
              fields: [...r.fields, participant.name],
            };
          }
          return r;
        });
      }

      return acc;
    },
    [],
  );

  return {
    conversionRates: conversionRates as CurrencyPair[],
    isStableCoin,
    isCrypto,
  };
};
