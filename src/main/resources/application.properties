spring.application.name=smart-recommendation-engine

server.port=8104

# RabbitMQ destinations - inbound requests:

rabbitmq.best-execution-broadcast-request-queue = sre-queue.smart-routing.BEST-EXECUTION-REQUEST

rabbitmq.best-execution-request-queue = sre-queue.smart-routing.%s.BEST-EXECUTION-REQUEST

rabbitmq.best-execution-request-broadcast-header = BROADCAST

rabbitmq.connector-state-event-queue-template = sre-queue.connector-wrapper.%s.CONNECTOR-STATE-EVENT

# SRE configs
# timeout for market data arrival (in seconds)
market-data.initialGiveUpDelay = 5
# timeout for market data unsubscription after BestRecommendationRequest is processed (in seconds)
market-data.subscription.giveUpDelay = 60
market-data.client.heartbeat-interval=15
market-data.queue-name-format = sre-queue.market-data.%s.EVENT

# sor splitting disabled, configuration for stop / stop limit orders
# percentage of venues that top bid / ask have to trigger stop price in order for whole smart order to be considered as triggered
stopOrderTriggerRequiredVenuesPercentage = 100

# max market data age in milliseconds, candidates with older MD will be ignoredk
maxMarketDataAge = 6000

management.endpoint.health.enabled=true
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=smart-recommendation-engine
management.endpoint.health.group.liveness.include=livenessState,rabbit,diskSpace
management.endpoint.health.group.readiness.include=readinessState

rabbitmq.username = whiterabbit
rabbitmq.password = follow
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =
