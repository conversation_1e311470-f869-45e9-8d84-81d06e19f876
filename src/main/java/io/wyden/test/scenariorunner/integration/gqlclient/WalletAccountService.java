package io.wyden.test.scenariorunner.integration.gqlclient;

import io.qameta.allure.Step;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountCreateRequest;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearchInput;
import io.wyden.apiserver.rest.walletaccount.model.WalletAccountSearchResponse;
import io.wyden.apiserver.rest.walletaccount.model.WalletTypeDto;
import io.wyden.cloud.utils.rest.pagination.PaginationModel.CursorConnection;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.UUID;

public class WalletAccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WalletAccountService.class);

    private final GraphQLClient gqlClient;
    private final String clientId;

    public WalletAccountService(GraphQLClient gqlClient, String clientId) {
        this.gqlClient = gqlClient;
        this.clientId = clientId;
    }

    @Step("Create wallet account {0}")
    public void create(WalletAccountCreateRequest request) {
        LOGGER.info("GraphQL Actor ({}): Requesting new wallet account {}", clientId, request);
        gqlClient.wallet().createWalletAccount(request);
    }

    @Step("Create and wait wallet account {0}")
    public WalletAccountSearchResponse createAndWait(String walletId, WalletTypeDto walletType) {
        WalletAccountCreateRequest request = createWalletRequest(walletId, walletType);
        return createAndWait(request);
    }

    @Step("Create and wait wallet account {0}")
    public WalletAccountSearchResponse createAndWait(WalletAccountCreateRequest request) {
        create(request);
        return WaitUtils.waitUntilIgnoringExceptions("created wallet account with id=[%s] returned by wallet account search".formatted(request.id()),
                () -> search(new WalletAccountSearchInput(null, 1, null, request.id()))
                    .getAllNodes()
                    .stream()
                    .filter(wa -> wa.id().equals(request.id()))
                    .findFirst(),
                Optional::isPresent,
                Timeouts.WAIT_FOR_CONDITION)
            .orElseThrow(() -> new NoSuchElementException("Wallet account with id=[%s] not returned by walletAccountSearch query".formatted(request.id())));
    }

    @Step("Search wallets by {0}")
    public CursorConnection<WalletAccountSearchResponse> search(WalletAccountSearchInput input) {
        LOGGER.info("GraphQL Actor ({}): Search wallets by {}", clientId, input);
        return gqlClient.wallet().getWallets(input);
    }

    public static WalletAccountCreateRequest createWalletRequest(String walletId, WalletTypeDto walletType) {
        String correlationObject = UUID.randomUUID().toString();
        return new WalletAccountCreateRequest(walletId, walletId, walletType, correlationObject);
    }

}
