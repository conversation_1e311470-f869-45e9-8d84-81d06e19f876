package io.wyden.rest.management.validation;

import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.TransactionRequest;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.rest.management.domain.TransactionModel;
import org.junit.jupiter.api.Test;

import static io.wyden.rest.management.common.Identifiers.randomSuffix;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class AccountCashTransferValidationTest extends TransactionValidationTest {

    @Test
    void shouldNotCreateTransferWhenSourceDoesNotExist() throws Exception {

        // source and target do not exist in the system
        TransactionModel.AccountCashTransferRequest request = createAccountCashTransferRequest();

        postTransactionAndExpectValidationError(request, "Account does not exist: " + request.sourceAccountId());
    }

    @Test
    void shouldNotCreateTransferWhenTargetDoesNotExist() throws Exception {

        VenueAccount account = createAccount();

        // with existing source
        TransactionModel.AccountCashTransferRequest request = createAccountCashTransferRequest()
            .withSourceAccountId(account.getId());

        postTransactionAndExpectValidationError(request, "Account does not exist: " + request.targetAccountId());
    }

    @Test
    void shouldNotCreateTransferWhenFeePortfolioDoesNotExist() throws Exception {

        VenueAccount account = createAccount();

        // with existing source and target
        TransactionModel.AccountCashTransferRequest request = createAccountCashTransferRequest()
            .withSourceAccountId(account.getId())
            .withTargetAccountId(account.getId());

        postTransactionAndExpectValidationError(request, "Portfolio does not exist: " + request.feePortfolioId());
    }

    @Test
    void shouldNotCreateTransferWhenFeeAccountDoesNotExist() throws Exception {

        VenueAccount account = createAccount();
        Portfolio portfolio = createPortfolio();

        // with existing source and target and feePortfolioId
        TransactionModel.AccountCashTransferRequest request = createAccountCashTransferRequest()
            .withSourceAccountId(account.getId())
            .withTargetAccountId(account.getId())
            .withFeePortfolioId(portfolio.getId());

        postTransactionAndExpectValidationError(request, "Account does not exist: " + request.feeAccountId());
    }

    @Test
    void shouldCreateTransfer() throws Exception {

        VenueAccount account = createAccount();
        Portfolio portfolio = createPortfolio();

        // with existing source and target and feePortfolioId
        TransactionModel.AccountCashTransferRequest request = createAccountCashTransferRequest()
            .withSourceAccountId(account.getId())
            .withTargetAccountId(account.getId())
            .withFeePortfolioId(portfolio.getId())
            .withFeeAccountId(account.getId());

        when(bookingEngineRabbitClient.requestTransaction(any(TransactionRequest.class)))
            .thenReturn(TransactionSnapshot.newBuilder()
                .setAccountCashTransfer(
                    AccountCashTransferSnapshot.newBuilder()
                        .setUuid(randomSuffix("account-cash-transfer"))
                        .setReservationRef(request.reservationRef())
                        // ... other fields
                        .build())
                .build());

        postTransactionAndExpectSuccess(request);
    }
}
