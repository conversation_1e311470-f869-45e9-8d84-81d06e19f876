dependencies {
    implementation project(":connector-wrapper-base")
    implementation dependencyCatalog.connector.coinapi
    implementation dependencyCatalog.rate.service.client
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9113"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev",
            "SPRING_CLOUD_VAULT_ENABLED": "false",
            "ACCOUNT_NAME": "coinapi"
    ])
}

// workaround for AC-703

bootJar {
    archiveClassifier = "bootjar"
}

task patchBootJar(type: Zip) {
    def genericPath = project.configurations.compileClasspath.find { it.name.startsWith("connector-coinapi-") }

    entryCompression = org.gradle.api.tasks.bundling.ZipEntryCompression.STORED

    archiveName = "${baseName}-${version}.jar"
    destinationDirectory = file("${buildDir}/libs")

    from(zipTree("${buildDir}/libs/${baseName}-${version}-bootjar.jar")) {
        include("**/*")
    }

    duplicatesStrategy = "FAIL"
    dependsOn(bootJar)
}

tasks['bootJar'].finalizedBy 'patchBootJar'

// workaround for AC-703 end
