package io.wyden.oems.ordercollider.collider.service.fsm;

import io.wyden.oems.ordercollider.collider.service.connector.outbound.VenueRequestEmitter;
import io.wyden.oems.ordercollider.collider.service.oems.outbound.OemsResponseEmitter;
import io.wyden.oems.ordercollider.collider.service.referencedata.InstrumentsService;
import io.wyden.oems.ordercollider.collider.service.tracking.OrderCache;
import io.wyden.oems.ordercollider.collider.service.tracking.OrderIdentificationTracker;
import io.wyden.oems.ordercollider.collider.service.tracking.UnknownExecutionReportService;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueResponse;
import org.springframework.stereotype.Component;


@Component
public class OrderService {

    private final OrderCache orderCache;
    private final OrderIdentificationTracker orderIdentificationTracker;
    private final UnknownExecutionReportService unknownExecutionReportService;
    private final InstrumentsService instrumentsService;
    private final OemsResponseEmitter oemsResponseEmitter;
    private final VenueRequestEmitter venueRequestEmitter;

    public OrderService(OrderCache orderCache,
                        OrderIdentificationTracker orderIdentificationTracker,
                        UnknownExecutionReportService unknownExecutionReportService,
                        InstrumentsService instrumentsService,
                        OemsResponseEmitter oemsResponseEmitter,
                        VenueRequestEmitter venueRequestEmitter) {
        this.orderCache = orderCache;
        this.orderIdentificationTracker = orderIdentificationTracker;
        this.unknownExecutionReportService = unknownExecutionReportService;
        this.instrumentsService = instrumentsService;
        this.oemsResponseEmitter = oemsResponseEmitter;
        this.venueRequestEmitter = venueRequestEmitter;
    }

    public void onOemsRequest(OemsRequest request) {
        OrderContext context = new OrderContext(this);
        context.onOemsRequest(request);
    }

    public void onCancelReject(VenueResponse venueCancelReject) {
        OrderContext context = new OrderContext(this);
        context.onCancelReject(venueCancelReject);
    }

    public void onExecutionReport(VenueResponse venueExecutionReport) {
        OrderContext context = new OrderContext(this);
        context.onExecutionReport(venueExecutionReport);
    }

    public void onReconciliationResult(VenueResponse result) {
        OrderContext context = new OrderContext(this);
        context.onReconciliationResult(result);
    }

    public void onOrderNotDelivered(VenueRequest venueOrder) {
        OrderContext context = new OrderContext(this);
        context.onOrderNotDelivered(venueOrder);
    }

    public void onCancelNotDelivered(VenueRequest venueCancel) {
        OrderContext context = new OrderContext(this);
        context.onCancelNotDelivered(venueCancel);
    }

    OrderCache getOrderCache() {
        return orderCache;
    }

    OrderIdentificationTracker getOrderIdentificationTracker() {
        return orderIdentificationTracker;
    }

    UnknownExecutionReportService getUnknownExecutionReportService() {
        return unknownExecutionReportService;
    }

    InstrumentsService getInstrumentsService() {
        return instrumentsService;
    }

    public OemsResponseEmitter getOemsResponseEmitter() {
        return oemsResponseEmitter;
    }

    public VenueRequestEmitter getConnectorCommandEmitter() {
        return venueRequestEmitter;
    }
}
