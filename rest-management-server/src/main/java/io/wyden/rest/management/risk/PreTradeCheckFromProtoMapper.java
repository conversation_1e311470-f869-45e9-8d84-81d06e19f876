package io.wyden.rest.management.risk;

import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeCheckLevel;
import io.wyden.published.risk.PreTradeCheckPropertyValue;
import io.wyden.published.risk.PreTradeChecksList;
import io.wyden.rest.management.domain.PortfolioModel;
import io.wyden.rest.management.domain.RiskModel;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public final class PreTradeCheckFromProtoMapper {

    private PreTradeCheckFromProtoMapper() {}

    public static Collection<RiskModel.PreTradeCheck> map(PreTradeChecksList preTradeChecks) {
        if (preTradeChecks == null) {
            return Collections.emptyList();
        }

        return preTradeChecks.getItemsList().stream()
            .map(PreTradeCheckFromProtoMapper::map)
            .toList();
    }

    public static RiskModel.PreTradeCheck map(PreTradeCheck preTradeCheck) {
        if (preTradeCheck == null) {
            return null;
        }

        return new RiskModel.PreTradeCheck(
            preTradeCheck.getId(),
            preTradeCheck.getType(),
            map(preTradeCheck.getLevel()),
            preTradeCheck.getPortfoliosList().stream().toList(),
            map(preTradeCheck.getPortfolioTagsMap()),
            mapConfiguration(preTradeCheck.getPropertiesMap())
        );
    }

    private static RiskModel.PreTradeCheckLevel map(PreTradeCheckLevel level) {
        if (level == null) {
            return null;
        }

        return switch (level) {
            case WARN -> RiskModel.PreTradeCheckLevel.WARN;
            case BLOCK -> RiskModel.PreTradeCheckLevel.BLOCK;
            default -> null;
        };
    }

    private static Collection<PortfolioModel.Tag> map(Map<String, String> portfolioTags) {
        if (portfolioTags == null) {
            return Collections.emptyList();
        }

        return portfolioTags.entrySet().stream()
            .map(entry -> new PortfolioModel.Tag(entry.getKey(), entry.getValue()))
            .toList();
    }

    private static Collection<RiskModel.PreTradeCheckConfigurationItem> mapConfiguration(Map<String, PreTradeCheckPropertyValue> properties) {
        if (properties == null) {
            return Collections.emptyList();
        }

        return properties.entrySet().stream()
            .map(entry -> {
                PreTradeCheckPropertyValue property = entry.getValue();
                RiskModel.PreTradeCheckConfigurationItemType type = null;
                Collection<String> values = Collections.emptyList();

                if (property.hasStringValue()) {
                    type = RiskModel.PreTradeCheckConfigurationItemType.STRING;
                    values = List.of(property.getStringValue());
                }

                if (property.hasDecimalValue()) {
                    type = RiskModel.PreTradeCheckConfigurationItemType.NUMBER;
                    values = List.of(property.getDecimalValue());
                }

                if (property.hasStringArray()) {
                    type = RiskModel.PreTradeCheckConfigurationItemType.STRING_LIST;
                    values = property.getStringArray().getStringValueList().stream().toList();
                }

                return new RiskModel.PreTradeCheckConfigurationItem(type, entry.getKey(), values);
            })
            .toList();
    }
}
