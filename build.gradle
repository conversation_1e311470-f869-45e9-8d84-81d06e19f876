plugins {
	id 'java'
	id 'idea'
}

subprojects {
	apply plugin: 'idea'
	apply plugin: 'java'
	sourceCompatibility = '17'

	ext {
		repository_username = System.env.NEXUS_DEPLOY_USERNAME
		repository_password = System.env.NEXUS_DEPLOY_PASSWORD
		buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
	}

	repositories {
		mavenLocal()
		maven {
			name 'nexus-snapshots'
			url 'https://repo.wyden.io/nexus/repository/snapshots/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		mavenCentral()
		maven { url 'https://repo.spring.io/milestone' }
	}
}

allprojects {
	ext {
		buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
	}
	group = 'io.wyden'
	version = "0.15.0$buildVersion"
	sourceCompatibility = '17'
}
