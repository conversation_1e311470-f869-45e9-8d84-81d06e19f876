plugins {
    id 'java'
    id "jacoco"
    id 'maven-publish'
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.jacocoToCobertura
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

group = 'exchange.core2'
version = "0.5.2-AERON$buildVersion"
description = 'exchange.core2:collections'

java {
    sourceCompatibility = '1.8'
    targetCompatibility = '1.8'
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-releases'
        url 'https://repo.wyden.io/nexus/repository/releases/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    maven {
        name 'nexus-snapshot'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
}

dependencies {
    implementation dependencyCatalog.agrona
    testImplementation dependencyCatalog.eclipse.collections.api
    testImplementation dependencyCatalog.eclipse.collections
    testImplementation dependencyCatalog.hdrhistogram
    testImplementation dependencyCatalog.logback.classic
    testImplementation dependencyCatalog.hamcrest.library
    testImplementation dependencyCatalog.guava
    testImplementation dependencyCatalog.commons.lang3
    testImplementation dependencyCatalog.commons.math
    testImplementation dependencyCatalog.junit4
    testImplementation dependencyCatalog.slf4j.api
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            repositories {
                maven {
                    name 'nexus-snapshots'
                    url 'https://repo.wyden.io/nexus/repository/snapshots/'
                    credentials {
                        username repository_username
                        password repository_password
                    }
                }
            }
            from components.java
        }
    }
}

test {
    jvmArgs '--enable-preview'
}

testing {
    suites {
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}