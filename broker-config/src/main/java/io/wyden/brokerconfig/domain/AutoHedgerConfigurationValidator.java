package io.wyden.brokerconfig.domain;

import io.wyden.published.brokerdesk.AutoHedgerValidation;
import io.wyden.published.brokerdesk.ConfigCurrencyValidationResult;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.ThresholdConfig;
import io.wyden.published.referencedata.Instrument;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class AutoHedgerConfigurationValidator {
    private final InstrumentsCacheFacade instrumentsCacheFacade;

    public AutoHedgerConfigurationValidator(InstrumentsCacheFacade instrumentsCacheFacade) {
        this.instrumentsCacheFacade = instrumentsCacheFacade;
    }

    public AutoHedgerValidation validate(String portfolioId, HedgingConfig hedgingConfig) {
        GraphCycleDetector validator = new GraphCycleDetector();
        List<ConfigCurrencyValidationResult> validationResults = new ArrayList<>();

        for (ThresholdConfig thresholdConfig : hedgingConfig.getThresholdList()) {
            String hedgeInstrumentId = thresholdConfig.getHedgeInstrument();
            Instrument hedgeInstrument = getInstrumentOrThrow(hedgeInstrumentId);

            String fromCurrency = thresholdConfig.getAsset();
            List<String> toCurrenciesList = extractInstrumentCurrencies(hedgeInstrument);

            Optional<List<String>> cycle = validator.createsCycle(fromCurrency, toCurrenciesList);
            if (cycle.isPresent()) {
                for (String currency : cycle.get()) {
                    validationResults.add(ConfigCurrencyValidationResult.newBuilder()
                        .setCurrency(currency)
                        .setIsValid(false)
                        .setError("Cycle detected involving currency: " + currency)
                        .build());
                }
            } else {
                validator.addToGraph(fromCurrency, toCurrenciesList);
            }
        }

        return AutoHedgerValidation.newBuilder()
            .setPortfolioId(portfolioId)
            .addAllResultsPerCurrency(validationResults)
            .build();
    }

    private Instrument getInstrumentOrThrow(String instrumentId) {
        return instrumentsCacheFacade.find(instrumentId)
            .orElseThrow(() -> new IllegalArgumentException("Cannot find client side instrument: %s".formatted(instrumentId)));
    }

    private List<String> extractInstrumentCurrencies(Instrument instrument) {
        List<String> currencies = new ArrayList<>();
        if (instrument.hasBaseInstrument()) {
            currencies.add(instrument.getBaseInstrument().getQuoteCurrency());
        }

        if (instrument.hasForexSpotProperties()) {
            currencies.add(instrument.getForexSpotProperties().getBaseCurrency());
        }
        return currencies;
    }
}
