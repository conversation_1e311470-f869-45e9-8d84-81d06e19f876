package io.wyden.settlement.server.settlement.run.leg.fsm.transitions;

import io.wyden.published.settlement.SettlementStatus;
import io.wyden.settlement.server.settlement.infrastructure.rabbit.StateChangeRabbitEmitter;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegNotFoundException;
import io.wyden.settlement.server.settlement.run.leg.fsm.state.LegState;
import io.wyden.settlement.server.settlement.run.leg.fsm.state.LegStateCompleted;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InitiateLegService {

    private Logger LOGGER = LoggerFactory.getLogger(InitiateLegService.class);

    private final SettlementRunRepository runRepository;
    private final StateChangeRabbitEmitter stateChangeRabbitEmitter;

    public InitiateLegService(StateChangeRabbitEmitter stateChangeRabbitEmitter, SettlementRunRepository runRepository) {
        this.stateChangeRabbitEmitter = stateChangeRabbitEmitter;
        this.runRepository = runRepository;
    }

    public LegState initiate(SettlementRunEntity run, Long legId) {
        SettlementLegEntity updatedLeg = run.legs().stream()
            .filter(leg -> legId.equals(leg.id()))
            .map(leg -> leg.toBuilder().status(SettlementStatus.IN_PROGRESS).build())
            .findFirst().orElseThrow(() -> new SettlementLegNotFoundException(legId));
        List<SettlementLegEntity> updatedLegs = run.legs().stream()
            .map(leg -> leg.id() != null && leg.id().equals(updatedLeg.id()) ? updatedLeg : leg)
            .toList();

        SettlementRunEntity updatedRun = run.toBuilder()
            .legs(updatedLegs)
            .status(SettlementStatus.IN_PROGRESS)
            .build();
        runRepository.persist(updatedRun);

        if (run.status() != SettlementStatus.IN_PROGRESS) {
            LOGGER.info("Run {}, switched state to in progress", run.id());
            stateChangeRabbitEmitter.emitRunStateChange(run.id(), SettlementStatus.IN_PROGRESS);
        }

        stateChangeRabbitEmitter.emitLegStateChange(updatedLeg.id(), updatedLeg.runId(), updatedLeg.venueAccountId(), updatedLeg.status());
        LOGGER.info("Leg {}, switched state to in progress", legId);
        return new LegStateCompleted(updatedRun, updatedLeg.id());
    }


}
