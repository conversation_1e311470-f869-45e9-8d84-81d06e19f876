package io.wyden.cloudutils.rabbitmq;

import com.google.protobuf.Message;
import com.rabbitmq.client.BuiltinExchangeType;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.rabbitmq.client.BuiltinExchangeType.DIRECT;
import static com.rabbitmq.client.BuiltinExchangeType.HEADERS;

public class RabbitExchange<T extends Message> implements MessageExchange {

    private static final Logger LOGGER = LoggerFactory.getLogger(RabbitExchange.class);
    private final RawRabbitExchange rawRabbitExchange;
    @Nullable
    private final RabbitExchange<T> alternativeExchange;

    public RabbitExchange(RawRabbitExchange rawRabbitExchange, @Nullable RabbitExchange<T> alternativeExchange) {
        this.rawRabbitExchange = rawRabbitExchange;
        this.alternativeExchange = alternativeExchange;
    }

    public static <T extends Message> RabbitExchange<T> simpleDirectExchange(RabbitIntegrator rabbitIntegrator, String exchangeName) {
        return new RabbitExchangeBuilder<T>(rabbitIntegrator)
            .setExchangeName(exchangeName)
            .setExchangeType(DIRECT)
            .setDurable(true)
            .setAutoDelete(false)
            .declare();
    }

    public static <T extends Message> RabbitExchange<T> durableHeadersExchange(RabbitIntegrator rabbitIntegrator, String exchangeName) {
        return new RabbitExchangeBuilder<T>(rabbitIntegrator)
            .setExchangeName(exchangeName)
            .setExchangeType(HEADERS)
            .setDurable(true)
            .setAutoDelete(false)
            .declare();
    }

    public void declare() {
        rawRabbitExchange.declare();
    }

    /**
     * Publish message on Rabbit Exchange.
     * Publishing will be treated as mandatory.
     * If broker detects there is no matching routing for this message, it will be returned back to the producer via the return channel.
     *
     * @param message message body
     * @param routingKey routingKey that wil be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    public CompletableFuture<Void> publish(T message, String routingKey) {
        return publishWithHeaders(message, routingKey, Map.of());
    }

    /**
     * Publish message on Rabbit Exchange with headers. Only supported for HEADERS type Exchange.
     * Publishing will be treated as mandatory.
     * If broker detects there is no matching routing for this message, it will be returned back to the producer via the return channel.
     *
     * @param message message body
     * @param routingKey routingKey that wil be used for routing to bounded queues.
     * @param headers headers that will be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    public CompletableFuture<Void> publishWithHeaders(T message, String routingKey, Map<String, String> headers) {
        String contentType = message.getClass().getName();
        Map<String, String> rabbitHeaders = new HashMap<>(headers);
        rabbitHeaders.put(MetaDataHeader.PROTOBUF_TYPE.getHeaderName(), contentType);
        return rawRabbitExchange.publishWithHeaders(message.toByteArray(), routingKey, rabbitHeaders, contentType);
    }

    /**
     * Publish message on Rabbit Exchange with headers. Only supported for HEADERS type Exchange.
     * Publishing will be treated as mandatory.
     * If broker detects there is no matching routing for this message, it will be returned back to the producer via the return channel.
     *
     * @param message message body
     * @param headers headers that will be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    public CompletableFuture<Void> publishWithHeaders(T message, Map<String, String> headers) {
        return publishWithHeaders(message, StringUtils.EMPTY, headers);
    }

    /**
     * Publish message on Rabbit Exchange with headers. Only supported for HEADERS type Exchange.
     * Publishing will be treated as mandatory.
     * If broker detects there is no matching routing for this message, it will be returned back to the producer via the return channel.
     *
     * @deprecated use {@link RabbitExchange#publishWithHeaders(Message, String, Map)}, content-type will be derived from given message type
     * @param message message body
     * @param routingKey routingKey that wil be used for routing to bounded queues.
     * @param headers headers that will be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    @Deprecated(forRemoval = true)
    public CompletableFuture<Void> publishWithHeaders(T message, String routingKey, Map<String, String> headers, String contentType) {
        return publishWithHeaders(message, routingKey, headers);
    }

    /**
     * Publish message on Rabbit Exchange.
     * Publishing will be NOT be treated as mandatory to deliver.
     *
     * @param message message body
     * @param routingKey routingKey that wil be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    public CompletableFuture<Void> tryPublish(T message, String routingKey) {
        return tryPublishWithHeaders(message, routingKey, Map.of());
    }

    /**
     * Publish message on Rabbit Exchange.
     * Publishing will be NOT be treated as mandatory to deliver.
     *
     * @param message message body
     * @param routingKey routingKey that wil be used for routing to bounded queues.
     * @param headers headers that will be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    public CompletableFuture<Void> tryPublishWithHeaders(T message, String routingKey, Map<String, String> headers) {
        String contentType = message.getClass().getName();
        Map<String, String> rabbitHeaders = new HashMap<>(headers);
        rabbitHeaders.put(MetaDataHeader.PROTOBUF_TYPE.getHeaderName(), contentType);
        return rawRabbitExchange.tryPublishWithHeaders(message.toByteArray(), routingKey, rabbitHeaders, contentType);

    }

    /**
     * Publish message on Rabbit Exchange.
     * Publishing will be NOT be treated as mandatory to deliver.
     *
     * @param message message body
     * @param headers headers that will be used for routing to bounded queues.
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    public CompletableFuture<Void> tryPublishWithHeaders(T message, Map<String, String> headers) {
        return tryPublishWithHeaders(message, StringUtils.EMPTY, headers);
    }

    /**
     * Publish message on Rabbit Exchange.
     * Publishing will be NOT be treated as mandatory to deliver.
     *
     * @deprecated Use {@link RabbitExchange#tryPublishWithHeaders(Message, String, Map)}, content-type will be derived from given message type
     * @param message message body
     * @param routingKey routingKey that wil be used for routing to bounded queues.
     * @param headers headers that will be used for routing to bounded queues.
     * @param contentType class name, used for deserialization
     * @return completable future, execution will be triggered asynchronously from the publisher thread.
     * This makes the publish method safe to execute within Rabbit message consumption code.
     */
    @Deprecated(forRemoval = true)
    public CompletableFuture<Void> tryPublishWithHeaders(T message, String routingKey, Map<String, String> headers, String contentType) {
        return tryPublishWithHeaders(message, routingKey, headers);
    }

    public String getName() {
        return name();
    }

    public boolean isBindableWithRoutingKey() {
        return rawRabbitExchange.isBindableWithRoutingKey();
    }

    public boolean isBindableWithHeaders() {
        return rawRabbitExchange.isBindableWithHeaders();
    }

    public RabbitIntegrator rabbitIntegrator() {
        return rawRabbitExchange.rabbitIntegrator();
    }

    public String name() {
        return rawRabbitExchange.getName();
    }

    public BuiltinExchangeType exchangeType() {
        return rawRabbitExchange.exchangeType();
    }

    public boolean durable() {
        return rawRabbitExchange.durable();
    }

    public boolean autoDelete() {
        return rawRabbitExchange.autoDelete();
    }

    public RabbitExchange<T> alternateExchange() {
        return alternativeExchange;
    }

    @Override
    public boolean equals(Object obj) {
        return rawRabbitExchange.equals(obj);
    }

    @Override
    public int hashCode() {
        return rawRabbitExchange.hashCode();
    }

    @Override
    public String toString() {
        return rawRabbitExchange.toString();
    }

    public RawRabbitExchange asRawExchange() {
        return rawRabbitExchange;
    }
}
