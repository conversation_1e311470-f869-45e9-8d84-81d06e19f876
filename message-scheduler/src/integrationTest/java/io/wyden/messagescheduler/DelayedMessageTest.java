package io.wyden.messagescheduler;

import com.rabbitmq.client.AMQP;
import io.wyden.messagescheduler.domain.map.DelayedMessageMapConfig;
import io.wyden.messagescheduler.util.MessageSchedulerRestClient;
import io.wyden.published.booking.PositionSnapshot;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.locks.LockSupport;

import static org.assertj.core.api.Assertions.assertThat;

public class DelayedMessageTest extends IntegrationTestBase {

    @BeforeEach
    void setUpService() {
        awaitServiceRunning();
    }

    @AfterEach
    void cleanup() {
        DelayedMessageMapConfig.getMap(hazelcastInstance).clear();
    }

    @Test
    void shouldDeliverDelayedMessage() {
        // when
        String messageId = "test-scheduled-message-" + UUID.randomUUID();
        scheduledMessageExchange.schedulePublishWithHeaders(
            messageId,
            testMessage(messageId),
            "",
            testHeaders(),
            ZonedDateTime.now().plusSeconds(1),
            positionSnapshotExchange
        );

        // then
        positionSnapshotObserver.awaitMessage(positionSnapshot -> Objects.equals(positionSnapshot, testMessage(messageId)));
        assertThat(positionSnapshotObserver.getMessagesWithProperties())
            .last()
            .satisfies(m -> {
                PositionSnapshot received = m.message();
                AMQP.BasicProperties properties = m.properties();

                assertThat(received).isEqualTo(testMessage(messageId));
                assertThat(properties.getHeaders()).containsKeys(testHeaders().keySet().toArray(new String[0]));
            });
    }

    @Test
    void shouldBeAbleToTriggerManually() {
        // when
        String messageId = "test-scheduled-message-" + UUID.randomUUID();
        scheduledMessageExchange.schedulePublishWithHeaders(
            messageId,
            testMessage(messageId),
            "",
            testHeaders(),
            ZonedDateTime.now().plusHours(1),
            positionSnapshotExchange
        );

        assertThat(MessageSchedulerRestClient.send(messageId)).isEqualTo(200);

        // then
        positionSnapshotObserver.awaitMessage(positionSnapshot -> Objects.equals(positionSnapshot, testMessage(messageId)));
        assertThat(positionSnapshotObserver.getMessagesWithProperties())
            .last()
            .satisfies(m -> {
                PositionSnapshot received = m.message();
                AMQP.BasicProperties properties = m.properties();

                assertThat(received).isEqualTo(testMessage(messageId));
                assertThat(properties.getHeaders()).containsKeys(testHeaders().keySet().toArray(new String[0]));
            });

        assertThat(MessageSchedulerRestClient.send(messageId)).isEqualTo(400);
    }

    @Test
    void shouldCancelDelayedMessageDelivery() {
        // given
        String messageId = "message-to-be-canceled-" + UUID.randomUUID();

        // when
        scheduledMessageExchange.schedulePublishWithHeaders(
            messageId,
            testMessage(messageId),
            "",
            testHeaders(),
            ZonedDateTime.now().plusSeconds(2),
            positionSnapshotExchange
        );

        LockSupport.parkNanos(Duration.ofSeconds(1).toNanos());

        scheduledMessageExchange.cancelPublish(messageId);

        LockSupport.parkNanos(Duration.ofSeconds(4).toNanos());

        // then
        assertThat(positionSnapshotObserver.getMessages()).hasSize(0);
    }

    @Test
    void shouldDeliverMessagesInScheduledOrder() {
        String idEarly = "test-scheduled-early-" + UUID.randomUUID();
        String idLate = "test-scheduled-late-" + UUID.randomUUID();
        scheduledMessageExchange.schedulePublishWithHeaders(
            idLate,
            testMessage(idLate),
            "",
            testHeaders(),
            ZonedDateTime.now().plusSeconds(2),
            positionSnapshotExchange
        );
        scheduledMessageExchange.schedulePublishWithHeaders(
            idEarly,
            testMessage(idEarly),
            "",
            testHeaders(),
            ZonedDateTime.now().plusSeconds(1),
            positionSnapshotExchange
        );
        positionSnapshotObserver.awaitMessage(positionSnapshot -> Objects.equals(positionSnapshot, testMessage(idLate)));
        var received = positionSnapshotObserver.getMessagesWithProperties();
        int earlyIdx = -1, lateIdx = -1;
        for (int i = 0; i < received.size(); i++) {
            var msg = received.get(i).message();
            if (msg.equals(testMessage(idEarly))) earlyIdx = i;
            if (msg.equals(testMessage(idLate))) lateIdx = i;
        }
        assertThat(earlyIdx).isNotEqualTo(-1);
        assertThat(lateIdx).isNotEqualTo(-1);
        assertThat(earlyIdx).isLessThan(lateIdx);
    }
}
