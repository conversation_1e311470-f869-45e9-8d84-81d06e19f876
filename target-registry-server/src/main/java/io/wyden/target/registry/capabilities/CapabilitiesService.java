package io.wyden.target.registry.capabilities;

import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.DiagnosticEvent;
import io.wyden.published.diagnostic.Health;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.published.targetregistry.ConnectorCapabilities;
import io.wyden.published.targetregistry.ConnectorState;
import io.wyden.published.targetregistry.VenueCapabilitiesSnapshot;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Component
public class CapabilitiesService {

    private final VenueCapabilitiesCache venueCapabilitiesCache;
    private final Set<Capability> supportedCapabilities;

    CapabilitiesService(VenueCapabilitiesCache venueCapabilitiesCache,
                        SupportedCapabilitiesConfiguration supportedCapabilitiesConfiguration) {
        this.venueCapabilitiesCache = venueCapabilitiesCache;
        supportedCapabilities = supportedCapabilitiesConfiguration.getSupportedCapabilities();
    }

    public Set<Capability> getSupportedCapabilities() {
        return supportedCapabilities;
    }

    public List<DiagnosticEvent> filterSupportedCapabilities(List<DiagnosticEvent> diagnosticEvents) {
        return diagnosticEvents.stream()
            .filter(event -> supportedCapabilities.contains(event.getCapability()))
            .toList();
    }

    public ConnectorState filterSupportedCapabilities(ConnectorState connectorState) {
        return connectorState.toBuilder()
            .clearDiagnosticEvents()
            .addAllDiagnosticEvents(filterSupportedCapabilities(connectorState.getDiagnosticEventsList()))
            .build();
    }

    public void registerCapabilities(String venue, ConnectorCapabilities capabilities) {
        venueCapabilitiesCache.registerCapabilities(venue, capabilities);
    }

    public VenueCapabilitiesSnapshot getAllCapabilities() {
        return venueCapabilitiesCache.getAllCapabilities();
    }

    public ConnectorState addMissingCapabilities(ConnectorState connectorState, String venueAccount, String venue) {
        List<DiagnosticEvent> missingEvents = prepareMissingEvents(connectorState, venueAccount, venue);
        return connectorState.toBuilder()
            .addAllDiagnosticEvents(missingEvents)
            .build();
    }

    private List<DiagnosticEvent> prepareMissingEvents(ConnectorState connectorState, String venueAccount, String venue) {
        return connectorState.getCapabilities().getConnectorCapabilitiesList().stream()
            .filter(c -> connectorState.getDiagnosticEventsList().stream().noneMatch(e -> e.getCapability().equals(c)))
            .map(c -> DiagnosticEvent.newBuilder()
                .setCapability(c)
                .setHealth(Health.newBuilder()
                    .setStatus(HealthStatus.HEALTH_STATUS_UNSPECIFIED)
                    .setMsg("Initialized, awaiting for capabilities")
                    .build())
                .setTimestamp(Instant.now().toString())
                .setVenue(venue)
                .setVenueAccount(venueAccount)
                .build())
            .toList();
    }
}
