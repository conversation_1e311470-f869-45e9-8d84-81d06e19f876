# Cloud Utils Telemetry

## Tracing

All started from **Dapper** (Google). First implementation **Zipkin** (Twitter) - 2012r -
currently implements **OpenTracing** (2016r) standard. Second implementation **Jaeger**
(Uber) - 2016r - now it is **CNCF** (Cloud Native Computing Foundation) project.

Client libraries: **OpenCensus** (Google, Microsoft) - 2018r. OpenTracing i OpenCensus join
as **OpenTelemetry** - 2019r. OpenTracing is deprecated (since 2022).

### OpenTelemetry concepts

Trace is directed acyclic graph of spans. Each span is time-period where some computing occured.
Each span contains:
* **traceId** - unique id of trace (common for all spans in trace)
* **spanId** - unique id of span
* **name** - human readable name of span
* **start time**
* **stop time**
* **childOf** - spanId of parent span - should be used for sync calls
* **followsFrom** - spanId of parent span - should be used for async calls (unused in our lib)
* **tags** - key-value pairs used for searching. Tag in one span allows to find whole trace.
* **logs** - events or exceptions attached to span. All have timestamp.
* **baggage** - similar to tags, but propagate between services. Baggage items don't show on UI
and need to be copied to tags to be visible (we do it in our lib).

Tracing is very time-bound - almost everyting is marked with timestamp.

Semantic conventions - common names for different kinds of operations and data. They are
somewhat http-centric and we don't use them - develop our own instead.

### Usage

Library is available after adding dependency:
```gradle
implementation dependencyCatalog.cloud.utils.telemetry
```

To integrate with logging also add env variable to values-dev.yaml:
```
env:
  - name: TRACING_COLLECTOR_ENDPOINT
    value: "http://localhost:4317/"
  - name: LOGGING_CONFIG
    value: "classpath:logback-dev.xml"
```

... and matching entry in docker-compose.yml:
```
environment:
 - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
 - FLUENTD_HOST=fluentd
```

#### Tracing instance / bean

Outside of Spring - wrap application in following code:

```java
String version = TracingUtil.getAppVersion(TracingConfiguration.class);
try (Tracing tracing = TracingUtil.createTracing("http://otel-collector:4317/", "order-collider", "core", version)) {
    ... application code ...
}
```

Inside Spring, wrap Tracing interface in bean, use PostConstruct and PreDestroy for initialization/finalization, and pass it via autowiring.

```java
@Configuration
public class TracingConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(TracingConfiguration.class);

    private static final String SERVICE = "access-gateway";
    private static final String SCOPE = "io.wyden.accessgateway";
    private static final String VERSION = TracingUtil.getAppVersion(TracingConfiguration.class);

    private final String tracingEndpoint;
    private Tracing tracing;

    public TracingConfiguration(@Value("${tracing.collector.endpoint:disabled}") String tracingEndpoint) {
        this.tracingEndpoint = tracingEndpoint;
    }

    @PostConstruct
    void setup() {
        tracing = TracingUtil.createTracing(this.tracingEndpoint, SERVICE, SCOPE, VERSION);
    }

    @PreDestroy
    void destroy() {
        try {
            tracing.close();
        } catch (Exception ex) {
            LOGGER.error("Exception when closing tracing service", ex);
        }
    }

    @Bean
    public Tracing otlTracing() {
        return this.tracing;
    }
}
```

Strings SERVICE and SCOPE configure OpenTelemetry Resource class and should describe microservice. We use human-readable service name as
SERVICE and toplevel service package as SCOPE.

TracingUtil provides function for obtaining service version from Gradle. It is done via ```Implementation-Version```
manifest property, works only in jar (in bootRun shows 'DEVELOPMENT' instead of version), and needs following block in
```build.gradle```:

```gradle
bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}
```

Tracing collector (now Jaeger, later maybe Tempo) is defined in ```application.properties``` because this value is
different in Kubernetes, Docker compose and bootRun modes. By default, it should be http://localhost:4317:
```text
tracing.collector.endpoint=http://localhost:4317
```

Tracing collector can be disabled by setting this value to 'disabled' either by application.properties or env variable.
It should be also disabled if this value is missing in properties file.

Tracing can be noisy in logs, logging level can be adjusted in ```application.properties```:
```text
logging.level.io.wyden.cloudutils.telemetry.tracing = ERROR
```

#### Creating baggage and span

Both Baggage and Span use try-with-resources API. New Baggage and Span can be created as follows. Baggage is optional
and may be omitted if no new properties are defined.

```java
    public void emitExecReport(VenueExecutionReport executionReport) {
        Map<String, String> baggage = Map.of(
            TracingConv.EXEC_TYPE, executionReport.getExecType().toString()
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("venueexecutionreport.emit")) {
                emitExecReportInner(executionReport);
            }
        }
    }
```

For consistency and easy reference, all string constants used in tracing are defined in ```TracingConv``` class.
For readability it is best to rename wrapped class adding suffix ```...Inner``` and leave wrapping code in separate
method. Remember to make Inner metod private.

To start new trace (breaking spans relationship) use ```Context.root()``` as parent when creating new span:

```java
    public void emitOrder(OemsRequest request) {
        Map<String, String> baggage = Map.of(
            TracingConv.API_SERVER, TracingConv.EXECUTION_ENGINE_API_SERVER,
            TracingConv.REQUEST_ID, request.getRequestId(),
            TracingConv.ORDER_ID, request.getOrderId(),
            TracingConv.VENUE_ACCOUNT, request.getVenueAccount(),
            TracingConv.SYMBOL, request.getSymbol(),
            TracingConv.CLIENT_ID, request.getClientId(),
            TracingConv.EVENT_DOMAIN, TracingConv.TRADING_DOMAIN,
            TracingConv.EVENT_NAME, TracingConv.NEW_ORDER_EVENT
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("oemsrequest.emit.street", Context.root())) {
                emitOrderInner(request);
            }
        }
    }
```

Each trace should have ```TracingConv.EVENT_DOMAIN``` and ```TracingConv.EVENT_NAME``` properties set - they help to
identify and filter business process under the trace.

#### Adding data to current span

To add attribute to current span perform:
```java
Span.current().setAttribute(TracingConv.SYMBOL, currencyPair.key());
```
Note: attributes are not propagated to child spans - to propagate use baggage instead.

To add timestamped event to current span peform:
```java
Span.current().addEvent("Order not found - cancel rejected");
```

To add exception to current span perform:
```java
Span.current().recordException(e);
```

To mark current span with error status perform:
```java
Span.current().setStatus(StatusCode.ERROR, "Exception when processing VenueCancelReject - re-queueing");
```
Note: status has three values: UNSET, ERROR and OK and only the last one is recorded. The OK status should be used
only to override previously set ERROR message (for example when we resolve error state). Normal flow should leave
span in UNSET status

#### Propagating span context between threads and services

To propagate context OpenTelemetry needs some container and two functions - getter and setter operating on it.

In RabbitMQ propagation is done via message headers. Following code can be used to save context:

```java
Map<String, Object> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(), RabbitHeadersPropagator.setter())
    .getHeaders();
return exchange.publishWithHeaders(request, routingKey, headers);
```

loading context from headers can be done as follows (note, that context must be passed to both baggage and span):

```java
Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
try (var ignored = otlTracing.createBaggage(parent)) {
    try (var ignored2 = otlTracing.createSpan("venuecancelreject.dlq", parent)) {
        return consumeInner(request);
    }
}
```

REST propagator is not yet done, to implement it, we only need class similar to RabbitHeadersPropagator but using
```HttpHeaders``` or ```HttpURLConnection``` as container. Implementation evxample is included in
[OpenTelemetry manual instrumentation](https://opentelemetry.io/docs/instrumentation/java/manual/).

To propagate context between threads in same microservice just use ```RabbitHeadersPropagator``` and pass container
map to new thread.

```java
Map<String, Object> otlContainer = otlTracing.saveContext(RabbitHeadersPropagator.create(), RabbitHeadersPropagator.setter())
    .getHeaders();
executor.submit(() -> {
    Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(otlContainer), RabbitHeadersPropagator.getter());
    try (var ignored = otlTracing.createBaggage(parent)) {
        try (var ignored2 = otlTracing.createSpan("venuecancelreject.dlq", parent)) {
            consumeInner(request);
        }
    }
});
```

Note: `Baggage.current()` is available, but should be modified only in try-with-resources API - baggage instance is
immutable and `otlTracing.createBaggage()` is performing all neccessary steps to extend existing baggage and activate it.
`Baggage.current()` can be still used as read-only data bag.

Headers example:
```text
'baggage': 'clOrderId=14005d76-2396-4ec2-b854-57cd63202c27,clientId=first_sirian_bank_trader,event.domain=trading,event.name=execution-report,execType=FILL,executionId=execution-2,extId=919eb644-0ff9-4f18-a6a3-14896f3d68cc,orderId=********-432e-461d-8e02-28ceba9a1f68,origClOrderId=,origOrderId=,requestId=,responseId=,instrumentId=DOGEUSD@Spot@BitMEX,venueAccount=BITMEX_Account_FirstSirian'
'traceparent': '00-bb9ab9fc56c1d299c1c857a624591626-33b990fb0d495ad3-01'
```

#### Tests

OpenTelemetry provides testing framework, but it is currently unused (seems to have little value)
in testing. We provide only `TracingMock` class in cloud-utils-test libaray which can be used in unit-tests instead of real
OTL tracing implementation. It can be used as follows:
```java
Tracing otlTracing = TracingMock.createMock();
```

### Classes and files

* Tracing - main interface providing tracing API.
* TracingUtil - Helpers for creating Tracing instance.
* SafeCloseable - java AutoCloseable extension handling all exceptions in ```close()``` method.
All Tracing methods return SafeCloseable to avoid `throws` keyword.
* NoopCloseable - SafeCloseable with empty `close()` - to use in tests, mocking, etc. Currenly
unused because returning null works ok too in try-with-resources.
* RabbitHeadersPropagator - saves and loads Context from RabbitMQ headers map.
* TracingConv - trace semantic conventions. All tracing string constants for consistency and reference.
* FluentdAppender (cloud-utils-spring) - propagates trace_id and span_id to Kibana in local env.
* logback-dev.xml - propagates trace_id and span_id to Kibana in Kubernetes env.
* TracingMock (cloud-utils-test) - mocked Tracing implementation for use in testing.

### References

* [Patoarchitekci: Observability - Distributed tracing](https://www.youtube.com/watch?v=OBylxXQXuY0)
* [Patoarchitekci - transcript](https://patoarchitekci.io/22/)
* [OpenTelemetry manual instrumentation](https://opentelemetry.io/docs/instrumentation/java/manual/)
* [Semantic conventions](https://opentelemetry.io/docs/concepts/semantic-conventions/)
* [Mockito Mock wrapper](https://handstandsam.com/2020/06/08/wrapping-mockito-mocks-for-reusability/)
