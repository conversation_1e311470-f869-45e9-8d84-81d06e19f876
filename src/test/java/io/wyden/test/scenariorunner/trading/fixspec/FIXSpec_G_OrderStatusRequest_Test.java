package io.wyden.test.scenariorunner.trading.fixspec;

import io.qameta.allure.Epic;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientResponse;
import io.wyden.test.scenariorunner.assertion.trading.ClientResponseSoftAssert;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.trading.TradingTestBase;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

import static io.wyden.test.scenariorunner.config.Timeouts.POLL_INTERVAL;
import static io.wyden.test.scenariorunner.config.Timeouts.WAIT_FOR_CONDITION;
import static io.wyden.test.scenariorunner.data.infra.Epics.STREET_ORDER_MANAGEMENT;

/**
 * Tests for part G of FIX Online Specification, Order State Changes:
 * <a href="https://www.fixtrading.org/online-specification/order-state-changes/#g-status-1">Fix Trading Spec</a>
 */
@Epic(STREET_ORDER_MANAGEMENT)
public abstract class FIXSpec_G_OrderStatusRequest_Test extends TradingTestBase {

    private static final String NONE = "NONE";
    private static final Logger LOGGER = LoggerFactory.getLogger(FIXSpec_G_OrderStatusRequest_Test.class);
    private static final double PRICE = 10_000;

    /*
     * G.1.a - Order status request rejected for unknown order
     */
    @Test
    void shouldGetOrderStatusRequestRejectedForUnknownOrder(ClientActor actor, ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        // when (execution trade for 1000)
        conn.fillPart("executionId-1", 1000, PRICE);
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED)
            .quantitiesAre(REQUESTED_QTY, 1000.0, 9000.0, 1000.0)
            .clOrderIdIs(client.getClientOrderId())
            .assertAll();

        // when (send orders status request with wrong order id)
        actor.trading().getOrderStatus(actor.getClientId(), UUID.randomUUID().toString(), "SomeWrongOrderId", client.getOrder().getSide());
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS, ClientOrderStatus.REJECTED)
            .textIs("Provided id is unknown or Order is not active")
            .quantitiesAre(0.0, 0.0, 0.0, 0.0)
            .assertAll();
    }

    /*
     * G.1.b – Transmitting a CMS-style “Nothing Done” in response to a status request
     */
    @Test
    void shouldGetOrderStatusRequestWithNothingDoneText(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        createAcceptedOrderWithGivenOrderLimit(client, conn, REQUESTED_QTY);
        // when
        client.sendOrderStatusRequest();
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS, ClientOrderStatus.NEW)
            .quantitiesAre(10000.0, 0.0, 10000.0, 0.0)
            .assertAll();
    }

    /*
     * G.1.c – Order sent, immediately followed by a status request. Subsequent status requests sent during life of order
     */
    @Test
    void shouldGetOrderStatusDuringLifeOfOrder(ClientSession client, ConnectorMockSession conn) throws Exception {
        // given
        client.sendDefaultStreetLimitOrder(REQUESTED_QTY);

        // then
        ClientOrderStatus expectedInitialOrderStatus = ClientOrderStatus.PENDING_NEW;
        Awaitility.await()
            .pollInterval(POLL_INTERVAL)
            .atMost(WAIT_FOR_CONDITION)
            .until(() -> {
                client.sendOrderStatusRequest();
                ClientResponse clientExecutionReport = client.receiveExecutionReport();

                if (clientExecutionReport.getOrderId().equals(NONE)) {
                    LOGGER.info("Received OrderStatus indicates that Order is not yet fully registered in the system - this is OK for a while, retrying. " +
                        "Received execution report: {}", clientExecutionReport);
                } else if (!clientExecutionReport.getOrderStatus().equals(expectedInitialOrderStatus)) {
                    LOGGER.error("Received unexpected OrderStatus in ExecutionReport: {}", clientExecutionReport);
                }

                return !clientExecutionReport.getOrderId().equals(NONE);
            });
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS, expectedInitialOrderStatus)
            .assertAll();

        // when
        conn.acceptNewOrder();
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW)
            .quantitiesAre(REQUESTED_QTY, 0.0, REQUESTED_QTY, 0.0)
            .assertAll();

        // when
        client.sendOrderStatusRequest();
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS, ClientOrderStatus.NEW)
            .quantitiesAre(REQUESTED_QTY, 0.0, REQUESTED_QTY, 0.0)
            .assertAll();

        // when (execution trade for 2000)
        conn.fillPart("executionId-1", 2000, PRICE);
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED)
            .quantitiesAre(REQUESTED_QTY, 2000.0, 8000.0, 2000.0)
            .clOrderIdIs(client.getClientOrderId())
            .assertAll();

        // when
        client.sendOrderStatusRequest();
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS, ClientOrderStatus.PARTIALLY_FILLED)
            .quantitiesAre(REQUESTED_QTY, 2000.0, 8000.0, 2000.0)
            .assertAll();

        // when (execution trade for 8000)
        conn.fillPart("executionId-2", 8000, PRICE);
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED)
            .quantitiesAre(REQUESTED_QTY, REQUESTED_QTY, 0.0, 8000.0)
            .clOrderIdIs(client.getClientOrderId())
            .assertAll();

        // when
        client.sendOrderStatusRequest();
        // then
        client.receiveExecutionReport();
        // assert
        ClientResponseSoftAssert.assertThat(client.getExecutionReport())
            .execTypeAndOrderStatusAre(ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS, ClientOrderStatus.FILLED)
            .quantitiesAre(REQUESTED_QTY, REQUESTED_QTY, 0.0, 8000.0)
            .assertAll();

        // This is end of the test because our system doesn't support Replace/Cancel request after order became in status 'Filed'
    }

}

@Fix
class Fix_FIXSpec_G_OrderStatusRequest_Test extends FIXSpec_G_OrderStatusRequest_Test {

}

@GraphQL
@Disabled("Disabled because of issue with order status retrieval - https://algotrader.atlassian.net/browse/AC-1085")
class GraphQL_FIXSpec_G_OrderStatusRequest_Test extends FIXSpec_G_OrderStatusRequest_Test {

}

@Rest
@Disabled("Disabled because of issue with order status retrieval - https://algotrader.atlassian.net/browse/AC-1085")
class Rest_FIXSpec_G_OrderStatusRequest_Test extends FIXSpec_G_OrderStatusRequest_Test {

}
