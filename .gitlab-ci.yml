include:
  - project: infrastructure/components
    ref: main
    file: components.yml

stages:
  - version
  - build
  - test
  - publish
  - scan
  - deploy
  - tag
  - qa
  - notification

variables:
  CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress"
  IMAGE_NAME: wyden-ui
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: wyden-ui
  NODE_OPTIONS: "--max-old-space-size=4096"

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - node_modules/

version-verify:
  extends: .pipeline-verification
  stage: version
  variables:
    VERSION_FILE: "package.json"

build:
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
      - cache/Cypress
  image: node:18
  stage: build
  interruptible: true
  script:
    - npm ci
    - npm run build
    - mv dist dist-build
    - ls dist-build
  artifacts:
    paths:
      - dist-build/
    expire_in: 1 day
  except:
    - schedules
    - tags
  tags:
    - image-builder

test:
  image: cypress/browsers:node-18.20.3-chrome-125.0.6422.141-1-ff-126.0.1-edge-125.0.2535.85-1
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
      - cache/Cypress
  stage: test
  interruptible: true
  allow_failure: false
  variables:
    GIT_DEPTH: 0 # Ustawienie pełnego klonowania repozytorium
  before_script:
    - git fetch origin main # Pobierz main
    - git fetch origin ${CI_COMMIT_REF_NAME} --depth=1 # Pobierz bieżącą gałąź, np. my-branch
    - git checkout ${CI_COMMIT_REF_NAME} # Upewnij się, że jesteś na właściwej gałęzi
  script:
    - npm run test:ci:changed
  artifacts:
    when: always
    paths:
      - cypress/videos/**/*.mp4
      - cypress/screenshots/**/*.png
    expire_in: 1 day
  except:
    - schedules
    - tags
  tags:
    - image-builder

publish:
  extends:
    - .publish_image
  variables:
    DOCKERFILE_PATH: ./Dockerfile.ci
  only:
    - main
  except:
    - schedules
  cache: []
  dependencies:
    - build

container-scan:
  extends:
    - .docker_scan
  dependencies: []
  artifacts:
    when: always
    paths:
      - ./results-container.html
    expire_in: 1 days
  allow_failure: true
  only:
    - main

retag:
  extends:
    - .retag_image
  stage: publish
  dependencies: []
  only:
    - tags

deploy-dev:
  extends:
    - .deploy_helm
  only:
    - main
  except:
    - schedules
  cache: []

audit:
  extends:
    - .audit_trails
  stage: publish
  only:
    - tags

tag_rc:
  extends: .tag_repository
  stage: tag
  variables:
    VERSION_FILE: "package.json"
  only:
    - main
  except:
    - schedules

tag_rc_scheduled:
  extends: .tag_repository_scheduled
  stage: tag
  variables:
    VERSION_FILE: "package.json"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $E2E_TESTS == "true"
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule" && $REPORT_PORTAL_TESTS == "true"
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: always

  ##################CRONJOBS######
cypress component tests:
  image: cypress/browsers:node-18.20.3-chrome-125.0.6422.141-1-ff-126.0.1-edge-125.0.2535.85-1
  stage: test
  interruptible: true
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $E2E_TESTS == "true"
      when: always
  script:
    - npm ci
    - npm run build-development
    - npm run preview & npx wait-on http://localhost:3000 &
    - npm run test:ci
  artifacts:
    paths:
      - cypress/videos/**/*.mp4
      - cypress/screenshots/**/*.png
    expire_in: 1 day
  tags:
    - image-builder

report_portal_tests:
  image: cypress/browsers:node-18.20.3-chrome-125.0.6422.141-1-ff-126.0.1-edge-125.0.2535.85-1
  stage: test
  interruptible: true
  allow_failure: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $REPORT_PORTAL_TESTS == "true"
      when: always
  script:
    - npm ci
    - npm run build-development
    - npm run preview & npx wait-on http://localhost:3000 &
    - REPORT_PORTAL_API_KEY=$REPORT_PORTAL_API_KEY npm run test:cypress-component-report-portal
  artifacts:
    paths:
      - cypress/videos/**/*.mp4
      - cypress/screenshots/**/*.png
    expire_in: 1 day
  tags:
    - image-builder

notify_teams:
  image: alpine/curl:8.4.0
  stage: notification
  needs:
    - job: cypress component tests
      artifacts: false
  script:
    - 'curl -H "Content-Type: application/json" -d "{\"text\": \"Cypress tests had failed. More details at $CI_PIPELINE_URL\"}" ${WEBHOOK_URL}'
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $E2E_TESTS == "true"
      when: on_failure

sonarqube-check:
  extends: .sonar-other