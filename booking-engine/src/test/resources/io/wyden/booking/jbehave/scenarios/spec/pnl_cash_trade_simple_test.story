Meta: Implements "Wyden Cloud - Accounting.xlsx", tab "PnL - CashTrade" but with simple scenario

Scenario: buy ETH vs BTC (cross-currency triangulation!)

Given market data prices:
| instrument | price      |
| USD        | 1 USD      |
| BTC        | 10_000 USD |
| ETH        | 100 USD    |
| ETH        | 0.01 BTC   |

When Bank trades 100 ETH/BTC with 10 USD transaction fee on Bitfinex
Then position net state change to:
| portfolioId | instrument | qty | cost    | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Bank        | ETH        | 100 | 10_010  | 10_000      | -           | -10           | 100.1        |
| Bank        | BTC        | -1  | -10_000 | -10_000     | -           | -             | 10_000       |
| Bank        | USD        | -10 | -10     | -10         | -           | -             | 1            |

Then all Bank aggregated positions state changes to:
| cost | marketValue | realizedPnL | unrealizedPnL |
| -    | -10         | -           | -10           |

Scenario: sell ETH vs USD
Given market data prices:
| instrument | price      |
| USD        | 1 USD      |
| BTC        | 10_000 USD |
| ETH        | 200 USD    |
| ETH        | 0.02 BTC   |

When Bank trades -100 ETH/BTC with 10 USD transaction fee on Bitfinex
Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Bank        | ETH        | -   | -      | -           | 9_980       | -             | -            |
| Bank        | BTC        | 1   | 10_000 | 10_000      | -           | -             | 10_000       |
| Bank        | USD        | -20 | -20    | -20         | -           | -             | 1            |

And all Bank aggregated positions state changes to:
| cost  | marketValue | realizedPnL | unrealizedPnL |
| 9_980 | 9_980       | 9_980       | -             |
