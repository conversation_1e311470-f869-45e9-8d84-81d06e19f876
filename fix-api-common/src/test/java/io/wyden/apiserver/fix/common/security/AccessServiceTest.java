package io.wyden.apiserver.fix.common.security;

import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.apiserver.fix.common.dictionary.WydenAccount;
import io.wyden.apiserver.fix.common.dictionary.WydenPortfolio;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import quickfix.SessionID;
import quickfix.fix44.NewOrderSingle;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_TRADE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AccessServiceTest {

    private static final String VENUE_ACCOUNT = "ACC1";
    private static final String PORTFOLIO = "PORT1";
    private static final String SENDER_COMP_ID = "SENDER";
    private static final String TARGET_COMP_ID = "TARGET";
    private AccessService accessService;
    private PermissionService permissionService;
    private LicenseService licenseService;
    private Telemetry telemetry;
    private PortfoliosCacheFacade portfoliosCacheFacade;
    private VenueAccountCacheFacade venueAccountCacheFacade;

    @BeforeEach
    void setup() {
        permissionService = mock(PermissionService.class);
        licenseService = mock(LicenseService.class);
        telemetry = mock(Telemetry.class);
        portfoliosCacheFacade = mock(PortfoliosCacheFacade.class);
        venueAccountCacheFacade = mock(VenueAccountCacheFacade.class);

        accessService = new AccessService(permissionService, licenseService, telemetry, portfoliosCacheFacade, venueAccountCacheFacade);
    }

    // Valid session with proper permissions returns true for trade access
    @Test
    void givenValidSessionAndPermissions_whenCheckingTradeAccess_thenReturnsTrue() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingle();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.of(VenueAccount.newBuilder().build()));
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE)).thenReturn(false);
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE, List.of(PORTFOLIO))).thenReturn(true);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isTrue();
    }

    @Test
    void givenEmptyPortfolioIdAndStaticPortfolioPermission_whenCheckingTradeAccess_thenReturnsFalse() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingleWithoutPortfolio();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.of(VenueAccount.newBuilder().build()));
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE)).thenReturn(true);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isFalse();
    }

    // Returns false when portfolio cannot be found in cache
    @Test
    void givenNoPortfolioInCache_whenCheckingTradeAccess_thenReturnsFalse() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingle();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.empty());
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.of(VenueAccount.newBuilder().build()));
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE, List.of(PORTFOLIO))).thenReturn(true);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isFalse();
    }

    // Returns false when venue account cannot be found in cache
    @Test
    void givenNoVenueAccountInCache_whenCheckingTradeAccess_thenReturnsFalse() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingle();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.empty());
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE, List.of(PORTFOLIO))).thenReturn(true);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isFalse();
    }

    // Returns false when portfolio permission cannot be found
    @Test
    void givenNoPortfolioPermission_whenCheckingTradeAccess_thenReturnsFalse() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingle();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.of(VenueAccount.newBuilder().build()));
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE, List.of(PORTFOLIO))).thenReturn(false);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isFalse();
    }

    // Returns false when venue account permission cannot be found
    @Test
    void givenNoVenueAccountPermission_whenCheckingTradeAccess_thenReturnsFalse() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingle();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.empty());
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE, List.of(PORTFOLIO))).thenReturn(true);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(false);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isFalse();
    }

    @Test
    void givenEmptyPortfolioIdAndNoStaticPortfolioPermission_whenCheckingTradeAccess_thenReturnsFalse() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingleWithoutPortfolio();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.empty());
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE)).thenReturn(false);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, List.of(VENUE_ACCOUNT))).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isFalse();
    }

    @Test
    void givenNoVenueAccount_whenCheckingTradeAccess_thenReturnsTrue() throws Exception {
        //given
        SessionID sessionId = getValidSessionId();
        NewOrderSingle message = getNewOrderSingleWithoutVenueAccount();

        when(portfoliosCacheFacade.find(PORTFOLIO)).thenReturn(Optional.of(Portfolio.newBuilder().build()));
        when(venueAccountCacheFacade.find(VENUE_ACCOUNT)).thenReturn(Optional.empty());
        when(permissionService.hasPermission(TARGET_COMP_ID, PORTFOLIO_TRADE, List.of(PORTFOLIO))).thenReturn(true);
        when(permissionService.hasPermission(TARGET_COMP_ID, VENUE_ACCOUNT_TRADE, Collections.emptyList())).thenReturn(true);

        //when + then
        assertThat(accessService.hasTradeAccess(sessionId, message)).isTrue();
    }

    // Returns false when sender ID contains 'MARKET'
    @Test
    void shouldReturnFalseWhenSenderIdContainsMarket() throws Exception {
        // given
        SessionID sessionId = new SessionID("FIX.4.4", "ATFIXMARKET", TARGET_COMP_ID, "");
        NewOrderSingle message = new NewOrderSingle();

        // when
        boolean result = accessService.hasTradeAccess(sessionId, message);

        // then
        assertThat(result).isFalse();
    }

    private static @NotNull SessionID getValidSessionId() {
        return new SessionID("FIX.4.4", SENDER_COMP_ID, TARGET_COMP_ID, "");
    }

    private static @NotNull NewOrderSingle getNewOrderSingle() {
        NewOrderSingle message = new NewOrderSingle();
        message.setField(new WydenAccount(VENUE_ACCOUNT));
        message.setField(new WydenPortfolio(PORTFOLIO));
        return message;
    }

    private static @NotNull NewOrderSingle getNewOrderSingleWithoutPortfolio() {
        NewOrderSingle message = new NewOrderSingle();
        message.setField(new WydenAccount(VENUE_ACCOUNT));
        return message;
    }

    private static @NotNull NewOrderSingle getNewOrderSingleWithoutVenueAccount() {
        NewOrderSingle message = new NewOrderSingle();
        message.setField(new WydenPortfolio(PORTFOLIO));
        return message;
    }
}
