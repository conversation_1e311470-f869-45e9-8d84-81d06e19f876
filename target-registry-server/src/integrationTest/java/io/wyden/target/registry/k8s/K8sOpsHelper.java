package io.wyden.target.registry.k8s;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1DeleteOptions;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1Pod;
import io.wyden.target.registry.utils.YamlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class K8sOpsHelper {
    private Logger LOGGER = LoggerFactory.getLogger(K8sOpsHelper.class);

    private final ApiClient apiClient;

    public K8sOpsHelper(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public void removePods() {
        try {
            Set<V1Pod> pods = getPods();
            pods.forEach(this::deletePod);
        } catch (ApiException e) {
            throw new RuntimeException("Exception while removing pods", e);
        }
    }

    public Set<V1Pod> getPods() throws ApiException {
        return new CoreV1Api(apiClient)
            .listPodForAllNamespaces(false, null, null, null, null, null, null, null, null, null)
            .getItems()
            .stream()
            .filter(item -> item.getMetadata().getName().contains("connector"))
            .collect(Collectors.toSet());
    }

    public V1ConfigMap applyConfigMap(String filename) {
        try {
            V1ConfigMap v1ConfigMap = YamlUtils.yamlAsClass(filename, V1ConfigMap.class);
            return new CoreV1Api(apiClient).createNamespacedConfigMap("default", v1ConfigMap, null, null, null, null);
        } catch (IOException | ApiException e) {
            throw new RuntimeException("Error while loading config map from test resources");
        }
    }

    private void deletePod(V1Pod pod) {
        try {
            new CoreV1Api(apiClient).deleteNamespacedPod(pod.getMetadata().getName(),
                pod.getMetadata().getNamespace(), null, null, null, null, null, new V1DeleteOptions().gracePeriodSeconds(0l));
        } catch (ApiException e) {
            throw new RuntimeException("Exception while removing pods", e);
        }
    }

    private static void deleteDeployment(AppsV1Api v1Apps, V1Deployment v1Deployment) {
        try {
            v1Apps.deleteNamespacedDeployment(v1Deployment.getMetadata().getName(),
                v1Deployment.getMetadata().getNamespace(), null, null, null, null, null, new V1DeleteOptions().gracePeriodSeconds(0l));
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public void removeDeployments() {
        AppsV1Api v1Apps = new AppsV1Api(apiClient);

        try {
            v1Apps.listDeploymentForAllNamespaces(false, null, null, null, null, null, null, null, null, null)
                .getItems()
                .forEach(v1Deployment -> deleteDeployment(v1Apps, v1Deployment));
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }
}
