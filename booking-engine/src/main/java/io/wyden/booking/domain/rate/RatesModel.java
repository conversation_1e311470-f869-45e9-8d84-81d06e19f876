package io.wyden.booking.domain.rate;

import io.wyden.cloudutils.tools.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;

public final class RatesModel {

    private static final Logger LOGGER = LoggerFactory.getLogger(RatesModel.class);

    private static final int MAX_PRECISION = 30;
    private static final int MAX_SCALE = 18;

    private RatesModel() {}

    public record Rate(String instrument, String quoteCurrency, BigDecimal value, ZonedDateTime timestamp) {

        public Rate {
            if (value != null) {
                value = enforceNumericPrecision(value);
            }
        }

        public static Rate defaultRate(String baseCurrency, String quoteCurrency) {
            return new Rate(baseCurrency, quoteCurrency, BigDecimal.ONE, ZonedDateTime.now());
        }

        public static Rate from(io.wyden.published.rate.Rate rate) {
            BigDecimal rateValue = bd(rate.getValue());
            ZonedDateTime rateTimestamp = DateUtils.isoUtcTimeToZonedDateTime(rate.getTimestamp());

            return new Rate(rate.getBaseCurrency(),
                rate.getQuoteCurrency(),
                rateValue,
                rateTimestamp);
        }

        private static BigDecimal enforceNumericPrecision(BigDecimal value) {
            if (value == null) {
                return null;
            }

            // Get the precision (total number of digits) and scale (digits after decimal)
            int precision = value.precision();
            int scale = value.scale();

            // Check if precision exceeds maximum allowed
            if (precision > MAX_PRECISION) {
                LOGGER.warn("Value precision ({}) exceeds maximum allowed precision ({}), rounding: {}",
                    precision, MAX_PRECISION, value);

                // Calculate how many digits we need to remove from the scale
                int excessDigits = precision - MAX_PRECISION;
                int newScale = Math.max(0, scale - excessDigits);
                value = value.setScale(newScale, RoundingMode.HALF_UP);
            }

            // Check if scale exceeds maximum allowed
            if (scale > MAX_SCALE) {
                LOGGER.warn("Value scale ({}) exceeds maximum allowed scale ({}), rounding: {}",
                    scale, MAX_SCALE, value);
                value = value.setScale(MAX_SCALE, RoundingMode.HALF_UP);
            }

            return value;
        }
    }
}