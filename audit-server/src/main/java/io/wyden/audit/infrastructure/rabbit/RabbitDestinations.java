package io.wyden.audit.infrastructure.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.audit.AuditEvent;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitDestinations {

    private final RabbitExchange<AuditEvent> auditEventsExchange;

    public RabbitDestinations(RabbitIntegrator rabbitIntegrator) {
        this.auditEventsExchange = OemsExchange.AuditEvents.declareAuditEventsExchange(rabbitIntegrator);
    }

    public RabbitExchange<AuditEvent> getAuditEventsExchange() {
        return auditEventsExchange;
    }
}
