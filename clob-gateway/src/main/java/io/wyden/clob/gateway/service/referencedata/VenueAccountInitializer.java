package io.wyden.clob.gateway.service.referencedata;

import com.google.protobuf.TextFormat;
import com.rabbitmq.client.AMQP;
import io.wyden.clob.gateway.infrastructure.rabbit.RabbitDestinations;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Grant;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.Nullable;

import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Component
public class VenueAccountInitializer {
    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountInitializer.class);
    private static final Duration DEFAULT_RETRY_DELAY = Duration.ofSeconds(30);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<VenueAccountCreateRequest> venueAccountCreateRequestExchange;
    private final boolean prefillEnabled;
    private final int retryMaxAttempts;
    private final String venueName;
    private final String venueAccountId;
    private final String ownerUsername;
    private final List<String> readGroups;
    private final List<String> tradeGroups;
    private final List<String> manageGroups;
    private final String applicationName;

    private final List<Duration> retryDelays;
    private final ScheduledExecutorService retryScheduledExecutorService;
    private int retryAttempt = 0;

    public VenueAccountInitializer(RabbitIntegrator rabbitIntegrator,
                                   RabbitExchange<VenueAccountCreateRequest> venueAccountCreateRequestExchange,
                                   @Value("${clob.venue.prefill}") boolean prefillEnabled,
                                   @Value("${clob.venue.prefill.retry.delays}") List<Duration> retryDelays,
                                   @Value("${clob.venue.prefill.retry.max-attempts}") int retryMaxAttempts,
                                   @Value("${clob.venue.name}") String venueName,
                                   @Value("${clob.venue.account.id}") String venueAccountId,
                                   @Value("${clob.venue.account.owner}") String ownerUsername,
                                   @Value("${clob.venue.account.grants.read}") List<String> readGroups,
                                   @Value("${clob.venue.account.grants.trade}") List<String> tradeGroups,
                                   @Value("${clob.venue.account.grants.manage}") List<String> manageGroups,
                                   @Value("${spring.application.name}") String applicationName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.venueAccountCreateRequestExchange = venueAccountCreateRequestExchange;
        this.prefillEnabled = prefillEnabled;
        this.retryMaxAttempts = retryMaxAttempts;
        this.venueName = venueName;
        this.venueAccountId = venueAccountId;
        this.ownerUsername = ownerUsername;
        this.readGroups = readGroups;
        this.tradeGroups = tradeGroups;
        this.manageGroups = manageGroups;
        this.applicationName = applicationName;

        this.retryScheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
        this.retryDelays = retryDelays;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void init() {
        if (!prefillEnabled) {
            LOGGER.info("Prefill disabled");
            return;
        }

        setupRetries();
        createClobVenueAccount();
    }

    private void createClobVenueAccount() {
        LOGGER.info("Creating venueName: {}, venueAccountId: {}, ownerUsername: {}, readGroups: {}, tradeGroups: {}, manageGroups: {}",
            venueName, venueAccountId, ownerUsername, readGroups, tradeGroups, manageGroups);
        VenueAccountCreateRequest.Builder request = VenueAccountCreateRequest.newBuilder()
            .setMessageId(UUID.randomUUID().toString())
            .setAccountType(AccountType.ACCOUNT_TYPE_CLOB)
            .setVenueName(venueName)
            .setVenueAccountId(venueAccountId)
            .setVenueAccountName(venueAccountId)
            .setOwnerUsername(ownerUsername)
            .setMetadata(Metadata.newBuilder()
                .setCreatedAt(DateUtils.toIsoUtcTime(Instant.now()))
                .setRequesterId(applicationName));

        if (isNotEmpty(readGroups)) {
            request.addGrants(Grant.newBuilder()
                .addAllGroups(readGroups)
                .setScope("read")
                .build());
        }

        if (isNotEmpty(tradeGroups)) {
            request.addGrants(Grant.newBuilder()
                .addAllGroups(tradeGroups)
                .setScope("trade")
                .build());
        }

        if (isNotEmpty(manageGroups)) {
            request.addGrants(Grant.newBuilder()
                .addAllGroups(manageGroups)
                .setScope("manage")
                .build());
        }

        LOGGER.info("Emitting venue account creation request: {}", TextFormat.shortDebugString(request));
        venueAccountCreateRequestExchange.publish(request.build(), StringUtils.EMPTY);
    }

    private void setupRetries() {
        rabbitIntegrator.getDeclarationAndPublishChannel().addReturnListener(returned -> {
            if (venueAccountCreateRequestExchange.name().equals(returned.getExchange()) && returned.getReplyCode() == AMQP.NO_ROUTE) {
                Duration retryDelay = getRetryDelay(retryAttempt);
                if (retryDelay != null) {
                    LOGGER.debug("Venue account creation request returned. Will retry in {} ms", retryDelay.toMillis());
                    retryAttempt = retryAttempt + 1;
                    retryScheduledExecutorService.schedule(this::createClobVenueAccount, retryDelay.toMillis(), TimeUnit.MILLISECONDS);
                } else {
                    LOGGER.debug("Venue account creation request returned. Will not retry");
                }
            }
        });
    }

    @Nullable
    private Duration getRetryDelay(int retryAttempt) {
        if (retryMaxAttempts > 0 && retryAttempt >= retryMaxAttempts) {
            return null;
        }

        if (retryDelays.isEmpty()) {
            return DEFAULT_RETRY_DELAY;
        }

        return retryDelays.get(Math.min(retryDelays.size() - 1, retryAttempt));
    }
}
