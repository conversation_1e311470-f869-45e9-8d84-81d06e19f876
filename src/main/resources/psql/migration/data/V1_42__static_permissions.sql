INSERT INTO wyden_permission(permission_resource, permission_scope) VALUES ('venue.account', 'read');
INSERT INTO wyden_permission(permission_resource, permission_scope) VALUES ('venue.account', 'trade');
INSERT INTO wyden_permission(permission_resource, permission_scope) VALUES ('portfolio', 'read');
INSERT INTO wyden_permission(permission_resource, permission_scope) VALUES ('portfolio', 'trade');

INSERT INTO groups_permissions(group_id, permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
                            JOIN wyden_permission wp ON wp.permission_resource =  'venue.account' and wp.permission_scope = 'read'
WHERE wg.group_name = 'administrators';

INSERT INTO groups_permissions(group_id, permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
                            JOIN wyden_permission wp ON wp.permission_resource =  'venue.account' and wp.permission_scope = 'trade'
WHERE wg.group_name = 'administrators';

INSERT INTO groups_permissions(group_id, permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
                            JOIN wyden_permission wp ON wp.permission_resource =  'portfolio' and wp.permission_scope = 'read'
WHERE wg.group_name = 'administrators';

INSERT INTO groups_permissions(group_id, permission_id)
SELECT wg.id,wp.id FROM wyden_group wg
                            JOIN wyden_permission wp ON wp.permission_resource =  'portfolio' and wp.permission_scope = 'trade'
WHERE wg.group_name = 'administrators';
