package io.wyden.test.scenariorunner.assertion.accounting;

import io.qameta.allure.Step;
import io.wyden.apiserver.rest.booking.BookingModel;
import io.wyden.apiserver.rest.booking.BookingModel.LedgerEntryType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.test.scenariorunner.data.trading.ClientResponseFeeExtractor;
import org.assertj.core.api.AbstractCollectionAssert;

import java.math.BigDecimal;
import java.util.Collection;

import static io.wyden.cloud.utils.test.TestUtils.bd;
import static io.wyden.test.scenariorunner.assertion.accounting.LedgerEntrySoftAssert.hasAbsQuantity;
import static io.wyden.test.scenariorunner.assertion.accounting.LedgerEntrySoftAssert.hasCurrency;
import static io.wyden.test.scenariorunner.assertion.accounting.LedgerEntrySoftAssert.isOfType;

public class LedgerEntryCollectionAssert extends AbstractCollectionAssert<LedgerEntryCollectionAssert, Collection<BookingModel.LedgerEntryResponse>, BookingModel.LedgerEntryResponse, LedgerEntrySoftAssert> {

    protected LedgerEntryCollectionAssert(Collection<BookingModel.LedgerEntryResponse> ledgerEntryResponses) {
        super(ledgerEntryResponses, LedgerEntryCollectionAssert.class);
    }

    public static LedgerEntryCollectionAssert assertThat(Collection<BookingModel.LedgerEntryResponse> ledgerEntries) {
        return new LedgerEntryCollectionAssert(ledgerEntries);
    }

    @Override
    protected LedgerEntrySoftAssert toAssert(BookingModel.LedgerEntryResponse value, String description) {
        return new LedgerEntrySoftAssert(value);
    }

    @Override
    @SuppressWarnings("unchecked")
    protected LedgerEntryCollectionAssert newAbstractIterableAssert(Iterable<? extends BookingModel.LedgerEntryResponse> iterable) {
        return new LedgerEntryCollectionAssert((Collection<BookingModel.LedgerEntryResponse>) iterable);
    }

    @Step
    public LedgerEntryCollectionAssert containOnlyReservationLedgerEntries() {
        as("not only RESERVATION ledger entries are present")
            .extracting(BookingModel.LedgerEntryResponse::type)
            .containsOnly(
                LedgerEntryType.RESERVATION
            );
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert containValidTypesAsPartiallyFilledOrder() {
        as("not of ledger entry types: CASH_TRADE_CREDIT, CASH_TRADE_DEBIT, TRADING_FEE, RESERVATION, RESERVATION_RELEASE")
            .extracting(BookingModel.LedgerEntryResponse::type)
            .containsOnly(
                LedgerEntryType.CASH_TRADE_CREDIT,
                LedgerEntryType.CASH_TRADE_DEBIT,
                LedgerEntryType.TRADING_FEE,
                LedgerEntryType.RESERVATION,
                LedgerEntryType.RESERVATION_RELEASE
            );
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert containValidTypesAsFullyFilledOrder() {
        as("not of ledger entry types: CASH_TRADE_CREDIT, CASH_TRADE_DEBIT, TRADING_FEE, RESERVATION, RESERVATION_RELEASE, RESERVATION_RELEASE_REMAINING")
            .extracting(BookingModel.LedgerEntryResponse::type)
            .isSubsetOf(
                LedgerEntryType.CASH_TRADE_CREDIT,
                LedgerEntryType.CASH_TRADE_DEBIT,
                LedgerEntryType.TRADING_FEE,
                LedgerEntryType.RESERVATION,
                LedgerEntryType.RESERVATION_RELEASE,
                LedgerEntryType.RESERVATION_RELEASE_REMAINING
            );
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasCashTradeCreditOriginToExecutionReport(ClientResponse clientResponse) {

        BigDecimal expectedQuantity = ClientSide.BUY.equals(clientResponse.getSide())
            ? bd(clientResponse.getLastQty())
            : bd(clientResponse.getLastQty()).multiply(bd(clientResponse.getLastPrice()));

        as("expected CASH_TRADE_CREDIT with abs quantity=%s not present".formatted(expectedQuantity))
            .filteredOn(isOfType(LedgerEntryType.CASH_TRADE_CREDIT))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isCashTradeCreditOriginToExecutionReport(clientResponse)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasCashTradeDebitOriginToExecutionReport(ClientResponse clientResponse) {

        BigDecimal expectedQuantity = ClientSide.BUY.equals(clientResponse.getSide())
            ? bd(clientResponse.getLastQty()).multiply(bd(clientResponse.getLastPrice()))
            : bd(clientResponse.getLastQty());

        as("expected CASH_TRADE_DEBIT not present")
            .filteredOn(isOfType(LedgerEntryType.CASH_TRADE_DEBIT))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isCashTradeDebitOriginToExecutionReport(clientResponse)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasTradingFeeOriginToExecutionReport(ClientResponse clientResponse) {
        return hasTradingFeeOriginToExecutionReport(clientResponse, true);
    }

    @Step
    public LedgerEntryCollectionAssert hasTradingFeeOriginToExecutionReport(ClientResponse clientResponse, boolean isClient) {

        BigDecimal feeAmountSum = ClientResponseFeeExtractor.getFeeAmountSum(clientResponse);

        as("expected TRADING_FEE with abs quantity=%s not present".formatted(feeAmountSum))
            .filteredOn(isOfType(LedgerEntryType.TRADING_FEE))
            .filteredOn(hasAbsQuantity(feeAmountSum))
            .first()
            .isTradingFeeOriginToExecutionReport(clientResponse, isClient)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasBaseCurrencyReservationOriginToOrder(ClientRequest order, ClientResponse orderER, String baseCurrency, BigDecimal price) {

        as("expected RESERVATION  with currency=%s not present".formatted(baseCurrency))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION))
            .filteredOn(hasCurrency(baseCurrency))
            .first()
            .isBaseCurrencyReservationOriginToOrder(order, orderER, price, true)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert asCounterPortfolioHasQuoteCurrencyReservationOriginToExecutionReport(ClientRequest order, ClientResponse orderER, String quoteCurrency, BigDecimal price, BigDecimal fee) {

        as("expected counter portfolio RESERVATION with currency=%s not present".formatted(quoteCurrency))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION))
            .filteredOn(hasCurrency(quoteCurrency))
            .last()
            .isQuoteCurrencyReservationOriginToOrder(order, orderER, price, fee, false)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert asCounterPortfolioHasBaseCurrencyReservationOriginToOrder(ClientRequest order, ClientResponse orderER, String baseCurrency, BigDecimal price) {

        as("expected counter portfolio RESERVATION  with currency=%s not present".formatted(baseCurrency))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION))
            .filteredOn(hasCurrency(baseCurrency))
            .last()
            .isBaseCurrencyReservationOriginToOrder(order, orderER, price, false)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasQuoteCurrencyReservationOriginToExecutionReport(ClientRequest order, ClientResponse orderER, String quoteCurrency, BigDecimal price, BigDecimal fee) {

        as("expected RESERVATION with currency=%s not present".formatted(quoteCurrency))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION))
            .filteredOn(hasCurrency(quoteCurrency))
            .last()
            .isQuoteCurrencyReservationOriginToOrder(order, orderER, price, fee, true)
            .assertAll();
        return this;
    }


    @Step
    public LedgerEntryCollectionAssert hasTradingFeeOriginToExecutionReport(ClientResponse newOrder, String quoteCurrency, boolean isClient, BigDecimal agencyFixedFee) {

        as("expected RESERVATION with currency=%s and expectedQty=%s not present".formatted(quoteCurrency, agencyFixedFee))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION))
            .filteredOn(hasCurrency(quoteCurrency))
            .filteredOn(hasAbsQuantity(agencyFixedFee))
            .first()
            .isQuoteCurrencyFeeReservationOriginToOrder(newOrder, isClient, agencyFixedFee)
            .assertAll();
        return this;
    }

    /**
     * Be careful with reservation release with several fills as those ledger entries has to be distinguished properly.<br>
     */
    @Step
    public LedgerEntryCollectionAssert hasBaseCurrencyReservationReleaseOriginToExecutionReport(ClientResponse clientResponse, String baseCurrency) {

        BigDecimal expectedQuantity = bd(clientResponse.getLastQty());

        as("expected RESERVATION_RELEASE with base currency=%s and abs quantity=%s not present".formatted(baseCurrency, expectedQuantity))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION_RELEASE))
            .filteredOn(hasCurrency(baseCurrency))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isBaseCurrencyReservationReleaseOriginToExecutionReport(clientResponse, true)
            .assertAll();
        return this;
    }

    /**
     * Be careful with reservation release with several fills as those ledger entries has to be distinguished properly.<br>
     */
    @Step
    public LedgerEntryCollectionAssert hasQuoteCurrencyReservationReleaseOriginToExecutionReport(ClientResponse clientResponse, String quoteCurrency, String price) {

        BigDecimal expectedQuantity = bd(clientResponse.getLastQty()).multiply(bd(price));

        as("expected RESERVATION_RELEASE with quote currency=%s and abs quantity=%s not present".formatted(quoteCurrency, expectedQuantity))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION_RELEASE))
            .filteredOn(hasCurrency(quoteCurrency))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isQuoteCurrencyReservationReleaseOriginToExecutionReport(clientResponse, true, price)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasQuoteCurrencyReservationReleaseOriginToExecutionReport(ClientResponse clientResponse, String quoteCurrency, BigDecimal expectedQuantity) {
        as("expected RESERVATION_RELEASE with quote currency=%s and abs quantity=%s not present".formatted(quoteCurrency, expectedQuantity))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION_RELEASE))
            .filteredOn(hasCurrency(quoteCurrency))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isQuoteCurrencyReservationReleaseOriginToExecutionReport(clientResponse, true, expectedQuantity)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert hasReservationReleaseRemaining(ClientRequest order, BigDecimal remainingQuantity, String quoteCurrency, boolean isClient, BigDecimal fee) {

        BigDecimal quantityToRelease = isClient ? remainingQuantity.add(fee) : remainingQuantity.subtract(fee);
        BigDecimal expectedRemainingQuantity = ClientSide.BUY.equals(order.getSide()) ? quantityToRelease : quantityToRelease.negate();

        as("quote currency RESERVATION_RELEASE_REMAINING not present")
            .filteredOn(isOfType(LedgerEntryType.RESERVATION_RELEASE_REMAINING))
            .first()
            .quantityIs(expectedRemainingQuantity)
            .priceIs(BigDecimal.ONE)
            .feeIs(BigDecimal.ZERO)
            .currencyIs(quoteCurrency)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert asCounterPortfolioHasCashTradeCreditOriginToExecutionReport(ClientResponse clientResponse) {
        as("expected counter portfolio CASH_TRADE_CREDIT not present")
            .filteredOn(isOfType(LedgerEntryType.CASH_TRADE_CREDIT))
            .first()
            .isBankCashTradeCreditOriginToClientExecutionReport(clientResponse)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert asCounterPortfolioHasCashTradeDebitOriginToExecutionReport(ClientResponse clientResponse) {
        as("expected counter portfolio CASH_TRADE_DEBIT not present")
            .filteredOn(isOfType(LedgerEntryType.CASH_TRADE_DEBIT))
            .first()
            .isBankCashTradeDebitOriginToClientExecutionReport(clientResponse)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert portfolioIs(String portfolioId) {
        as("not all ledger entries has portfolio=%s", portfolioId)
            .allMatch(ledgerEntry -> ledgerEntry.portfolioId().equals(portfolioId));
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert venueAccountIs(String venueAccount) {
        as("not all ledger entries has venueAccount=%s", venueAccount)
            .allMatch(ledgerEntry -> ledgerEntry.accountName().equals(venueAccount));
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert asCounterPortfolioHasBaseCurrencyReservationReleaseOriginToExecutionReport(ClientResponse clientResponse, String baseCurrency) {

        BigDecimal expectedQuantity = bd(clientResponse.getLastQty());

        as("expected counter portfolio RESERVATION_RELEASE with base currency=%s and abs quantity=%s not present".formatted(baseCurrency, expectedQuantity))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION_RELEASE))
            .filteredOn(hasCurrency(baseCurrency))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isBaseCurrencyReservationReleaseOriginToExecutionReport(clientResponse, false)
            .assertAll();
        return this;
    }

    @Step
    public LedgerEntryCollectionAssert asCounterPortfolioHasQuoteCurrencyReservationReleaseOriginToExecutionReport(ClientResponse clientResponse, String quoteCurrency, String price) {

        BigDecimal expectedQuantity = bd(clientResponse.getLastQty()).multiply(bd(price));

        as("expected counter portfolio RESERVATION_RELEASE with quote currency=%s and abs quantity=%s not present".formatted(quoteCurrency, expectedQuantity))
            .filteredOn(isOfType(LedgerEntryType.RESERVATION_RELEASE))
            .filteredOn(hasCurrency(quoteCurrency))
            .filteredOn(hasAbsQuantity(expectedQuantity))
            .first()
            .isQuoteCurrencyReservationReleaseOriginToExecutionReport(clientResponse, false, price)
            .assertAll();
        return this;
    }

}
