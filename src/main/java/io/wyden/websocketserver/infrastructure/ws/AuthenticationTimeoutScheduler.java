package io.wyden.websocketserver.infrastructure.ws;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;

import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Component
public class AuthenticationTimeoutScheduler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticationTimeoutScheduler.class);

    private final ScheduledExecutorService authenticationTimeoutScheduler = Executors.newSingleThreadScheduledExecutor(new CustomizableThreadFactory("authentication-timeout-scheduler"));

    private final Duration authenticationTimeout;

    public AuthenticationTimeoutScheduler(@Value("${ws.authentication.timeout}") Duration authenticationTimeout) {
        this.authenticationTimeout = authenticationTimeout;
    }

    public void scheduleAuthenticationTimeout(WebSocketSession session) {
        ScheduledFuture<?> authenticationTimeoutTask = authenticationTimeoutScheduler.schedule(() -> {
            try {
                boolean authenticated = Boolean.TRUE.equals(SessionAttributes.AUTHENTICATED.get(session));
                if (authenticated) {
                    return;
                }

                LOGGER.info("Authentication timeout for session {}, closing", session.getId());

                if (session.isOpen()) {
                    session.close(CloseStatus.NORMAL.withReason("Authentication timeout"));
                }
            } catch (Exception e) {
                LOGGER.warn("Authentication timeout task exception", e);
            }
        }, authenticationTimeout.toMillis(), TimeUnit.MILLISECONDS);
        SessionAttributes.AUTHENTICATION_TIMEOUT_TASK.put(session, authenticationTimeoutTask);
    }

    public void cancelAuthenticationTimeoutTask(WebSocketSession session) {
        ScheduledFuture<?> timeoutTask = SessionAttributes.AUTHENTICATION_TIMEOUT_TASK.get(session);
        timeoutTask.cancel(false);
    }
}
