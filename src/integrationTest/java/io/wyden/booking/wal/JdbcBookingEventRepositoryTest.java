package io.wyden.booking.wal;

import io.wyden.booking.wal.domain.model.BookingEvent;
import io.wyden.booking.wal.infrastructure.persistence.JdbcBookingEventRepository;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WalEvent;
import io.wyden.published.booking.command.Command;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class JdbcBookingEventRepositoryTest extends TestContainersIntegrationBase {

    @Autowired
    private JdbcBookingEventRepository repository;

    @BeforeEach
    void setUp() throws Exception {
        DB.clearTable("booking_wal");
        assertThat(repository.findAllWithSequenceGreaterThan(0)).asList().isEmpty();
    }

    @Test
    void appendAndFetch() {
        BookingEvent bookingEvent = createBookingEvent();
        long seqNum = repository.append(bookingEvent);

        List<WalEvent.Builder> fetched = repository.findAllWithSequenceGreaterThan(seqNum - 1);
        assertThat(fetched).asList().isNotEmpty();
        assertThat(fetched.get(0).getSequenceNumber()).isEqualTo(seqNum);
        assertThat(fetched.get(0).hasTransaction()).isTrue();
        assertThat(fetched.get(0).getTransaction()).isEqualTo(bookingEvent.getBookingDomainEvent().get());
    }

    @Test
    void appendAndFetchNoSubtype() {
        BookingEvent bookingEvent = createBookingEventNoSubtype();
        long seqNum = repository.append(bookingEvent);

        List<WalEvent.Builder> fetched = repository.findAllWithSequenceGreaterThan(seqNum - 1);
        assertThat(fetched).asList().isNotEmpty();
        assertThat(fetched.get(0).getSequenceNumber()).isEqualTo(seqNum);
        assertThat(fetched.get(0).hasTransaction()).isTrue();
        assertThat(fetched.get(0).getTransaction()).isEqualTo(bookingEvent.getBookingDomainEvent().get());
    }

    @Test
    void shouldMonotonicallyIncreaseSeqNums() {
        long offsetEvent = repository.append(createBookingEvent());
        assertThat(repository.append(createBookingEvent())).isEqualTo(offsetEvent + 1);
        assertThat(repository.append(createBookingEvent())).isEqualTo(offsetEvent + 2);
        assertThat(repository.append(createBookingEvent())).isEqualTo(offsetEvent + 3);
    }

    private BookingEvent createBookingEvent() {
        String msgId = UUID.randomUUID().toString();

        OemsResponse oemsResponse = OemsResponse.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(msgId)
                .build())
            .setSymbol("BTCUSD")
            .setSide(OemsSide.BUY)
            .setLastQty("1")
            .setLastPrice("100000")
            .setVenueAccount("Binance")
            .setPortfolioId("George")
            .build();

        Command sourceEvent = Command.newBuilder()
            .setOemsResponse(oemsResponse)
            .build();

        TransactionSnapshot transactionSnapshot = TransactionSnapshot.newBuilder()
            .setStreetCashTrade(StreetCashTradeSnapshot.newBuilder()
                .setBaseCurrency("BTC")
                .setCurrency("USD")
                .setQuantity("1")
                .setPrice("100000")
                .setPortfolio("George")
                .setVenueAccount("Binance")
                .build())
            .build();

        return new BookingEvent(sourceEvent, msgId, transactionSnapshot);
    }

    private BookingEvent createBookingEventNoSubtype() {
        String msgId = UUID.randomUUID().toString();

        OemsResponse oemsResponse = OemsResponse.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(msgId)
                .build())
            .setSymbol("BTCUSD")
            .setSide(OemsSide.BUY)
            .setLastQty("1")
            .setLastPrice("100000")
            .setVenueAccount("Binance")
            .setPortfolioId("George")
            .build();

        TransactionSnapshot transactionSnapshot = TransactionSnapshot.newBuilder()
            .setStreetCashTrade(StreetCashTradeSnapshot.newBuilder()
                .setBaseCurrency("BTC")
                .setCurrency("USD")
                .setQuantity("1")
                .setPrice("100000")
                .setPortfolio("George")
                .setVenueAccount("Binance")
                .build())
            .build();

        return new BookingEvent(oemsResponse, msgId, transactionSnapshot);
    }
}
