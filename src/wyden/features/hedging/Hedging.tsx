import { styled } from '@ui/styled';
import { LoadingDataContainer } from '../LoadingDataContainer';
import { HedgingForm } from './HedgingForm';
import {
  PortfolioResponse,
  usePortfolioConfigurationQuery,
} from '@wyden/services/graphql/generated/graphql';
import { HedgingCurrencies } from './HedgingCurrencies';
import { useParams } from 'react-router-dom';
import { Notification, useNotification } from '../error-indicators/notification/useNotification';
import { useTranslation } from 'react-i18next';
import { useContext } from 'react';
import { ConfigContext } from '../broker-desk-configuration-form/useBrokerDeskConfig';
import { Alert } from '@ui/Alert';

export function Hedging({ portfolio }: { portfolio?: PortfolioResponse | null }) {
  const { portfolioId } = useParams();
  const { addMessage } = useNotification();
  const { t } = useTranslation();
  const { resource } = useContext(ConfigContext);

  const configQuery = usePortfolioConfigurationQuery({
    variables: {
      id: portfolioId || '',
    },
    onError: () => {
      addMessage(Notification.ERROR, t('brokerDesk.configurationFetch'));
    },
    fetchPolicy: 'network-only',
  });

  const previewOnly = !resource.managable;

  return (
    <LoadingDataContainer {...configQuery}>
      {previewOnly && <Alert severity={'info'}>{t('autoHedging.previewOnly')}</Alert>}
      <StyledContainer>
        <HedgingForm
          configuration={configQuery?.data?.portfolioConfiguration}
          configRefetch={configQuery.refetch}
        />
        <HedgingCurrencies
          configuration={configQuery?.data?.portfolioConfiguration}
          configRefetch={configQuery.refetch}
          portfolio={portfolio}
        />
      </StyledContainer>
    </LoadingDataContainer>
  );
}

const StyledContainer = styled('div')`
  display: flex;
  justify-content: space-between;
  min-height: 50vh;
  max-height: 100%;
  gap: 72px;
`;
