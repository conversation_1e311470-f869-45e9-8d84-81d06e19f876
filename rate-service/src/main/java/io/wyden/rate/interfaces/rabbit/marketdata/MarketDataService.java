package io.wyden.rate.interfaces.rabbit.marketdata;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.protobuf.TextFormat;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.rate.domain.map.EventType;
import io.wyden.rate.domain.map.MarketDataKey;
import io.wyden.rate.domain.map.VenueType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Clock;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.concurrent.TimeUnit;

import static org.apache.commons.lang3.StringUtils.isBlank;

public class MarketDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataService.class);
    private final IMap<MarketDataKey, MarketDataEvent> marketDataMap;
    private final Cache<MarketDataKey, Instant> lastConsumedTimestamps = CacheBuilder.newBuilder()
        .expireAfterWrite(1, TimeUnit.MINUTES)
        .build();
    private final long marketDataConsumeIntervalMillis;
    private final Clock clock;

    public MarketDataService(IMap<MarketDataKey, MarketDataEvent> marketDataMap, long marketDataConsumeIntervalMillis) {
        this(marketDataMap, marketDataConsumeIntervalMillis, Clock.systemDefaultZone());
    }

    public MarketDataService(IMap<MarketDataKey, MarketDataEvent> marketDataMap, long marketDataConsumeIntervalMillis, Clock clock) {
        this.marketDataMap = marketDataMap;
        this.marketDataConsumeIntervalMillis = marketDataConsumeIntervalMillis;
        this.clock = clock;
    }

    public void updateMarketData(MarketDataEvent marketDataEvent) {
        MarketDataIdentifier identifier = marketDataEvent.getIdentifier();
        MarketDataEvent.EventTypeCase eventType = marketDataEvent.getEventTypeCase();
        MarketDataKey marketDataKey;
        if (isClientSide(identifier)) {
            marketDataKey = createClientSideKey(identifier, eventType);
        } else if (isStreetSide(identifier)) {
            marketDataKey = createStreetSideKey(identifier, eventType);
        } else {
            LOGGER.warn("Could not update market data using marketDataEvent: %s".formatted(TextFormat.shortDebugString(marketDataEvent)));
            return;
        }

        if (canConsume(marketDataKey)) {
            LOGGER.trace("Market data event received: %s".formatted(marketDataEvent));
            marketDataEvent = ensureTimestamped(marketDataEvent);
            marketDataMap.put(marketDataKey, marketDataEvent);
        } else {
            LOGGER.trace("Skipping consumption of market data event: %s".formatted(marketDataEvent));
        }
    }

    private MarketDataEvent ensureTimestamped(MarketDataEvent marketDataEvent) {
        if (isBlank(marketDataEvent.getIdentifier().getDateTime())) {
            return marketDataEvent.toBuilder()
                .mergeIdentifier(MarketDataIdentifier.newBuilder().setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now())).build())
                .build();
        } else {
            return marketDataEvent;
        }
    }

    private boolean isClientSide(MarketDataIdentifier marketDataIdentifier) {
        return StringUtils.isNotBlank(marketDataIdentifier.getPortfolioId());
    }

    private boolean isStreetSide(MarketDataIdentifier marketDataIdentifier) {
        return StringUtils.isNotBlank(marketDataIdentifier.getVenueAccount());
    }

    private MarketDataKey createClientSideKey(MarketDataIdentifier marketDataIdentifier, MarketDataEvent.EventTypeCase eventTypeCase) {
        return new MarketDataKey(marketDataIdentifier.getInstrumentId(), VenueType.CLIENT, marketDataIdentifier.getStreamId(), null, EventType.fromEventTypeCase(eventTypeCase));
    }

    private MarketDataKey createStreetSideKey(MarketDataIdentifier marketDataIdentifier, MarketDataEvent.EventTypeCase eventTypeCase) {
        return new MarketDataKey(marketDataIdentifier.getInstrumentId(), VenueType.STREET, null, marketDataIdentifier.getVenueAccount(), EventType.fromEventTypeCase(eventTypeCase));
    }

    private boolean canConsume(MarketDataKey key) {
        Instant currentTime = Instant.now(clock);
        Instant lastConsumedTime = lastConsumedTimestamps.getIfPresent(key);

        if (lastConsumedTime == null || lastConsumedTime.plusMillis(marketDataConsumeIntervalMillis).isBefore(currentTime)) {
            lastConsumedTimestamps.put(key, currentTime);
            return true;
        }
        return false;
    }
}
