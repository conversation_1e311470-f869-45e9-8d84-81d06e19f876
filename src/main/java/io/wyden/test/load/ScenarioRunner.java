package io.wyden.test.load;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.util.concurrent.RateLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import io.wyden.test.load.actors.Actor;
import io.wyden.test.load.actors.Message;
import io.wyden.test.load.actors.cloud.NewOrderMessage;
import io.wyden.test.load.scenarios.Scenario;
import io.wyden.test.load.scenarios.ScenarioFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import javax.annotation.Nullable;
import javax.annotation.ParametersAreNonnullByDefault;

@ParametersAreNonnullByDefault
public class ScenarioRunner {

    private static final Logger LOGGER = LoggerFactory.getLogger(ScenarioRunner.class);

    private final String runnerId;
    private final Actor actor;
    private final Scenario scenario;
    private final MeterRegistry meterRegistry;
    private final IdGenerator idGenerator = IdGenerator.getInstance();
    private ScenarioStarter scenarioStarter;
    private MessageHandler messageHandler;

    private Thread scenarioStarterThread;
    private Thread messageHandlerThread;

    ScenarioRunner(String runnerId, Actor actor, Scenario scenario, MeterRegistry meterRegistry) {
        this.runnerId = runnerId;
        this.actor = actor;
        this.scenario = scenario;
        this.meterRegistry = meterRegistry;
    }

    public String getRunnerId() {
        return runnerId;
    }

    public String getActorId() {
        return actor.getActorId();
    }

    public String getScenarioName() {
        return scenario.getScenarioName();
    }

    public void setScenarioStarter(ScenarioStarter scenarioStarter) {
        this.scenarioStarter = scenarioStarter;
    }

    public void setMessageHandler(MessageHandler messageHandler) {
        this.messageHandler = messageHandler;
    }

    synchronized void start() {
        LOGGER.info("Starting ScenarioRunner runnerId={}, scenarioName={}", getRunnerId(), getScenarioName());
        if (messageHandlerThread == null && messageHandler != null) {
            messageHandlerThread = new Thread(messageHandler);
            messageHandlerThread.start();
        }
        if (scenarioStarterThread == null && scenarioStarter != null) {
            scenarioStarterThread = new Thread(scenarioStarter);
            scenarioStarterThread.start();
        }
        LOGGER.info("Started ScenarioRunner runnerId={}, scenarioName={}", getRunnerId(), getScenarioName());
    }

    synchronized void stop() throws InterruptedException {
        LOGGER.info("Stopping ScenarioRunner runnerId={}, scenarioName={}", getRunnerId(), getScenarioName());
        if (scenarioStarterThread != null) {
            scenarioStarterThread.interrupt();
            scenarioStarterThread.join();
            scenarioStarterThread = null;
        }
        if (messageHandlerThread != null) {
            messageHandlerThread.interrupt();
            messageHandlerThread.join();
            messageHandlerThread = null;
        }
        LOGGER.info("Stopped ScenarioRunner runnerId={}, scenarioName={}", getRunnerId(), getScenarioName());
    }

    boolean applyConfig(ScenarioRunnerConfig config) {
        if (!config.actorId().equals(getActorId())) {
            return false;
        }
        if (!config.name().equals(getScenarioName())) {
            return false;
        }
        if (scenarioStarter != null) {
            return scenarioStarter.applyConfig(config);
        }
        return true;
    }

    public void onMessage(ExecutionState state, Message message) {
        messageHandler.handle(state, message);
    }

    public void onTimeout(ExecutionState state, String timerName) {
        synchronized (state) {
            scenario.onTimeout(actor, state, timerName);
        }
    }

    private Iterable<Tag> getTags() {
        return Tags.of("actorId", actor.getActorId(), "actorName", actor.getActorName(),
            "runnerId", runnerId, "scenarioName", scenario.getScenarioName());
    }

    @Nullable
    public static ScenarioRunner createScenarioRunner(ScenarioRunnerConfig config, ScenarioFactory scenarioFactory, Map<String, Actor> actorMap, MeterRegistry meterRegistry) {
        LOGGER.info("Creating scenario runner runnerId={}, scenarioName={}", config.id(), config.name());
        Scenario scenario = scenarioFactory.create(config);
        if (scenario == null) {
            LOGGER.error("Scenario name={} not found for id={}", config.name(), config.id());
            return null;
        }
        Actor actor = actorMap.get(config.actorId());
        if (actor == null) {
            LOGGER.error("Actor actorId={} not found for id={}", config.actorId(), config.id());
            return null;
        }
        ScenarioRunner runner = new ScenarioRunner(config.id(), actor, scenario, meterRegistry);
        ScenarioStarter scenarioStarter = runner.createScenarioStarter(config);
        if (scenarioStarter == null) {
            LOGGER.error("Scenario starter not found for id={}", config.id());
            return null;
        }
        runner.setScenarioStarter(scenarioStarter);
        MessageHandler messageHandler = runner.createMessageHandler(config);
        if (messageHandler == null) {
            LOGGER.error("MessageHandler not found for id={}", config.id());
            return null;
        }
        runner.setMessageHandler(messageHandler);
        return runner;
    }

    @Nullable
    // TODO: Replace nulls with exceptions
    private ScenarioStarter createScenarioStarter(ScenarioRunnerConfig config) {
        Assert.isTrue(config.perSecond() == null || config.count() == null,
            "Count and perSecond cannot both be set id=%s".formatted(config.id()));
        if (config.perSecond() != null) {
            ScenarioStarter scenarioStarter = new RatedScenarioStarter();
            scenarioStarter.applyConfig(config);
            return scenarioStarter;
        } else if (config.count() != null) {
            ScenarioStarter scenarioStarter = new CountScenarioStarter();
            scenarioStarter.applyConfig(config);
            return scenarioStarter;
        } else {
            LOGGER.error("Count and perSecond cannot both be unset id={}", config.id());
            return null;
        }
    }

    @Nullable
    private MessageHandler createMessageHandler(ScenarioRunnerConfig config) {
        MessageHandler messageHandler = new MessageHandler();
        messageHandler.applyConfig(config);
        return messageHandler;
    }

    // TODO: Separate classes - not inner

    private interface ScenarioStarter extends Runnable {
        boolean applyConfig(ScenarioRunnerConfig config);
    }

    @SuppressWarnings("UnstableApiUsage")
    private class RatedScenarioStarter implements ScenarioStarter {

        private final RateLimiter throttle = RateLimiter.create(1);

        @Override
        public boolean applyConfig(ScenarioRunnerConfig config) {
            if (config.perSecond() == null) {
                return false;
            }
            throttle.setRate(config.perSecond());
            return true;
        }

        @Override
        public void run() {
            scenario.init(actor, new ExecutionState(idGenerator.generate(), ScenarioRunner.this));

            while (!Thread.currentThread().isInterrupted()) {
                try {
                    throttle.acquire();
                    String scenarioId = idGenerator.generate();
                    ExecutionState state = new ExecutionState(scenarioId, ScenarioRunner.this);
                    scenario.request(actor, state);
                } catch (Exception ex) {
                    LOGGER.error("Exception when starting scenario {} on actor {}", scenario, actor);
                }
            }
        }
    }

    private class CountScenarioStarter implements ScenarioStarter {

        private int target = 0;
        private final List<ExecutionState> states = new ArrayList<>();

        @Override
        public boolean applyConfig(ScenarioRunnerConfig config) {
            if (config.count() == null) {
                return false;
            }
            Assert.isTrue(config.count() >= 0, "Count must be positive number (%s)".formatted(runnerId));
            this.target = config.count();
            return true;
        }

        @Override
        public void run() {
            scenario.init(actor, new ExecutionState(idGenerator.generate(), ScenarioRunner.this));

            while (!Thread.currentThread().isInterrupted()) {
                try {
                    scaleScenariosUp(target);
                    scaleScenariosDown(target);
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                } catch (Exception ex) {
                    LOGGER.error("Exception in CountScenarioStarter", ex);
                }
            }
            scaleScenariosDown(0);
        }

        private void scaleScenariosUp(int target) {
            while (states.size() < target) {
                String scenarioId = idGenerator.generate();
                ExecutionState state = new ExecutionState(scenarioId, ScenarioRunner.this);
                states.add(state);
                scenario.request(actor, state);
            }
        }

        private void scaleScenariosDown(int target) {
            while (states.size() > target) {
                int last = states.size() - 1;
                ExecutionState state = states.remove(last);
                scenario.stop(actor, state);
            }
        }
    }

    private class MessageHandler implements Runnable {

        private final BlockingQueue<MessageContext> messageQueue = new LinkedBlockingQueue<>();

        MessageHandler() {
            meterRegistry.gaugeCollectionSize("wyden.load.queue.runner.incoming", getTags(), messageQueue);
        }

        void applyConfig(ScenarioRunnerConfig config) {
        }

        void handle(ExecutionState state, Message message) {
            messageQueue.add(new MessageContext(state, message));
        }

        @Override
        public void run() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    MessageContext context = messageQueue.take();
                    scenario.onMessage(actor, context.state, context.message);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record ScenarioRunnerConfig(String id, String name, String actorId, @Nullable Double perSecond, @Nullable Integer count, @Nullable OrderMetadata orderMetadata) {}

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public record OrderMetadata(@Nullable String instrumentId, @Nullable NewOrderMessage.Side side, @Nullable BigDecimal quantity, @Nullable NewOrderMessage.TIF tif, @Nullable BigDecimal limitPrice) {}

    private record MessageContext(ExecutionState state, Message message) {}
}
