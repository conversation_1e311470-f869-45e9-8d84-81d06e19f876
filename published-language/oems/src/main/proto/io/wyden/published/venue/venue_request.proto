syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.venue;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/venue/enums.proto";
import "io/wyden/published/referencedata/instrument.proto";

message VenueRequest {

  io.wyden.published.common.Metadata metadata = 1;

  VenueRequestType request_type = 2;

  // Wyden system Order identifier
  // Equal to ClientRequest.order_id (if Order originated from ClientRequest), OemsRequest.order_id (if Order originated from OEMS)
  // Mandatory for request_type = VENUE_ORDER
  string order_id = 10;

  // Order submission internal id - generated by Connector and sent to external Venue
  // Mandatory for request_type = VENUE_CANCEL, unless ext_id is specified
  string int_id = 11;
  // Order submission external (venue) id - assigned by external Venue
  // Mandatory for request_type = VENUE_CANCEL, unless int_id is specified
  string ext_id = 12;

  // Account name on the external venue
  // Connector instance identifier
  // Unique across Connectors in scope of the Wyden system
  string venue_account = 13;

  // Instrument details:
  VenueInstrumentType instrument_type = 20;
  io.wyden.published.referencedata.InstrumentIdentifiers instrument_identifiers = 21;

  // Order details:

  VenueOrderType order_type = 30;
  VenueSide side = 31;

  // Requested Order quantity
  // String representation of java.math.BigDecimal
  string quantity = 32;

  // Mandatory for Limit and StopLimit Orders
  // String representation of java.math.BigDecimal
  string price = 33;

  // Mandatory for Stop and StopLimit Orders
  // String representation of java.math.BigDecimal
  string stop_price = 34;

  VenueTIF tif = 35;

  // Date format compatible with Fix expireDate: yyyyMMdd
  // Deprecated: cannot be handled by connectors
  string expire_date_deprecated = 36 [deprecated = true];
  // DateTime format compatible with FIX expireTime: yyyyMMdd-HH:mm:ss.SSS
  string expire_time = 37;

  // Indicates if Order should be treated by the execution target as Post-only (should be only executed as Maker order)
  // Only relevant for venues that support it, otherwise ignored
  // Default = false
  bool post_only = 38;
}
