import { AlertProps, CircularProgress, Alert as M<PERSON><PERSON><PERSON>t, useTheme } from '@mui/material';
import { useEffect, useState } from 'react';
import { styled } from './styled';
import { color } from './theme/colors';

export function ProgressAlert(
  props: AlertProps & {
    blink?: boolean;
    progress: number;
    ongoing: boolean;
  },
) {
  const theme = useTheme();
  const { blink, ...alertProps } = props;

  const [progressState, setProgressState] = useState(0);
  const [ongoingState, setOngoingState] = useState(false);

  //Makes the progress spinner animation look smooth, it delays the dissapearance of the spinner so the animation can finish, and also delays the appearence of first progress state > 0 for animation to appear from 0 to progress
  useEffect(() => {
    //That's the start - we delays setting of the progress percent and the "ongoing" boolean - thanks to that the spnner will first render with 0 progress (see "|| props.ongoing === true" in the render) and then will render with higher progress, thanks to that effect here
    if (ongoingState === false && props.ongoing === true) {
      setTimeout(() => {
        setProgressState(props.progress);
        setOngoingState(true);
      }, 4);
    }
    //That's the end - we delay the dissapearance of the spinner by 600ms which is the duration of the spinner animation, so it can finish. We set the progress state almost immediately to 100 as an effect of higher component telling us here that the progress has finished (ongoing === false)
    if (ongoingState === true && props.ongoing === false) {
      setTimeout(() => {
        setProgressState(100);
      }, 4);
      setTimeout(() => {
        setOngoingState(false);
        setProgressState(0);
      }, 600);
    }

    //That's the middle - we update the progress state if the progress is higher than 0 and the ongoing is true, so the spinner can animate from 0 to the progress value
    if (props.ongoing === true && props.progress > 0) {
      setProgressState(props.progress);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.progress, props.ongoing]);

  const alertColor =
    (alertProps.severity &&
      {
        warning: color[theme.palette.mode].fillsSemanticFillWarningBg,
        error: color[theme.palette.mode].fillsSemanticFillErrorBg,
        info: color[theme.palette.mode].fillsSemanticFillInfoBg,
        success: color[theme.palette.mode].fillsSemanticFillSuccessBg,
      }[alertProps.severity]) ||
    color[theme.palette.mode].fillsSemanticFillInfo;

  return ongoingState || props.ongoing ? (
    <StyledAlert
      icon={
        <CircularProgress variant="determinate" size="13px" color="success" value={progressState} />
      }
      {...alertProps}
      $color={alertColor}
      $blink={blink}
    ></StyledAlert>
  ) : (
    <></>
  );
}

const StyledAlert = styled(MUIAlert)<{ $color: string; $blink?: boolean }>`
  background-color: ${({ $color }) => $color};
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  align-items: center;
  padding: 0 12px;

  animation: ${({ $blink }) => ($blink ? 'blink 1s linear' : 'none')};

  .MuiCircularProgress-circle {
    transition: stroke-dashoffset 600ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  }

  @keyframes blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
`;
