plugins {
    id 'java'
    id 'idea'
    id 'jacoco'

//    alias dependencyCatalog.plugins.allure.plugin
    alias dependencyCatalog.plugins.openapi.generator
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

java {
    sourceCompatibility = '17'
}

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
    buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
}

group = 'io.wyden'
version = "0.2.1$buildVersion"

repositories {
    mavenLocal()
    maven {
        name 'nexus-snapshots'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

dependencies {

    implementation project(':rest-management-domain')

    implementation dependencyCatalog.published.language.oems

    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.rest
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.reactor.core.micrometer

    implementation dependencyCatalog.access.gateway.client
    implementation dependencyCatalog.access.gateway.domain
    implementation dependencyCatalog.reference.data.client
    implementation dependencyCatalog.rate.service.client

    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf

    implementation dependencyCatalog.spring.cloud.starter.vault.config
    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.web
    implementation dependencyCatalog.spring.boot.starter.security
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.validation

    implementation dependencyCatalog.jakarta.annotation
    implementation dependencyCatalog.javax.annotation.api
    implementation dependencyCatalog.jackson.databind.nullable

    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.spring.security.test
    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.reactor.test
    testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }

    testImplementation dependencyCatalog.testcontainers
    testImplementation dependencyCatalog.testcontainers.junit.jupiter
    testImplementation dependencyCatalog.testcontainers.rabbitmq
    testImplementation 'software.xdev.mockserver:testcontainers:1.0.9'

    // Allure reporting
//    testImplementation 'io.qameta.allure:allure-junit5:2.22.0'

    runtimeOnly(dependencyCatalog.netty.resolver.dns.native.macos) { artifact { classifier = 'osx-aarch_64' } }

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

test {
    testLogging {
        showStandardStreams = true
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project

                implementation project(':rest-management-domain')

                // add dependency to test to use the same factory methods
                implementation sourceSets.test.output
                implementation dependencyCatalog.reactor.test
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.hazelcast
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.cloud.utils.tools
                implementation dependencyCatalog.cloud.utils.rest
                implementation dependencyCatalog.cloud.utils.spring
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.rate.service.client
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.spring.boot.starter.web
                implementation dependencyCatalog.spring.security.test
                implementation dependencyCatalog.spring.boot.starter.security
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
//                implementation dependencyCatalog.testcontainers.mockserver
                implementation dependencyCatalog.mockserver.client
                implementation dependencyCatalog.testcontainers.postgresql
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.testcontainers.vault
                implementation dependencyCatalog.reference.data.client
                implementation dependencyCatalog.spring.cloud.starter.vault.config
                implementation dependencyCatalog.access.gateway.client
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests' } }

                implementation 'software.xdev.mockserver:testcontainers:1.0.9'
                implementation 'com.github.dasniko:testcontainers-keycloak:3.6.0'
                implementation "org.testcontainers:testcontainers:1.20.6"
                implementation "org.testcontainers:junit-jupiter:1.20.6"
//                implementation dependencyCatalog.testcontainers.mockserver
                implementation dependencyCatalog.mockserver.client
                implementation dependencyCatalog.testcontainers.postgresql

            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}"
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9106"]
    environment([
            "FLUENTD_HOST"          : "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "rest-management"
        property "sonar.projectName", "REST Management API"
    }
}

test {
    finalizedBy jacocoTestReport
    useJUnitPlatform()
    systemProperty 'junit.jupiter.extensions.autodetection.enabled', 'true'
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

// OpenAPI Validator configuration
openApiValidate {
    inputSpec = "$projectDir/../openapi.json"
}

// OpenAPI Generator configuration
openApiGenerate {
    generatorName = "java"
    library = "resttemplate"
    inputSpec = "$projectDir/../openapi.json"
    outputDir = "$buildDir/generated"
    apiPackage = "io.wyden.rest.management.contract.client"
    modelPackage = "io.wyden.rest.management.contract.model"
    configOptions = [
            dateLibrary: "java8",
            sourceFolder: "src/gen/java/main",
//            hideGenerationTimestamp: "true",
            supportsInheritance: "true",
            legacyDiscriminatorBehavior: "false"
    ]
    generateApiTests = true
}

// Add openapi generated sources to the project
sourceSets {
    main {
        java {
            srcDir "$buildDir/generated/src/gen/java/main"
        }
    }
}

tasks.compileJava.dependsOn tasks.openApiGenerate

tasks.register('contractTests', Test) {
    description = 'Runs contract tests'
    group = 'verification'

    useJUnitPlatform {
        includeTags 'contract-tests'
    }
}

// Make contract tests depend on OpenAPI validation
contractTests.dependsOn tasks.openApiValidate

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
