package io.wyden.booking.snapshotter.infrastructure.web;

import org.slf4j.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import static org.slf4j.LoggerFactory.getLogger;

@Configuration
@EnableRetry
public class RetryConfig {

    @Bean
    public RetryTemplate retryTemplate(RetryListener defaultRetryListener) {
        RetryTemplate retryTemplate = new RetryTemplate();

        FixedBackOffPolicy fixedBackOffPolicy = new FixedBackOffPolicy();
        fixedBackOffPolicy.setBackOffPeriod(1_000L);
        retryTemplate.setBackOffPolicy(fixedBackOffPolicy);

        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(3);
        retryTemplate.setRetryPolicy(retryPolicy);

        retryTemplate.registerListener(defaultRetryListener);

        return retryTemplate;
    }

    @Bean
    public RetryListener defaultRetryListener() {
        return new DefaultRetryListener();
    }

    public static class DefaultRetryListener implements RetryListener {

        private static final Logger LOGGER = getLogger(DefaultRetryListener.class);

        @Override
        public <T, E extends Throwable> void onError(RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
            // This method is called on every retryable exception.
            int retryCount = context.getRetryCount();
            LOGGER.info("Retry attempt #{} due to exception: {}", retryCount, throwable.toString());
        }
    }
}
