Meta: Verify pending quantity correctness for cash orders

Scenario: When new LIMIT cash order is submitted, pending quantity changes (price is in OemsRequest)

Given market data prices:
| instrument | price      |
| BTC        | 12_000 USD |
| ETH        | 1_200 USD  |

When Bank 1 deposits 10 BTC into BitMEX account
And Bank 1 deposits 1_000 ETH into BitMEX account
And Bank 2 deposits 10 BTC into BitMEX account
And Client 1 deposits 300_000 USD into BitMEX account
And Client 2 deposits 100_000 USD into BitMEX account

Given an order request:
| order id       | base currency | quote currency | qty    | qty currency | price  | portfolio | counter portfolio |
| client-order-1 | BTC           | USD            | 25_000 | USD          | 10_000 | Client 2  | Bank 2            |

When order is submitted

Then position net state change to:
| portfolioId | instrument | qty     | pendingQty |
| Client 2    | BTC        | -       | 2.5        |
| Client 2    | USD        | 100_000 | 75_000     |
| Bank 2      | BTC        | 10      | 7.5        |
| Bank 2      | USD        | -       | 25_000     |


Scenario: When new MARKET cash order is submitted, pending quantity changes (price is taken from PricingService)

Given an order request:
| order id       | base currency | quote currency | qty    | qty currency | price | portfolio | counter portfolio |
| client-order-2 | BTC           | USD            | 30_000 | USD          | -     | Client 1  | Bank 1            |

When order is submitted
Then street pending quantity changes to 0 BTC

And position net state change to:
| portfolioId | instrument | qty     | pendingQty |
| Client 1    | BTC        | -       | 2.5        |
| Client 1    | USD        | 300_000 | 270_000    |
| Bank 1      | BTC        | 10      | 7.5        |
| Bank 1      | USD        | -       | 30_000     |

