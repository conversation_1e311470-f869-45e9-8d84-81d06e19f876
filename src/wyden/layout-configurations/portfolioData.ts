import { WIDGETS_NAMES } from '@wyden/features/widgets-renderer/widget-names';

export const portfolioDataWorkspace = {
  id: 'nest-basicRow',
  type: 'LayoutCell',
  name: '<PERSON><PERSON>',
  direction: 'DIRECTION_VERTICAL',
  children: [
    {
      id: 'nest-mainRow',
      type: 'LayoutCell',
      name: '<PERSON><PERSON>',
      direction: 'DIRECTION_VERTICAL',
      children: [
        {
          id: 'nest-main-workspace',
          type: 'LayoutCell',
          name: 'MAINWORKSPACE',
          direction: 'DIRECTION_HORIZONTAL',
          children: [
            {
              id: 'nest-center-workspace',
              type: 'LayoutCell',
              name: 'CENTERWORKSPACE',
              direction: 'DIRECTION_VERTICAL',
              children: [
                {
                  id: 'nest-dashboard-workspace',
                  type: 'LayoutCell',
                  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
                  children: [
                    {
                      id: 'portfolio-data',
                      type: 'LayoutComponent',
                      name: WIDGETS_NAMES.PORTFOLIO_DATA,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};
