package io.wyden.websocketserver.security.apikey;

import org.apache.commons.lang3.builder.ToStringBuilder;

public record VaultApiKey(
    String apiKeyId,
    String secret,
    String ownerId,
    String username
) {
    @Override
    public String toString() {
        return new ToStringBuilder(this)
            .append("apiKeyId", apiKeyId)
            .append("ownerId", ownerId)
            .append("username", username)
            .toString();
    }
}
