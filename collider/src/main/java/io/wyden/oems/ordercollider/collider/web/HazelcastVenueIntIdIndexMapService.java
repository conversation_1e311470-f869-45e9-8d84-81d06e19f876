package io.wyden.oems.ordercollider.collider.web;

import io.wyden.oems.ordercollider.domain.map.VenueIntIdIndexMapConfig;

import com.hazelcast.core.HazelcastInstance;
import org.springframework.stereotype.Service;

@Service
public class HazelcastVenueIntIdIndexMapService extends AbstractHazelcastMapService<String, String>{

    public HazelcastVenueIntIdIndexMapService(HazelcastInstance hazelcastInstance) {
        super(VenueIntIdIndexMapConfig.getMap(hazelcastInstance));
    }


}
