Meta: Implements "Wyden Cloud - Accounting.xlsx", tab "PnL Example"

Scenario: Scenario: Verify PnL calculations

Given a XRP/USD security with contract size of 10
And market data prices:
| instrument | price   |
| XRP/USD    | 100 USD |

When Client 1 trades 10 contracts of XRP/USD with 10 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | 10  | 10_010 | 10_000      | -           | -10           | 100.1        |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 105 USD |

Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | 10  | 10_010 | 10_500      | -           | 490           | 100.1        |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 110 USD |

And Client 1 trades 2 contracts of XRP/USD with 2 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | 12  | 12_212 | 13_200      | -           | 988           | 101.76666667 |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 115 USD |

Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | 12  | 12_212 | 13_800      | -           | 1588          | 101.76666667 |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 120 USD |

And Client 1 trades -12 contracts of XRP/USD with 12 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | -   | -    | -           | 2176        | -             | -            |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 125 USD |

Then position net state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | -   | -    | -           | 2176        | -             | -            |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 130 USD |

When Client 1 trades -20 contracts of XRP/USD with 20 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | -20 | -25980 | -26000      | 2176        | -20           | 129.9        |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 135 USD |

Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | -20 | -25980 | -27000      | 2176        | -1020         | 129.9        |

When market data prices change to:
| instrument | price   |
| XRP/USD    | 140 USD |

When Client 1 trades 12 contracts of XRP/USD with 12 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | XRP/USD    | -8  | -10392 | -11200      | 952         | -808          | 129.9        |
