package io.wyden.exchange.infra;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Use if there is no rejections expected by the test class suite
 */
public abstract class NoRejectionsIntegrationTest extends IntegrationTestBase {

    @BeforeEach
    void beforeEach() throws Exception {
        testFixClient.init();
    }

    @AfterEach
    void afterEach() {
        // no rejection messages were observed during the test
        assertThat(testFixClient.getApplication().getRejections(testFixClient.getMarketDataSession())).isNullOrEmpty();
        assertThat(testFixClient.getApplication().getRejections(testFixClient.getTradingSession())).isNullOrEmpty();

        assertThat(testFixClient.getApplication().getBusinessRejections(testFixClient.getMarketDataSession())).isNullOrEmpty();
        assertThat(testFixClient.getApplication().getBusinessRejections(testFixClient.getTradingSession())).isNullOrEmpty();

        assertThat(testFixClient.getApplication().getLogonRejections(testFixClient.getMarketDataSession())).isNullOrEmpty();
        assertThat(testFixClient.getApplication().getLogonRejections(testFixClient.getTradingSession())).isNullOrEmpty();

        assertThat(testFixClient.getApplication().getMdRequestRejections(testFixClient.getMarketDataSession())).isNullOrEmpty();

        assertThat(testFixClient.getApplication().getOrderCancelRejections(testFixClient.getTradingSession())).isNullOrEmpty();

        testFixClient.destroy();
    }
}
