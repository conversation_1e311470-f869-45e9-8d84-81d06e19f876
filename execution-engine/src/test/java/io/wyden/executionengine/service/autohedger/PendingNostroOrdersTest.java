package io.wyden.executionengine.service.autohedger;

import io.wyden.executionengine.domain.model.Execution;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;


class PendingNostroOrdersTest {

    public static final String ORDER_ID_1 = "abc1.0";
    public static final String ORDER_ID_2 = "abc2.0";
    public static final String INSTRUMENT_ID = "BTCUSD";
    public static final String LIMIT_PRICE = "20000";
    public static final String HALF_QUANTITY = "5";
    public static final String QUANTITY = "10";
    public static final String ONE_AND_HALF_QUANTITY = "15";

    @Test
    void emptyBookReturnsZeroSize() {
        PendingNostroOrders book = PendingNostroOrders.emptyBuyBook();
        assertThat(book.getAll().size()).isEqualTo(0);
    }

    @Test
    void getOrderReturnsOrdersByOrderId() {
        PendingNostroOrders book = PendingNostroOrders.emptyBuyBook();
        OemsRequest order = buyMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1);
        book.accept(order);
        assertThat(book.getAll().size()).isEqualTo(1);
        assertThat(book.getBookEntry(ORDER_ID_1).getOrder()).isEqualTo(order);
    }

    @Test
    void getOrderOnNonExistingKeyReturnsNull() {
        PendingNostroOrders book = PendingNostroOrders.emptyBuyBook();
        assertThat(book.getBookEntry("some-id")).isNull();
    }

    @Test
    void buyOrderNotAllowedInSellBook() {
        PendingNostroOrders book = PendingNostroOrders.emptySellBook();
        assertThatThrownBy(() -> book.accept(buyMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
    }

    @Test
    void sellOrderNotAllowedInBuyBook() {
        PendingNostroOrders book = PendingNostroOrders.emptyBuyBook();
        assertThatThrownBy(() -> book.accept(sellMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
    }

    @Test
    void limitAndStopOrderNotAllowedInEitherBook() {
        PendingNostroOrders buyBook = PendingNostroOrders.emptyBuyBook();
        PendingNostroOrders sellBook = PendingNostroOrders.emptySellBook();
        assertThatThrownBy(() -> buyBook.accept(buyLimitOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
        assertThatThrownBy(() -> sellBook.accept(sellLimitOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
        assertThatThrownBy(() -> buyBook.accept(buyStopOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
        assertThatThrownBy(() -> sellBook.accept(sellStopOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1)));
    }

    @Test
    void shouldReturnRemainingBuyQuantityConsideringPartialOrder() {
        PendingNostroOrders book = PendingNostroOrders.emptyBuyBook();
        OemsRequest partial = buyMarketOrder("", QUANTITY, ORDER_ID_1);
        OemsRequest market = buyMarketOrder("", QUANTITY, ORDER_ID_2);

        book.accept(partial);
        book.accept(market);

        book.getBookEntry(partial.getOrderId()).execute(decimal(HALF_QUANTITY), decimal("20000"));
        assertThat(book.getRemainingQuantity()).isEqualByComparingTo(decimal(ONE_AND_HALF_QUANTITY));
    }

    @Test
    void shouldReturnRemainingSellQuantityConsideringPartialOrder() {
        PendingNostroOrders book = PendingNostroOrders.emptySellBook();
        OemsRequest partial = sellMarketOrder("", QUANTITY, ORDER_ID_1);
        OemsRequest market = sellMarketOrder("", QUANTITY, ORDER_ID_2);

        book.accept(partial);
        book.accept(market);

        book.getBookEntry(partial.getOrderId()).execute(decimal(HALF_QUANTITY), decimal("20000"));
        assertThat(book.getRemainingQuantity()).isEqualByComparingTo(decimal(ONE_AND_HALF_QUANTITY));
    }

    @Test
    void buyExecutionReportNotAllowedInSellBook() {
        PendingNostroOrders book = PendingNostroOrders.emptySellBook();
        book.accept(sellMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1));
        OemsResponse response = buyResponse(ORDER_ID_1, ORDER_ID_1, QUANTITY, QUANTITY, "0", QUANTITY);
        assertThatThrownBy(() -> book.execute(response));
    }

    @Test
    void sellExecutionReportNotAllowedInBuyBook() {
        PendingNostroOrders book = PendingNostroOrders.emptyBuyBook();
        book.accept(buyMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1));
        OemsResponse response = sellResponse(ORDER_ID_1, ORDER_ID_1, QUANTITY, QUANTITY, "0", QUANTITY);
        assertThatThrownBy(() -> book.execute(response));
    }

    @Test
    void unknownExecutionReportThrowsAnException() {
        PendingNostroOrders book = PendingNostroOrders.emptySellBook();
        book.accept(sellMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1));
        OemsResponse response = sellResponse(ORDER_ID_1, ORDER_ID_2, QUANTITY, QUANTITY, "0", QUANTITY);
        assertThatThrownBy(() -> book.execute(response));
    }

    @Test
    void executeReducesRemainingQuantity() {
        PendingNostroOrders book = PendingNostroOrders.emptySellBook();
        OemsRequest order1 = sellMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_1);
        OemsRequest order2 = sellMarketOrder(LIMIT_PRICE, QUANTITY, ORDER_ID_2);
        book.accept(order1);
        book.accept(order2);
        OemsResponse response = sellResponse(ORDER_ID_1, ORDER_ID_1, QUANTITY, HALF_QUANTITY, HALF_QUANTITY, HALF_QUANTITY);
        Execution execution = book.execute(response);
        assertThat(execution.order()).isEqualTo(order1);
        assertThat(execution.lastQty()).isEqualTo(HALF_QUANTITY);
        assertThat(book.getBookEntry(order1.getOrderId()).getRemainingQuantity()).isEqualTo(HALF_QUANTITY);
        assertThat(book.getRemainingQuantity()).isEqualTo(ONE_AND_HALF_QUANTITY);
    }

    private OemsRequest buyMarketOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.MARKET)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest sellMarketOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.SELL)
            .setOrderType(OemsOrderType.MARKET)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest buyLimitOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.LIMIT)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest sellLimitOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.SELL)
            .setOrderType(OemsOrderType.LIMIT)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest buyStopOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.STOP)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsRequest sellStopOrder(String limitPrice, String quantity, String orderId) {
        return OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderType(OemsOrderType.STOP)
            .setPrice(limitPrice)
            .setQuantity(quantity)
            .build();
    }

    private OemsResponse buyResponse(String responseId, String orderId, String orderQty, String cumQty, String leavesQty, String lastQty) {
        return OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(responseId)
                .build())
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.BUY)
            .setOrderQty(orderQty)
            .setLeavesQty(leavesQty)
            .setCumQty(cumQty)
            .setLastQty(lastQty)
            .setLastPrice("0.04")
            .setAvgPrice("0.04")
            .build();
    }

    private OemsResponse sellResponse(String responseId, String orderId, String orderQty, String cumQty, String leavesQty, String lastQty) {
        return OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setMetadata(Metadata.newBuilder()
                .setResponseId(responseId)
                .build())
            .setOrderId(orderId)
            .setInstrumentId(INSTRUMENT_ID)
            .setSide(OemsSide.SELL)
            .setOrderQty(orderQty)
            .setLeavesQty(leavesQty)
            .setCumQty(cumQty)
            .setLastQty(lastQty)
            .setLastPrice("0.04")
            .setAvgPrice("0.04")
            .build();
    }

    private BigDecimal decimal(String value) {
        return new BigDecimal(value);
    }
}
