package io.wyden.settlement.server.settlement.settlementconfiguration;

import io.wyden.settlement.client.settlementconfiguration.DayOfTheWeek;
import io.wyden.settlement.client.settlementconfiguration.SettlementSchedulePoint;
import org.springframework.scheduling.support.CronExpression;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class SettlementConfigurationTimeZoneMapper {

    private SettlementConfigurationTimeZoneMapper() { //hide
    }

    public static SettlementAccountConfigurationEntity map(SettlementAccountConfigurationEntity entity, ZoneId targetZoneId) {
        if (targetZoneId == null) {
            return entity;
        }

        ZoneId sourceZoneId = ZoneId.of(entity.configuration().scheduleTZid());
        List<SettlementSchedulePoint> points = entity.configuration().schedule().stream()
            .map(point -> mapSchedulePoint(point, sourceZoneId, targetZoneId))
            .toList();

        return new SettlementAccountConfigurationEntity(entity.accountId(),
            entity.configuration().withSettlementsScheduledPointsAndTimeZone(points, targetZoneId.getId()));
    }

    private static SettlementSchedulePoint mapSchedulePoint(SettlementSchedulePoint point, ZoneId sourceZoneId, ZoneId targetZoneId) {
        LocalTime time = LocalTime.parse(point.time());
        ZonedDateTime sourceZonedDateTime = ZonedDateTime.of(LocalDate.now(), LocalTime.parse(point.time()), sourceZoneId);

        String cronStr = "0 %s %s * * %s".formatted(time.getMinute(), time.getHour(), point.day().name().substring(0, 3).toUpperCase());
        CronExpression cronExpression = CronExpression.parse(cronStr);

        ZonedDateTime nextRunTimeInSourceTimeZone = cronExpression.next(sourceZonedDateTime);
        if (nextRunTimeInSourceTimeZone == null) {
            throw new SettlementConfigurationException("Failed to find next run time for schedule point: " + cronStr + " and zone " + sourceZonedDateTime);
        }

        ZonedDateTime targetNextRunDateTime = nextRunTimeInSourceTimeZone.withZoneSameInstant(targetZoneId);
        return new SettlementSchedulePoint(
            mapDayOfWeek(targetNextRunDateTime.getDayOfWeek()),
            targetNextRunDateTime.format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    private static DayOfTheWeek mapDayOfWeek(DayOfWeek dayOfWeek) {
        return DayOfTheWeek.valueOf(dayOfWeek.name());
    }

}
