package io.wyden.agencytrading.service.tracking;

import io.wyden.agencytrading.utils.BigDecimalUtils;
import io.wyden.agencytrading.utils.ProtobufUtils;
import io.wyden.cloudutils.tools.MarkupUtils;
import io.wyden.published.brokerdesk.ExecutionMode;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static io.wyden.agencytrading.utils.BigDecimalUtils.bd;
import static io.wyden.cloudutils.tools.MarkupUtils.askWithMarkup;
import static io.wyden.cloudutils.tools.MarkupUtils.askWithMarkupRemoved;
import static io.wyden.cloudutils.tools.MarkupUtils.bidWithMarkup;
import static io.wyden.cloudutils.tools.MarkupUtils.bidWithMarkupRemoved;

@Service
public class OrderMarkupProcessor {

    private final InstrumentPrecisionService instrumentPrecisionService;

    public OrderMarkupProcessor(InstrumentPrecisionService instrumentPrecisionService) {
        this.instrumentPrecisionService = instrumentPrecisionService;
    }

    public String calculateMarkup(String instrumentId, OemsSide side, String markup, String lastPrice) {
        if (!BigDecimalUtils.isCreatable(markup)) {
            return lastPrice;
        }
        BigDecimal bdMarkup = new BigDecimal(markup);
        BigDecimal scale = instrumentPrecisionService.getPriceIncr(instrumentId);
        if (side == OemsSide.BUY) {
            return askWithMarkup(bd(lastPrice), bdMarkup, scale).toPlainString();
        } else if (side == OemsSide.SELL) {
            return bidWithMarkupRemoved(bd(lastPrice), bdMarkup, scale).toPlainString();
        }

        throw new IllegalArgumentException("Unsupported side: %s".formatted(side));
    }

    public OemsRequest applyMarkup(OemsRequest request, String markup, ExecutionMode executionMode) {
        if (!BigDecimalUtils.isCreatable(markup)) {
            return request;
        }
        BigDecimal bdMarkup = new BigDecimal(markup);

        BigDecimal scale;
        if (executionMode == ExecutionMode.SOR) {
            scale = MarkupUtils.DEFAULT_SOR_SCALE;
        } else {
            scale = instrumentPrecisionService.getPriceIncr(request.getInstrumentId());
        }

        if (request.getOrderType() == OemsOrderType.MARKET) {
            return request;
        } else if (request.getSide() == OemsSide.BUY && request.getOrderType() == OemsOrderType.STOP) {
            return applyToBuyStopOrder(request, bdMarkup, scale);
        } else if (request.getSide() == OemsSide.SELL && request.getOrderType() == OemsOrderType.STOP) {
            return applyToSellStopOrder(request, bdMarkup, scale);
        } else if (request.getSide() == OemsSide.BUY && request.getOrderType() == OemsOrderType.LIMIT) {
            return applyToBuyLimitOrder(request, bdMarkup, scale);
        } else if (request.getSide() == OemsSide.SELL && request.getOrderType() == OemsOrderType.LIMIT) {
            return applyToSellLimitOrder(request, bdMarkup, scale);
        } else if (request.getSide() == OemsSide.BUY && request.getOrderType() == OemsOrderType.STOP_LIMIT) {
            return applyToBuyStopLimitOrder(request, bdMarkup, scale);
        } else if (request.getSide() == OemsSide.SELL && request.getOrderType() == OemsOrderType.STOP_LIMIT) {
            return applyToSellStopLimitOrder(request, bdMarkup, scale);
        }

        throw new IllegalArgumentException("Unsupported side or orderType, request: %s".formatted(ProtobufUtils.shortDebugString(request)));
    }

    private static OemsRequest applyToBuyStopOrder(OemsRequest request, BigDecimal markup, BigDecimal scale) {
        BigDecimal stopPrice = bd(request.getStopPrice());
        String stopPriceWithMarkup = askWithMarkupRemoved(stopPrice, markup, scale).toPlainString();

        return request.toBuilder()
            .setStopPrice(stopPriceWithMarkup)
            .build();
    }

    private static OemsRequest applyToSellStopOrder(OemsRequest request, BigDecimal markup, BigDecimal scale) {
        BigDecimal stopPrice = bd(request.getStopPrice());
        String stopPriceWithMarkup = bidWithMarkup(stopPrice, markup, scale).toPlainString();

        return request.toBuilder()
            .setStopPrice(stopPriceWithMarkup)
            .build();
    }

    private static OemsRequest applyToBuyLimitOrder(OemsRequest request, BigDecimal markup, BigDecimal scale) {
        BigDecimal limitPrice = bd(request.getPrice());
        String limitPriceWithMarkup = askWithMarkupRemoved(limitPrice, markup, scale).toPlainString();

        return request.toBuilder()
            .setPrice(limitPriceWithMarkup)
            .build();
    }

    private static OemsRequest applyToSellLimitOrder(OemsRequest request, BigDecimal markup, BigDecimal scale) {
        BigDecimal limitPrice = bd(request.getPrice());
        String limitPriceWithMarkup = bidWithMarkup(limitPrice, markup, scale).toPlainString();

        return request.toBuilder()
            .setPrice(limitPriceWithMarkup)
            .build();
    }

    private static OemsRequest applyToBuyStopLimitOrder(OemsRequest request, BigDecimal markup, BigDecimal scale) {
        BigDecimal limitPrice = bd(request.getPrice());
        BigDecimal stopPrice = bd(request.getStopPrice());
        String limitPriceWithMarkup = askWithMarkupRemoved(limitPrice, markup, scale).toPlainString();
        String stopPriceWithMarkup = askWithMarkupRemoved(stopPrice, markup, scale).toPlainString();

        return request.toBuilder()
            .setPrice(limitPriceWithMarkup)
            .setStopPrice(stopPriceWithMarkup)
            .build();
    }

    private static OemsRequest applyToSellStopLimitOrder(OemsRequest request, BigDecimal markup, BigDecimal scale) {
        BigDecimal limitPrice = bd(request.getPrice());
        BigDecimal stopPrice = bd(request.getStopPrice());
        String limitPriceWithMarkup = bidWithMarkup(limitPrice, markup, scale).toPlainString();
        String stopPriceWithMarkup = bidWithMarkup(stopPrice, markup, scale).toPlainString();

        return request.toBuilder()
            .setPrice(limitPriceWithMarkup)
            .setStopPrice(stopPriceWithMarkup)
            .build();
    }
}
