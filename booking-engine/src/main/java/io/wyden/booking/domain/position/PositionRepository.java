package io.wyden.booking.domain.position;

import io.wyden.booking.domain.common.JpaRepository;
import io.wyden.booking.domain.instrument.Instrument;
import io.wyden.booking.domain.ledgerentry.EntryReference;
import io.wyden.booking.domain.ledgerentry.RawLedgerEntry;
import io.wyden.booking.interfaces.rest.RequestModel.PositionSearch;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static io.wyden.booking.interfaces.rest.RequestModel.PositionSearch.symbolAndAccount;
import static io.wyden.booking.interfaces.rest.RequestModel.PositionSearch.symbolAndPortfolio;
import static io.wyden.booking.utils.CollectionUtils.concat;
import static io.wyden.booking.utils.RawLedgerEntryUtil.accounts;
import static io.wyden.booking.utils.RawLedgerEntryUtil.portfolios;
import static io.wyden.booking.utils.RawLedgerEntryUtil.symbols;

public interface PositionRepository extends JpaRepository<Position, Long> {

    @Transactional
    default Optional<Position> findByReference(EntryReference reference) {
        if (reference.getReference() instanceof PortfolioReference portfolioReference) {
            return findByInstrumentAndPortfolioId(reference.getInstrument(), portfolioReference.getReferenceId());
        } else {
            return findByInstrumentAndAccountId(reference.getInstrument(), reference.getReference().getReferenceId());
        }
    }

    @Transactional
    default Collection<Position> findByReferences(Collection<RawLedgerEntry> references) {
        Collection<String> symbols = symbols(references);
        Collection<String> portfolios = portfolios(references);
        Collection<String> accounts = accounts(references);
        Collection<Position> positionsByAccounts = accounts.isEmpty() ? List.of() : findByPropertiesForWrite(symbolAndAccount(symbols, accounts));
        Collection<Position> positionsByPortfolios = portfolios.isEmpty() ? List.of() : findByPropertiesForWrite(symbolAndPortfolio(symbols, portfolios));
        return concat(positionsByAccounts, positionsByPortfolios);
    }

    @Transactional
    default Optional<Position> findByInstrumentAndPortfolioId(Instrument instrument, String portfolioId) {
        PositionSearch bySymbolAndPortfolio = symbolAndPortfolio(instrument.getSymbol(), portfolioId);
        return findByPropertiesForWrite(bySymbolAndPortfolio).stream()
            .filter(position -> position.getInstrument().equals(instrument))
            .filter(position -> position.getPortfolioId().equals(portfolioId))
            .findFirst();
    }

    @Transactional
    default Optional<Position> findByInstrumentAndAccountId(Instrument instrument, String venueAccountId) {
        PositionSearch bySymbolAndAccount = symbolAndAccount(instrument.getSymbol(), venueAccountId);
        return findByPropertiesForWrite(bySymbolAndAccount).stream()
            .filter(position -> position.getInstrument().equals(instrument))
            .filter(position -> position.getAccountId().equals(venueAccountId))
            .findFirst();
    }

    @Transactional
    long countByProperties(PositionSearch request);

    @Transactional
    long countByProperties(PositionSearch request, String after);

    @Transactional
    Collection<Position> findByProperties(PositionSearch request);

    @Transactional
    default Collection<Position> findByPropertiesForWrite(PositionSearch request) {
        return findByProperties(request);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void deleteByProperties(PositionSearch request);
}
