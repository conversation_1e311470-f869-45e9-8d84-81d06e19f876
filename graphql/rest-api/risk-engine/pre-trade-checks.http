### Get pre trade checks

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query preTradeChecks{
    preTradeChecks {
        id
        type
        level
        channels
        portfolioTags {
            key
            value
        }
        portfolios {
            id
            name
        }
        configuration {
            name
            type
            values
        }
    }
}

### Get pre-trade check form schema

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query GetPreTradeCheckFormSchema {
    preTradeCheckFormSchema {
        type
        configuration {
            type
            name
            required
            isEnum
            format
            options
        }
    }
}