package io.wyden.test.scenariorunner.integration.gqlclient.searchinput;

import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;
import io.wyden.apiserver.rest.booking.BookingModel.LedgerEntrySearchInput;
import io.wyden.apiserver.rest.booking.BookingModel.LedgerEntryType;

import java.util.List;

public class LedgerEntrySearchInputs {

    private static final int DEFAULT_PAGE_SIZE = 100;

    private LedgerEntrySearchInputs() {
    }

    public static LedgerEntrySearchInput all() {
        return new LedgerEntrySearchInput(null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byAccounts(String... venueAccount) {
        return new LedgerEntrySearchInput(
            null,
            null,
            List.of(venueAccount),
            null,
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byPortfolios(String... portfolios) {
        return new LedgerEntrySearchInput(
            null,
            null,
            null,
            List.of(portfolios),
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byCurrencyAndAccount(String currency, String account) {
        return new LedgerEntrySearchInput(
            null,
            List.of(currency),
            List.of(account),
            null,
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byCurrenciesAndPortfolios(List<String> currencies, List<String> portfolios) {
        return new LedgerEntrySearchInput(
            null,
            currencies,
            null,
            portfolios,
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput bySymbols(String... symbols) {
        return new LedgerEntrySearchInput(
            List.of(symbols),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byCurrencies(String... currencies) {
        return new LedgerEntrySearchInput(
            null,
            List.of(currencies),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byTypes(LedgerEntryType... type) {
        return byTypes(DEFAULT_PAGE_SIZE, type);
    }

    public static LedgerEntrySearchInput byTypes(Integer pageSize, LedgerEntryType... type) {
        return new LedgerEntrySearchInput(
            null,
            null,
            null,
            null,
            List.of(type),
            null,
            null,
            null,
            null,
            pageSize,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byOrderId(String orderId) {
        return new LedgerEntrySearchInput(
            null,
            null,
            null,
            null,
            null,
            null,
            orderId,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput byTransactionId(String transactionId) {
        return new LedgerEntrySearchInput(
            null,
            null,
            null,
            null,
            null,
            transactionId,
            null,
            null,
            null,
            DEFAULT_PAGE_SIZE,
            null,
            SortingOrder.DESC
        );
    }

    public static LedgerEntrySearchInput firstNRecordsInSortOrder(int pageSize, SortingOrder sortOrder) {
        return new LedgerEntrySearchInput(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            pageSize,
            null,
            sortOrder
        );
    }

    public static LedgerEntrySearchInput firstNRecords(int pageSize, String after) {
        return new LedgerEntrySearchInput(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            pageSize,
            after,
            null
        );
    }

}
