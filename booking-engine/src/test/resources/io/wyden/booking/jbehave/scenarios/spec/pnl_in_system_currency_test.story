Meta: Extends the scope of "Wyden Cloud - Accounting_xlsx", tab "PnL - AssetTrade"

Scenario: deposit USD and ETH
Given market data prices:
| instrument | price      |
| BTC        | 12_000 USD |
| ETH        | 1_600 USD  |
| ETH        | 0.12 BTC   |
| ETH/BTC    | 0.12 BTC   |

When Client 1 deposits 20_000 USD into Bitfinex account
And Client 1 deposits 10 ETH into Bitfinex account
Then position net state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_000 | 20_000 | 20_000      | -           | -             | 1            |
| Client 1    | ETH/BTC    | -      | -      | -           | -           | -             | -            |
| Client 1    | ETH        | 10     | 16_000 | 16_000      | -           | -             | 1_600        |
| Client 1    | BTC        | -      | -      | -           | -           | -             | -            |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | -   | -    | -           | -           | -             | -            |

Then position net (in system currency) state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 20_000 | 20_000 | 20_000      | -           | -             | 1            |
| Client 1    | ETH/BTC    | -      | -      | -           | -           | -             | -            |
| Client 1    | ETH        | 10     | 16_000 | 16_000      | -           | -             | 1_600        |
| Client 1    | BTC        | -      | -      | -           | -           | -             | -            |

Then position gross (in system currency) state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | -   | -    | -           | -           | -             | -            |

Scenario: buy BTC/USD
When Client 1 trades 10 contracts of ETH/BTC with 90 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 19_910 | 19_910 | 19_910      | -           | -             | 1            |
| Client 1    | ETH/BTC    | 10     | 1.2075 | 1.2         | -           | -0.0075       | 0.12075      |
| Client 1    | ETH        | 10     | 16_000 | 16_000      | -           | -             | 1_600        |
| Client 1    | BTC        | -      | -      | -           | -           | -             | -            |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | 10  | 1.2  | 1.2         | -           | -             | 0.12         |

Then position net (in system currency) state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 19_910 | 19_910 | 19_910      | -           | -             | 1            |
| Client 1    | ETH/BTC    | 10     | 14_490 | 14_400      | -           | -90           | 0.12075      |
| Client 1    | ETH        | 10     | 16_000 | 16_000      | -           | -             | 1_600        |
| Client 1    | BTC        | -      | -      | -           | -           | -             | -            |

Then position gross (in system currency) state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | 10  | 14_400 | 14_400      | -           | -             | 0.12         |


Scenario: sell BTC/USD
Given market data prices:
| instrument | price      |
| BTC        | 12_000 USD |
| ETH        | 1_800 USD  |
| ETH        | 0.15 BTC   |
| ETH/BTC    | 0.15 BTC   |

When Client 1 trades -10 contracts of ETH/BTC with 90 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 19_820 | 19_820 | 19_820      | -           | -             | 1            |
| Client 1    | ETH/BTC    | -      | -      | -           | 0.2850      | -             | -            |
| Client 1    | ETH        | 10     | 16_000 | 18_000      | -           | 2_000         | 1_600        |
| Client 1    | BTC        | 0.3    | 3_600  | 3_600       | -           | -             | 12_000       |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | -   | -    | -           | 0.3         | -             | -            |

And position net (in system currency) state change to:
| portfolioId | instrument | qty    | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | 19_820 | 19_820 | 19_820      | -           | -             | 1            |
| Client 1    | ETH/BTC    | -      | -      | -           | 3_420       | -             | -            |
| Client 1    | ETH        | 10     | 16_000 | 18_000      | -           | 2_000         | 1_600        |
| Client 1    | BTC        | 0.3    | 3_600  | 3_600       | -           | -             | 12_000       |

And position gross (in system currency) state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | -   | -    | -           | 3_600       | -             | -            |


Scenario: withdraw USD, BTC and ETH
When Client 1 withdraws all remaining USD
And Client 1 withdraws all remaining ETH
And Client 1 withdraws all remaining BTC

Then position net state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | -   | -    | -           | -           | -             | -            |
| Client 1    | ETH/BTC    | -   | -    | -           | 0.2850      | -             | -            |
| Client 1    | ETH        | -   | -    | -           | 2_000       | -             | -            |
| Client 1    | BTC        | -   | -    | -           | -           | -             | -            |

And position gross state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | -   | -    | -           | 0.3         | -             | -            |

And position net (in system currency) state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | -   | -    | -           | -           | -             | -            |
| Client 1    | ETH/BTC    | -   | -    | -           | 3_420       | -             | -            |
| Client 1    | ETH        | -   | -    | -           | 2_000       | -             | -            |
| Client 1    | BTC        | -   | -    | -           | -           | -             | -            |

And position gross (in system currency) state change to:
| portfolioId | instrument | qty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | ETH/BTC    | -   | -    | -           | 3_600       | -             | -            |
