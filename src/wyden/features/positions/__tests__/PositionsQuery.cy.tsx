import { graphql } from 'msw';
import { worker } from '../../../../../mocks/browser';
import { LedgerEntryType, Scope } from '../../../services/graphql/generated/graphql';
import { aPortfolioResponse } from '../../../services/graphql/generated/mocks';
import { WIDGETS_NAMES } from '../../widgets-renderer/widget-names';
import { usePositionFilters } from '../usePositionsFilters';
import { PositionsQueryPO } from './PositionsQueryPO.cy';

afterEach(() => {
  cy.clearLocalStorage();
});

const dogeInstrument = {
  baseInstrument: {
    assetClass: 'FOREX',
    description: 'DOGEUSD@WydenMock',
    symbol: 'DOGEUSD',
    quoteCurrency: 'USD',
    feeCurrency: 'USD',
    settlementCurrency: 'ADA',
    inverseContract: true,
    venueType: 'STREET',
    venueName: 'WydenMock',
    __typename: 'BaseInstrumentResponse',
  },
  forexSpotProperties: {
    baseCurrency: 'ADA',
    __typename: 'ForexSpotPropertiesResponse',
  },
  tradingConstraints: {
    priceIncr: '1',
    qtyIncr: '1',
    minQty: null,
    maxQty: null,
    tradeable: true,
    __typename: 'TradingConstraintsResponse',
  },
  instrumentIdentifiers: {
    adapterTicker: 'ADAUSD',
    instrumentId: 'ADAUSD@FOREX@WydenMock',
    __typename: 'InstrumentIdentifiersResponse',
  },
  archivedAt: null,
  __typename: 'InstrumentResponse',
};

describe('Positions', () => {
  it('displays positions list properly and loaded information when not trading dashboard', () => {
    const PO = new PositionsQueryPO();

    PO.render();

    PO.gridHeaders().should('have.length', 11);

    PO.allTableRows().should('have.length', 6);

    PO.row(0).instrumentId().should('contain.text', '');
    PO.row(0).quantity().should('contain.text', '');

    PO.row(1).instrumentId().should('contain.text', '');
    PO.row(1).quantity().should('contain.text', '');

    PO.loadedSection().should('exist');
  });

  it('renders properly default grid settings', () => {
    const PO = new PositionsQueryPO();

    PO.render();

    PO.gridHeaders().should('have.length', 11);
    PO.gridHeaders().eq(0).should('contain.text', 'Symbol');
    PO.gridHeaders().eq(1).should('contain.text', 'Quantity');
    PO.gridHeaders().eq(2).should('contain.text', 'Currency');
    PO.gridHeaders().eq(3).should('contain.text', 'Market Value');
    PO.gridHeaders().eq(4).should('contain.text', 'Portfolio');
    PO.gridHeaders().eq(5).should('contain.text', 'Account');
    PO.gridHeaders().eq(6).should('contain.text', 'Gross Cost');
    PO.gridHeaders().eq(7).should('contain.text', 'Gross Average Price');
    PO.gridHeaders().eq(8).should('contain.text', 'Gross Unrealized PnL');
    PO.gridHeaders().eq(9).should('contain.text', 'Gross Realized PnL');
    PO.gridHeaders().eq(10).should('contain.text', '');
  });

  it('opens settings tab properly', () => {
    const PO = new PositionsQueryPO();

    PO.render();

    PO.toggleSettings();
    PO.settingsHeader().should('exist');
  });

  it('adds and removes properly headers', () => {
    worker.use(
      graphql.query('UserData', (req, res, ctx) => {
        const userData = {
          timestamp: Date.now(),
          widgets: [
            {
              id: 'test-widget',
              type: WIDGETS_NAMES.POSITIONS_QUERY,
            },
          ],
        };
        return res(
          ctx.data({
            userData: { data: JSON.stringify(userData) },
          }),
        );
      }),
    );
    const PO = new PositionsQueryPO();

    PO.render();

    PO.columnSettings.openSettings();
    PO.columnSettings.toggleColumn('Symbol');
    PO.columnSettings.toggleColumn('Quantity');
    PO.columnSettings.toggleColumn('Currency');
    PO.columnSettings.toggleColumn('Market Value Sc');
    PO.columnSettings.saveConfig();

    PO.gridHeaders().should('have.length', 9);

    PO.gridHeaders().eq(0).should('contain.text', 'Market Value');
    PO.gridHeaders().eq(1).should('contain.text', 'Portfolio');
    PO.gridHeaders().eq(2).should('contain.text', 'Account');
    PO.gridHeaders().eq(3).should('contain.text', 'Gross Cost');
    PO.gridHeaders().eq(4).should('contain.text', 'Gross Average Price');
    PO.gridHeaders().eq(5).should('contain.text', 'Gross Unrealized PnL');
    PO.gridHeaders().eq(6).should('contain.text', 'Gross Realized PnL');
    PO.gridHeaders().eq(7).should('contain.text', 'Market Value Sc');
    PO.gridHeaders().eq(8).should('contain.text', '');
  });

  it('shows master details properly (with proper instrumentId)', () => {
    worker.use(
      graphql.query('LedgerEntries', (req, res, ctx) => {
        return res(
          ctx.status(200),
          ctx.data({
            ledgerEntries: {
              edges: [
                {
                  node: {
                    __typename: 'LedgerEntryResponse',
                    updatedAt: *************,
                    quantity: 10,
                    price: 20,
                    fee: 1,
                    type: LedgerEntryType.Fee,
                    instrument: 'instrumentId',
                  },
                },
                {
                  node: {
                    __typename: 'LedgerEntryResponse',
                    updatedAt: 1689582978890,
                    quantity: 100,
                    price: 200,
                    fee: 2,
                    type: LedgerEntryType.Deposit,
                    instrument: 'instrumentId',
                  },
                },
              ],
            },
          }),
        );
      }),
    );
    const PO = new PositionsQueryPO();

    PO.render();

    PO.gridHeaders().should('have.length', 11);
    PO.allTableRows().should('have.length', 6);
    PO.row(1).quantity().should('contain.text', 'BTC');
  });

  it('should display selected filters', () => {
    const PO = new PositionsQueryPO();
    PO.render();
    worker.use(
      graphql.query('InstrumentSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            instrumentSearch: {
              edges: [
                {
                  node: dogeInstrument,
                  cursor: '1',
                },
              ],
              pageInfo: {
                hasNextPage: false,
                endCursor: '1',
              },
            },
          }),
        );
      }),
    );

    PO.selectCurrency('ETH');
    PO.expectFilterToBeSelected('ETH');

    PO.resetFilters();
    PO.expectFilterToBeNotSelected('ADAUSD@FOREX@WydenMock');
    PO.expectFilterToBeNotSelected('FILLED');
    PO.expectFilterToBeNotSelected('TEST');
    PO.expectResetButtonToNotBeVisible();
  });

  it('should display different filters on both widget instances', () => {
    const PO = new PositionsQueryPO();
    PO.renderMultiple();
    worker.use(
      graphql.query('InstrumentSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            instrumentSearch: {
              edges: [
                {
                  node: dogeInstrument,
                  cursor: '1',
                },
              ],
              pageInfo: {
                hasNextPage: false,
                endCursor: '1',
              },
            },
          }),
        );
      }),
    );
    PO.selectCurrency('ETH');
    PO.expectFilterToBeSelected('ETH');

    PO.selectSecondTestWidget();

    PO.expectFilterToBeNotSelected('ETH');

    PO.selectActiveTestWidget();
    PO.expectFilterToBeSelected('ETH');
  });

  it.skip('should display persisted filters', () => {
    usePositionFilters.getState().setInitialState();
    const PO = new PositionsQueryPO();
    PO.render();

    worker.use(
      graphql.query('UserData', (req, res, ctx) => {
        const userData = {
          timestamp: Date.now(),
          widgets: [
            {
              id: 'test-widget',
              type: 'BASE',
              filters: {
                accounts: [],
                portfolios: [],
                wallets: [],
                currency: ['ETH', 'BTC'],
              },
            },
          ],
          workspaces: [
            {
              name: 'current-workspace',
              id: 'current-workspace',
              json: {},
              isTrading: false,
              portfolio: aPortfolioResponse({
                id: 'portfolio_trader_1',
                name: 'portfolio_trader_1',
                scopes: [Scope.Manage, Scope.Trade, Scope.Read],
              }),
            },
          ],
        };
        return res(
          ctx.data({
            userData: { data: JSON.stringify(userData) },
          }),
        );
      }),
    );
    PO.render();
    PO.expectFilterToBeSelected('ETH, BTC');
  });
});
