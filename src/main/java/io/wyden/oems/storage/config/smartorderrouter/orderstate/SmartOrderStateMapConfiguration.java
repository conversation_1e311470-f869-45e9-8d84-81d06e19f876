package io.wyden.oems.storage.config.smartorderrouter.orderstate;

import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.config.StoredMapConfigBean;
import io.wyden.oems.storage.web.StringProtobufMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.sor.domain.map.SmartOrderStateMapConfig;
import io.wyden.sor.model.SmartOrderState;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static io.wyden.oems.storage.config.MapStores.defaultNoCoalescingStoreConfig;

@Configuration
public class SmartOrderStateMapConfiguration {

    @Bean
    HazelcastMapConfigBean smartOrderStateMapConfig(SmartOrderStateMapStore mapStore,
                                                    @Value("${hz.smartorderrouter.orderstate.writedelay}") int writeDelay,
                                                    @Value("${hz.smartorderrouter.orderstate.mapstore}") boolean mapStoreEnabled) {
        HazelcastMapConfig hazelcastMapConfig = new SmartOrderStateMapConfig();
        MapStoreConfig mapStoreConfig = defaultNoCoalescingStoreConfig(mapStore, writeDelay, mapStoreEnabled);
        return new StoredMapConfigBean(hazelcastMapConfig, mapStoreConfig);
    }

    @Bean
    WebAccess smartOrderStateMapWebAccess(HazelcastInstance hazelcast, SmartOrderStateMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new SmartOrderStateMapConfig();
        return new StringProtobufMapWebAccess<>(hazelcast, hazelcastMapConfig, SmartOrderState.getDefaultInstance(), mapStore);
    }
}
