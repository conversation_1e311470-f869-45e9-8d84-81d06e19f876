import { describe, expect, it, vi } from 'vitest';
import { useLedgerFilters } from '@wyden/features/ledgers/useLedgerFilters';

const userStoreSynchronizerMock = {
  updateUserData: vi.fn(),
  getWidgetData: vi.fn(),
};

describe('Leger Filers', () => {
  it.skip(`should save accounts filter to user storage synchronizer `, () => {
    const useLedgerFiltersState = useLedgerFilters.getState();
    useLedgerFiltersState.getUserStoreSynchronizer = vi
      .fn()
      .mockReturnValue(userStoreSynchronizerMock);
    useLedgerFiltersState.setAccounts([]);

    expect(userStoreSynchronizerMock.updateUserData).toHaveBeenCalledTimes(1);
    expect(userStoreSynchronizerMock.updateUserData.mock.calls[0][0].variables).toMatchSnapshot();
  });
});
