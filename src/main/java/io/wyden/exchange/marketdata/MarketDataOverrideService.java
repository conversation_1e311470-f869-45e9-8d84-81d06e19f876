package io.wyden.exchange.marketdata;

import io.wyden.exchange.marketdata.override.AskEventConfig;
import io.wyden.exchange.marketdata.override.BatchEventConfig;
import io.wyden.exchange.marketdata.override.BidAskQuoteEventConfig;
import io.wyden.exchange.marketdata.override.BidEventConfig;
import io.wyden.exchange.marketdata.override.EventConfig;
import io.wyden.exchange.marketdata.override.OrderBookEventConfig;
import io.wyden.exchange.marketdata.override.TradeEventConfig;
import io.wyden.exchange.util.InstrumentId;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class MarketDataOverrideService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataOverrideService.class);
    private static final String MIN_TIME_BETWEEN_EVENTS_EMITTED = "${marketData.minTimeBetweenEventsEmittedMillis}";

    private final MarketDataSourceService marketDataSourceService;
    private final MarketDataCacheService marketDataCacheService;
    private final MeterRegistry meterRegistry;
    private final Map<MarketDataOverrideKey, MarketDataOverride> marketDataOverrideMap = new ConcurrentHashMap<>();
    private final Map<String, Boolean> marketDataOverrideEnabledMap = new ConcurrentHashMap<>();

    @Scheduled(fixedDelayString = MIN_TIME_BETWEEN_EVENTS_EMITTED)
    protected void handleTimeEvents() {
        marketDataOverrideMap.values().forEach(MarketDataOverride::handleTimeEvent);
    }

    public MarketDataOverrideService(MarketDataSourceService marketDataSourceService, MarketDataCacheService marketDataCacheService, MeterRegistry meterRegistry) {
        this.marketDataSourceService = marketDataSourceService;
        this.marketDataCacheService = marketDataCacheService;
        this.meterRegistry = meterRegistry;
    }

    public void subscribe(InstrumentId instrumentId, MarketDataDepth marketDataDepth) {
        marketDataSourceService.subscribe(instrumentId.tickerId(), marketDataDepth);
    }

    public void unsubscribe(InstrumentId instrumentId, MarketDataDepth marketDataDepth) {
        marketDataSourceService.unsubscribe(instrumentId.tickerId(), marketDataDepth);
    }

    public Flux<MarketDataEventDTO> getFlux(InstrumentId instrumentId) {
        return getOverride(instrumentId).getFlux();
    }

    public synchronized void reset() {
        LOGGER.debug("Resetting {}", this.getClass().getSimpleName());
        marketDataOverrideMap.forEach((k, v) -> v.reset());
        marketDataOverrideMap.clear();
        marketDataOverrideEnabledMap.clear();
        marketDataSourceService.reset();
        marketDataCacheService.reset();
    }

    public synchronized void setEnabled(String targetCompId, boolean enabled) {
        if (isEnabled(targetCompId) != enabled) {
            LOGGER.debug("Setting override for {} to {}", targetCompId, enabled);
            marketDataOverrideEnabledMap.put(targetCompId, enabled);
            marketDataOverrideMap.forEach((k, v) -> {
                if (StringUtils.equals(targetCompId, k.targetCompId())) v.setEnabled(enabled);
            });
        } else {
            LOGGER.debug("Override for {} already {}. Skipping.", targetCompId, enabled);
        }
    }

    public boolean isEnabled(InstrumentId instrumentId) {
        return isEnabled(instrumentId.sessionId().getTargetCompID());
    }

    public boolean isEnabled(String targetCompId) {
        return marketDataOverrideEnabledMap.getOrDefault(targetCompId, false);
    }

    public void setBidEventConfig(String targetCompId, BidEventConfig eventConfig) {
        getOverride(targetCompId, eventConfig.getTickerId()).setBidEventConfig(eventConfig);
    }

    public void setAskEventConfig(String targetCompId, AskEventConfig eventConfig) {
        getOverride(targetCompId, eventConfig.getTickerId()).setAskEventConfig(eventConfig);
    }

    public void setBidAskQuoteEventConfig(String targetCompId, BidAskQuoteEventConfig eventConfig) {
        getOverride(targetCompId, eventConfig.getTickerId()).setBidAskQuoteEventConfig(eventConfig);
    }

    public void setTradeEventConfig(String targetCompId, TradeEventConfig eventConfig) {
        getOverride(targetCompId, eventConfig.getTickerId()).setTradeEventConfig(eventConfig);
    }

    public void setOrderBookEventConfig(String targetCompId, OrderBookEventConfig eventConfig) {
        getOverride(targetCompId, eventConfig.getTickerId()).setOrderBookEventConfig(eventConfig);
    }

    public void setBatchEventConfig(String targetCompId, BatchEventConfig eventConfig) {
        eventConfig.getBids().forEach(config -> getOverride(targetCompId, config.getTickerId()).setBidEventConfig(config));
        eventConfig.getAsks().forEach(config -> getOverride(targetCompId, config.getTickerId()).setAskEventConfig(config));
        eventConfig.getBidAskQuotes().forEach(config -> getOverride(targetCompId, config.getTickerId()).setBidAskQuoteEventConfig(config));
        eventConfig.getTrades().forEach(config -> getOverride(targetCompId, config.getTickerId()).setTradeEventConfig(config));
        eventConfig.getOrderBooks().forEach(config -> getOverride(targetCompId, config.getTickerId()).setOrderBookEventConfig(config));
    }

    private MarketDataOverride getOverride(InstrumentId instrumentId) {
        return getOverride(instrumentId.sessionId().getTargetCompID(), instrumentId.tickerId());
    }

    private synchronized MarketDataOverride getOverride(String targetCompId, String tickerId) {
        boolean enabled = marketDataOverrideEnabledMap.getOrDefault(targetCompId, false);
        return marketDataOverrideMap.computeIfAbsent(new MarketDataOverrideKey(targetCompId, tickerId), k -> new MarketDataOverride(k, enabled));
    }

    private void updateMetrics(String name, Tags tags) {
        try {
            this.meterRegistry.counter(name, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private class MarketDataOverride {

        private enum EventType { BID, ASK, BIDASK, TRADE, ORDERBOOK }

        private final Sinks.Many<MarketDataEventDTO> sink = Sinks.many().multicast().directBestEffort();
        private final MarketDataOverrideKey marketDataOverrideKey;

        private final Map<EventType, EventConfig> eventConfigMap = new ConcurrentHashMap<>();
        private final Map<EventType, ZonedDateTime> lastEventMap = new ConcurrentHashMap<>();
        private Disposable disposable;
        private Boolean enabled;

        private ZonedDateTime dateGuard = ZonedDateTime.now();

        MarketDataOverride(MarketDataOverrideKey marketDataOverrideKey, boolean enabled) {
            LOGGER.debug("Creating MarketDataOverride for {} with state {}", marketDataOverrideKey, enabled);
            this.marketDataOverrideKey = marketDataOverrideKey;
            setEnabled(enabled);
        }

        Flux<MarketDataEventDTO> getFlux() {
            LOGGER.debug("Requested flux from override {}", marketDataOverrideKey);
            return sink.asFlux();
        }

        public MarketDataOverrideKey getMarketDataOverrideKey(){
            return marketDataOverrideKey;
        }

        void reset() {
            if (disposable != null && !disposable.isDisposed()) {
                disposable.dispose();
            }
        }

        void setEnabled(boolean enabled) {
            if (this.enabled == null || this.enabled != enabled) {
                LOGGER.debug("Setting override {} to {}", marketDataOverrideKey, enabled);
                this.enabled = enabled;
                reset();
                dateGuard = ZonedDateTime.now();
                marketDataCacheService.reset(getMarketDataOverrideKey().targetCompId(), getMarketDataOverrideKey().tickerId());
                if (!enabled) {
                    LOGGER.debug("Connecting override {} flux to source service", marketDataOverrideKey);
                    disposable = marketDataSourceService.getFlux(marketDataOverrideKey.tickerId())
                            .subscribeOn(Schedulers.boundedElastic())
                            .subscribe(this::emmitExchangeEvent);
                }
            } else {
                LOGGER.debug("Override {} already {}. Skipping", marketDataOverrideKey, enabled);
            }
        }

        void setBidEventConfig(BidEventConfig eventConfig) {
            setEventConfig(EventType.BID, eventConfig);
        }

        void setAskEventConfig(AskEventConfig eventConfig) {
            setEventConfig(EventType.ASK, eventConfig);
        }

        void setBidAskQuoteEventConfig(BidAskQuoteEventConfig eventConfig) {
            setEventConfig(EventType.BIDASK, eventConfig);
        }

        void setTradeEventConfig(TradeEventConfig eventConfig) {
            setEventConfig(EventType.TRADE, eventConfig);
        }

        void setOrderBookEventConfig(OrderBookEventConfig eventConfig) {
            setEventConfig(EventType.ORDERBOOK, eventConfig);
        }

        void setEventConfig(EventType eventType, EventConfig eventConfig) {
            LOGGER.debug("Setting override {}: type={}, event={}", marketDataOverrideKey, eventType, eventConfig);
            eventConfigMap.put(eventType, eventConfig);
            lastEventMap.put(eventType, ZonedDateTime.now());
        }

        public void handleTimeEvent() {
            if (Boolean.TRUE.equals(enabled)) {
                Set<EventType> configToRemoveAfterEmmit = new HashSet<>();
                eventConfigMap.forEach((type, config) -> {
                    if (ZonedDateTime.now().isAfter(lastEventMap.get(type).plus(config.period()))) {
                        lastEventMap.put(type, ZonedDateTime.now());
                        emitSimulatedEvent(config);
                        if (shouldBeEmittedOnlyOnce(config)) {
                            configToRemoveAfterEmmit.add(type);
                        }
                    }
                });
                configToRemoveAfterEmmit.forEach(eventConfigMap::remove);
            }
        }

        private static boolean shouldBeEmittedOnlyOnce(EventConfig config) {
            return config.getFixedDelayInMillis() == 0;
        }

        private void emmitExchangeEvent(MarketDataEventDTO dto){
            if (dto.getDateTime().isAfter(dateGuard)) {
                sink.tryEmitNext(dto);
                marketDataCacheService.onEvent(marketDataOverrideKey.targetCompId(), dto);
            } else {
                LOGGER.debug("Exchange event {} ignored", dto.getDateTime());
            }
        }

        private void emitSimulatedEvent(EventConfig eventConfig) {
            updateMetrics("wyden.market-data.events-generated", Tags.empty());
            MarketDataEventDTO dto = eventConfig.convert();
            if (dto.getDateTime().isAfter(dateGuard)) {
                sink.tryEmitNext(dto);
            } else {
                LOGGER.debug("Simulated event {} ignored", dto.getDateTime());
            }
        }
    }

    private record MarketDataOverrideKey(String targetCompId, String tickerId) {
    }
}
