package io.wyden.apiserver.rest.apiui.mocks.custody;

import io.wyden.apiserver.rest.apiui.mocks.orderhistory.TransferStateResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

@Service
public class TransferMockService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransferMockService.class);

    private final List<TransferStateResponse> transfers = new ArrayList<>();
    private final Random random = new Random();

    public TransferMockService() {
        IntStream.range(0, 100).forEach(i -> {
            TransferStateResponse transferStateResponse = generateTransferStateResponse();
            transfers.add(transferStateResponse);
        });
    }

    public Collection<TransferStateResponse> getTransferStates() {
        return transfers.stream()
            .sorted((t1, t2) -> t1.initiateDateTime().compareTo(t2.initiateDateTime()))
            .limit(100)
            .toList();
    }

    public Flux<TransferStateResponse> transferStatesFlux() {
        return Flux.interval(Duration.ofSeconds(1))
            .map(tick -> generateTransferStateResponse())
            .doOnNext(state -> {
                LOGGER.info("Emitting: {}", state);
                transfers.add(state);
                transfers.remove(0);
            });
    }

    private TransferStateResponse generateTransferStateResponse() {
        TransferStateResponse transferStateResponse;
        transferStateResponse = new TransferStateResponse(
            UUID.randomUUID().toString(),
            "Wallet_A",
            "Wallet_B",
            "1.5",
            "BTC",
            TransferStateResponse.TransferStatus.values()[random.nextInt(TransferStateResponse.TransferStatus.values().length)],
            "Initiator_X",
            String.valueOf(System.currentTimeMillis()),
            String.valueOf(System.currentTimeMillis() + 60000),
            String.valueOf(System.currentTimeMillis()),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            "0.001"
        );
        return transferStateResponse;
    }
}
