package io.wyden.booking.spring;

import io.wyden.booking.domain.common.Identifiers;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.position.CashPosition;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferReservationRequest;
import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.DepositReservationRequest;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.PortfolioCashTransferReservationRequest;
import io.wyden.published.booking.PortfolioCashTransferReservationSnapshot;
import io.wyden.published.booking.ReservationRequest;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.booking.WithdrawalReservationRequest;
import io.wyden.published.booking.WithdrawalReservationSnapshot;
import io.wyden.published.booking.command.Command;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.Result;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;

import org.junit.After;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.time.ZonedDateTime;
import java.util.UUID;

import static io.wyden.booking.domain.common.Identifiers.randomSuffix;
import static io.wyden.booking.interfaces.rest.RequestModel.PositionSearch.justAccount;
import static io.wyden.booking.interfaces.rest.RequestModel.PositionSearch.justPortfolio;
import static io.wyden.cloud.utils.test.TestUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;

public class ReservationTest extends MvcSpringTest {

    @After
    public void tearDown() throws Exception {
        super.tearDown();
        reset(commandResultsExchange);
    }

    @Test
    public void shouldCreateDepositReservation() throws Exception {
        String portfolioId = randomSuffix("Bank");
        String accountId = randomSuffix("Kraken");

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("BTC").build());
        venueAccountsMap.put(accountId, VenueAccount.newBuilder().setId(accountId).build());

        DepositReservationRequest depositRequest = DepositReservationRequest.newBuilder()
            .setReservationRef(Identifiers.randomIdentifier())
            .setTransactionType(TransactionType.TRANSACTION_TYPE_DEPOSIT)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setCurrency("BTC")
            .setQuantity("1.5")
            .setPortfolioId(portfolioId)
            .setAccountId(accountId)
            .setFeeAccountId(accountId)
            .setFeePortfolioId(portfolioId)
            .build();

        ReservationRequest reservationRequest = ReservationRequest.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequestId(UUID.randomUUID().toString()).build())
            .setDepositReservationRequest(depositRequest)
            .build();

        commandConsumer.consume(Command.newBuilder().setReservationRequest(reservationRequest).build());
        await().until(() -> messageHistoryService.isCompleted(reservationRequest.getMetadata().getRequestId()));

        ArgumentCaptor<Result> captor = ArgumentCaptor.forClass(Result.class);
        verify(commandResultsExchange).publishWithHeaders(captor.capture(), anyMap());

        Result emittedResult = captor.getValue();
        assertThat(emittedResult.hasSuccess()).isTrue();
        assertThat(emittedResult.getSuccess().hasReservation()).isTrue();
        ReservationSnapshot reservationSnapshot = emittedResult.getSuccess().getReservation();

        assertThat(reservationSnapshot.hasDepositReservation()).isTrue();

        DepositReservationSnapshot reservation = reservationSnapshot.getDepositReservation();
        assertThat(reservation.getReservationRef()).isEqualTo(depositRequest.getReservationRef());
        assertThat(reservation.getTransactionType()).isEqualTo(depositRequest.getTransactionType());
        assertThat(reservation.getDateTime()).isEqualTo(depositRequest.getDateTime());
        assertThat(reservation.getCurrency()).isEqualTo(depositRequest.getCurrency());
        assertThat(reservation.getQuantity()).isEqualTo(depositRequest.getQuantity());
        assertThat(reservation.getPortfolioId()).isEqualTo(depositRequest.getPortfolioId());
        assertThat(reservation.getAccountId()).isEqualTo(depositRequest.getAccountId());
        assertThat(reservation.getFeeAccountId()).isEqualTo(depositRequest.getFeeAccountId());
        assertThat(reservation.getFeePortfolioId()).isEqualTo(depositRequest.getFeePortfolioId());

        statePersistenceScheduler.flushAll();

        // TODO SPL rest-management should reservations include fees also?
        assertThat(positionRepository.findByProperties(justPortfolio(portfolioId)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("BTC");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getPortfolioId()).isEqualTo(portfolioId);
                assertThat(position.getQuantity()).isZero();
                assertThat(position.getAvailableForTradingQuantity()).isZero();
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("1.5"));
            });

        assertThat(positionRepository.findByProperties(justAccount(accountId)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getAccountId()).isEqualTo(accountId);
                assertThat(position.getQuantity()).isZero();
                assertThat(position.getAvailableForTradingQuantity()).isZero();
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("1.5"));
            });
    }

    @Test
    public void shouldCreateWithdrawalReservation() throws Exception {
        String portfolioId = randomSuffix("Bank");
        String accountId = randomSuffix("Kraken");

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        venueAccountsMap.put(accountId, VenueAccount.newBuilder().setId(accountId).build());

        stateManager.store(new CashPosition(bd("10"), ofPortfolioId(portfolioId), new Currency("BTC")));
        stateManager.store(new CashPosition(bd("10"), ofAccountId(accountId), new Currency("BTC")));

        WithdrawalReservationRequest withdrawalRequest = WithdrawalReservationRequest.newBuilder()
            .setReservationRef(Identifiers.randomIdentifier())
            .setTransactionType(TransactionType.TRANSACTION_TYPE_WITHDRAWAL)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setCurrency("BTC")
            .setQuantity("1.5")
            .setPortfolioId(portfolioId)
            .setAccountId(accountId)
            .setFeeAccountId(accountId)
            .setFeePortfolioId(portfolioId)
            .build();

        ReservationRequest reservationRequest = ReservationRequest.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequestId(UUID.randomUUID().toString()).build())
            .setWithdrawalReservationRequest(withdrawalRequest)
            .build();

        commandConsumer.consume(Command.newBuilder().setReservationRequest(reservationRequest).build());
        await().until(() -> messageHistoryService.isCompleted(reservationRequest.getMetadata().getRequestId()));

        ArgumentCaptor<Result> captor = ArgumentCaptor.forClass(Result.class);
        verify(commandResultsExchange).publishWithHeaders(captor.capture(), anyMap());

        Result emittedResult = captor.getValue();
        assertThat(emittedResult.hasSuccess()).isTrue();
        assertThat(emittedResult.getSuccess().hasReservation()).isTrue();

        ReservationSnapshot reservationSnapshot = emittedResult.getSuccess().getReservation();

        assertThat(reservationSnapshot.hasWithdrawalReservation()).isTrue();

        WithdrawalReservationSnapshot reservation = reservationSnapshot.getWithdrawalReservation();
        assertThat(reservation.getReservationRef()).isEqualTo(withdrawalRequest.getReservationRef());
        assertThat(reservation.getTransactionType()).isEqualTo(withdrawalRequest.getTransactionType());
        assertThat(reservation.getDateTime()).isEqualTo(withdrawalRequest.getDateTime());
        assertThat(reservation.getCurrency()).isEqualTo(withdrawalRequest.getCurrency());
        assertThat(reservation.getQuantity()).isEqualTo(withdrawalRequest.getQuantity());
        assertThat(reservation.getPortfolioId()).isEqualTo(withdrawalRequest.getPortfolioId());
        assertThat(reservation.getAccountId()).isEqualTo(withdrawalRequest.getAccountId());
        assertThat(reservation.getFeeAccountId()).isEqualTo(withdrawalRequest.getFeeAccountId());
        assertThat(reservation.getFeePortfolioId()).isEqualTo(withdrawalRequest.getFeePortfolioId());

        statePersistenceScheduler.flushAll();

        assertThat(positionRepository.findByProperties(justPortfolio(portfolioId)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getPortfolioId()).isEqualTo(portfolioId);
                assertThat(position.getQuantity()).isEqualByComparingTo(bd("10"));

                // withdrawals change available quantity - contrary to the deposits
                assertThat(position.getAvailableForTradingQuantity()).isEqualByComparingTo(bd("8.5"));
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("8.5"));
            });

        assertThat(positionRepository.findByProperties(justAccount(accountId)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getAccountId()).isEqualTo(accountId);
                assertThat(position.getQuantity()).isEqualByComparingTo(bd("10"));

                // withdrawals change available quantity - contrary to the deposits
                assertThat(position.getAvailableForTradingQuantity()).isEqualByComparingTo(bd("8.5"));
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("8.5"));
            });
    }

    @Test
    public void shouldCreatePortfolioTransferReservation() throws Exception {
        String from = randomSuffix("Bank");
        String to = randomSuffix("Client");

        portfoliosMap.put(from, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(to, Portfolio.newBuilder().setPortfolioCurrency("USD").build());

        stateManager.store(new CashPosition(bd("10"), ofPortfolioId(from), new Currency("BTC")));

        PortfolioCashTransferReservationRequest transferRequest = PortfolioCashTransferReservationRequest.newBuilder()
            .setReservationRef(Identifiers.randomIdentifier())
            .setTransactionType(TransactionType.TRANSACTION_TYPE_PORTFOLIO_CASH_TRANSFER)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setCurrency("BTC")
            .setQuantity("1.5")
            .setFromPortfolioId(from)
            .setToPortfolioId(to)
            .build();

        ReservationRequest reservationRequest = ReservationRequest.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequestId(UUID.randomUUID().toString()).build())
            .setPortfolioCashTransferReservationRequest(transferRequest)
            .build();

        commandConsumer.consume(Command.newBuilder().setReservationRequest(reservationRequest).build());
        await().until(() -> messageHistoryService.isCompleted(reservationRequest.getMetadata().getRequestId()));

        ArgumentCaptor<Result> captor = ArgumentCaptor.forClass(Result.class);
        verify(commandResultsExchange).publishWithHeaders(captor.capture(), anyMap());

        Result emittedResult = captor.getValue();
        assertThat(emittedResult.hasSuccess()).isTrue();
        assertThat(emittedResult.getSuccess().hasReservation()).isTrue();

        ReservationSnapshot reservationSnapshot = emittedResult.getSuccess().getReservation();

        assertThat(reservationSnapshot.hasPortfolioCashTransferReservation()).isTrue();

        PortfolioCashTransferReservationSnapshot reservation = reservationSnapshot.getPortfolioCashTransferReservation();
        assertThat(reservation.getReservationRef()).isEqualTo(transferRequest.getReservationRef());
        assertThat(reservation.getTransactionType()).isEqualTo(transferRequest.getTransactionType());
        assertThat(reservation.getDateTime()).isEqualTo(transferRequest.getDateTime());
        assertThat(reservation.getCurrency()).isEqualTo(transferRequest.getCurrency());
        assertThat(reservation.getQuantity()).isEqualTo(transferRequest.getQuantity());
        assertThat(reservation.getFromPortfolioId()).isEqualTo(transferRequest.getFromPortfolioId());
        assertThat(reservation.getToPortfolioId()).isEqualTo(transferRequest.getToPortfolioId());

        statePersistenceScheduler.flushAll();

        assertThat(positionRepository.findByProperties(justPortfolio(from)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getPortfolioId()).isEqualTo(from);
                assertThat(position.getQuantity()).isEqualByComparingTo(bd("10"));

                // available quantity is decreased on the 'sending' side of transfer
                assertThat(position.getAvailableForTradingQuantity()).isEqualByComparingTo(bd("8.5"));
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("8.5"));
            });

        assertThat(positionRepository.findByProperties(justPortfolio(to)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getPortfolioId()).isEqualTo(to);
                assertThat(position.getQuantity()).isZero();
                assertThat(position.getAvailableForTradingQuantity()).isZero();
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("1.5"));
            });
    }

    @Test
    public void shouldCreateAccountTransferReservation() throws Exception {
        String from = randomSuffix("Bank");
        String to = randomSuffix("Client");

        portfoliosMap.put("Bank", Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        venueAccountsMap.put(to, VenueAccount.newBuilder().setId(to).build());

        stateManager.store(new CashPosition(bd("10"), ofAccountId(from), new Currency("BTC")));

        AccountCashTransferReservationRequest transferRequest = AccountCashTransferReservationRequest.newBuilder()
            .setReservationRef(Identifiers.randomIdentifier())
            .setTransactionType(TransactionType.TRANSACTION_TYPE_ACCOUNT_CASH_TRANSFER)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setCurrency("BTC")
            .setQuantity("1.5")
            .setFromAccountId(from)
            .setToAccountId(to)
            .setFeeAccountId(from)
            .setFeePortfolioId("Bank")
            .build();

        ReservationRequest reservationRequest = ReservationRequest.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequestId(UUID.randomUUID().toString()).build())
            .setAccountCashTransferReservationRequest(transferRequest)
            .build();

        commandConsumer.consume(Command.newBuilder().setReservationRequest(reservationRequest).build());
        await().until(() -> messageHistoryService.isCompleted(reservationRequest.getMetadata().getRequestId()));

        ArgumentCaptor<Result> captor = ArgumentCaptor.forClass(Result.class);
        verify(commandResultsExchange).publishWithHeaders(captor.capture(), anyMap());

        Result emittedResult = captor.getValue();
        assertThat(emittedResult.hasSuccess()).isTrue();
        assertThat(emittedResult.getSuccess().hasReservation()).isTrue();

        ReservationSnapshot reservationSnapshot = emittedResult.getSuccess().getReservation();

        assertThat(reservationSnapshot.hasAccountCashTransferReservation()).isTrue();

        AccountCashTransferReservationSnapshot reservation = reservationSnapshot.getAccountCashTransferReservation();
        assertThat(reservation.getReservationRef()).isEqualTo(transferRequest.getReservationRef());
        assertThat(reservation.getTransactionType()).isEqualTo(transferRequest.getTransactionType());
        assertThat(reservation.getDateTime()).isEqualTo(transferRequest.getDateTime());
        assertThat(reservation.getCurrency()).isEqualTo(transferRequest.getCurrency());
        assertThat(reservation.getQuantity()).isEqualTo(transferRequest.getQuantity());
        assertThat(reservation.getFromAccountId()).isEqualTo(transferRequest.getFromAccountId());
        assertThat(reservation.getToAccountId()).isEqualTo(transferRequest.getToAccountId());
        assertThat(reservation.getFeeAccountId()).isEqualTo(transferRequest.getFeeAccountId());
        assertThat(reservation.getFeePortfolioId()).isEqualTo(transferRequest.getFeePortfolioId());

        statePersistenceScheduler.flushAll();

        assertThat(positionRepository.findByProperties(justAccount(from)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getAccountId()).isEqualTo(from);
                assertThat(position.getQuantity()).isEqualByComparingTo(bd("10"));

                // available quantity is decreased on the 'sending' side of transfer
                assertThat(position.getAvailableForTradingQuantity()).isEqualByComparingTo(bd("8.5"));
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("8.5"));
            });

        assertThat(positionRepository.findByProperties(justAccount(to)))
            .hasSize(1)
            .allSatisfy(position -> {
                assertThat(position.getCurrency()).isEqualTo("USD");
                assertThat(position.getInstrumentCurrency()).isEqualTo("BTC");
                assertThat(position.getAccountId()).isEqualTo(to);
                assertThat(position.getQuantity()).isZero();
                assertThat(position.getAvailableForTradingQuantity()).isZero();
                assertThat(position.getPendingQuantity()).isEqualByComparingTo(bd("1.5"));
            });
    }
}
