package io.wyden.test.scenariorunner.data.trading;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.test.scenariorunner.data.refdata.InstrumentId;

import java.time.ZonedDateTime;
import java.util.UUID;

/**
 * NOTE:<br>
 * Consider creating client-side orders with following <PERSON><PERSON><PERSON><PERSON> comment:<br>
 * We are setting venueAccount = counterPortfolio, because venueAccount is later translated into ClientRequest.target and OemsRequest.target,
 * which are both representing the counter portfolio of the trade (see proto description for these fields)<br>
 * Currently order-gateway incorrectly assumes that target='Bank' is internal trade, while other targets are external trades
 */
public class SimpleOrderFactory {

    private static final double DEFAULT_QUANTITY = 1.0;
    private static final ClientSide DEFAULT_SIDE = ClientSide.BUY;
    static final ClientTIF DEFAULT_TIF = ClientTIF.GTC;

    public static final String DEFAULT_LIMIT_PRICE = "40000.0";
    private static final String DEFAULT_STOP_PRICE = "30000.0";

    public static final String DEFAULT_STREET_INSTRUMENT_ID = InstrumentId.BTC_USD_FOREX_WYDENMOCK.getName();
    public static final String DEFAULT_CLIENT_INSTRUMENT_ID = InstrumentId.DOGE_USD_FOREX_BANK.getName();

    private final String defaultPortfolio;
    private final String clientId;
    // in case of simple order venueAccounts should have 1 value
    private final String target;

    //TODO should be final
    private PriceProvider priceProvider;

    public SimpleOrderFactory(String clientId, String target, String portfolio) {
        this.clientId = clientId;
        this.target = target;
        this.defaultPortfolio = portfolio;
    }

    public SimpleOrderFactory(String clientId, String defaultPortfolio) {
        this.clientId = clientId;
        this.target = null;
        this.defaultPortfolio = defaultPortfolio;
    }

    public SimpleOrderFactory(String clientId, String defaultPortfolio, PriceProvider priceProvider) {
        this.clientId = clientId;
        this.target = null;
        this.defaultPortfolio = defaultPortfolio;
        this.priceProvider = priceProvider;
    }

    public ClientRequest defaultClientOrder(ClientOrderType type, ClientSide side, double quantity, String price, String stopPrice) {
        return defaultOrder(type, side, quantity, DEFAULT_CLIENT_INSTRUMENT_ID, price, stopPrice, DEFAULT_TIF, defaultPortfolio)
            .toBuilder()
            .clearVenueAccounts()
            .clearTarget()
            .build();
    }

    public ClientRequest defaultStreetLimitOrder(double quantity) {
        return defaultLimitOrder(DEFAULT_STREET_INSTRUMENT_ID, quantity, DEFAULT_LIMIT_PRICE, DEFAULT_TIF);
    }

    public ClientRequest defaultStreetLimitOrder(double quantity, ClientTIF tif) {
        return defaultLimitOrder(DEFAULT_STREET_INSTRUMENT_ID, quantity, DEFAULT_LIMIT_PRICE, tif);
    }

    public ClientRequest defaultStreetLimitOrder(String price) {
        return defaultLimitOrder(DEFAULT_STREET_INSTRUMENT_ID, DEFAULT_QUANTITY, price, DEFAULT_TIF);
    }

    public ClientRequest defaultLimitOrder(String instrumentId, double quantity) {
        return defaultOrder(ClientOrderType.LIMIT, DEFAULT_SIDE, quantity, instrumentId, ClientTIF.GTC, defaultPortfolio);
    }

    public ClientRequest defaultLimitOrder(String instrumentId, double quantity, String price, ClientTIF tif) {
        return defaultLimitOrder(instrumentId, DEFAULT_SIDE, quantity, price, tif);
    }

    public ClientRequest defaultLimitOrder(String instrumentId, ClientSide side, double quantity, String price, ClientTIF tif) {
        return defaultOrder(ClientOrderType.LIMIT, side, quantity, instrumentId, price, "", tif, defaultPortfolio);
    }

    public ClientRequest defaultMarketOrder(ClientSide side, double quantity, String instrumentId) {
        return defaultOrder(ClientOrderType.MARKET, side, quantity, instrumentId, DEFAULT_TIF, defaultPortfolio);
    }

    public ClientRequest defaultMarketOrder(ClientSide side, double quantity, String instrumentId, ClientTIF tif) {
        return defaultOrder(ClientOrderType.MARKET, side, quantity, instrumentId, tif, defaultPortfolio);
    }

    public ClientRequest defaultOrder(ClientOrderType clientOrderType, String instrumentId, ClientSide side, double quantity, String price, String stopPrice, ClientTIF tif, String portfolio) {
        return defaultOrder(clientOrderType, side, quantity, instrumentId, price, stopPrice, tif, portfolio);
    }

    public ClientRequest defaultOrder(String instrumentId, ClientSide side, double quantity, String price, ClientTIF tif, ClientOrderType orderType) {
        return defaultOrder(orderType, side, quantity, instrumentId, price, "", tif, defaultPortfolio);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType) {
        return defaultOrder(orderType, DEFAULT_SIDE, DEFAULT_QUANTITY, DEFAULT_STREET_INSTRUMENT_ID, DEFAULT_TIF, defaultPortfolio);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType, String instrument) {
        return defaultOrder(orderType, DEFAULT_SIDE, DEFAULT_QUANTITY, instrument, DEFAULT_TIF, defaultPortfolio);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType, String instrument, double quantity) {
        return defaultOrder(orderType, DEFAULT_SIDE, quantity, instrument, DEFAULT_TIF, defaultPortfolio);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, String price, String stopPrice, String portfolioId) {
        return defaultOrder(orderType, side, quantity, instrumentId, price, stopPrice, DEFAULT_TIF, portfolioId);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, ClientTIF tif, String portfolioId) {

        String price = switch (orderType) {
            case LIMIT, STOP_LIMIT -> DEFAULT_LIMIT_PRICE;
            default -> "";
        };
        String stopPrice = switch (orderType) {
            case STOP, STOP_LIMIT -> DEFAULT_STOP_PRICE;
            default -> "";
        };

        return defaultOrder(orderType, side, quantity, instrumentId, price, stopPrice, tif, portfolioId);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, String price, String stopPrice, ClientTIF tif, String portfolioId) {
        return defaultOrder(orderType, side, quantity, instrumentId, price, stopPrice, tif, portfolioId, null);
    }

    public ClientRequest defaultOrder(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, String price, String stopPrice, ClientTIF tif, String portfolioId, String cashOrderCurrency) {

        ClientRequest.Builder builder = setDefaults()
            .setInstrumentId(instrumentId)
            .setPortfolioId(portfolioId)
            .setOrderType(orderType)
            .setQuantity(String.valueOf(quantity))
            .setPrice(price)
            .setStopPrice(stopPrice)
            .setSide(side)
            .setTif(tif);

        if (tif == ClientTIF.GTD) {
            ZonedDateTime tifDateTime = ZonedDateTime.now().plusHours(1);
            builder.setExpireTime(DateUtils.toFixUtcTime(tifDateTime));
        }

        if (cashOrderCurrency != null && !cashOrderCurrency.isBlank()) {
            builder.setCurrency(cashOrderCurrency);
        }

        return builder.build();
    }

    public ClientRequest orderToBeFilledImmediately(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, ClientTIF tif) {
        return orderToBeFilledImmediately(orderType, side, quantity, instrumentId, tif, null);
    }

    public ClientRequest orderToBeFilledImmediately(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, ClientTIF tif, String cashOrderCurrency) {
        String limitPrice = "";
        if (orderType == ClientOrderType.LIMIT) {
            limitPrice = String.valueOf(priceProvider.getOrderLimitPriceToBeFilledImmediately(side));
        }
        //TODO add support for STOP/STOP_LIMIT orders
        return defaultOrder(orderType, side, quantity, instrumentId, limitPrice, "", tif, defaultPortfolio, cashOrderCurrency);
    }

    public ClientRequest cashOrderToBeFilledImmediately(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, ClientTIF tif, String cashOrderCurrency) {
        return orderToBeFilledImmediately(orderType, side, quantity, instrumentId, tif, cashOrderCurrency);
    }

    public ClientRequest defaultCashOrder(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, ClientTIF tif, String cashOrderCurrency, double price) {
        return defaultOrder(orderType, side, quantity, instrumentId, String.valueOf(price), "", tif, defaultPortfolio, cashOrderCurrency);
    }

    private ClientRequest.Builder setDefaults() {
        ClientRequest.Builder builder = ClientRequest.newBuilder();
        if (target != null) {
            builder.setTarget(target);
            builder.addVenueAccounts(target);
        }
        return builder
            .setClOrderId(UUID.randomUUID().toString())
            .setOrderId(UUID.randomUUID().toString())
            .setClientId(clientId)
            .setPortfolioId(defaultPortfolio);
    }

    public static ClientSide oppositeSide(ClientSide side) {
        switch (side) {
            case BUY -> {
                return ClientSide.SELL;
            }
            case SELL -> {
                return ClientSide.BUY;
            }
            case SELL_SHORT -> {
                return ClientSide.REDUCE_SHORT;
            }
            case REDUCE_SHORT -> {
                return ClientSide.SELL_SHORT;
            }
            default -> throw new IllegalArgumentException("Unsupported type " + side);
        }
    }

}
