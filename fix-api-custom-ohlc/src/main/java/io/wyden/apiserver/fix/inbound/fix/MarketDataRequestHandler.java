package io.wyden.apiserver.fix.inbound.fix;

import io.wyden.apiserver.fix.common.fix.FixMessageHandler;
import io.wyden.apiserver.fix.common.marketdata.MarketDataService;
import io.wyden.apiserver.fix.tracking.MarketDataRequestTracker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import quickfix.FieldNotFound;
import quickfix.MessageCracker;
import quickfix.SessionID;
import quickfix.field.SubscriptionRequestType;
import quickfix.fix44.MarketDataRequest;

@Component
public class MarketDataRequestHandler implements FixMessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataRequestHandler.class);

    private final MarketDataService marketDataService;
    private final MarketDataRequestTracker marketDataRequestTracker;

    public MarketDataRequestHandler(MarketDataService marketDataService, MarketDataRequestTracker marketDataRequestTracker) {
        this.marketDataService = marketDataService;
        this.marketDataRequestTracker = marketDataRequestTracker;
    }

    @MessageCracker.Handler
    public void onMessage(MarketDataRequest message, SessionID sessionId) {
        try {
            validate(message);
            marketDataRequestTracker.addIfAbsent(message, sessionId);

            SubscriptionRequestType subscriptionRequestType = message.getSubscriptionRequestType();
            switch (subscriptionRequestType.getValue()) {
                case SubscriptionRequestType.SNAPSHOT_UPDATES -> marketDataService.subscribeMarketData(sessionId, message);
                case SubscriptionRequestType.DISABLE_PREVIOUS_SNAPSHOT_UPDATE_REQUEST -> marketDataService.unsubscribeMarketData(sessionId, message);
                default -> marketDataService.rejectMessage(sessionId, message, new FieldNotFound("Unknown subscriptionRequestType"));
            }
        } catch (Exception e) {
            LOGGER.error("Failed to process message: {} for session: {}", message, sessionId, e);
            marketDataService.rejectMessage(sessionId, message, e);

            try {
                marketDataRequestTracker.delete(message, sessionId);
            } catch (Exception ex) {
                LOGGER.error("Failed to remove MarketDataRequest from tracking! Request: {}, SessionID: {}", message, sessionId, e);
            }
        }
    }

    private void validate(MarketDataRequest marketDataRequest) throws FieldNotFound {
        if (marketDataRequest.getMarketDepth().getValue() != 1) {
            LOGGER.warn("Market data request received is incorrect - unsupported depth: {}", marketDataRequest);
            throw new IllegalArgumentException("Unsupported depth. Supported values: `1`");
        }
    }
}
