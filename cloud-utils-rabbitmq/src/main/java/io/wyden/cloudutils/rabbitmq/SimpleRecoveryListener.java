package io.wyden.cloudutils.rabbitmq;

import com.rabbitmq.client.Recoverable;
import com.rabbitmq.client.RecoveryListener;
import com.rabbitmq.client.impl.recovery.AutorecoveringConnection;
import org.slf4j.Logger;

import java.util.stream.Collectors;

import static org.slf4j.LoggerFactory.getLogger;

public class SimpleRecoveryListener implements RecoveryListener {

    private static final Logger LOGGER = getLogger(SimpleRecoveryListener.class);

    @Override
    public void handleRecoveryStarted(Recoverable recoverable) {
        LOGGER.warn("RabbitMQ connection recovery started for {}", recoverable);
    }

    @Override
    public void handleRecovery(Recoverable recoverable) {
        LOGGER.warn("RabbitMQ connection recovery completed for {}", recoverable);

        if (recoverable instanceof AutorecoveringConnection autorecoveringConnection) {
            LOGGER.warn("Recovered exchanges: [{}], queues: [{}], bindings: [{}]",
                    autorecoveringConnection.getRecordedExchanges().keySet(),
                    autorecoveringConnection.getRecordedQueues().keySet(),
                    autorecoveringConnection.getRecordedBindings().stream()
                            .map(binding -> "from: %s, to: %s, headers: %s".formatted(binding.getSource(), binding.getDestination(), binding.getRoutingKey()))
                            .collect(Collectors.joining(" | ")));
        }
    }

    @Override
    public void handleTopologyRecoveryStarted(Recoverable recoverable) {
        LOGGER.warn("RabbitMQ topology recovery started for: {}", recoverable);
    }
}
