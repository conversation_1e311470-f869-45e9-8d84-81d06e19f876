syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.booking;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/booking/transaction_snapshot.proto";
import "io/wyden/published/booking/reservation_snapshot.proto";

message WalEvent {
  io.wyden.published.common.Metadata metadata = 1;
  int64 sequence_number = 2;

  oneof event {
    TransactionSnapshot transaction = 10;
    ReservationSnapshot reservation = 11;
    ReservationReleaseRequest reservation_release = 12;
  }
}
