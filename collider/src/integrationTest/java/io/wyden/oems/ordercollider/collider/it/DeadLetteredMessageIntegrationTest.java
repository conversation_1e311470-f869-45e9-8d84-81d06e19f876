package io.wyden.oems.ordercollider.collider.it;

import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.oems.ordercollider.collider.service.tracking.VenueOemsMapper;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueRequestType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static io.wyden.published.oems.OemsResponse.Result.CONNECTOR_UNAVAILABLE;
import static org.assertj.core.api.Assertions.assertThat;

class DeadLetteredMessageIntegrationTest extends ColliderIntegrationTestBase {

    private OemsRequest orderInFlight;

    @BeforeEach
    void setUp() throws Exception {
        orderInFlight = client.emitsOemsRequest(testingData.clientSingleOrderRequest(OemsOrderType.MARKET, OemsRequest.OemsPTCStatus.APPROVED));
        connector.awaitVenueRequest();
        connector.emits(testingData.correlationReportOfOrderIdIntId().build());
    }

    @Test
    void deadLetteredVenueOrderShouldBeRejectedToClient() {
        VenueRequest venueOrder = VenueOemsMapper.toVenueOrder(orderInFlight, InstrumentIdentifiers.newBuilder().build());
        destinations.getTradingDLX().publishWithHeaders(venueOrder, Map.of(OemsHeader.MESSAGE_TYPE.getHeaderName(), VenueRequest.class.getSimpleName()));
        OemsResponse response = client.awaitExecutionReportWithOrderStatusReceived(OemsOrderStatus.STATUS_REJECTED);

        assertThat(response.getOrderId()).isEqualTo(testingData.orderId);
        assertThat(response.getRequestResult()).isEqualTo(CONNECTOR_UNAVAILABLE);
        assertThat(response.getReason()).contains("Connector not available");
    }

    @Test
    void deadLetteredVenueCancelShouldBeRejectedToClient() {
        String cancelRequestId = "cancelRequestId";
        OemsRequest cancel = client.emitsCancelRequest(cancelRequestId, orderInFlight);
        connector.awaitVenueRequest();

        VenueRequest venueCancel = toVenueCancel(cancel);
        destinations.getTradingDLX().publishWithHeaders(venueCancel, Map.of(OemsHeader.MESSAGE_TYPE.getHeaderName(), VenueRequest.class.getSimpleName()));

        OemsResponse cancelReject = client.awaitCancelReject();
        assertThat(cancelReject.getOrderId()).isEqualTo(orderInFlight.getOrderId());
    }

    static VenueRequest toVenueCancel(OemsRequest request) {
        return VenueRequest.newBuilder()
            .setRequestType(VenueRequestType.VENUE_CANCEL)
            .setMetadata(Metadata.newBuilder()
                .setRequestId(request.getMetadata().getRequestId())
                .build())
            .setOrderId(request.getOrderId())
            .setVenueAccount(request.getTarget())
            .build();
    }
}
