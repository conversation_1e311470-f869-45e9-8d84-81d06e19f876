package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.test.scenariorunner.extension.common.InvalidExtensionConfigException;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.SecurityIntegrator;
import io.wyden.test.scenariorunner.integration.restmgmtclient.RestManagementActor;
import io.wyden.test.scenariorunner.integration.service.accessgateway.KeySecret;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static io.wyden.test.scenariorunner.extension.ExtensionUtils.getGlobalStore;

/**
 * Extension creates {@link RestManagementActor} before all tests
 */
public class RestManagementActorExtension implements BeforeAllCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(RestManagementActorExtension.class);

    private final String clientId;
    private RestManagementActor restManagementActor;

    public RestManagementActorExtension(String clientId) {
        this.clientId = clientId;
    }

    public RestManagementActorExtension() {
        this.clientId = ClientActor.generateClientId();
    }

    @Override
    public void beforeAll(ExtensionContext extensionContext) throws Exception {
        SecurityIntegrator securityIntegrator = getGlobalStore(extensionContext).get(SecurityIntegratorExtension.class, SecurityIntegrator.class);
        securityIntegrator.setupAdministrator(clientId);
        KeySecret keySecret = securityIntegrator.createKeySecret(clientId);
        restManagementActor = new RestManagementActor(keySecret, clientId);
    }

    public RestManagementActor actor() {
        if (restManagementActor == null) {
            throw new InvalidExtensionConfigException("Rest Management Actor was not initialized. Please check if key and secret are properly configured");
        }
        return restManagementActor;
    }

    public String getClientId() {
        return clientId;
    }
}
