package io.wyden.risk.engine.infrastructure.rabbit;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.published.oems.OemsRequest;
import io.wyden.risk.engine.interfaces.rabbit.OemsRequestConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import javax.annotation.Nullable;

@Component
public class TradingMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingMessageConsumer.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations rabbitDestinations;
    private final OemsRequestConsumer oemsRequestConsumer;
    private final Tracing otlTracing;
    private final String queueName;
    private final String consumerName;

    private RabbitQueue<Message> queue;

    TradingMessageConsumer(RabbitIntegrator rabbitIntegrator,
                           RabbitDestinations rabbitDestinations,
                           OemsRequestConsumer oemsRequestConsumer,
                           Tracing otlTracing,
                           @Value("${rabbitmq.trading-risk-engine-queue}") String queueName,
                           @Value("${spring.application.name}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitDestinations = rabbitDestinations;
        this.oemsRequestConsumer = oemsRequestConsumer;
        this.otlTracing = otlTracing;
        this.queueName = queueName;
        this.consumerName = consumerName;

        declareQueue();
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("request.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(message, properties);
            }
        }
    }

    private ConsumptionResult consumeInner(@Nullable Message data, AMQP.BasicProperties properties) {
        LOGGER.debug("Received new Trading message. Properties: {}", properties);

        try {
            if (data == null) {
                LOGGER.error("Message parsing failed");
                Span.current().setStatus(StatusCode.ERROR, "Message parsing failed");
                return ConsumptionResult.failureNonRecoverable();
            } else if (data instanceof OemsRequest oemsRequest) {
                return oemsRequestConsumer.consume(oemsRequest, properties);
            } else {
                LOGGER.error("Message type not supported: {}", data.getClass().getSimpleName());
                Span.current().setStatus(StatusCode.ERROR, "Message type not supported");
                return ConsumptionResult.failureNonRecoverable();
            }
        } catch (Exception ex) {
            LOGGER.error("Failed to process message, dropping", ex);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(ex);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private void declareQueue() {
        queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.attachConsumer(TradingMessageParser.parser(), this);

        bindQueueToPtcRequiredRequests(rabbitDestinations.getTradingIngressExchange());
    }

    private <T extends Message> void bindQueueToPtcRequiredRequests(RabbitExchange<Message> exchange) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.PTC.getHeaderName(), OemsRequest.OemsPTCStatus.REQUIRED.name()
        );
        bindQueue(exchange, headers);
    }

    private void bindQueue(RabbitExchange<Message> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }
}
