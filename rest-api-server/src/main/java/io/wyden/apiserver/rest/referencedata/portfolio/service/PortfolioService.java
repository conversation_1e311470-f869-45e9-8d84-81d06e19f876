package io.wyden.apiserver.rest.referencedata.portfolio.service;

import io.wyden.apiserver.rest.referencedata.ReferenceDataHttpClient;
import io.wyden.apiserver.rest.referencedata.portfolio.entity.PortfolioEntity;
import io.wyden.apiserver.rest.referencedata.portfolio.model.AggregatedPortfolioTag;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioSearchInputDto;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioChangeCommandType;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Service
public class PortfolioService {

    private final ReferenceDataHttpClient referenceDataHttpClient;
    private final PortfolioChangeRequestEmitter portfolioChangeRequestEmitter;
    private final PortfolioRepository portfolioRepository;

    public PortfolioService(ReferenceDataHttpClient referenceDataHttpClient,
                            PortfolioChangeRequestEmitter portfolioChangeRequestEmitter,
                            PortfolioRepository portfolioRepository) {
        this.referenceDataHttpClient = referenceDataHttpClient;
        this.portfolioChangeRequestEmitter = portfolioChangeRequestEmitter;
        this.portfolioRepository = portfolioRepository;
    }

    public Mono<Void> sendRequestToCreatePortfolio(PortfolioEntity portfolioEntity, String requester, String correlationObject) {
        return Mono.fromRunnable(() -> emitPortfolioChangeRequest(portfolioEntity, requester, correlationObject));
    }

    private void emitPortfolioChangeRequest(PortfolioEntity portfolioEntity, String requester, String correlationObject) {
        portfolioChangeRequestEmitter.emit(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_CREATE, portfolioEntity, requester, correlationObject);
    }

    public void sendRequestToUpdatePortfolio(PortfolioEntity portfolioEntity, String requester, String correlationObject) {
        portfolioChangeRequestEmitter.emit(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE, portfolioEntity, requester, correlationObject);
    }

    public Mono<CursorConnection> getPortfolios(PortfolioSearchInputDto searchInput) {
        return Mono.just(searchInput)
            .map(portfolioRepository::search);

    }

    public CursorConnection getPortfoliosCursor(PortfolioSearchInputDto searchInput) {
        return portfolioRepository.search(searchInput);
    }

    public Mono<List<AggregatedPortfolioTag>> getAllPortfolioTags() {
        return referenceDataHttpClient.getPortfolioTags();
    }

    public Collection<Portfolio> findAll() {
        return portfolioRepository.findAll();
    }

    public Optional<Portfolio> findById(String portfolioId) {
        return portfolioRepository.find(portfolioId);
    }
}
