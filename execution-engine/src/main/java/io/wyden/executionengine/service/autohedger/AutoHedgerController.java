package io.wyden.executionengine.service.autohedger;

import io.wyden.executionengine.service.utils.ProtobufUtils;
import io.wyden.published.brokerdesk.AutoHedgerConfig;
import io.wyden.published.brokerdesk.AutoHedgerConfigKey;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.Collection;

import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
public class AutoHedgerController {

    private static final Logger LOGGER = getLogger(AutoHedgerController.class);

    private final AutoHedgerConfigService autoHedgerConfigService;
    private final AutoHedgerFactory autoHedgerFactory;

    public AutoHedgerController(AutoHedgerConfigService autoHedgerConfigService, AutoHedgerFactory autoHedgerFactory) {
        this.autoHedgerConfigService = autoHedgerConfigService;
        this.autoHedgerFactory = autoHedgerFactory;
    }

    @GetMapping(value = "/auto-hedger", produces = APPLICATION_PROTOBUF_VALUE)
    public Flux<AutoHedgerConfig> getAutoHedgerConfig() {
        LOGGER.info("Retrieving auto hedger config");
        Collection<AutoHedgerConfig> autoHedgerConfig = autoHedgerConfigService.getAutoHedgerConfig();
        return Flux.fromIterable(autoHedgerConfig);
    }

    @PutMapping("/auto-hedger")
    public ResponseEntity<String> updateAutoHedgerConfig(@RequestBody AutoHedgerConfig config) {
        LOGGER.info("Updating auto hedger config: {}", ProtobufUtils.shortDebugString(config));
        try {
            autoHedgerConfigService.updateAutoHedgerConfig(config);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            LOGGER.error("Failed to update auto hedger using config: ({}). Reason: ({})", config, e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(e.getMessage());
        }
    }

    @PostMapping("/auto-hedger")
    public ResponseEntity<String> createAutoHedger(@RequestBody AutoHedgerConfig config) {
        LOGGER.info("Creating new auto hedger using config: {}", ProtobufUtils.shortDebugString(config));
        try {
            autoHedgerFactory.createAutoHedger(config);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            LOGGER.error("Failed to create new auto hedger using config: ({}). Reason: ({})", config, e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(e.getMessage());
        }
    }

    @DeleteMapping("/auto-hedger")
    public ResponseEntity<String> deleteAutoHedger(@RequestParam String asset, @RequestParam String portfolioId) {
        LOGGER.info("Removing auto hedger: asset=({}), portfolioId=({})", asset, portfolioId);
        try {
            AutoHedgerConfigKey configKey = AutoHedgerConfigKey.newBuilder()
                    .setAsset(asset)
                    .setPortfolioId(portfolioId)
                    .build();
            autoHedgerFactory.deleteAutoHedger(configKey);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            LOGGER.error("Failed to remove auto hedger for: asset=({}), portfolioId=({}). Reason: ({})", asset, portfolioId, e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.BAD_REQUEST)
                    .body(e.getMessage());
        }
    }
}
