package io.wyden.aeron.node;

import io.wyden.aeron.util.SignalUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
public class AeronExchangeCoreApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(AeronExchangeCoreApplication.class);

    public static void main(final String[] args) {
        ConfigurableApplicationContext context = new SpringApplicationBuilder(AeronExchangeCoreApplication.class)
        .web(WebApplicationType.SERVLET)
        .run(args);

        SignalUtil.register(() -> {
            context.close();
            LOGGER.info("Shutdown Cluster Node");
        });
    }
}
