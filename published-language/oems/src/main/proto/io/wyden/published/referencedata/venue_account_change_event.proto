syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.referencedata;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/referencedata/portfolio/portfolio_modification_request.proto";
import "io/wyden/published/referencedata/venue_account.proto";

message VenueAccountChangeEvent {
  io.wyden.published.common.Metadata metadata = 1;
  VenueAccountChangeEventType venue_account_change_event_type = 2;
  VenueAccount venue_account = 3;

  // when account is created, grant access to the account to the following groups of users
  repeated Grant grants = 4;
}

enum VenueAccountChangeEventType {
  VENUE_ACCOUNT_CHANGE_EVENT_TYPE_UNSPECIFIED = 0;
  VENUE_ACCOUNT_CHANGE_EVENT_TYPE_CREATED = 1;
  VENUE_ACCOUNT_CHANGE_EVENT_TYPE_UPDATED = 2;
}
