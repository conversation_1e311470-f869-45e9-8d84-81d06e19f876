package io.wyden.test.scenariorunner.referencedata;

import io.qameta.allure.Epic;
import io.wyden.apiserver.rest.referencedata.instruments.mapper.InstrumentEntityMapper;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.VenueTypeDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.create.CreateInstrumentDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.entity.InstrumentEntity;
import io.wyden.test.scenariorunner.assertion.EventLogEventSoftAssert;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.ErrorMsg;
import io.wyden.test.scenariorunner.data.refdata.InstrumentFactory;
import io.wyden.test.scenariorunner.data.user.Administrator;
import io.wyden.test.scenariorunner.data.user.Manager;
import io.wyden.test.scenariorunner.extension.alltest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.alltest.GraphQLActorExtension;
import io.wyden.test.scenariorunner.extension.alltest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.extension.alltest.VenueAccountExtension;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.infra.OemsHealthObserverExtension;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.mapper.InstrumentResponseMapper;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.model.EventLogEventDTO;
import io.wyden.test.scenariorunner.util.Randomizer;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestClientException;

import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;

import static io.wyden.test.scenariorunner.data.refdata.InstrumentFactory.createInstrumentId;
import static io.wyden.test.scenariorunner.data.infra.Epics.REFERENCE_DATA;
import static io.wyden.test.scenariorunner.data.infra.TestTags.SMOKE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@Epic(REFERENCE_DATA)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ExtendWith({
    OemsHealthObserverExtension.class,
    SecurityIntegratorExtension.class
})
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.REST_API_SERVER,
    Service.FIX_ACTOR,
    Service.FIX_API_SERVER,
    Service.REFERENCE_DATA,
    Service.STORAGE
})
public abstract class CreateInstrumentTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(CreateInstrumentTest.class);

    @Order(1)
    @RegisterExtension
    ClientActorExtension accountCreatorExtension = new ClientActorExtension(Administrator.GROUP_NAME, false);
    @Order(2)
    @RegisterExtension
    VenueAccountExtension venueAccountCreatorExtension = new VenueAccountExtension(
        accountCreatorExtension.clientId(), List.of(VenueAccountConstants.MOCK_VENUE));

    protected ClientActor accountCreatorAdmin;
    protected GraphQLActor accountCreatorAdminGql;

    @BeforeAll
    void setupAccountCreator() {
        accountCreatorAdmin = accountCreatorExtension.actor();
        accountCreatorAdminGql = accountCreatorExtension.configGqlActor();
    }

    @ParameterizedTest(name = "{0} whenAccountCreatorCreateInstrument_thenCreatedInstrumentAppearInInstrumentList")
    @EnumSource(value = VenueTypeDto.class)
    protected void whenAccountCreatorCreateInstrument_thenCreatedInstrumentAppearInInstrumentList(VenueTypeDto venueType) {
        String baseCurrency = Randomizer.randomString(4);
        String quoteCurrency = Randomizer.randomString(4);
        CreateInstrumentDto instrumentToCreateDto = InstrumentFactory.createForexInstrument(venueType, VenueAccountConstants.MOCK_VENUE, baseCurrency, quoteCurrency);

        accountCreatorAdminGql.instrument().create(instrumentToCreateDto);

        String instrumentId = createInstrumentId(baseCurrency, quoteCurrency, AssetClassDto.FOREX, VenueAccountConstants.MOCK_VENUE);

        WaitUtils.waitUntilIgnoringExceptions("created instrument with instrumentId [%s] is present in instrument list".formatted(instrumentId),
            () -> accountCreatorAdmin.getInstruments().stream().anyMatch(instrumentIdEqualTo(instrumentId)), Timeouts.WAIT_FOR_CONDITION_M);

        InstrumentEntity instrumentToCreateEntity = InstrumentEntityMapper.map(instrumentToCreateDto);
        instrumentToCreateEntity.getInstrumentIdentifiers().setInstrumentId(instrumentId);

        InstrumentResponseDto createdSecurityResponse = getInstrumentByInstrumentId(accountCreatorAdmin.getInstruments(), instrumentId);

        InstrumentEntity createdSecurityEntity = InstrumentResponseMapper.map(createdSecurityResponse);

        verifyCreatedInstrument(createdSecurityEntity, instrumentToCreateEntity);
    }

    public static InstrumentResponseDto getInstrumentByInstrumentId(Collection<InstrumentResponseDto> list, String instrumentId) {
        LOGGER.info("Get instrument by instrumentId: {} from instrument list", instrumentId);
        return list
            .stream()
            .filter(instrumentIdEqualTo(instrumentId))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("Created instrument with instrumentId %s is not present in instrument list".formatted(instrumentId)));
    }

    protected void verifyCreatedInstrument(InstrumentEntity actualSecurity, InstrumentEntity expectedSecurity) {
        assertThat(actualSecurity)
            .usingRecursiveComparison()
            .ignoringExpectedNullFields()
            .isEqualTo(expectedSecurity);
    }

    public static Predicate<InstrumentResponseDto> instrumentIdEqualTo(String instrumentId) {
        return instrument -> instrument.instrumentIdentifiers().instrumentId().equals(instrumentId);
    }

}

@Fix
class FixCreateInstrumentTest extends CreateInstrumentTest {

    @Override
    protected void verifyCreatedInstrument(InstrumentEntity actualSecurity, InstrumentEntity expectedSecurity) {

        assertThat(actualSecurity)
            .usingRecursiveComparison()
            .comparingOnlyFields(
                "baseInstrument.venueName",
                "baseInstrument.assetClass",
                "baseInstrument.quoteCurrency",
//                "baseInstrument.venueType", //TODO uncomment when FIX API start to produce instruments with all fields set
                "instrumentIdentifiers.instrumentId",
                "tradingConstraints.minQty")//TODO ignoringExpectedNullFields when FIX API start to produce instruments with all fields set
            .isEqualTo(expectedSecurity);
    }

}

@GraphQL
class GraphQLCreateInstrumentTest extends CreateInstrumentTest {

    @RegisterExtension
    GraphQLActorExtension managerGqlExtension = new GraphQLActorExtension(Manager.GROUP_NAME, false);

    private GraphQLActor managerGql;

    @BeforeAll
    void setupNonAccountCreator() {
        managerGql = managerGqlExtension.actor();
    }

    @Tag(SMOKE)
    @Override
    @ParameterizedTest(name = "{0} whenAccountCreatorCreateInstrument_thenCreatedInstrumentAppearInInstrumentList")
    @EnumSource(value = VenueTypeDto.class)
    @Timeout(Timeouts.SCENARIO_TIMEOUT_S)
    protected void whenAccountCreatorCreateInstrument_thenCreatedInstrumentAppearInInstrumentList(VenueTypeDto venueType) {
        super.whenAccountCreatorCreateInstrument_thenCreatedInstrumentAppearInInstrumentList(venueType);
    }

    @Test
    protected void whenNonAdministratorCreateClientSideInstrument_thenAccessDeniedIsReturned() {

        CreateInstrumentDto instrumentToCreateDto = InstrumentFactory.createRandomForexInstrument(VenueTypeDto.CLIENT, VenueAccountConstants.MOCK_VENUE);

        RestClientException exception = assertThrows(RestClientException.class, () -> managerGql.instrument().create(instrumentToCreateDto));
        assertThat(exception.getMessage())
            .contains(ErrorMsg.ACCESS_DENIED);
    }

    @Test
    void creatingInstrumentEmitsEventLogEvent() {

        CreateInstrumentDto instrumentToCreateDto = InstrumentFactory.createRandomForexInstrument(VenueTypeDto.CLIENT, VenueAccountConstants.MOCK_VENUE);
        accountCreatorAdminGql.instrument().create(instrumentToCreateDto);

        EventLogEventDTO eventLogEvent = accountCreatorAdminGql.eventLog().await("instrumentCreateRequestStatus", instrumentToCreateDto.correlationObject());
        EventLogEventSoftAssert.assertThat(eventLogEvent)
            .statusIs(EventLogEventDTO.EventLogStatus.SUCCESS)
            .clientIdIs(accountCreatorAdminGql.getClientId())
            .eventTypeIs("instrumentCreateRequestStatus")
            .messageContains("Successfully created instrument")
            .correlationObjectIs(instrumentToCreateDto.correlationObject())
            .assertAll();
    }
}
