package io.wyden.booking.domain.ledgerentry;

import io.wyden.booking.domain.common.JpaRepository;
import io.wyden.booking.interfaces.rest.RequestModel.LedgerEntrySearch;

import java.math.BigDecimal;
import java.util.Collection;

public interface LedgerEntryRepository extends JpaRepository<LedgerEntry, Long> {

    long countByProperties(LedgerEntrySearch request);

    long countByProperties(LedgerEntrySearch request, String after);

    Collection<LedgerEntry> findByProperties(LedgerEntrySearch request);

    Collection<LedgerEntry> findByReservationRef(String reservationRef);

    void deleteByProperties(LedgerEntrySearch request);

    BigDecimal sumReservationsByInstrumentAndReferenceAndReservationRef(String currency, String portfolioId, String accountId, String reservationRef);
}
