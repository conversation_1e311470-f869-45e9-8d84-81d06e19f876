package io.wyden.clob.gateway.service.tracking;

import io.micrometer.common.util.StringUtils;
import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.fsm.OrderService;

public class OrderUuidResolver {
    public static String resolveOrderUuid(OrderIdentificationTracker orderIdentificationTracker, MatchingEngineEvent.PlaceOrder placeOrder) {
        return resolveOrderUuid(orderIdentificationTracker, placeOrder.orderUuid(), placeOrder.orderId());
    }

    public static String resolveOrderUuid(OrderIdentificationTracker orderIdentificationTracker, MatchingEngineEvent.TakerTradeEvent takerTradeEvent) {
        return resolveOrderUuid(orderIdentificationTracker, takerTradeEvent.takerOrderUuid(), takerTradeEvent.takerOrderId());
    }

    public static String resolveOrderUuid(OrderIdentificationTracker orderIdentificationTracker, MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade) {
        return resolveOrderUuid(orderIdentificationTracker, makerTrade.makerOrderUuid(), makerTrade.makerOrderId());
    }

    public static String resolveOrderUuid(OrderIdentificationTracker orderIdentificationTracker, MatchingEngineEvent.CancelOrder cancelOrder) {
        return resolveOrderUuid(orderIdentificationTracker, cancelOrder.orderUuid(), cancelOrder.orderId());
    }

    private static String resolveOrderUuid(OrderIdentificationTracker orderIdentificationTracker, String orderUuid, long orderId) {
        if (StringUtils.isNotEmpty(orderUuid)) {
            return orderUuid;
        }

        return orderIdentificationTracker.findOrderId(orderId)
            .orElseThrow(() -> new IllegalArgumentException("Order not found for matchingEngineOrderId: " + orderId));
    }
}
