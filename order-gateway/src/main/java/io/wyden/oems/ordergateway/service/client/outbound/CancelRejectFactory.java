package io.wyden.oems.ordergateway.service.client.outbound;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.published.client.ClientCancelRejectResponseTo;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.client.Result;
import io.wyden.published.common.Metadata;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.UUID;
import javax.annotation.Nullable;

import static io.wyden.published.client.ClientCancelRejectResponseTo.CANCEL_REJECT_RESPONSE_TO_UNSPECIFIED;
import static io.wyden.published.client.ClientCancelRejectResponseTo.ORDER_CANCEL_REPLACE_REQUEST;
import static io.wyden.published.client.ClientCancelRejectResponseTo.ORDER_CANCEL_REQUEST;

@Component
public class CancelRejectFactory {

    public static ClientResponse cancelReject(ClientRequest clientRequest, String reason) {
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        Metadata metadata = Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setRequestId(clientRequest.getClOrderId())
            .setRequesterId(clientRequest.getMetadata().getRequesterId())
            .setCreatedAt(now)
            .build();


        return ClientResponse.newBuilder()
            .setMetadata(metadata)
            .setOrderStatus(ClientOrderStatus.REJECTED)
            .setResponseType(ClientResponseType.CANCEL_REJECT)
            .setOrderId(clientRequest.getOrderId())
            .setOrigOrderId(clientRequest.getOrigOrderId())
            .setClOrderId(clientRequest.getClOrderId())
            .setClientId(clientRequest.getClientId())
            .setCancelRejectResponseTo(resolveCancelRejectResponseTo(clientRequest))
            .setReason(reason)
            .setRequestResult(Result.OEMS_CANCEL_ERROR)
            .setTimestamp(now)
            .setTif(clientRequest.getTif())
            .setCurrency(clientRequest.getCurrency())
            .setExpireTime(clientRequest.getExpireTime())
            .setRequest(clientRequest)
            .build();
    }

    public static ClientResponse cancelReject(ClientRequest clientRequest, ClientOrderState orderState, @Nullable String reason) {
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        Metadata metadata = Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setRequestId(clientRequest.getClOrderId())
            .setRequesterId(clientRequest.getMetadata().getRequesterId())
            .setCreatedAt(now)
            .build();

        return ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.CANCEL_REJECT)
            .setMetadata(metadata)
            .setOrderId(clientRequest.getOrderId())
            .setOrigOrderId(clientRequest.getOrigOrderId())
            .setClOrderId(clientRequest.getClOrderId())
            .setClientId(orderState.getOrder().getClientId())
            .setOrigClOrderId(orderState.getOrder().getClOrderId())
            .setCancelRejectResponseTo(resolveCancelRejectResponseTo(clientRequest))
            .setReason(ObjectUtils.firstNonNull(reason, "null"))
            .setRequestResult(Result.OEMS_CANCEL_ERROR)
            .setTimestamp(now)
            .setOrderStatus(orderState.getCurrentStatus())
            .setTif(clientRequest.getTif())
            .setCurrency(clientRequest.getCurrency())
            .setExpireTime(clientRequest.getExpireTime())
            .setRequest(clientRequest)
            .build();
    }

    public static ClientResponse rejectNoLiveOrderWithGivenIds(ClientRequest clientRequest) {
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        Metadata metadata = Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setRequestId(clientRequest.getClOrderId())
            .setRequesterId(clientRequest.getMetadata().getRequesterId())
            .setCreatedAt(now)
            .build();

        ClientResponse.Builder cancelReject = ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.CANCEL_REJECT)
            .setMetadata(metadata)
            .setClOrderId(clientRequest.getClOrderId())
            .setClientId(clientRequest.getClientId())
            .setOrigClOrderId(clientRequest.getOrigClOrderId())
            .setRequestResult(Result.OEMS_CANCEL_ERROR_UNKNOWN_ORDER)
            .setCancelRejectResponseTo(resolveCancelRejectResponseTo(clientRequest))
            .setOrderStatus(ClientOrderStatus.REJECTED)
            .setTimestamp(now)
            .setTif(clientRequest.getTif())
            .setCurrency(clientRequest.getCurrency())
            .setExpireTime(clientRequest.getExpireTime())
            .setRequest(clientRequest);

        if (!clientRequest.getOrigOrderId().isBlank()) {
            cancelReject.setOrigOrderId(clientRequest.getOrigOrderId());
        }
        if (!clientRequest.getOrderId().isBlank()) {
            cancelReject.setOrderId(clientRequest.getOrderId());
        }

        // todo in .setText() we could specify which fields are missing exactly

        return cancelReject.build();
    }

    public static ClientResponse connectorNotAvailableCancelFailed(ClientRequest clientCancelRequest, ClientOrderState orderState) {
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        Metadata metadata = Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setRequestId(clientCancelRequest.getMetadata().getRequestId())
            .setRequesterId(clientCancelRequest.getMetadata().getRequesterId())
            .setCreatedAt(now)
            .build();

        return ClientResponse.newBuilder()
            .setResponseType(ClientResponseType.CANCEL_REJECT)
            .setMetadata(metadata)
            .setCancelRejectResponseTo(ORDER_CANCEL_REQUEST)
            .setOrderId(clientCancelRequest.getOrderId())
            .setOrigOrderId(clientCancelRequest.getOrigOrderId())
            .setClOrderId(clientCancelRequest.getClOrderId())
            .setRequestResult(Result.CONNECTOR_UNAVAILABLE)
            .setOrderStatus(orderState.getCurrentStatus())
            .setTimestamp(now)
            .setTif(clientCancelRequest.getTif())
            .setCurrency(orderState.getOrder().getCurrency())
            .setExpireTime(clientCancelRequest.getExpireTime())
            .setRequest(clientCancelRequest)
            .build();
    }

    public static ClientResponse connectorNotAvailableCancelFailed(String pendingCancelClOrderId, String pendingCancelReplaceClOrderId,
                                                                   String requesterId, ClientOrderState orderState) {
        ClientCancelRejectResponseTo clientCancelRejectResponseTo;
        String clOrderId;
        if (!pendingCancelClOrderId.isBlank()) {
            clientCancelRejectResponseTo = ORDER_CANCEL_REQUEST;
            clOrderId = pendingCancelClOrderId;
        } else {
            clientCancelRejectResponseTo = ORDER_CANCEL_REPLACE_REQUEST;
            clOrderId = pendingCancelReplaceClOrderId;
        }

        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        Metadata metadata = Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setRequestId(clOrderId)
            .setRequesterId(requesterId)
            .setCreatedAt(now)
            .build();

        ClientRequest clientRequest = orderState.getOrder();
        return ClientResponse.newBuilder()
            .setMetadata(metadata)
            .setResponseType(ClientResponseType.CANCEL_REJECT)
            .setCancelRejectResponseTo(clientCancelRejectResponseTo)
            .setOrigOrderId(clientRequest.getOrderId())
            .setClOrderId(clOrderId)
            .setRequestResult(Result.CONNECTOR_UNAVAILABLE)
            .setOrderStatus(orderState.getCurrentStatus())
            .setTimestamp(now)
            .setTif(clientRequest.getTif())
            .setCurrency(clientRequest.getCurrency())
            .setExpireTime(clientRequest.getExpireTime())
            .setRequest(clientRequest)
            .build();
    }

    private static @NotNull ClientCancelRejectResponseTo resolveCancelRejectResponseTo(ClientRequest clientRequest) {
        return clientRequest.getRequestType().equals(ClientRequestType.CANCEL) ? ORDER_CANCEL_REQUEST :
            clientRequest.getRequestType().equals(ClientRequestType.CANCEL_REPLACE) ? ORDER_CANCEL_REPLACE_REQUEST :
                CANCEL_REJECT_RESPONSE_TO_UNSPECIFIED;
    }
}
