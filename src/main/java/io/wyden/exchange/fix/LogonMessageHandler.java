package io.wyden.exchange.fix;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import quickfix.FieldNotFound;
import quickfix.RejectLogon;
import quickfix.SessionID;
import quickfix.field.Password;
import quickfix.field.RawData;
import quickfix.field.Username;
import quickfix.fix44.Logon;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

@Component
public class LogonMessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogonMessageHandler.class);
    public static final Map<SessionID, Integer> FILLS_COUNT = new ConcurrentHashMap<>();
    public static final Set<SessionID> OTC_BROKERS = new CopyOnWriteArraySet<>();

    void onLogon(Logon message, SessionID sessionId) throws RejectLogon {
        String apiKeyId;
        String apiSecret;
        try {
            apiKeyId = message.get(new Username()).getValue();
            apiSecret = message.get(new Password()).getValue();
            parseRawData(message, sessionId);
        } catch (FieldNotFound e) {
            LOGGER.error(String.valueOf(e));
            throw new RejectLogon("Logon failed due to missing Username or Password tag.");
        }
    }
    // TODO authenticate the received credentials

    private void parseRawData(Logon message, SessionID sessionId) {
        try {
            FILLS_COUNT.remove(sessionId);
            OTC_BROKERS.remove(sessionId);
            if (message.isSet(new RawData())) {
                String[] parameters = message.get(new RawData()).getValue().split(";");
                for (String parameter : parameters) {
                    if ("otcBroker".equals(parameter)) {
                        OTC_BROKERS.add(sessionId);
                    }
                    if (parameter.startsWith("fillsCount_")) {
                        String[] data = parameter.split("_");
                        if (data.length == 2 && data[0].equalsIgnoreCase("fillsCount")) {
                            FILLS_COUNT.put(sessionId, Integer.parseInt(data[1]));
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("Parsing raw data failed for message {}", message, e);
        }
    }

}
