package io.wyden.apiserver.rest.marketdata.model;

import io.wyden.published.client.ClientSide;
import io.wyden.published.marketdata.Ask;
import io.wyden.published.marketdata.Bid;
import io.wyden.published.marketdata.BidAskQuote;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.published.marketdata.OTCOrderBook;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import io.wyden.published.marketdata.Trade;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import static io.wyden.apiserver.rest.utils.ProtobufUtils.protoStringToInteger;

public class MarketDataParser {

    public static L1Event parseL1Event(MarketDataIdentifier identifier, Ask ask) {
        return new L1Event(
            new MarketDataIdentifierDTO(identifier),
            null,
            null,
            NumberUtils.createBigDecimal(ask.getPrice()),
            NumberUtils.createBigDecimal(ask.getSize()),
            null,
            null,
            null,
            null,
            MarketDataTypeDTO.ASK);
    }

    public static L1Event parseL1Event(MarketDataIdentifier identifier, Bid bid) {
        return new L1Event(
            new MarketDataIdentifierDTO(identifier),
            NumberUtils.createBigDecimal(bid.getPrice()),
            NumberUtils.createBigDecimal(bid.getSize()),
            null,
            null,
            null,
            null,
            null,
            null,
            MarketDataTypeDTO.BID);
    }

    public static L1Event parseL1Event(MarketDataIdentifier identifier, BidAskQuote bidAsk) {
        return new L1Event(
            new MarketDataIdentifierDTO(identifier),
            NumberUtils.createBigDecimal(bidAsk.getBidPrice()),
            NumberUtils.createBigDecimal(bidAsk.getBidSize()),
            NumberUtils.createBigDecimal(bidAsk.getAskPrice()),
            NumberUtils.createBigDecimal(bidAsk.getAskSize()),
            null,
            null,
            null,
            null,
            MarketDataTypeDTO.BIDASK);
    }

    public static L1Event parseL1Event(MarketDataIdentifier identifier, Trade trade) {
        return new L1Event(
            new MarketDataIdentifierDTO(identifier),
            null,
            null,
            null,
            null,
            NumberUtils.createBigDecimal(trade.getVol().isEmpty() ? null : trade.getVol()),
            NumberUtils.createBigDecimal(trade.getLastPrice()),
            NumberUtils.createBigDecimal(trade.getLastSize()),
            trade.getSide(),
            MarketDataTypeDTO.TRADE);
    }

    public static L1Event buildL1Tick(L1Event l1Event1, L1Event l1Event2) {
        MarketDataIdentifierDTO identifierDTO = ObjectUtils.firstNonNull(l1Event2.identifier(), l1Event1.identifier());
        BigDecimal bidPrice = ObjectUtils.firstNonNull(l1Event2.bidPrice(), l1Event1.bidPrice());
        BigDecimal bidSize = ObjectUtils.firstNonNull(l1Event2.bidSize(), l1Event1.bidSize());
        BigDecimal askPrice = ObjectUtils.firstNonNull(l1Event2.askPrice(), l1Event1.askPrice());
        BigDecimal askSize = ObjectUtils.firstNonNull(l1Event2.askSize(), l1Event1.askSize());
        BigDecimal vol = ObjectUtils.firstNonNull(l1Event2.vol(), l1Event1.vol());
        BigDecimal lastPrice = ObjectUtils.firstNonNull(l1Event2.lastPrice(), l1Event1.lastPrice());
        BigDecimal lastSize = ObjectUtils.firstNonNull(l1Event2.lastSize(), l1Event1.lastSize());
        ClientSide side = ObjectUtils.firstNonNull(l1Event2.side(), l1Event1.side());
        return new L1Event(identifierDTO, bidPrice, bidSize, askPrice, askSize, vol, lastPrice, lastSize, side, MarketDataTypeDTO.TICK);
    }

    public static OrderBookEvent parseOrderBookEvent(MarketDataIdentifier identifier, OrderBook orderBook) {
        MarketDataIdentifierDTO marketDataIdentifierDTO = new MarketDataIdentifierDTO(identifier);

        Map<String, OrderBookLevelDTO> bids = orderBook.getBidsMap().entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> new OrderBookLevelDTO(
                NumberUtils.createBigDecimal(entry.getValue().getPrice()),
                NumberUtils.createBigDecimal(entry.getValue().getAmount()),
                protoStringToInteger(entry.getValue().getCount()))
            ));

        Map<String, OrderBookLevelDTO> asks = orderBook.getAsksMap().entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> new OrderBookLevelDTO(
                NumberUtils.createBigDecimal(entry.getValue().getPrice()),
                NumberUtils.createBigDecimal(entry.getValue().getAmount()),
                protoStringToInteger(entry.getValue().getCount()))
            ));

        @Nullable OrderBookLevelDTO topBid = isNotEmpty(orderBook.getTopBid()) ? new OrderBookLevelDTO(
            NumberUtils.createBigDecimal(orderBook.getTopBid().getPrice()),
            NumberUtils.createBigDecimal(orderBook.getTopBid().getAmount()),
            protoStringToInteger(orderBook.getTopBid().getCount())) : null;

        @Nullable OrderBookLevelDTO topAsk = isNotEmpty(orderBook.getTopAsk()) ? new OrderBookLevelDTO(
            NumberUtils.createBigDecimal(orderBook.getTopAsk().getPrice()),
            NumberUtils.createBigDecimal(orderBook.getTopAsk().getAmount()),
            protoStringToInteger(orderBook.getTopAsk().getCount())) : null;

        return new OrderBookEvent(marketDataIdentifierDTO, bids, asks, topBid, topAsk);
    }

    public static OTCOrderBookEvent parseOTCOrderBookEvent(MarketDataIdentifier identifier, OTCOrderBook orderBook) {
        MarketDataIdentifierDTO marketDataIdentifierDTO = new MarketDataIdentifierDTO(identifier);

        Map<String, OrderBookLevelDTO> bids = orderBook.getBidsMap().entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> new OrderBookLevelDTO(
                NumberUtils.createBigDecimal(entry.getValue().getPrice()),
                NumberUtils.createBigDecimal(entry.getValue().getAmount()),
                protoStringToInteger(entry.getValue().getCount()))
            ));

        Map<String, OrderBookLevelDTO> asks = orderBook.getAsksMap().entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> new OrderBookLevelDTO(
                NumberUtils.createBigDecimal(entry.getValue().getPrice()),
                NumberUtils.createBigDecimal(entry.getValue().getAmount()),
                protoStringToInteger(entry.getValue().getCount()))
            ));

        @Nullable OrderBookLevelDTO topBid = isNotEmpty(orderBook.getTopBid()) ? new OrderBookLevelDTO(
            NumberUtils.createBigDecimal(orderBook.getTopBid().getPrice()),
            NumberUtils.createBigDecimal(orderBook.getTopBid().getAmount()),
            protoStringToInteger(orderBook.getTopBid().getCount())) : null;

        @Nullable OrderBookLevelDTO topAsk = isNotEmpty(orderBook.getTopAsk()) ? new OrderBookLevelDTO(
            NumberUtils.createBigDecimal(orderBook.getTopAsk().getPrice()),
            NumberUtils.createBigDecimal(orderBook.getTopAsk().getAmount()),
            protoStringToInteger(orderBook.getTopAsk().getCount())) : null;

        return new OTCOrderBookEvent(marketDataIdentifierDTO, bids, asks, topBid, topAsk);
    }

    private static boolean isNotEmpty(OrderBookLevel level) {
        return !level.getPrice().isBlank()
            && !level.getCount().isBlank()
            && !level.getAmount().isBlank();
    }
}
