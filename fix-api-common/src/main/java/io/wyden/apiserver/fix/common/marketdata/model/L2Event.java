package io.wyden.apiserver.fix.common.marketdata.model;

import java.util.Map;

import static io.wyden.apiserver.fix.common.marketdata.model.MarketDataTypeDTO.L2;

public record L2Event(MarketDataIdentifierDTO identifier,
                      Map<String, OrderBookLevelDTO> bids, // bids map per price
                      Map<String, OrderBookLevelDTO> asks, // asks map per price
                      OrderBookLevelDTO topBid,
                      OrderBookLevelDTO topAsk) implements MarketDataEvent {
    @Override
    public MarketDataTypeDTO marketDataType() {
        return L2;
    }
}
