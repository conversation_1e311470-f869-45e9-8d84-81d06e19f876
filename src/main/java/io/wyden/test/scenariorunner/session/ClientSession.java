package io.wyden.test.scenariorunner.session;

import io.qameta.allure.Step;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.test.scenariorunner.data.refdata.Currency;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.data.trading.SimpleOrderFactory;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.fixclient.FixTradingActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;
import java.util.function.Predicate;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * This class presents session of user against specific venue account with default traded portfolio:<br>
 * <li>contain methods to send specific orders with less arguments then {@link ClientRequest} itself</li>
 * <li>can archive sent order to use its fields for cancel/cancel replace operations</li>
 */
public class ClientSession {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientSession.class);

    public static final String ENGINE_VENUE_ACCOUNT = VenueAccountConstants.BROKER_DESK_EXECUTION_TARGET;
    public static final String ENGINE_STREET_VENUE_ACCOUNT = VenueAccountConstants.MOCK_STREET_VENUE_ACCOUNT;
    public static final String BASE_CURRENCY = Currency.DOGE.name();
    public static final String QUOTE_CURRENCY = Currency.USD.name();
    public static final String HEDGING_TICKER = BASE_CURRENCY + QUOTE_CURRENCY;

    private final ClientActor clientActor;
    private final String clientId;
    private final String account;
    private final String portfolio;

    private String originalClientOrderId;
    private String clientOrderId;
    private ClientRequest originalOrder;
    private ClientRequest order;
    private String originalOrderId;
    private String orderId;

    private String cancelClientOrderId;
    private ClientResponse cancelReject;
    private ClientResponse executionReport;

    private final SimpleOrderFactory orderFactory;

    public ClientSession(ClientActor clientActor, String account, String portfolio) {
        this.clientActor = clientActor;
        this.account = account;
        this.portfolio = portfolio;
        this.clientId = clientActor.getClientId();
        this.orderFactory = new SimpleOrderFactory(clientId, account, portfolio);
    }

    public String getClientOrderId() {
        return clientOrderId;
    }

    public String getCancelClientOrderId() {
        return cancelClientOrderId;
    }

    public String getOriginalClientOrderId() {
        return originalClientOrderId;
    }

    public String getClientId() {
        return clientId;
    }

    public void sendStreetLimitOrderWithReusedClientOrderId(double quantity) {
        order = orderFactory.defaultStreetLimitOrder(quantity)
            .toBuilder()
            .setClOrderId(this.clientOrderId)
            .build();
        clientActor.trading().sendOrder(order);
    }

    public void sendDefaultLimitOrder(double quantity, String instrumentId) {
        sendOrder(orderFactory.defaultLimitOrder(instrumentId, quantity));
    }

    public void sendDefaultLimitOrder(double quantity, String instrumentId, String price) {
        sendOrder(orderFactory.defaultLimitOrder(instrumentId, quantity, price, ClientTIF.GTC));
    }

    public void sendDefaultStreetLimitOrder(String price) {
        sendOrder(orderFactory.defaultStreetLimitOrder(price));
    }

    public void sendDefaultStreetLimitOrder(double quantity) {
        sendOrder(orderFactory.defaultStreetLimitOrder(quantity));
    }

    public void sendDefaultClientLimitOrder(ClientSide side, double quantity, String price) {
        sendOrder(orderFactory.defaultClientOrder(ClientOrderType.LIMIT, side, quantity, price, ""));
    }

    public void sendOrder(ClientOrderType orderType) {
        sendOrder(orderFactory.defaultOrder(orderType));
    }

    public void sendOrder(ClientOrderType orderType, ClientSide side, double quantity, String instrumentId, String price, String portfolioId) {
        sendOrder(orderFactory.defaultOrder(orderType, side, quantity, instrumentId, price, "", portfolioId));
    }

    public void sendClientMarketOrder(ClientSide side, double quantity) {
        sendOrder(orderFactory.defaultClientOrder(ClientOrderType.MARKET, side, quantity, "", ""));
    }

    public void sendMarketOrder(ClientSide side, double quantity, String instrumentId, String portfolioId) {
        sendOrder(orderFactory.defaultOrder(ClientOrderType.MARKET, side, quantity, instrumentId, ClientTIF.GTC, portfolioId));
    }

    public void sendDefaultLimitStreetOrder(double quantity, ClientTIF tif) {
        sendOrder(orderFactory.defaultStreetLimitOrder(quantity, tif));
    }

    public void sendOrder(ClientRequest order) {
        this.order = order;
        this.clientOrderId = order.getClOrderId();
        clientActor.trading().sendOrder(order);
    }

    public void sendCancel() {
        cancelClientOrderId = UUID.randomUUID().toString();
        originalOrderId = orderId;
        originalClientOrderId = clientOrderId;
        clientActor.trading().cancelOrder(clientOrderId, cancelClientOrderId, order.getSide());
    }

    public void sendForceCancel() {
        cancelClientOrderId = UUID.randomUUID().toString();
        originalOrderId = orderId;
        originalClientOrderId = clientOrderId;
        clientActor.trading().forceCancelOrder(clientOrderId, cancelClientOrderId, order.getSide());
    }

    public void sendCustomCancel(String clientOrderId, String cancelClientOrderId, ClientSide side) {
        clientActor.trading().cancelOrder(clientOrderId, cancelClientOrderId, side);
    }

    public void sendCancelReplace(double quantity) {
        sendCancelReplace(orderFactory.defaultStreetLimitOrder(quantity));
    }

    public void sendCancelReplace(double quantity, String instrumentId) {
        sendCancelReplace(orderFactory.defaultLimitOrder(instrumentId, quantity));
    }

    public void sendCancelReplace(double quantity, String instrumentId, String price) {
        sendCancelReplace(orderFactory.defaultLimitOrder(instrumentId, quantity, price, ClientTIF.GTC));
    }

    public void sendCancelReplace(double quantity, ClientTIF tif) {
        sendCancelReplace(orderFactory.defaultStreetLimitOrder(quantity, tif));
    }

    public void sendCancelReplace(double quantity, String instrumentId, String price, ClientSide side) {
        sendCancelReplace(orderFactory.defaultLimitOrder(instrumentId, side, quantity, price, ClientTIF.GTC));
    }

    public void sendClientCancelReplace(double quantity, String price, ClientSide side) {
        sendCancelReplace(orderFactory.defaultClientOrder(ClientOrderType.LIMIT, side, quantity, price, ""));
    }

    public void sendCancelReplace(double quantity, String price, ClientOrderType orderType) {
        ClientRequest cancelReplaceOrder = orderFactory
            .defaultOrder(orderType, this.order.getSide(), quantity, this.order.getInstrumentId(), price, "", ClientTIF.GTC, this.order.getPortfolioId());
        sendCancelReplace(cancelReplaceOrder);
    }

    public void sendCancelReplace(ClientRequest cancelReplaceOrder) {
        archiveOrder();
        order = cancelReplaceOrder;
        clientOrderId = order.getClOrderId();
        clientActor.trading().cancelReplaceOrder(originalClientOrderId, order, order.getSide());
    }

    public ClientResponse receiveExecutionReport() {
        return receiveExecutionReport(er -> true);
    }

    public ClientResponse receiveExecutionReport(Predicate<ClientResponse> predicate) {
        executionReport = clientActor.awaitReceiveExecutionReport(predicate);
        if (executionReport.getExecType() == ClientExecType.CLIENT_EXEC_TYPE_NEW) {
            orderId = executionReport.getOrderId();
        }

        return executionReport;
    }

    @Step("Verify no more ERs")
    public void assertNoMoreExecutionReports() {
        clientActor.awaitNoMoreExecutionReports();
    }

    public void receiveCancelRejectAfterCancel() {
        receiveCustomCancelReject();
    }

    public void receiveCancelRejectAfterCancelReplace() {
        receiveCustomCancelReject();
        restoreOrder();
    }

    public void receiveCustomCancelReject() {
        cancelReject = clientActor.awaitReceiveOrderCancelReject();
    }

    public void sendOrderStatusRequest() {
        clientActor.trading().getOrderStatus(clientId, clientOrderId, orderId, order.getSide());
    }

    public ClientRequest getOrder() {
        return order;
    }

    public ClientRequest getOriginalOrder() {
        return originalOrder;
    }

    public ClientResponse getExecutionReport() {
        return executionReport;
    }

    public ClientResponse getCancelReject() {
        return cancelReject;
    }

    @Step("Verify ER execType={0}, orderStatus={1}")
    //TODO dkouzan https://algotrader.atlassian.net/browse/AC-2334 use ClientResponseSoftAssert instead and remove all assertions from ClientSession
    public void assertExecutionReportStatus(ClientExecType execType, ClientOrderStatus orderStatus) {
        // execType not provided in GQL order states
        if (execType != null && !(clientActor instanceof GraphQLActor)) {
            assertThat(executionReport.getExecType()).isEqualTo(execType);
        }
        if (orderStatus != null) {
            assertThat(executionReport.getOrderStatus()).isEqualTo(orderStatus);
        }
    }

    @Step("Verify executionId not blank")
    public void assertExecutionIdNotBlank() {
        if (clientActor instanceof GraphQLActor) {
            LOGGER.info("executionId verification skipped since not provided in GQL order states");
            return;
        }
        assertThat(executionReport.getExecutionId()).isNotBlank();
    }

    @Step("Verify venueExecutionId={0}")
    public void assertVenueExecutionId(String venueExecutionId) {
        if (clientActor instanceof GraphQLActor) {
            LOGGER.info("venueExecutionId verification skipped since not provided in GQL order states");
            return;
        }
        assertThat(executionReport.getVenueExecutionId()).isEqualTo(venueExecutionId);
    }

    public ClientOrderStatus cancelRejectOrderStatus() {
        return cancelReject.getOrderStatus();
    }

    public String cancelRejectCxlRejectReason() {
        return cancelReject.getReason();
    }

    @Step("Verify ER origClOrderId")
    public void assertExecutionReportOrigClOrderId() {
        //TODO remove after fix @Disabled origClOrderId is not provided in order states on cancel replace
        if (clientActor instanceof GraphQLActor) {
            return;
        }
        assertThat(executionReport.getOrigClOrderId()).isEqualTo(originalClientOrderId);
    }

    @Step("Verify ER clientOrderId={0}")
    public void assertExecutionReportClientOrderId(String clientOrderId) {
        assertThat(executionReport.getClOrderId()).isEqualTo(clientOrderId);
    }

    @Step("Verify cancel reject order original client order id")
    public void assertCancelRejectOrderOriginalClientId() {
        assertThat(cancelReject.getOrigClOrderId()).isEqualTo(clientOrderId);
    }

    @Step("Verify ER quantities orderQty={0}, cumQty={1}, leavesQty={2}, lastQty={3}")
    public void assertExecutionReportQuantities(double orderQty, double cumQty, double leavesQty, double lastQty) {
        assertThat(Double.parseDouble(executionReport.getOrderQty())).as("orderQty").isEqualTo(orderQty);
        assertThat(Double.parseDouble(executionReport.getCumQty())).as("cumQty").isEqualTo(cumQty);
        assertThat(Double.parseDouble(executionReport.getLeavesQty())).as("leavesQty").isEqualTo(leavesQty);
        assertThat(Double.parseDouble(executionReport.getLastQty())).as("lastQty").isEqualTo(lastQty);
    }

    @Step("Verify ER side={0}")
    public void assertExecutionReportSide(ClientSide side) {
        if (clientActor instanceof FixTradingActor && side == ClientSide.REDUCE_SHORT) {
            // FIX Protocol doesn't have a notion of "REDUCE_SHORT", but it is an equivalent of "BUY" so let's assert that instead:
            side = ClientSide.BUY;
        }
        assertThat(executionReport.getSide().toString()).isEqualTo(side.toString());
    }

    @Step("Verify ER text={0}")
    public void assertExecutionReportText(String text) {
        assertThat(executionReport.getText()).isEqualTo(text);
    }

    @Step("Verify expire time")
    public void assertExpireTime() {
        String expectedExpireTime = order.getExpireTime();
        String actualExpireTime = executionReport.getExpireTime();
        if (clientActor instanceof GraphQLActor) {
            String epochMillis = executionReport.getExpireTime();
            actualExpireTime = DateUtils.toFixUtcTime(DateUtils.epochMillisToZonedDateTime(epochMillis));
        }
        assertThat(actualExpireTime)
            .as("expireTime is incorrect")
            .isEqualTo(expectedExpireTime);
    }

    public void archiveOrder() {
        this.originalClientOrderId = this.clientOrderId;
        this.originalOrder = this.order;
        this.originalOrderId = this.orderId;
        this.clientOrderId = null;
        this.order = null;
        this.orderId = null;
    }

    public void restoreOrder() {
        this.clientOrderId = this.originalClientOrderId;
        this.order = this.originalOrder;
        this.orderId = this.originalOrderId;
        this.originalClientOrderId = null;
        this.originalOrder = null;
        this.originalOrderId = null;
    }

    public void clear() {
        this.clientOrderId = null;
        this.order = null;
        this.orderId = null;
    }
}
