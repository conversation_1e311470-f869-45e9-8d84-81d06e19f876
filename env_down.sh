#!/bin/bash

set -e

usage() {
  echo "Usage: $0 [-v] [-p bitstamp] [-d oracle_mac]"
  echo
  echo "Shut down whole environment."
  echo
  echo "Options:"
  echo "  -h    Display help"
  echo "  -p    Additional profiles to stop beside all"
  echo "  -v    Delete all volumes"
  echo "  -d    Used database, default is postgres other options: oracle, oracle_mac"
}

PROFILES=all
OPTS=
DATABASE=postgres
while getopts "hvp:d:" o; do
  case "${o}" in
    h)
      usage
      exit 0
      ;;
    p)
      PROFILES="${PROFILES},${OPTARG}"
      ;;
    d)
      DATABASE="${OPTARG}"
      ;;
    v)
      OPTS="--volumes"
      ;;
    :)
      echo "Error: -${OPTARG} requires and argument."
      usage
      exit 255
      ;;
    *)
      usage
      exit 255
      ;;
  esac
done

echo "Stopping database: ${DATABASE}"

if [[ $DATABASE = "oracle" ]] ; then
  OVERRIDES="-f docker-compose-oracle.yml"
elif [[ $DATABASE = "oracle_mac" ]] ; then
  OVERRIDES="-f docker-compose-oracle.yml -f docker-compose-oracle-mac.yml"
else
  OVERRIDES=""
fi

echo "Shutting down whole environment for profiles: ${PROFILES} with option ${OPTS}"
COMPOSE_PROFILES="${PROFILES}" docker compose -f docker-compose.yml ${OVERRIDES} down ${OPTS}
