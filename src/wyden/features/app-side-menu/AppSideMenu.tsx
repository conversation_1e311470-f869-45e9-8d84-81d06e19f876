import HorizontalIcon from '@mui/icons-material/BorderHorizontal';
import VerticalIcon from '@mui/icons-material/BorderVertical';
import SettingsIcon from '@mui/icons-material/Settings';
import { Tabs } from '@mui/material';
import { useNestReact } from '@nest-react/hooks/useNestReact';
import { Menu } from '@ui/Menu';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { useMenuToggle } from '@ui/useMenuToggle';
import { ReactComponent as BreakingNewsIcon } from '@wyden/assets/breaking-news.svg';
import { ENTITLEMENTS, PREDEFINED_GROUPS, PREDEFINED_WORKSPACES_IDS } from '@wyden/constants';
import { useWorkspaces } from '@wyden/features/useWorkspaces';
import { useFullscreenMode } from '@wyden/hooks/useFullScreenMode';
import { usePermissions } from '@wyden/hooks/usePermissions';
import { horizontalEmptyWorkspace } from '@wyden/layout-configurations/horizontalEmpty';
import { verticalEmptyWorkspace } from '@wyden/layout-configurations/verticalEmpty';
import { Resource, Scope } from '@wyden/services/graphql/generated/graphql';
import { selectAuthenticatedUser, useAuthStore } from '@wyden/services/keycloak/auth/useAuthStore';
import { getSpacing } from '@wyden/utils/styles';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from '../Routes';
import { WorkspaceTab } from '../app-header/WorkspaceTab';
import { useEntitlements } from '../entitlements/useEntitlements';

export const AppSideMenu = () => {
  const { t } = useTranslation();
  const { user } = useAuthStore(selectAuthenticatedUser);
  const {
    workspaces,
    goToWorkspace,
    isSettingsPage,
    isRiskPage,
    createWorkspace,
    currentWorkspace,
  } = useWorkspaces();
  const { anchorEl, closeMenu } = useMenuToggle();
  const workspacesNames = workspaces?.map((ws) => ws.name) || [];
  const { exitFullScreenMode } = useFullscreenMode();
  const { app } = useNestReact();
  const { checkIfPermitted } = usePermissions();
  const { checkIfEntitled } = useEntitlements();

  const navigate = useNavigate();
  const location = useLocation();

  const goToSettings = () => {
    navigate(ROUTES.GENERAL_SETTINGS);
    closeMenu();
  };
  const goToRisk = () => {
    navigate(ROUTES.WORKSPACES + ROUTES.RISK.PTC);
    closeMenu();
  };

  const isWorkspaceDropdownOpen = Boolean(anchorEl && anchorEl.id === 'workspace-dropdown');

  const getNewDefaultWorkspaceName = (name = 'default', count = 0): string => {
    const newName = count > 0 ? `${name}-${count}` : name;

    if (workspacesNames.includes(newName)) {
      return getNewDefaultWorkspaceName(name, count + 1);
    }

    return newName;
  };

  const onWorkspaceChoose = (direction: 'horizontal' | 'vertical') => async () => {
    const workspace =
      direction === 'horizontal' ? horizontalEmptyWorkspace : verticalEmptyWorkspace;

    const newWorkspace = await createWorkspace({
      name: getNewDefaultWorkspaceName(),
      json: workspace,
    });

    if (newWorkspace) {
      goToWorkspace(newWorkspace);
    }

    closeMenu();
  };

  const canManageWorkspaces = user.groups?.includes(PREDEFINED_GROUPS.TRADER);

  const shouldShowTabs = (workspaces && workspaces.length > 1) || canManageWorkspaces;

  const settingWorkspaceIndex = workspaces.length;
  const isOnSettingsNav = location.pathname.includes('settings');

  const selectedWorkspaceIndex = workspaces?.findIndex(
    (workspace) => workspace.id === currentWorkspace?.id,
  );

  useEffect(() => {
    exitFullScreenMode();
    if (
      app.interactionManager &&
      location.pathname.includes(ROUTES.WORKSPACES) &&
      !location.pathname.includes(ROUTES.WORKSPACES + ROUTES.RISK.ROOT) &&
      !location.pathname.includes(ROUTES.WORKSPACES + ROUTES.SETTLEMENT.ROOT)
    ) {
      app.interactionManager.enabled = true;
    } else if (app.interactionManager) {
      app.interactionManager.enabled = false;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  return (
    <SideMenu>
      <WorkspaceTabs className="navigation-menu" data-testid="navigation-menu">
        {workspaces && workspaces.length > 0 && (
          <MenuTabsContainer>
            <Workspaces>
              {shouldShowTabs && (
                <StyledTabs
                  orientation="vertical"
                  value={isOnSettingsNav ? settingWorkspaceIndex : selectedWorkspaceIndex}
                >
                  {workspaces
                    .filter((workspace) => {
                      if (workspace.id === PREDEFINED_WORKSPACES_IDS.SETTLEMENT) {
                        return checkIfEntitled(ENTITLEMENTS['Settlement & Treasury Engine']);
                      }
                      return true;
                    })
                    .map((workspace) => (
                      <WorkspaceTab workspace={workspace} key={workspace.id} />
                    ))}
                  {checkIfPermitted({
                    resource: Resource.Risk,
                    scope: Scope.Read,
                  }) && (
                    <StyledMenuItem onClick={goToRisk}>
                      <StyledSettingsMenuItem
                        onClick={goToRisk}
                        $isActive={isRiskPage}
                        data-testid="risk-button"
                      >
                        <BreakingNewsIcon />
                        {t('header.dropdown.riskManagement')}
                      </StyledSettingsMenuItem>
                    </StyledMenuItem>
                  )}
                </StyledTabs>
              )}
              <Menu onClose={closeMenu} anchorEl={anchorEl} open={isWorkspaceDropdownOpen}>
                <StyledMenuItem onClick={onWorkspaceChoose('vertical')}>
                  <VerticalIcon />
                  {t('header.verticalEmpty')}
                </StyledMenuItem>
                <StyledMenuItem onClick={onWorkspaceChoose('horizontal')}>
                  <HorizontalIcon />
                  {t('header.horizontalEmpty')}
                </StyledMenuItem>
              </Menu>
            </Workspaces>
            <div>
              <StyledMenuItem onClick={goToSettings}>
                <StyledSettingsMenuItem
                  onClick={goToSettings}
                  $isActive={isSettingsPage}
                  data-testid="settings-button"
                >
                  <SettingsIcon />
                  {t('header.dropdown.settings')}
                </StyledSettingsMenuItem>
              </StyledMenuItem>
            </div>
          </MenuTabsContainer>
        )}
      </WorkspaceTabs>
    </SideMenu>
  );
};

const StyledMenuItem = styled('div')``;

const SideMenu = styled('div')`
  width: 100%;
  display: flex;
  position: relative;
`;

const Workspaces = styled('ul')`
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  width: 100%;
`;

const StyledTabs = styled(Tabs)`
  width: 100%;
  user-select: none;
`;

const MenuTabsContainer = styled('div')`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfaceSecondary};
  padding: 0 ${getSpacing(2)};
  height: calc(100% - 12px);
  justify-content: space-between;
`;

const WorkspaceTabs = styled('div')`
  max-width: 98px;

  .MuiTabs-indicator {
    display: none;
  }
`;

const StyledSettingsMenuItem = styled('div')<{ $isActive: boolean }>`
  width: 100%;
  display: flex;
  font-size: 11px;
  user-select: none;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 400ms linear;
  padding: ${getSpacing(3)} ${getSpacing(1)};
  color: ${({ theme, $isActive }) =>
    $isActive
      ? color[theme.palette.mode].textElementsTextPrimary
      : color[theme.palette.mode].textElementsTextSecondary};
  background-color: ${({ theme, $isActive }) =>
    $isActive ? color[theme.palette.mode].fillsElementsFillHover : 'transparent'};
  &:hover {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
    border-radius: 4px;
  }
`;
