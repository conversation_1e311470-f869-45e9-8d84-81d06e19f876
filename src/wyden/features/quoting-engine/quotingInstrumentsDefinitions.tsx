import {
  ConfigInstrumentValidationResult,
  ConfigValidationError,
  ErrorType,
  InstrumentQuotingConfiguration,
  InstrumentResponse,
} from '@wyden/services/graphql/generated/graphql';
import { buildObjectKeyValidator, getGenericField } from '../grid/utils';
import { ColDef, ICellRendererParams } from 'ag-grid-community';
import { InstrumentsIconRenderer } from '../instruments/renderers';
import { useQuotingEngineStore } from './useQuotingEngineStore';
import { Link } from 'react-router-dom';
import { styled } from '@ui/styled';
import i18n from '@wyden/i18n';
import { useTranslation } from 'react-i18next';
import { Tooltip } from '@mui/material';
import { ReactComponent as WarningIcon } from '@wyden/assets/errorExclamationMark.svg';
import { getSpacing } from '@wyden/utils/styles';
import { getInstrumentReadableIdentifier } from '@wyden/helpers/instrument';
import { color } from '@ui/theme/colors';

type InstrumentWithConfigurationAndValidation = InstrumentResponse & {
  configuration: InstrumentQuotingConfiguration | undefined;
  validation?: ConfigInstrumentValidationResult | null;
};

const validKey = buildObjectKeyValidator<InstrumentWithConfigurationAndValidation>();

export const NameRenderer = (
  data: ICellRendererParams<InstrumentWithConfigurationAndValidation>,
) => {
  const { setEditingInstrument } = useQuotingEngineStore();
  const { t } = useTranslation();

  const isActive = !data.data?.configuration?.deactivated;

  const setInstrumentToEdit = () => {
    data.data && setEditingInstrument(data.data);
  };

  return isActive ? (
    <Link onClick={setInstrumentToEdit} to="">
      {data.data?.baseInstrument?.symbol}
    </Link>
  ) : (
    <StyledInactive>
      <Link to="" onClick={setInstrumentToEdit}>
        {data.data?.baseInstrument?.symbol}
      </Link>
      <span>{t('common.inactive')}</span>
    </StyledInactive>
  );
};

const PrimaryInstrumentRenderer = (
  data: ICellRendererParams<InstrumentWithConfigurationAndValidation>,
) => {
  const configuration = data.data?.configuration;
  const source = configuration?.sourceConfigurations?.[0];

  return source?.sourceInstrument ? getInstrumentReadableIdentifier(source?.sourceInstrument) : '-';
};

const SecondaryInstrumentRenderer = (
  data: ICellRendererParams<InstrumentWithConfigurationAndValidation>,
) => {
  const configuration = data.data?.configuration;
  const source = configuration?.sourceConfigurations?.[0];

  return source?.conversionSourceInstrument
    ? getInstrumentReadableIdentifier(source?.conversionSourceInstrument)
    : '-';
};

const getValidationKey = (error: ConfigValidationError) => {
  return (
    {
      'quotingSource.sourceInstrumentId': {
        [ErrorType.Missing]: 'quotingEngine.asyncErrors.missingPrimaryInstrument',
        [ErrorType.Invalid]: 'quotingEngine.asyncErrors.invalidPrimaryInstrument',
      },
      quotingSources: {
        [ErrorType.Missing]: 'quotingEngine.asyncErrors.missingSecondaryInstrument',
        [ErrorType.Invalid]: 'quotingEngine.asyncErrors.invalidSources',
      },
    }[error.fieldName]?.[error.errorType] || ''
  );
};

const ValidationDataRenderer = (
  data: ICellRendererParams<InstrumentWithConfigurationAndValidation>,
) => {
  const { t } = useTranslation();
  const { isValid, errors } = data.data?.validation || {};

  const errorsToDisplay = errors?.map((error) => {
    return {
      message: getValidationKey(error) ? t(getValidationKey(error)) : error.errorMessage,
    };
  });

  const tooltipTitle = (
    <StyledErrors>
      {errorsToDisplay?.map((error, index) => <span key={index}>{error.message}</span>)}
    </StyledErrors>
  );
  return isValid === false ? (
    <Tooltip placement="left" title={tooltipTitle}>
      <div data-testid="error-marker">
        <WarningIcon />
      </div>
    </Tooltip>
  ) : (
    ''
  );
};

export const quotingInstrumentsDefinitions: ColDef<InstrumentWithConfigurationAndValidation>[] = [
  getGenericField('icon', {
    headerName: '',
    width: 24,
    cellRenderer: InstrumentsIconRenderer,
    sortable: false,
  }),
  getGenericField(validKey('baseInstrument.symbol'), {
    cellRenderer: NameRenderer,
    valueGetter: (params) =>
      params.data?.baseInstrument?.symbol + params.data?.configuration?.deactivated,
  }),
  getGenericField('validation', {
    headerName: '',
    cellRenderer: ValidationDataRenderer,
    sortable: false,
  }),
  getGenericField('primaryInstrument', {
    headerName: i18n.t('quotingEngine.instrumentConfig.primaryInstrument') as string,
    valueGetter: (params) => params.data?.configuration,
    cellRenderer: PrimaryInstrumentRenderer,
  }),
  getGenericField('secondaryInstrument', {
    headerName: i18n.t('quotingEngine.instrumentConfig.secondaryInstrument') as string,
    valueGetter: (params) => params.data?.configuration,
    cellRenderer: SecondaryInstrumentRenderer,
  }),
];

const StyledErrors = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
`;

const StyledInactive = styled('span')`
  display: flex;
  justify-content: space-between;
  padding-right: ${getSpacing(6)};
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;
