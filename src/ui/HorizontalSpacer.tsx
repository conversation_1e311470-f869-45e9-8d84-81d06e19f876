import { styled } from './styled';

type HorizontalSpacerProps = {
  space?: number;
  grow?: boolean;
};

export function HorizontalSpacer({ space = 1, grow = false }: HorizontalSpacerProps) {
  return <StyledSpacer space={space} grow={grow} />;
}

const StyledSpacer = styled('div')<{ space: number; grow: boolean }>`
  min-width: ${({ theme, space }) => theme.spacing(space)};
  min-width: ${({ theme, space }) => theme.spacing(space)};
  flex-grow: ${({ grow }) => (grow ? 1 : 0)};
  height: 100%;
  visibility: hidden;
  display: flex;
`;
