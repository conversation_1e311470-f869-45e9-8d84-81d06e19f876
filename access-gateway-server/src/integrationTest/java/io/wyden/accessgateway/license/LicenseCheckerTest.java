package io.wyden.accessgateway.license;

import ch.algotrader.resource.InvalidLicenseException;
import ch.algotrader.resource.ResourceManager;

import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.license.LicenseMapConfig;
import io.wyden.accessgateway.domain.license.LicenseState;
import io.wyden.accessgateway.testcontainers.TestContainerTestBase;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class LicenseCheckerTest extends TestContainerTestBase {

    @Autowired
    private IMap<String, LicenseState> licenseCache;

    @Mock
    private LicenseCheckScheduler licenseCheckScheduler;

    @Mock
    private ResourceManager resourceManagerMock;

    private static final String ENT1 = "ENT";
    private static final String ENT2 = "ENT";
    private static final String LICENSE_ID = "123444";
    private static final String LICENSE_KEY = "fake-license-key";

    @MockBean
    private LicenseChecker licenseChecker;

    private LicenseChecker realLicenseChecker;

    private MockedStatic<ResourceManager> mockedStatic;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        mockedStatic = Mockito.mockStatic(ResourceManager.class);
        mockedStatic.when(() -> ResourceManager.createInstanceNoShutDown(anyString()))
            .thenReturn(resourceManagerMock);

        when(resourceManagerMock.getLicenseId()).thenReturn(LICENSE_ID);
        when(resourceManagerMock.getEntitlements()).thenReturn(List.of(ENT1));

    }

    private void createLicenseChecker() throws InvalidLicenseException {
        realLicenseChecker = new LicenseChecker(
            licenseCache,
            licenseCheckScheduler,
            24,
            true,
            LICENSE_KEY
        );
    }

    @Test
    void shouldRunLicenseCheckOnStartupLicenseValid() throws InvalidLicenseException {
        // given
        doNothing().when(resourceManagerMock).verifyLicense();

        // when
        createLicenseChecker();

        // then
        LicenseState licenseState = licenseCache.get(LicenseMapConfig.LICENSE_MAP_KEY);
        assertNotNull(licenseState);
        assertEquals(LicenseState.LicenseStatus.VALID, licenseState.getStatus());
        assertEquals(LICENSE_ID, licenseState.getLicenseId());
        assertTrue(licenseState.getEntitlements().contains(ENT1));
    }

    @Test
    void shouldScheduleNextLicenseCheckOnLicenseValid() throws InvalidLicenseException {
        // given
        doNothing().when(resourceManagerMock).verifyLicense();

        // when
        createLicenseChecker();

        // then
        verify(licenseCheckScheduler).schedule(any());
    }

    @Test
    void shouldScheduleNextLicenseCheckOnLicenseNotValid() throws InvalidLicenseException {
        // given
        doThrow(new InvalidLicenseException("Invalid license"))
            .when(resourceManagerMock)
            .verifyLicense();

        // when
        createLicenseChecker();

        // then
        verify(licenseCheckScheduler).schedule(any());
    }

    @Test
    void shouldRunLicenseCheckOnStartupLicenseNotValid() throws InvalidLicenseException {
        // given
        doThrow(new InvalidLicenseException("Invalid license"))
            .when(resourceManagerMock)
            .verifyLicense();

        // when
        createLicenseChecker();

        // then
        LicenseState licenseState = licenseCache.get(LicenseMapConfig.LICENSE_MAP_KEY);
        assertNotNull(licenseState);
        assertEquals(LicenseState.LicenseStatus.NOT_VALID, licenseState.getStatus());
    }

    @Test
    void shouldUpdateEntitlementsOnForceUpdate() throws InvalidLicenseException {
        // given
        doNothing().when(resourceManagerMock).verifyLicense();

        // when
        createLicenseChecker();

        // given
        when(resourceManagerMock.getEntitlements()).thenReturn(List.of(ENT2));

        // when
        realLicenseChecker.updateLicenseState();


        // then
        LicenseState licenseState = licenseCache.get(LicenseMapConfig.LICENSE_MAP_KEY);
        assertNotNull(licenseState);
        assertEquals(LicenseState.LicenseStatus.VALID, licenseState.getStatus());
        assertEquals(LICENSE_ID, licenseState.getLicenseId());
        assertTrue(licenseState.getEntitlements().contains(ENT2));
    }

    @AfterEach
    void tearDown() {
        mockedStatic.close();
    }
}
