package io.wyden.accessgateway.referencedata;

import com.rabbitmq.client.AMQP;
import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.accessgateway.permissions.service.HazelcastPermissionFacade;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.tools.ProtobufUtils;
import io.wyden.published.referencedata.Grant;
import io.wyden.published.referencedata.PortfolioChangeEvent;
import io.wyden.published.referencedata.PortfolioChangeEventType;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.failureNeedsRequeue;

@Component
public class PortfolioChangeEventConsumer implements MessageConsumer<PortfolioChangeEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PortfolioChangeEventConsumer.class);

    private final HazelcastPermissionFacade hazelcastPermissionFacade;
    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange;

    public PortfolioChangeEventConsumer(RabbitIntegrator rabbitIntegrator,
                                        RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange,
                                        HazelcastPermissionFacade hazelcastPermissionFacade,
                                        @Value("${rabbitmq.reference-data-access-gateway-queue.portfolio-change-event}") final String queueName,
                                        @Value("${spring.application.name}") String consumerName) {
        this.portfolioChangeEventExchange = portfolioChangeEventExchange;
        this.rabbitIntegrator = rabbitIntegrator;
        this.hazelcastPermissionFacade = hazelcastPermissionFacade;
        this.queueName = queueName;
        this.consumerName = consumerName;
    }

    @PostConstruct
    void init() {
        declareQueue();
    }

    private void declareQueue() {
        RabbitQueue<PortfolioChangeEvent> queue = new RabbitQueueBuilder<PortfolioChangeEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(portfolioChangeEventExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", portfolioChangeEventExchange, queue);
        queue.attachConsumer(PortfolioChangeEvent.parser(), this);
    }

    @Override
    public ConsumptionResult consume(PortfolioChangeEvent portfolioChangeEvent, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming PortfolioChangeEvent message, {}", ProtobufUtils.shortDebugString(portfolioChangeEvent));
            if (portfolioChangeEvent.getPortfolioChangeEventType() == PortfolioChangeEventType.PORTFOLIO_CHANGE_EVENT_TYPE_CREATED) {
                String portfolioId = portfolioChangeEvent.getPortfolio().getId();
                String ownerClientId = portfolioChangeEvent.getOwnerClientId();
                Set<PermissionDto> permissionsToCreate = Permission.getDynamicPermissionsForPortfolio(portfolioId);

                LOGGER.info("Creating new permissions for portfolio with id, {}", portfolioId);
                hazelcastPermissionFacade.addClientPermissions(ownerClientId, permissionsToCreate);

                if (!portfolioChangeEvent.getGrantsList().isEmpty()) {
                    addAdditionalPermissions(portfolioChangeEvent.getGrantsList(), portfolioId);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Failed to consume incoming: %s, re-queueing...".formatted(portfolioChangeEvent), e);
            return failureNeedsRequeue();
        }

        return ConsumptionResult.consumed();
    }

    private void addAdditionalPermissions(List<Grant> grants, String portfolioId) {
        for (Grant grant : grants) {
            for (String groupName : grant.getGroupsList()) {
                PermissionDto groupPermission = new PermissionDto(Permission.Resources.PORTFOLIO, grant.getScope(), portfolioId);
                Set<PermissionDto> permissionsToCreate = Set.of(groupPermission);

                LOGGER.info("Creating additional permissions for group: {}, permissions: {}", groupName, groupPermission);
                hazelcastPermissionFacade.addGroupPermissions(groupName, permissionsToCreate);
            }
        }
    }
}
