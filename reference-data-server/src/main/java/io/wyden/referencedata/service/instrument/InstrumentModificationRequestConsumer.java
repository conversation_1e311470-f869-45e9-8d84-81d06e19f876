package io.wyden.referencedata.service.instrument;

import com.rabbitmq.client.AMQP;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.cloudutils.tools.ProtobufUtils;
import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.VenueType;
import io.wyden.published.referencedata.instrumentmodification.InstrumentModificationRequest;
import io.wyden.published.referencedata.instrumentmodification.InstrumentToModify;
import io.wyden.referencedata.mapper.InstrumentToModifyMapper;
import io.wyden.referencedata.service.EventLogEmitter;
import io.wyden.referencedata.util.InstrumentsUtil;
import io.wyden.referencedata.validator.InstrumentModificationRequestValidator;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;

import static io.wyden.cloudutils.tools.DateUtils.toIsoUtcTime;
import static io.wyden.referencedata.service.instrument.InstrumentChangeEventFactory.createErrorResponse;
import static io.wyden.referencedata.service.instrument.InstrumentChangeEventFactory.createSuccessResponse;

@Component
public class InstrumentModificationRequestConsumer implements MessageConsumer<InstrumentModificationRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstrumentModificationRequestConsumer.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<InstrumentModificationRequest> instrumentModificationRequestExchange;
    private final InstrumentModificationRequestValidator instrumentModificationRequestValidator;
    private final InstrumentChangeEventEmitter instrumentChangeEventEmitter;
    private final EventLogEmitter eventLogEmitter;
    private final InstrumentsRepository instrumentsRepository;
    private final String queueName;
    private final String consumerName;

    public InstrumentModificationRequestConsumer(final RabbitIntegrator rabbitIntegrator,
                                                 final RabbitExchange<InstrumentModificationRequest> instrumentModificationRequestExchange,
                                                 final InstrumentModificationRequestValidator instrumentModificationRequestValidator,
                                                 final InstrumentChangeEventEmitter instrumentChangeEventEmitter,
                                                 final EventLogEmitter eventLogEmitter,
                                                 final InstrumentsRepository instrumentsRepository,
                                                 @Value("${rabbitmq.reference-data-client-side-queue.instrument-modification-request}") final String queueName,
                                                 @Value("${spring.application.name}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.instrumentModificationRequestExchange = instrumentModificationRequestExchange;
        this.instrumentModificationRequestValidator = instrumentModificationRequestValidator;
        this.instrumentChangeEventEmitter = instrumentChangeEventEmitter;
        this.eventLogEmitter = eventLogEmitter;
        this.instrumentsRepository = instrumentsRepository;
        this.queueName = queueName;
        this.consumerName = consumerName;
    }

    @PostConstruct
    void init() {
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(InstrumentModificationRequest instrumentModificationRequest, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming InstrumentModificationRequest, {}", ProtobufUtils.shortDebugString(instrumentModificationRequest));
            instrumentModificationRequestValidator.validate(instrumentModificationRequest);
            modify(instrumentModificationRequest);
        } catch (Exception e) {
            instrumentChangeEventEmitter.emit(createErrorResponse(instrumentModificationRequest, e));
        }
        return ConsumptionResult.consumed();
    }

    private void modify(InstrumentModificationRequest instrumentModificationRequest) {
        final InstrumentToModify instrumentToModify = instrumentModificationRequest.getInstrumentToModify();
        switch (instrumentModificationRequest.getInstrumentModificationCommandType()) {
            case CREATE -> createInstrument(instrumentModificationRequest, instrumentToModify);
            case UPDATE -> updateInstrument(instrumentModificationRequest, instrumentToModify);
            default ->
                throw new RuntimeException("Invalid InstrumentModificationCommandType, request: %s".formatted(ProtobufUtils.shortDebugString(instrumentModificationRequest)));
        }
    }

    private void createInstrument(InstrumentModificationRequest instrumentModificationRequest, InstrumentToModify instrumentToModify) {
        String instrumentId = InstrumentsUtil.createIdentifier(instrumentToModify);
        String symbol = InstrumentsUtil.createSymbol(instrumentToModify);
        String now = toIsoUtcTime(ZonedDateTime.now());

        Instrument.Builder builder = InstrumentToModifyMapper.map(instrumentToModify, null, instrumentId, symbol)
            .toBuilder()
            .setCreatedAt(now)
            .setUpdatedAt(now);

        if (instrumentToModify.getBaseInstrument().getVenueType() == VenueType.CLOB) {
            Integer maxMatchingEngineId = instrumentsRepository.findMaxMatchingEngineId();
            int matchingEngineId = maxMatchingEngineId != null ? maxMatchingEngineId + 1 : 1;
            LOGGER.info("Generated matchingEngineId={}, maxMatchingEngineId={}", matchingEngineId, maxMatchingEngineId);
            builder.setInstrumentIdentifiers(builder.getInstrumentIdentifiers().toBuilder()
                .setMatchingEngineId(matchingEngineId));
        }

        Instrument instrument = builder.build();

        LOGGER.info("Saving instrument: {}", ProtobufUtils.shortDebugString(instrument));
        instrumentsRepository.saveInstrument(instrument);

        instrumentChangeEventEmitter.emit(createSuccessResponse(instrumentModificationRequest.getInstrumentModificationCommandType(), instrument));
        eventLogEmitter.emit(instrumentModificationRequest.getClientId(),
            DateUtils.toIsoUtcTime(ZonedDateTime.now()),
            "Successfully created instrument",
            EventLogStatus.SUCCESS,
            "instrumentCreateRequestStatus",
            instrumentModificationRequest.getMetadata().getCorrelationObject());
    }

    private void updateInstrument(InstrumentModificationRequest instrumentModificationRequest, InstrumentToModify instrumentToModify) {
        Instrument oldInstrument = instrumentsRepository.find(instrumentModificationRequest.getInstrumentToModify().getInstrumentIdentifiers().getInstrumentId());
        Instrument newInstrument;

        if (isStreetSideInstrument(oldInstrument)) {
            Instrument.Builder builder = Instrument.newBuilder(oldInstrument);
            builder.getBaseInstrumentBuilder().setSymbol(instrumentToModify.getBaseInstrument().getSymbol()).build();

            if (instrumentToModify.hasInstrumentIdentifiers()) {
                InstrumentIdentifiers.Builder instrumentIdentifierBuilder = builder.getInstrumentIdentifiers().toBuilder();
                String tradingViewId = instrumentModificationRequest.getInstrumentToModify().getInstrumentIdentifiers().getTradingViewId();
                String venueTradingViewId = instrumentModificationRequest.getInstrumentToModify().getInstrumentIdentifiers().getVenueTradingViewId();
                instrumentIdentifierBuilder.setTradingViewId(tradingViewId);
                instrumentIdentifierBuilder.setVenueTradingViewId(venueTradingViewId);
                builder.setInstrumentIdentifiers(instrumentIdentifierBuilder.build());
            }
            newInstrument = builder.build();
            LOGGER.info("Updating street-side instrument, old: {}, new: {}", ProtobufUtils.shortDebugString(oldInstrument), ProtobufUtils.shortDebugString(newInstrument));
        } else {
            newInstrument = InstrumentToModifyMapper.mapWithChanges(oldInstrument, instrumentToModify);
            LOGGER.info("Updating client-side instrument, old: {}, new: {}", ProtobufUtils.shortDebugString(oldInstrument), ProtobufUtils.shortDebugString(newInstrument));
        }

        String now = toIsoUtcTime(ZonedDateTime.now());
        newInstrument = newInstrument.toBuilder()
            .setUpdatedAt(now)
            .build();

        instrumentsRepository.saveInstrument(newInstrument);

        instrumentChangeEventEmitter.emit(createSuccessResponse(instrumentModificationRequest.getInstrumentModificationCommandType(), newInstrument));
        eventLogEmitter.emit(instrumentModificationRequest.getClientId(),
            DateUtils.toIsoUtcTime(ZonedDateTime.now()),
            "Successfully updated instrument",
            EventLogStatus.SUCCESS,
            "instrumentUpdateRequestStatus",
            instrumentModificationRequest.getMetadata().getCorrelationObject());
    }

    private boolean isStreetSideInstrument(Instrument instrument) {
        return instrument.getBaseInstrument().getVenueType() == VenueType.STREET;
    }

    private void declareQueue() {
        RabbitQueue<InstrumentModificationRequest> queue = new RabbitQueueBuilder<InstrumentModificationRequest>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(instrumentModificationRequestExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", instrumentModificationRequestExchange, queue);
        queue.attachConsumer(InstrumentModificationRequest.parser(), this);
    }
}
