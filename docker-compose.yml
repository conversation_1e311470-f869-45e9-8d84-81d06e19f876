services:
  rabbitmq:
    image: rabbitmq:management
    container_name: rabbitmq
    profiles:
      - infra
      - all
      - e2e_test
      - clob
    command: sh -c "sed 's/{{ .Values.auth.username }}/whiterabbit/g' /tmp/rabbit.definitions.json | sed 's/{{ .Values.auth.password }}/follow/g' > /tmp/rabbit.definitions.parsed.json && rabbitmq-server"
    ports:
      - "5672:5672"
      - "15672:15672"
      - "15692:15692"
    volumes:
      - ../architecture/infra/rabbitmq/rabbit.definitions.json:/tmp/rabbit.definitions.json
      - ../architecture/infra/rabbitmq/enabled_plugins:/etc/rabbitmq/enabled_plugins
    environment:
      - RABBITMQ_SERVER_ADDITIONAL_ERL_ARGS=-rabbitmq_management load_definitions "/tmp/rabbit.definitions.parsed.json"
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 60s
      retries: 5
      start_period: 30s

  db:
    image: postgres
    container_name: db
    profiles:
      - infra
      - all
      - e2e_test
      - clob
    command: [ "postgres", "-c", "max_connections=500" ]
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: password
    volumes:
      - ./sql/postgres:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "-d", "db_prod"]
      interval: 30s
      timeout: 60s
      retries: 5
      start_period: 80s

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin4_container
    restart: on-failure
    ports:
      - "5050:80"
    profiles:
      - infra
      - all
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json

  keycloak:
    container_name: keycloak
    build:
      context: ./docker/keycloak/.
      dockerfile: Dockerfile
    profiles:
      - infra
      - all
      - e2e_test
      - clob
    depends_on:
      db:
        condition: service_healthy
    environment:
      KC_DB: postgres
      KC_DB_URL: **********************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password
      KEYCLOAK_USER: admin
      KEYCLOAK_PASSWORD: Z#c9YgatDgr\,M}t
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: Z#c9YgatDgr\,M}t
    ports:
      - "8080:8080"
    command: ["start-dev"]
    healthcheck:
      test: "curl -f http://localhost:8080/realms/master"
      interval: 5s
      timeout: 5s
      retries: 20

  hazelcast-management:
    image: hazelcast/management-center:latest
    container_name: hazelcast-management

    profiles:
      - infra
      - all
    ports:
      - "8087:8080"
    environment:
      - MC_DEFAULT_CLUSTER=dev
      - MC_DEFAULT_CLUSTER_MEMBERS=storage
    extra_hosts:
      - "dockerhost:**********"

  vault:
    image: hashicorp/vault
    container_name: vault
    profiles:
      - infra
      - all
      - e2e_test
      - clob
    ports:
      - "8200:8200"
    environment:
      VAULT_SERVER: "http://127.0.0.1:8200"
      VAULT_DEV_ROOT_TOKEN_ID: my-token

  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: jaeger
    profiles:
      - infra
      - all
      - generator
    ports:
      - "16686:16686" # UI
      - "14268:14268" # jaeger.thrift
      - "14250:14250" # model.proto
    command:
      - "--query.ui-config=/etc/jaeger/jaeger-ui.json"
    environment:
      - JAEGER_DISABLED=true
      - METRICS_STORAGE_TYPE=prometheus
      - PROMETHEUS_SERVER_URL=http://prometheus:7300
    volumes:
      - "../architecture/local/jaeger/jaeger-ui.json:/etc/jaeger/jaeger-ui.json"
    restart: on-failure

  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.84.0
    command: "--config /etc/otelcol/otel-collector-config.yaml"
    container_name: otel-collector
    profiles:
      - infra
      - all
    volumes:
      - "../architecture/local/jaeger/otel-collector-config.yaml:/etc/otelcol/otel-collector-config.yaml"
    ports:
      - "4317:4317"
      - "8889:8889"
    depends_on:
      - jaeger
    restart: on-failure

  prometheus:
    image: prom/prometheus
    container_name: prometheus
    profiles:
      - infra
      - all
      - generator
    volumes:
      - "../architecture/local/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml"
    ports:
      - "7300:7300"
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--web.listen-address=:7300"
    restart: on-failure
    extra_hosts:
      - "dockerhost:**********"

  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    profiles:
      - infra
      - all
    ports:
      - "3200:3100"
    command: -config.file=/etc/loki/local-config.yaml

  grafana:
    image: grafana/grafana
    container_name: grafana
    profiles:
      - infra
      - all
      - generator
    ports:
      - "3100:3000"
    volumes:
      - "../architecture/local/grafana/datasource.yml:/etc/grafana/provisioning/datasources/datasource.yml"
      - "../architecture/local/grafana/dashboard.yml:/etc/grafana/provisioning/dashboards/dashboard.yml"
      - "../architecture/local/grafana/dashboard-for-grafana.json:/var/lib/grafana/dashboards/dashboard-for-grafana.json"
      - "../architecture/local/grafana/dashboard-for-jaeger.json:/var/lib/grafana/dashboards/dashboard-for-jaeger.json"
      - "../architecture/local/grafana/jvm-micrometer_rev9.json:/var/lib/grafana/dashboards/jvm-micrometer_rev9.json"
      - "../architecture/local/grafana/order-gateway.json:/var/lib/grafana/dashboards/order-gateway.json"
      - "../architecture/local/grafana/order-collider.json:/var/lib/grafana/dashboards/order-collider.json"
      - "../architecture/local/grafana/fix-api-trading.json:/var/lib/grafana/dashboards/fix-api-trading.json"
      - "../architecture/local/grafana/rest-api-server.json:/var/lib/grafana/dashboards/rest-api-server.json"
      - "../architecture/local/grafana/connector-wrapper.json:/var/lib/grafana/dashboards/connector-wrapper.json"
      - "../architecture/local/grafana/market-data.json:/var/lib/grafana/dashboards/market-data.json"
      - "../architecture/local/grafana/risk-engine.json:/var/lib/grafana/dashboards/risk-engine.json"
      - "../architecture/local/grafana/booking-engine.json:/var/lib/grafana/dashboards/booking-engine.json"
      - "../architecture/local/grafana/load-generator.json:/var/lib/grafana/dashboards/load-generator.json"
      - "../architecture/local/grafana/storage.json:/var/lib/grafana/dashboards/storage.json"
      - "../architecture/local/grafana/clob-gateway.json:/var/lib/grafana/dashboards/clob-gateway.json"
      - "../architecture/local/grafana/rate-service.json:/var/lib/grafana/dashboards/rate-service.json"
      - "../architecture/local/grafana/booking-wal.json:/var/lib/grafana/dashboards/booking-wal.json"
      - "../architecture/local/grafana/booking-snapshotter.json:/var/lib/grafana/dashboards/booking-snapshotter.json"
      - "../architecture/local/grafana/booking-pnl.json:/var/lib/grafana/dashboards/booking-pnl.json"
    environment:
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_NAME=Main Org.
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
    depends_on:
      - prometheus
      - jaeger
    restart: on-failure

  management-center:
    image: hazelcast/management-center:5.4.1
    profiles:
      - all
    ports:
      - "8041:8080"
    environment:
      - MC_DEFAULT_CLUSTER=dev
      - MC_DEFAULT_CLUSTER_MEMBERS=storage

  #  Our services
  storage:
    image: docker.wyden.io/cloud/dev/storage:latest
    container_name: storage
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      db:
        condition: service_healthy
    ports:
      - "8070:8070"
      - "9070:9070" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - JAVA_OPTS=-Xms512m -Xmx2048m
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9070,server=y,suspend=n
      - HZ_HZCONSOLEURL=*.*.*.*
      - SPRING_DATASOURCE_URL=*********************************
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
        test: "curl --fail --silent localhost:8070/actuator/health/readiness | grep UP || exit 1"
        interval: 20s
        timeout: 5s
        retries: 10
        start_period: 5s

  access-gateway-server:
    image: docker.wyden.io/cloud/dev/access-gateway:latest
    container_name: access-gateway
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      keycloak:
        condition: service_healthy
      vault:
        condition: service_started
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8089:8089"
      - "9089:9089" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_PROFILES_ACTIVE=dev
      - DB_DISABLE=true
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9089,server=y,suspend=n
      - KEYCLOAK_HOST=http://keycloak:8080
      - SWAGGER_KEYCLOAK_HOST=http://localhost:8080
      - SPRING_DATASOURCE_URL=****************************************
      - SECURITY_JWT_ISSUER_VALIDATION_DISABLED=true
      - SPRING_CLOUD_VAULT_HOST=vault
      - RABBITMQ_HOST=rabbitmq
      - WYDEN_KEYCLOAK_ACCESS_SETTINGS_ADDITIONAL_REDIRECT_URI=http://localhost:8095/*
      - HZ_ADDRESSLIST=storage
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - REFERENCE_DATA_HOST=http://reference-data:8098
      - LOGGING_CONFIG=
      - LICENSE_CHECK_ENABLED=true
      - LICENSE_KEY=F39389-5F2133-71B1CA-C5AE83-A8846E-V3
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent localhost:8089/actuator/health/readiness | grep UP || exit 1"
      interval: 20s
      timeout: 5s
      retries: 10
      start_period: 40s

  settlement:
    image: docker.wyden.io/cloud/dev/settlement:latest
    container_name: settlement
    profiles:
      - all
      - e2e_test
#    depends_on:
#      booking-engine:
#        condition: service_healthy
    ports:
      - "8067:8067"
      - "9067:9067" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_DATASOURCE_URL=************************************
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9067,server=y,suspend=n
      - RABBITMQ_HOST=rabbitmq
      - LOGGING_CONFIG=
      - HZ_ADDRESSLIST=storage
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent localhost:8067/actuator/health/readiness | grep UP || exit 1"
      interval: 20s
      timeout: 5s
      retries: 10
      start_period: 40s

  target-registry:
    image: docker.wyden.io/cloud/dev/target-registry:latest
    container_name: target-registry
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      keycloak:
        condition: service_healthy
      vault:
        condition: service_started
      rabbitmq:
        condition: service_healthy
    ports:
      - "8066:8066"
      - "9066:9066" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9066,server=y,suspend=n
      - KEYCLOAK_HOST=http://keycloak:8080
      - SWAGGER_KEYCLOAK_HOST=http://localhost:8080
      - SECURITY_JWT_ISSUER_VALIDATION_DISABLED=true
      - SPRING_CLOUD_VAULT_TOKEN=my-token
      - SPRING_CLOUD_VAULT_HOST=vault
      - RABBITMQ_HOST=rabbitmq
      - LOGGING_CONFIG=
      - HZ_ADDRESSLIST=storage
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent localhost:8066/actuator/health/readiness | grep UP || exit 1"
      interval: 20s
      timeout: 5s
      retries: 10
      start_period: 40s

  order-collider:
    image: docker.wyden.io/cloud/dev/order-collider:latest
    container_name: order-collider
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8091:8091"
      - "9091:9091" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_PROFILES_ACTIVE=dev
      - HZ_ADDRESSLIST=storage
      - RABBITMQ_HOST=rabbitmq
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9091,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8091/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  order-gateway:
    image: docker.wyden.io/cloud/dev/order-gateway:latest
    container_name: order-gateway
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8094:8094"
      - "9094:9094" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_PROFILES_ACTIVE=dev
      - HZ_ADDRESSLIST=storage
      - RABBITMQ_HOST=rabbitmq
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9094,server=y,suspend=n
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8094/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-api-trading:
    image: docker.wyden.io/cloud/dev/fix-api-trading:latest
    container_name: fix-api-trading
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8093:8093"
      - "9876:9876" #fix server port
      - "9093:9093" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - KEYCLOAK_HOST=http://keycloak:8080
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - SPRING_DATASOURCE_URL=*****************************************
      - SPRING_FLYWAY_ENABLED=true
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9093,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8093/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-api-market-data:
    image: docker.wyden.io/cloud/dev/fix-api-market-data:latest
    container_name: fix-api-market-data
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8084:8084"
      - "9877:9877" #fix server port
      - "9084:9084" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - KEYCLOAK_HOST=http://keycloak:8080
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9084,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8084/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-api-drop-copy:
    image: docker.wyden.io/cloud/dev/fix-api-drop-copy:latest
    container_name: fix-api-drop-copy
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8085:8085"
      - "9878:9878" #fix server port
      - "9085:9085" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - KEYCLOAK_HOST=http://keycloak:8080
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - SPRING_DATASOURCE_URL=*******************************************
      - SPRING_FLYWAY_ENABLED=true
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9085,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8085/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-api-custom-ohlc:
    image: docker.wyden.io/cloud/dev/fix-api-custom-ohlc:latest
    container_name: fix-api-custom-ohlc
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8086:8086"
      - "9879:9879" #fix server port
      - "9086:9086" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - KEYCLOAK_HOST=http://keycloak:8080
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - SPRING_DATASOURCE_URL=*********************************************
      - SPRING_FLYWAY_ENABLED=true
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9085,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8086/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  rest-api-server:
    image: docker.wyden.io/cloud/dev/rest-api-server:latest
    container_name: rest-api-server
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
      reference-data:
        condition: service_healthy
      order-gateway:
        condition: service_healthy
      booking-engine:
        condition: service_healthy
      risk-engine:
        condition: service_started
      broker-config-service:
        condition: service_healthy
      target-registry:
        condition: service_healthy
    ports:
      - "8095:8095"
      - "9095:9095" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9095,server=y,suspend=n
      - KEYCLOAK_HOST=http://keycloak:8080
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - SECURITY_JWT_ISSUER_VALIDATION_DISABLED=true
      - REFERENCE_DATA_HOST=http://reference-data:8098
      - ORDER_GATEWAY_HOST=http://order-gateway:8094
      - SWAGGER_KEYCLOAK_HOST=http://localhost:8080
      - BOOKING_ENGINE_HOST=http://booking-engine:8100
      - RISK_ENGINE_HOST=http://risk-engine:8300
      - BROKER_CONFIG_SERVICE_HOST=http://broker-config-service:8049
      - TARGET_REGISTRY_HOST=http://target-registry:8066
      - AUDIT_SERVER_HOST=http://audit-server:8030
      - ORDER_HISTORY_HOST=http://order-history:8040
      - CLOB_GATEWAY_HOST=http://clob-gateway:8069
      - QUOTING_ENGINE_HOST=http://quoting-engine:8071
      - SETTLEMENT_HOST=http://settlement:8067
      - RATE_SERVICE_HOST=http://rate-service:8052
      - SPRING_DATASOURCE_URL=***********************************
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8095/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  market-data-manager:
    image: docker.wyden.io/cloud/dev/market-data-manager:latest
    container_name: market-data-manager
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
    ports:
      - "8096:8096"
      - "9096:9096" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9096,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8096/actuator/health/readiness | grep UP || exit 1"
      interval: 20s
      timeout: 5s
      retries: 10
      start_period: 5s

  connector-wrapper-mock:
    image: docker.wyden.io/cloud/dev/connector-wrapper-mock:latest
    container_name: connector-wrapper-mock
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8092:8092"
      - "9092:9092" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9092,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false

  connector-wrapper-kraken:
    image: docker.wyden.io/cloud/dev/connector-wrapper-kraken:latest
    container_name: connector-wrapper-kraken
    profiles:
      - all
      - kraken
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8110:8110"
      - "9110:9110" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9110,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false

  connector-wrapper-coinapi:
    image: docker.wyden.io/cloud/dev/connector-wrapper-coinapi:latest
    container_name: connector-wrapper-coinapi
    profiles:
      - all
      - coinapi
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8113:8113"
      - "9113:9113" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9113,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - COINAPI_APIKEY=e921435d-9eda-4f82-861f-21bd925edd6a
      - ACCOUNT_NAME=coinapi

  connector-wrapper-bitmex:
    image: docker.wyden.io/cloud/dev/connector-wrapper-bitmex:latest
    container_name: connector-wrapper-bitmex
    profiles:
      - all
      - bitmex
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8099:8099"
      - "9099:9099" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9099,server=y,suspend=n
      - LOGGING_CONFIG=
      - BITMEX_APIKEY=7zzbn6SNxPByVuZFFFvw6fn5
      - BITMEX_APISECRET=ATO440u02eOjUDX6JvLGvhBMTMV0vTjPRjpcwHGrtfR_WaBi
      - BITMEX_RESTURL=https://testnet.bitmex.com/api/v1/
      - BITMEX_WSSURL=wss://ws.testnet.bitmex.com/realtime
      - ACCOUNT_NAME=bitmex-testnet1
      - SPRING_CLOUD_VAULT_ENABLED=false

  connector-wrapper-binance-spot:
    image: docker.wyden.io/cloud/dev/connector-wrapper-binance-spot:latest
    container_name: connector-wrapper-binance-spot
    profiles:
      - all
      - binance
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8101:8101"
      - "9101:9101" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9101,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - ACCOUNT_NAME=

  connector-wrapper-bitstamp:
    image: docker.wyden.io/cloud/dev/connector-wrapper-bitstamp:latest
    container_name: connector-wrapper-bitstamp
    profiles:
      - all
      - bitstamp
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8103:8103"
      - "9103:9103" #debug port
    environment:
      - BITSTAMP_APIKEY=copy_value_from_PSONO
      - BITSTAMP_APISECRET=copy_value_from_PSONO
      - BITSTAMP_CUSTOMERID=copy_value_from_PSONO
      - BITSTAMP_FIX_SENDERCOMPID=copy_value_from_PSONO
      - BITSTAMP_FIX_USERNAME=copy_value_from_PSONO
      - BITSTAMP_FIX_PASSWORD=copy_value_from_PSONO
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9103,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - ACCOUNT_NAME=bitstamp

  connector-wrapper-generic-outbound:
    image: docker.wyden.io/cloud/dev/connector-wrapper-generic-outbound:latest
    container_name: connector-wrapper-generic-outbound
    profiles:
      - all
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      exchange-simulator:
        condition: service_healthy
    ports:
      - "8102:8102"
      - "9102:9102" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9102,server=y,suspend=n
      - LOGGING_CONFIG=
      - GENERICCONNECTOR_EXCHANGENAME=Generic
      - GENERICCONNECTOR_INSTRUMENTTYPESSUPPORTED=ForexDTO
      - GENERICCONNECTOR_REQUESTSSUPPORTED=MarketOrderDTO=Submit,LimitOrderDTO=Submit&Cancel
      - GENERICCONNECTOR_TIFSSUPPORTED=MarketOrderDTO=GTC&IOC&FOK&DAY,LimitOrderDTO=GTC&IOC&FOK&DAY
      - GENERICCONNECTOR_BALANCESSUPPORTED=false
      - GENERICCONNECTOR_FIX_SESSION_MD_SOCKETCONNECTHOST=exchange-simulator
      - GENERICCONNECTOR_FIX_SESSION_MD_SOCKETCONNECTPORT=10001
      - GENERICCONNECTOR_FIX_SESSION_MD_TARGETCOMPID=WYDEN_EXCHANGE_MARKET_DATA
      - GENERICCONNECTOR_FIX_SESSION_MD_SENDERCOMPID=CLIENT_MARKET_DATA
      - GENERICCONNECTOR_FIX_SESSION_MD_USERNAME=simulator
      - GENERICCONNECTOR_FIX_SESSION_MD_PASSWORD=password
      - GENERICCONNECTOR_FIX_SESSION_MD_SOCKETUSESSL=N
      - GENERICCONNECTOR_FIX_SESSION_T_SOCKETCONNECTHOST=exchange-simulator
      - GENERICCONNECTOR_FIX_SESSION_T_SOCKETCONNECTPORT=10001
      - GENERICCONNECTOR_FIX_SESSION_T_TARGETCOMPID=WYDEN_EXCHANGE_TRADING
      - GENERICCONNECTOR_FIX_SESSION_T_SENDERCOMPID=CLIENT_TRADING
      - GENERICCONNECTOR_FIX_SESSION_T_USERNAME=simulator
      - GENERICCONNECTOR_FIX_SESSION_T_PASSWORD=password
      - GENERICCONNECTOR_FIX_SESSION_T_SOCKETUSESSL=N
      - GENERICCONNECTOR_FIX_SESSION_T_RESETONLOGON=Y
      - ACCOUNT_NAME=simulator
      - SPRING_CLOUD_VAULT_ENABLED=false

  connector-wrapper-bit2me:
    image: docker.wyden.io/cloud/dev/connector-wrapper-bit2me:latest
    container_name: connector-wrapper-bit2me
    profiles:
      - all
      - bit2me
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8107:8107"
      - "9107:9107" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9107,server=y,suspend=n
      - LOGGING_CONFIG=
#      - BIT2ME_APIKEY=copy_value_from_PSONO
#      - BIT2ME_APISECRET=copy_value_from_PSONO
      - ACCOUNT_NAME=bit2me
      - SPRING_CLOUD_VAULT_ENABLED=false

  connector-wrapper-360t:
    image: docker.wyden.io/cloud/dev/connector-wrapper-360t:latest
    container_name: connector-wrapper-360t
    profiles:
      - all
      - t360
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8112:8112"
      - "9112:9112" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9112,server=y,suspend=n
      - LOGGING_CONFIG=
      - CONNECTOR360T_FIX_SESSION_T_SENDERCOMPID=copy_value_from_PSONO
      - CONNECTOR360T_FIX_SESSION_MD_SENDERCOMPID=copy_value_from_PSONO
      - CONNECTOR360T_FIX_SESSION_MD_SENDERSUBID=GBBVAK.API
      - CONNECTOR360T_FIX_SESSION_T_SENDERSUBID=GBBVAK.API
      - CONNECTOR360T_FIX_SESSION_T_SENDERLOCATIONID=TRADE3
      - CONNECTOR360T_FIX_SESSION_MD_SENDERLOCATIONID=QUOTE3
      - CONNECTOR360T_FIX_SESSION_T_PASSWORD=copy_value_from_PSONO
      - CONNECTOR360T_FIX_SESSION_MD_PASSWORD=copy_value_from_PSONO
      - ACCOUNT_NAME=360t
      - SPRING_CLOUD_VAULT_ENABLED=false

  connector-wrapper-wintermute:
    image: docker.wyden.io/cloud/dev/connector-wrapper-wintermute:latest
    container_name: connector-wrapper-wintermute
    profiles:
      - all
      - wintermute
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8114:8114"
      - "9114:9114" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9114,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - WINTERMUTE_FIX_SESSION_MD_PASSWORD=copy_value_from_PSONO
      - WINTERMUTE_FIX_SESSION_MD_SENDERCOMPID=wynden_uat
      - WINTERMUTE_FIX_SESSION_MD_USERNAME=wynden
      - WINTERMUTE_FIX_SESSION_T_PASSWORD=copy_value_from_PSONO
      - WINTERMUTE_FIX_SESSION_T_RESETONLOGON=Y
      - WINTERMUTE_FIX_SESSION_T_SENDERCOMPID=wynden_uat
      - WINTERMUTE_FIX_SESSION_T_USERNAME=wynden
      - WINTERMUTE_REST_PASSWORD=copy_value_from_PSONO
      - WINTERMUTE_REST_SENDERCOMPID=wynden_uat
      - WINTERMUTE_REST_TARGETCOMPID=wynden_uat
      - WINTERMUTE_REST_USERNAME=wynden
      - WINTERMUTE_RESTURL=https://rfq-uat-3.wintermute-direct.com:19699
      - WINTERMUTE_FIX_SESSIONRESETONDISCONNECT=false
      - WINTERMUTE_DEFAULTTIFLIMIT=FOK
      - WINTERMUTE_DEFAULTTIFPREVIOUSLYINDICATED=FOK
      - WINTERMUTE_CONFIGURL=fix-wmt.cfg
      - WINTERMUTE_MDTHROTTLEPERSEC=10
      - ACCOUNT_NAME=wintermute

  connector-wrapper-fireblocks:
    image: docker.wyden.io/cloud/dev/connector-wrapper-fireblocks:latest
    container_name: connector-wrapper-fireblocks
    profiles:
      - all
      - fireblocks
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8109:8109"
      - "9109:9109" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9109,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - ACCOUNT_NAME=fireblocks
      - FIREBLOCKS_RESTURL=https://api.fireblocks.io
      - FIREBLOCKS_APIKEY=copy_value_from_PSONO
      - FIREBLOCKS_PRIVATE_KEY=copy_value_from_PSONO

  connector-wrapper-scrypt:
    image: docker.wyden.io/cloud/dev/connector-wrapper-scrypt:latest
    container_name: connector-wrapper-scrypt
    profiles:
      - all
      - scrypt
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8116:8116"
      - "9116:9116" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9116,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - SCRYPT_WEBSOCKET_APIKEY=value_from_PSONO
      - SCRYPT_WEBSOCKET_APISECRET=value_from_PSONO
      - SCRYPT_FIX_APIKEY=value_from_PSONO
      - SCRYPT_FIX_APISECRET=value_from_PSONO
      - SCRYPT_FIX_MARKETDATA_SENDERCOMPID=value_from_PSONO
      - SCRYPT_FIX_TRADING_SENDERCOMPID=value_from_PSONO
      - ACCOUNT_NAME=scrypt

  connector-wrapper-b2c2:
    image: docker.wyden.io/cloud/dev/connector-wrapper-b2c2:latest
    container_name: connector-wrapper-b2c2
    profiles:
      - all
      - b2c2
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
      - "8115:8115"
      - "9115:9115" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9114,server=y,suspend=n
      - LOGGING_CONFIG=
      - SPRING_CLOUD_VAULT_ENABLED=false
      - B2C2_APITOKEN=copy_value_from_PSONO
      - B2C2_FIX_SESSION_MD_SENDERCOMPID=copy_value_from_PSONO
      - B2C2_FIX_SESSION_MD_SOCKETCONNECTHOST=*************
      - B2C2_FIX_SESSION_MD_SOCKETCONNECTPORT=8888
      - B2C2_FIX_SESSION_MD_TARGETCOMPID=B2C2UMD
      - B2C2_FIX_SESSION_MD_USERNAME=copy_value_from_PSONO
      - B2C2_FIX_SESSION_T_RESETONLOGON=Y
      - B2C2_FIX_SESSION_T_SENDERCOMPID=copy_value_from_PSONO
      - B2C2_FIX_SESSION_T_SOCKETCONNECTHOST=*************
      - B2C2_FIX_SESSION_T_SOCKETCONNECTPORT=8888
      - B2C2_FIX_SESSION_T_TARGETCOMPID=B2C2UT
      - B2C2_FIX_SESSION_T_USERNAME=copy_value_from_PSONO
      - B2C2_RESTURL=https://api.uat.b2c2.net
      - ACCOUNT_NAME=b2c2
      - B2C2_FIX_SESSIONRESETONDISCONNECT=false
      - B2C2_FIX_SESSION_MD_SOCKETUSESSL=N
      - B2C2_FIX_SESSION_T_SOCKETUSESSL=N
      - B2C2_FIX_SESSION_MD_RESETONLOGON=Y
      - B2C2_FIX_SESSION_MD_PERSISTMESSAGES=N
      - B2C2_FIX_SESSION_T_RESETONLOGON=N

  fix-actor: #fix-actor-trading
    image: docker.wyden.io/cloud/dev/fix-actor:latest
    container_name: fix-actor
    profiles:
      - all
      - e2e_test
    depends_on:
      fix-api-trading:
        condition: service_healthy
    ports:
      - "8090:8090"
      - "9090:9090" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - FIX_SOCKETCONNECT_HOST=fix-api-trading
      - FIX_SOCKETCONNECT_PORT=9876
      - FIX_TARGETCOMPID=ATFIXSERVER
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9090,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8090/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-actor-drop-copy:
    image: docker.wyden.io/cloud/dev/fix-actor:latest
    container_name: fix-actor-drop-copy
    profiles:
      - all
      - e2e_test
    depends_on:
      fix-api-drop-copy:
        condition: service_healthy
    ports:
      - "8504:8504"
      - "9504:9504" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - FIX_SOCKETCONNECT_HOST=fix-api-drop-copy
      - FIX_SOCKETCONNECT_PORT=9878
      - FIX_TARGETCOMPID=ATFIXDROPCOPY
      - SERVER_PORT=8504
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9504,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8504/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-actor-market-data:
    image: docker.wyden.io/cloud/dev/fix-actor:latest
    container_name: fix-actor-market-data
    profiles:
      - all
      - e2e_test
    depends_on:
      fix-api-market-data:
        condition: service_healthy
    ports:
      - "8503:8503"
      - "9503:9503" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - FIX_SOCKETCONNECT_HOST=fix-api-market-data
      - FIX_SOCKETCONNECT_PORT=9877
      - FIX_TARGETCOMPID=ATFIXMARKET
      - SERVER_PORT=8503
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9503,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8503/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  fix-actor-custom-ohlc:
    image: docker.wyden.io/cloud/dev/fix-actor:latest
    container_name: fix-actor-custom-ohlc
    profiles:
      - all
      - e2e_test
    depends_on:
      fix-api-custom-ohlc:
        condition: service_healthy
    ports:
      - "8505:8505"
      - "9505:9505" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - FIX_SOCKETCONNECT_HOST=fix-api-custom-ohlc
      - FIX_SOCKETCONNECT_PORT=9879
      - FIX_TARGETCOMPID=ATFIXOHLC
      - SERVER_PORT=8505
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9505,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8505/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  reference-data:
    image: docker.wyden.io/cloud/dev/reference-data:latest
    container_name: reference-data
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      storage:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8098:8098"
      - "9098:9098" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9098,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - LOGGING_CONFIG=
      - INITIAL_CONFIG_INSTRUMENT_PREFILL=true
      - INITIAL_CONFIG_PORTFOLIO_PREFILL=true
      - INITIAL_CONFIG_VENUEACCOUNT_PREFILL=true
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8098/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  ui:
    image: docker.wyden.io/cloud/dev/wyden-ui:latest
    container_name: ui
    profiles:
      - all
      - clob
      - e2e_test
    ports:
      - "3000:80"

  exchange-simulator:
    image: docker.wyden.io/cloud/dev/exchange-simulator:latest
    container_name: exchange-simulator
    profiles:
      - all
      - clob
    ports:
      - "10000:10000" # REST
      - "10001:10001" # FIX
      - "11000:11000" # debug
    environment:
      - JAVA_OPTS=-Xms385m -Xmx1024m
      - COINAPI_APIKEY=e921435d-9eda-4f82-861f-21bd925edd6a
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:11000,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:10000/actuator/health/readiness | grep UP || exit 1"
      interval: 20s
      timeout: 5s
      retries: 30 # allow longer readiness health check (initial reference data download from CoinAPI takes time)
      start_period: 5s

  load-generator:
    image: docker.wyden.io/cloud/dev/load-generator:latest
    container_name: load-generator
    profiles:
      - all
    depends_on:
      rest-api-server:
        condition: service_healthy
      fix-actor:
        condition: service_healthy
    ports:
      - "8105:8105"
      - "9105:9105" #debug port
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9105,server=y,suspend=n
      - ENVIRONMENT=compose
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8105/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  booking-engine:
    image: docker.wyden.io/cloud/dev/booking-engine:latest
    container_name: booking-engine
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
      reference-data:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
    ports:
      - "8100:8100"
      - "9100:9100" #debug port
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9100,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - REFERENCE_DATA_URL=http://reference-data:8098
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - LICENSE_MANAGER_HOST=http://license-server-mock:8108
      #      - LICENSE_MANAGER_HOST=https://license.wyden.io
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8100/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  booking-wal:
    image: docker.wyden.io/cloud/dev/booking-wal:latest
    container_name: booking-wal
    profiles:
      - all
      - booking
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
      reference-data:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
      booking-engine:
        condition: service_healthy
    ports:
      - "8042:8042"
      - "9042:9042" #debug port
    environment:
      - SPRING_DATASOURCE_URL=*********************************_wal
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9042,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - REFERENCE_DATA_URL=http://reference-data:8098
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - LOGGING_CONFIG=
      - BOOKING_ENGINE_HOST=http://booking-engine:8100
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8042/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  booking-snapshotter:
    image: docker.wyden.io/cloud/dev/booking-snapshotter:latest
    container_name: booking-snapshotter
    profiles:
      - all
      - booking
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
      reference-data:
        condition: service_healthy
      access-gateway-server:
        condition: service_healthy
      booking-wal:
        condition: service_healthy
    ports:
      - "8043:8043"
      - "9043:9043" #debug port
    environment:
      - SPRING_DATASOURCE_URL=*********************************_snapshot
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9043,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - REFERENCE_DATA_URL=http://reference-data:8098
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - BOOKING_WAL_HOST=http://booking-wal:8042
      - LICENSE_MANAGER_HOST=http://license-server-mock:8108
      #      - LICENSE_MANAGER_HOST=https://license.wyden.io
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8043/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  booking-pnl:
    image: docker.wyden.io/cloud/dev/booking-pnl:latest
    container_name: booking-pnl
    profiles:
      - all
      - booking
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
      booking-snapshotter:
        condition: service_healthy
    ports:
      - "8044:8044"
      - "9044:9044" #debug port
    environment:
      - SPRING_DATASOURCE_URL=*********************************_pnl
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9044,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - BOOKING_SNAPSHOTTER_HOST=http://booking-snapshotter:8043
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8044/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  booking-reporting:
    image: docker.wyden.io/cloud/dev/booking-reporting:latest
    container_name: booking-reporting
    profiles:
      - all
      - booking
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
      booking-pnl:
        condition: service_healthy
    ports:
      - "8045:8045"
      - "9045:9045" #debug port
    environment:
      - SPRING_DATASOURCE_URL=*******************************************
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9045,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - BOOKING_SNAPSHOTTER_HOST=http://booking-snapshotter:8043
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8045/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  smart-recommendation-engine:
    image: docker.wyden.io/cloud/dev/smart-recommendation-engine:latest
    container_name: smart-recommendation-engine
    profiles:
       - all
       - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
    ports:
       - "8104:8104" # REST
       - "11001:11001" # debug
    environment:
       - RABBITMQ_HOST=rabbitmq
       - SPRING_PROFILES_ACTIVE=dev
       - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
       - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:11001,server=y,suspend=n
       - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
       test: "curl --fail --silent http://localhost:8104/actuator/health/readiness | grep UP || exit 1"
       interval: 10s
       timeout: 5s
       retries: 10
       start_period: 5s

  smart-order-router:
    image: docker.wyden.io/cloud/dev/smart-order-router:latest
    container_name: smart-order-router
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8060:8060"
      - "9060:9060" # debug port
    environment:
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - REFERENCE_DATA_HOST=http://reference-data:8098
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9060,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8060/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  risk-engine:
    image: docker.wyden.io/cloud/dev/risk-engine:latest
    container_name: risk-engine
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8300:8300"
      - "9300:9300" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - BOOKING_ENGINE_HOST=http://booking-engine:8100
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9300,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8300/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  agency-trading-service:
    image: docker.wyden.io/cloud/dev/agency-trading-service:latest
    container_name: agency-trading-service
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8050:8050"
      - "9050:9050" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9050,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8050/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  pricing-service:
    image: docker.wyden.io/cloud/dev/pricing-service:latest
    container_name: pricing-service
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8051:8051"
      - "9051:9051" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9051,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8051/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  broker-config-service:
    image: docker.wyden.io/cloud/dev/broker-config-service:latest
    container_name: broker-config-service
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8049:8049"
      - "9049:9049" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9049,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8049/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  audit-server:
    image: docker.wyden.io/cloud/dev/audit-server:latest
    container_name: audit-server
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8030:8030"
      - "9030:9030" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9030,server=y,suspend=n
      - SPRING_DATASOURCE_URL=**************************************
      - LOGGING_CONFIG=
      - HZ_ADDRESSLIST=storage
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8030/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  message-scheduler:
    image: docker.wyden.io/cloud/dev/message-scheduler:latest
    container_name: message-scheduler
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8106:8106"
      - "9106:9106" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9106,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8106/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  rate-service:
    image: docker.wyden.io/cloud/dev/rate-service:latest
    container_name: rate-service
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8052:8052"
      - "9052:9052" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9052,server=y,suspend=n
      - HZ_ADDRESSLIST=storage
      - COINAPI_APIKEY=661049ac-a7c7-43f1-9fea-2c82ee81e7f8
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8052/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  order-history:
    image: docker.wyden.io/cloud/dev/order-history:latest
    container_name: order-history
    profiles:
      - history
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8040:8040"
      - "9040:9040" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9040,server=y,suspend=n
      - SPRING_DATASOURCE_URL=***************************************
      - LOGGING_CONFIG=
      - HZ_ADDRESSLIST=storage
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8040/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  rest-management:
    image: docker.wyden.io/cloud/dev/rest-management:latest
    container_name: rest-management
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8083:8083"
      - "9083:9083" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_PROFILES_ACTIVE=dev
      - HZ_ADDRESSLIST=storage
      - RABBITMQ_HOST=rabbitmq
      - SPRING_CLOUD_VAULT_HOST=vault
      - SPRING_CLOUD_VAULT_TOKEN=my-token
      - BROKER_CONFIG_SERVICE_HOST=http://broker-config-service:8049
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - BOOKING_ENGINE_HOST=http://booking-engine:8100
      - RISK_ENGINE_HOST=http://risk-engine:8300
      - ORDER_HISTORY_HOST=http://order-history:8040
      - TARGET_REGISTRY_HOST=http://target-registry:8066
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9083,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8083/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  websocket-server:
    image: docker.wyden.io/cloud/dev/websocket-server:latest
    container_name: websocket-server
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8400:8400"
      - "9400:9400" #debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - SPRING_PROFILES_ACTIVE=dev
      - HZ_ADDRESSLIST=storage
      - RABBITMQ_HOST=rabbitmq
      - SPRING_CLOUD_VAULT_HOST=vault
      - SPRING_CLOUD_VAULT_TOKEN=my-token
      - ACCESS_GATEWAY_HOST=http://access-gateway:8089
      - BOOKING_ENGINE_HOST=http://booking-engine:8100
      - TARGET_REGISTRY_HOST=http://target-registry:8066
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9400,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8400/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  clob-gateway:
    image: docker.wyden.io/cloud/dev/clob-gateway:latest
    container_name: clob-gateway
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      reference-data:
        condition: service_healthy
    ports:
      - "8069:8069"
      - "9069:9069" # debug port
      - "7091:7091"
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_OPTS=-Xms1024m -Xmx2048m
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9069,server=y,suspend=n --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-exports=java.base/jdk.internal.ref=ALL-UNNAMED --add-exports=java.base/jdk.internal.util=ALL-UNNAMED
      - LOGGING_CONFIG=
      - CLOB_AERON_INGRESS_HOSTNAMES=aeron-cluster
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8069/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s
    shm_size: 200m

  quoting-engine:
    image: docker.wyden.io/cloud/dev/quoting-engine:latest
    container_name: quoting-engine
    profiles:
      - all
      - e2e_test
      - clob
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
      aeron-cluster:
        condition: service_started
    ports:
      - "8071:8071"
      - "9071:9071" # debug port
    environment:
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9071,server=y,suspend=n
      - LOGGING_CONFIG=
      - CLOB_AERON_INGRESS_HOSTNAMES=aeron-cluster
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8071/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s
    shm_size: 200m

  quoting-order-service:
    image: docker.wyden.io/cloud/dev/quoting-order-service:latest
    container_name: quoting-order-service
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8068:8068"
      - "9068:9068" # debug port
    environment:
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9068,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8068/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  aeron-cluster:
    image: docker.wyden.io/cloud/dev/aeron-cluster:latest
    container_name: aeron-cluster
    shm_size: '1gb'
    profiles:
      - all
      - e2e_test
      - clob
    volumes:
      - ./aeron-cluster:/home/<USER>/aeron-cluster
    environment:
      - NODE_ID=0
      - CLUSTER_ADDRESSES=0.0.0.0
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9072,server=y,suspend=n
    ports:
      - 9000-9020:9000-9020/udp
      - "8072:8072"
      - "9072:9072" # debug port

  aeron-snapshot:
    image: docker.wyden.io/cloud/dev/aeron-cluster:latest
    container_name: aeron-snapshot
    shm_size: '1gb'
    environment:
      MODE: SNAPSHOT
      CLUSTER_ADDRESSES: aeron-cluster
      POD_IP: aeron-snapshot
      SNAPSHOT_PERIOD: 60s
      VM_ARGUMENTS: "-Daeron.event.log=admin,send,receive"
    restart: always

  aeron-backup:
    image: docker.wyden.io/cloud/dev/aeron-cluster:latest
    container_name: aeron-backup
    shm_size: '1gb'
    environment:
      MODE: BACKUP
      CLUSTER_ADDRESSES: aeron-cluster
      POD_IP: aeron-backup
      CLUSTER_BACKUP_INTERVAL_NS: 60000000000
      DEBUG: false
    restart: always
    volumes:
      - ./aeron-backup:/home/<USER>/aeron-cluster

  auto-hedger:
    image: docker.wyden.io/cloud/dev/auto-hedger:latest
    container_name: auto-hedger
    profiles:
      - all
      - e2e_test
    depends_on:
      rabbitmq:
        condition: service_healthy
      storage:
        condition: service_healthy
    ports:
      - "8075:8075"
      - "9075:9075" # debug port
    environment:
      - RABBITMQ_HOST=rabbitmq
      - HZ_ADDRESSLIST=storage
      - SPRING_PROFILES_ACTIVE=dev
      - TRACING_COLLECTOR_ENDPOINT=http://otel-collector:4317
      - BROKER_CONFIG_SERVICE_HOST=http://broker-config-service:8049
      - BOOKING_ENGINE_HOST=http://booking-engine:8100
      - TARGET_REGISTRY_HOST=http://target-registry:8066
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9075,server=y,suspend=n
      - LOGGING_CONFIG=
    volumes:
      - ./curl-amd64:/bin/curl
    healthcheck:
      test: "curl --fail --silent http://localhost:8075/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

  license-server-mock:
    image: docker.wyden.io/cloud/dev/license-server-mock:latest
    container_name: license-server-mock
    profiles:
      - all
      - e2e_test
    ports:
      - "8108:8108"
      - "9108:9108" #debug port
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=*:9108,server=y,suspend=n
    healthcheck:
      test: "curl --fail --silent http://localhost:8108/actuator/health/readiness | grep UP || exit 1"
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

networks:
  default:
    ipam:
      driver: default
      config:
        - subnet: **********/16
          gateway: **********
