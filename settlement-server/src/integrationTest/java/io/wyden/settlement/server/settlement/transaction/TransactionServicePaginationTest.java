
package io.wyden.settlement.server.settlement.transaction;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.settlement.client.transaction.SettlementStreetCashTrade;
import io.wyden.settlement.server.settlement.TestContainersIntegrationBase;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.client.run.SortingOrder;
import io.wyden.settlement.client.transaction.TransactionSearchInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class TransactionServicePaginationTest extends TestContainersIntegrationBase {

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private SettlementRunRepository settlementRunRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        // Clear existing data
        jdbcTemplate.execute("DELETE FROM transaction_fee");
        jdbcTemplate.execute("DELETE FROM transactions");

        // Insert test transactions
        for (int i = 1; i <= 20; i++) {
            TransactionEntity transaction = createTestTransaction(i);
            transactionRepository.save(transaction);
        }
    }

    @Test
    void shouldReturnPaginatedResults() {
        // Given
        List<String> sortedVenueAccountIds = transactionRepository.findAll().stream()
            .map(transaction -> Integer.parseInt(transaction.venueAccount()))
            .sorted()
            .map(Object::toString)
            .toList();

        int pageSize = 5;
        TransactionSearchInput firstPageRequest = TransactionSearchInput.builder()
            .first(pageSize)
            .sortingOrder(SortingOrder.ASC)
            .build();

            // When
        PaginationModel.CursorConnection<SettlementStreetCashTrade> firstPage =
            transactionService.queryTransactions(firstPageRequest);

        // Then
        assertThat(firstPage.edges()).hasSize(pageSize);
        assertThat(firstPage.edges())
            .extracting(page -> page.node().venueAccount())
            .containsAll(sortedVenueAccountIds.subList(0, 5));
        assertThat(firstPage.pageInfo().hasNextPage()).isTrue();

        // Get next page using the cursor from the last item of first page
        String cursor = firstPage.pageInfo().endCursor();
        TransactionSearchInput secondPageRequest = TransactionSearchInput.builder()
            .first(pageSize)
            .sortingOrder(SortingOrder.ASC)
            .after(cursor)
            .build();

        // When
        PaginationModel.CursorConnection<SettlementStreetCashTrade> secondPage =
            transactionService.queryTransactions(secondPageRequest);

        // Then
        assertThat(secondPage.edges()).hasSize(pageSize);
        assertThat(secondPage.pageInfo().hasNextPage()).isTrue();
        assertThat(secondPage.edges())
            .extracting(page -> page.node().venueAccount())
            .containsAll(sortedVenueAccountIds.subList(5, 10));
        assertThat(secondPage.pageInfo().hasNextPage()).isTrue();

        // Verify no overlap between pages
        List<String> firstPageIds = firstPage.edges().stream()
            .map(edge -> edge.node().id())
            .toList();
        List<String> secondPageIds = secondPage.edges().stream()
            .map(edge -> edge.node().id())
            .toList();

        assertThat(firstPageIds).doesNotContainAnyElementsOf(secondPageIds);
    }

    @Test
    void shouldFilterByAccountId() {
        // Given
        String testAccountId = "5";
        TransactionSearchInput request = TransactionSearchInput.builder()
            .accountId(List.of(testAccountId))
            .build();

        // When
        PaginationModel.CursorConnection<SettlementStreetCashTrade> result =
            transactionService.queryTransactions(request);

        // Then
        assertThat(result.edges()).isNotEmpty();
        assertThat(result.edges()).allMatch(edge ->
            edge.node().venueAccount().equals(testAccountId));
    }

    private TransactionEntity createTestTransaction(int index) {
        return TransactionEntity.builder()
            .transactionUuid(UUID.randomUUID())
            .venueAccount("" + index)
            .currency("USD")
            .updatedAt(Timestamp.from(Instant.now()))
            .quantity(BigDecimal.valueOf(100L * index))
            .price(BigDecimal.valueOf(100L * index))
            .transactionDatetime(Timestamp.from(Instant.now()))
            .build();
    }
}
