package io.wyden.pricing.service.quoting;

import java.math.BigDecimal;

public class StaticPricer extends Pricer {

    private final BigDecimal askMarkup;
    private final BigDecimal bidMarkup;

    public StaticPricer(BigDecimal markup) {
        this.askMarkup = markup;
        this.bidMarkup = markup;
    }

    public StaticPricer(MarkupPair markupPair) {
        this.askMarkup = markupPair.askMarkup();
        this.bidMarkup = markupPair.bidMarkup();
    }

    @Override
    protected BigDecimal getBidMarkup() {
        return bidMarkup;
    }

    @Override
    protected BigDecimal getAskMarkup() {
        return askMarkup;
    }
}
