import { zodResolver } from '@hookform/resolvers/zod';
import { styled } from '@ui/styled';
import {
  PortfolioGroupConfigurationFlat,
  PortfolioResponse,
  PortfolioType,
  Resource,
  Scope,
} from '@wyden/services/graphql/generated/graphql';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  ConfirmPortfolioModal,
  flatPortfolioRequest,
} from '../portfolio-data/create-portfolio/CreatePortfolioDialogContent';
import { CreateTagDialog } from '../portfolio-data/CreateTagDialog';
import { usePortfolioGroupConfigurationList } from '../portfolio-data/usePortfolioGroupConfigurationIds';
import {
  PortfolioFormValues,
  usePortfolioManagement,
} from '../portfolio-data/usePortfolioManagement';
import { usePortfolioStore } from '../portfolio-data/usePortfolioStore';
import {
  EditPortfolioFormPage,
  PortfolioFormSchema,
  getPortfolioFormLabels,
} from '../portfolio-data/edit-portfolio/EditPortfolioForm';
import { useCurrencies } from '../currencies/useCurrencies';
import { usePermissions } from '@wyden/hooks/usePermissions';
import { Notification, useNotification } from '../error-indicators/notification/useNotification';

type Props = {
  portfolio?: PortfolioResponse | null;
};

export const EditPortfolioPage = ({ portfolio }: Props) => {
  const {
    createNewTagProps,
    clearNotSavedTags,
    confirmPortfolioCreateDialogOpen,
    toggleConfirmPortfolioCreateDialog,
  } = usePortfolioStore();

  const { addMessage } = useNotification();

  const onUpdateSuccess = () => {
    addMessage(Notification.SUCCESS, t('portfolioPage.portfolioUpdate'));
  };

  const onUpdateError = () => {
    addMessage(Notification.ERROR, t('portfolioPage.portfolioUpdate'));
  };
  const { updatePortfolio } = usePortfolioManagement({
    onUpdateSuccess,
    onUpdateError,
  });
  const { portfolioGroupConfigurationList } = usePortfolioGroupConfigurationList();
  const { t } = useTranslation();
  const { currencies } = useCurrencies();
  const isArchived = Boolean(portfolio?.archivedAt);
  const { checkIfPermitted } = usePermissions();

  const form = useForm<z.infer<typeof PortfolioFormSchema>>({
    resolver: zodResolver(PortfolioFormSchema),
    defaultValues: portfolio ? flatPortfolioRequest(portfolio) : undefined,
  });

  const setPortfolioFormTags = (tags: { key: string; value: string }[]) => {
    form.setValue('tags', tags);
  };
  const portfolioFormTags = form.getValues().tags;

  const formLabels = getPortfolioFormLabels(
    t,
    portfolioGroupConfigurationList?.filter(Boolean) as PortfolioGroupConfigurationFlat[],
    currencies,
    isArchived,
    checkIfPermitted({
      resource: Resource.BrokerConfig,
      scope: Scope.Read,
    }),
  );

  const isCreateTagDialogOpen = createNewTagProps.isOpen && createNewTagProps.action === 'edit';

  const handlePortfolioUpdate = (values: z.infer<typeof PortfolioFormSchema>) => {
    updatePortfolio(values as unknown as PortfolioFormValues, portfolio);
    clearNotSavedTags();
  };

  const handleSubmit = (values: z.infer<typeof PortfolioFormSchema>) => {
    const tryingToSubmitVostroPortfolioWithoutGroup =
      values.portfolioType === PortfolioType.Vostro && !values.portfolioGroup;

    if (tryingToSubmitVostroPortfolioWithoutGroup) {
      return toggleConfirmPortfolioCreateDialog();
    }

    handlePortfolioUpdate(values);
  };

  return (
    <>
      <Container>
        <EditPortfolioFormPage
          form={form}
          props={formLabels}
          schema={PortfolioFormSchema}
          onSubmit={handleSubmit}
        />
      </Container>

      {isCreateTagDialogOpen && (
        <CreateTagDialog
          setPortfolioFormTags={setPortfolioFormTags}
          portfolioFormTags={portfolioFormTags}
        />
      )}

      {confirmPortfolioCreateDialogOpen && (
        <ConfirmPortfolioModal
          onCancel={() => {
            toggleConfirmPortfolioCreateDialog();
          }}
          onSubmit={() => {
            handlePortfolioUpdate(form.getValues());
            toggleConfirmPortfolioCreateDialog();
          }}
        />
      )}
    </>
  );
};

const Container = styled('div')`
  display: flex;
  flex-direction: column;
  max-width: 600px;
`;
