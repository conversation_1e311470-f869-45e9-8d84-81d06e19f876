package io.wyden.risk.engine.referencedata;

import io.wyden.published.referencedata.Instrument;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import org.springframework.stereotype.Repository;

@Repository
public class InstrumentsRepository {
    private final InstrumentsCacheFacade instrumentsCacheFacade;

    public InstrumentsRepository(InstrumentsCacheFacade instrumentsCacheFacade) {
        this.instrumentsCacheFacade = instrumentsCacheFacade;
    }

    public Instrument find(String instrumentId) throws InstrumentNotFoundException {
        return instrumentsCacheFacade.find(instrumentId)
            .orElseThrow(() -> new InstrumentNotFoundException(instrumentId));
    }
}
