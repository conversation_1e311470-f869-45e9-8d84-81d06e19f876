package io.wyden.executionengine.domain.map;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.executionengine.domain.model.BookEntry;
import io.wyden.published.oems.OemsRequest;

import java.util.Map;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class StreetSidePendingOrdersSellMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "execution-engine-street-order-book-sells_0.2";

    public static IMap<String, Map<String, BookEntry>> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        if (findSerializerConfig(serializationConfig, OemsRequest.class).isEmpty()) {
            serializationConfig.addSerializerConfig(protobufSerializer(OemsRequest.class, OemsRequest.parser()));
        }
    }
}
