Feature: Portfolio Cash Transfer metrics calculation
  As a booking system
  I want to apply ledger entries to positions
  So that I calculate P&L for trades

  Background:
    Given last processed sequence number is 7

  Scenario: Client transfers USD to another portfolio with 10 USD fee booked on Bank portfolio
    Given portfolio Client_8 exists and is configured with USD currency
    Given portfolio Target_8 exists and is configured with USD currency
    And portfolio Bank_8 exists and is configured with USD currency

    Given a BookingCompleted event for test-transfer-8 with sequence number 8
    And the event contains a list of ledger entries with following details:
      | portfolioId | ledgerEntryType | instrument | qty     | price | fee    |
      | Client_8    | TRANSFER        | USD        | -10_000 | 1     | 10 USD |
      | Client_8    | TRANSFER_FEE    | USD        | -10     | 1     | -      |
      | Target_8    | TRANSFER        | USD        | 10_000  | 1     | -      |
      | Bank_8      | TRANSFER_FEE    | USD        | 10      | 1     | -      |

    When event with sequence number 8 is processed

    Then the ledger entries for test-transfer-8 should be stored in the database
    And positions should be updated with following details:
      | referenceId               | Client_8 | Target_8 | Bank_8 |
      | instrument                | USD      | USD      | USD    |
      | qty                       | -10_010  | 10_000   | 10     |
      | notionalQty               | -10_010  | 10_000   | 10     |
      | netAveragePrice           | 0.999001 | 1        | 1      |
      | grossAveragePrice         | 1        | 1        | 1      |
      | marketValue               | -10_010  | 10_000   | 10     |
      | marketValueSc             | -10_010  | 10_000   | 10     |
      | netCost                   | -10_000  | 10_000   | 10     |
      | netCostSc                 | -10_000  | 10_000   | 10     |
      | grossCost                 | -10_010  | 10_000   | 10     |
      | grossCostSc               | -10_010  | 10_000   | 10     |
      | netRealizedPnl            | -        | -        | -      |
      | netRealizedPnlSc          | -        | -        | -      |
      | grossRealizedPnl          | -        | -        | -      |
      | grossRealizedPnlSc        | -        | -        | -      |
      | netUnrealizedPnl          | -10      | -        | -      |
      | netUnrealizedPnlSc        | -10      | -        | -      |
      | grossUnrealizedPnl        | -        | -        | -      |
      | grossUnrealizedPnlSc      | -        | -        | -      |

# TODO SPL double check is transfers are settled immediately
