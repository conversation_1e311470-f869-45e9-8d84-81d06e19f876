package io.wyden.smartrecommendationengine.exchangeotcmix;

import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.published.smartrecommendationengine.Strategy;
import io.wyden.smartrecommendationengine.assertions.RankedCandidatesAssertions;
import io.wyden.smartrecommendationengine.domain.RecommendationSubscription;
import io.wyden.smartrecommendationengine.service.SmartRecommendationEngine;
import io.wyden.smartrecommendationengine.service.audit.AuditService;
import io.wyden.smartrecommendationengine.service.connectorstate.ConnectorStateService;
import io.wyden.smartrecommendationengine.service.marketdata.MarketDataCache;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static io.wyden.published.oems.OemsSide.BUY;
import static io.wyden.published.oems.OemsSide.SELL;
import static io.wyden.smartrecommendationengine.assertions.RankedCandidatesAssertions.assertThat;
import static java.util.Optional.empty;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BestExecutionRequestMixTest extends BestExecutionRequestMixTestBase {
    @Mock
    private MarketDataCache marketDataCache;
    @Mock
    private ConnectorStateService connectorStateService;
    @Mock
    private SmartRecommendationEngine smartRecommendationEngine;

    @Mock
    private RecommendationSubscription recommendationSubscription;

    @Mock
    private AuditService auditService;

    private final int stopOrderTriggerRequiredVenuesPercentage = 100;
    private final int maxMarketDataAge = 6000;

    public BestExecutionRequestMixTest() {
        marketDataCache = mock(MarketDataCache.class);
        connectorStateService = mock(ConnectorStateService.class);
        auditService = mock(AuditService.class);
        doNothing().when(auditService).sendAuditEventWithOrderBooks(anyList(), anyString(), anyString());

        when(connectorStateService.isTradingAlive(anyString())).thenReturn(true);
    }

    @Test
    public void testBuyOrderBetterExchange() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5"), // 1 * 100 + 2 * 200 + 300 = 1100
        new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
        );
    }

    @Test
    public void testBuyOrderBetterOtc() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5"), // 5 * 300 = 1500
        new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 1 * 400 + 2 * 500 + 2 * 600 = 2600
        );
    }

    @Test
    public void testSellOrderBetterExchange() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5"), // 3 * 600 + 2 * 400 = 2600
            new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
        );
    }

    @Test
    public void testSellOrderBetterOtc() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockWorstOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5"), // 5 * 400 = 2000
        new RankedCandidatesAssertions.RankingResult(BINANCE, "1300", "5") // 3 * 300 + 2 * 200 = 1300
        );
    }

    @Test
    public void testOBNotDeepEnoughForExchangeCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("20")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "8000", "20"), // 20 * 400 = 8000
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2800", "6") // 3 + 600 + 2 * 400 + 200 = 2800
        );
    }

    @Test
    public void testOBNotDeepEnoughForOtcCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockNotDeepEnoughOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 + 600 + 2 * 400 = 2600
        );
    }

    @Test
    public void testOBNotDeepEnoughForExchangeCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("20")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1400", "6"), // 1 * 100 + 2 * 200 + 3 * 300 = 1400
            new RankedCandidatesAssertions.RankingResult(B2C2, "6000", "20") // 20 * 300 = 6000
        );
    }

    @Test
    public void testOBNotDeepEnoughForOtcCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockNotDeepEnoughOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 2 * 300 = 1100
        );
    }

    @Test
    public void testOBNotDeepEnoughForBothCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("40")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2800", "6") // 3 + 600 + 2 * 400 + 200 = 2800
        );
    }

    @Test
    public void testOBNotDeepEnoughForBothCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("40")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        // Binance has volume of 6 - we take as much as we can
        // B2C2 has max volume of 2 - candidate excluded
        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1400", "6") // 1 * 100 + 2 * 200 + 3 * 300 = 1400
        );
    }

    @Test
    public void testOBNotPresentForExchangeCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
        );
    }

    @Test
    public void testOBNotPresentForOtcCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 300 = 1100
        );
    }

    @Test
    public void testOBNotPresentForExchangeCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
        );
    }

    @Test
    public void testOBNotPresentForOtcCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 * 600 + 2 * 400 = 2600
        );
    }

    @Test
    public void testOBNotPresentForBothCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

    @Test
    public void testOBNotPresentForBothCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(empty());
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(empty());

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

    @Test
    public void testOBTooOldForExchangeCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "1500", "5") // 5 * 300 = 1500
        );
    }

    @Test
    public void testOBTooOldForOtcCandidateBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "1100", "5") // 1 * 100 + 2 * 200 + 300 = 1100
        );
    }

    @Test
    public void testOBTooOldForExchangeCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(B2C2, "2000", "5") // 5 * 400 = 2000
        );
    }

    @Test
    public void testOBTooOldForOtcCandidateSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOtcOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockBestOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("5")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isCompletedWithResults(
            new RankedCandidatesAssertions.RankingResult(BINANCE, "2600", "5") // 3 * 600 + 2 * 400 = 2600
        );
    }

    @Test
    public void testOBTooOldForTwoCandidatesSellOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(SELL)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

    @Test
    public void testOBTooOldForTwoCandidatesBuyOrder() {
        when(marketDataCache.getOrderBookForInstrument(B2C2)).thenReturn(mockTooOldOrderBook(B2C2));
        when(marketDataCache.getOrderBookForInstrument(BINANCE)).thenReturn(mockTooOldOrderBook(BINANCE));

        BestExecutionRequest request = BestExecutionRequest.newBuilder()
            .setQuantity("10")
            .setSide(BUY)
            .setStrategy(Strategy.SIMPLE)
            .addAllInstruments(createInstrumentKeyList(B2C2, BINANCE))
            .build();

        smartRecommendationEngine = new SmartRecommendationEngine(request, recommendationSubscription, marketDataCache, connectorStateService, stopOrderTriggerRequiredVenuesPercentage, maxMarketDataAge, auditService);

        assertThat(smartRecommendationEngine.getRankedCandidates()).isEmpty();
    }

}
