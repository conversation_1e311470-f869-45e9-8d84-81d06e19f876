package io.wyden.apiserver.fix.infrastructure.fix;

import io.wyden.apiserver.fix.common.fix.FixMessageHandler;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.List;

@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE)
public class FixApiWiring {

    @Bean
    public GatewayFixApplication createGatewayFixApplication(FixSessionWrapper fixSessionWrapper,
                                                             List<FixMessageHandler> messageHandlers,
                                                             Telemetry telemetry,
                                                             @Value("${fix.socketAccept.port}") int socketAcceptPort) {
        GatewayFixApplication application = new GatewayFixApplication(messageHandlers, telemetry);
        fixSessionWrapper.init(application, socketAcceptPort);
        return application;
    }
}
