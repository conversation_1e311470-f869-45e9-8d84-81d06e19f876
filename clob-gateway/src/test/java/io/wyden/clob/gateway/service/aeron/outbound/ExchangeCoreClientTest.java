package io.wyden.clob.gateway.service.aeron.outbound;

import io.aeron.Publication;
import io.aeron.cluster.client.AeronCluster;
import org.agrona.concurrent.SleepingIdleStrategy;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ExchangeCoreClientTest {
    @Test
    void shouldRetryCommandSending() {
        AeronCluster cluster = mock(AeronCluster.class, Mockito.RETURNS_DEEP_STUBS);
        ExchangeCoreClient client = new ExchangeCoreClient(cluster, mock(), mock(), Duration.ofMillis(100));
        SleepingIdleStrategy idleStrategy = new SleepingIdleStrategy(10);
        when(cluster.context().idleStrategy()).thenReturn(idleStrategy);
        when(cluster.offer(any(), anyInt(), anyInt())).thenReturn(Publication.ADMIN_ACTION, Publication.BACK_PRESSURED, 1L);

        assertEquals(ExchangeCoreClient.ExchangeCoreCommandResult.SUCCESSFUL, client.symbolSpecifications());

        verify(cluster, times(3)).offer(any(), anyInt(), anyInt());
    }

    @Test
    void shouldHandleRetryTimeout() {
        AeronCluster cluster = mock(AeronCluster.class, Mockito.RETURNS_DEEP_STUBS);
        ExchangeCoreClient client = new ExchangeCoreClient(cluster, mock(), mock(), Duration.ofMillis(100));
        SleepingIdleStrategy idleStrategy = new SleepingIdleStrategy(10);
        when(cluster.context().idleStrategy()).thenReturn(idleStrategy);
        when(cluster.offer(any(), anyInt(), anyInt())).thenReturn(Publication.ADMIN_ACTION);

        assertEquals(ExchangeCoreClient.ExchangeCoreCommandResult.RETRY_TIMED_OUT, client.symbolSpecifications());

        verify(cluster, atLeast(5)).offer(any(), anyInt(), anyInt());
    }
}
