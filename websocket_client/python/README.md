
# Python client usage

You will need fairly recent Python, I have used 3.9.18.

Preparation:
1. Create virtual env with command ```python -m venv venv```
2. Activate virtual env with command ```. venv/bin/activate```
3. Install libraries ```pip install -r requirements.txt```
4. In UI generate API Key and API Secret
5. Put both values in ws.py file as api_key and api_secret

Testing on dev:
1. run ```python ws.py```

Testing on local:
1. Change in ws.py value ws_url to ```ws://localhost:8400/ws```
2. run ```python ws.py```
