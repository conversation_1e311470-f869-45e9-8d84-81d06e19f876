package io.wyden.booking.snapshotter.application.wal;

import io.wyden.booking.snapshotter.application.state.StateProvider;
import io.wyden.booking.snapshotter.domain.BookingService;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.snapshotter.domain.ledgerentry.SimpleReference;
import io.wyden.booking.snapshotter.domain.state.ReservationReleaseStateInput;
import io.wyden.booking.snapshotter.domain.state.StateOutput;
import io.wyden.published.booking.ReservationReleaseRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static io.wyden.booking.snapshotter.domain.transaction.Transaction.NO_FEES;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isNonZero;

@Component
public class ReservationReleaseProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReservationReleaseProcessor.class);

    private final BookingService bookingService;
    private final StateProvider stateProvider;

    public ReservationReleaseProcessor(BookingService bookingService, StateProvider stateProvider) {
        this.bookingService = bookingService;
        this.stateProvider = stateProvider;
    }

    public StateOutput process(long sequenceNumber, ReservationReleaseRequest reservationRelease) {
        String reservationReference = reservationRelease.getReservationReference();
        ReservationReleaseStateInput stateInput = stateProvider.fetchStateInput(reservationRelease);

        // find all ledger entries for reservationRef
        // and group them by portfolio (or account) + instrument
        Map<SimpleReference, List<LedgerEntry>> groupedLedgerEntries = stateInput.getActiveReservationEntries(reservationReference).stream()
            .collect(Collectors.groupingBy(l -> new SimpleReference(l.getSymbol(), l.getPortfolioId(), l.getAccountId())));

        // for each unique group, calculate outstanding reservation value, and create RawLedgerEntry of type RELEASE
        List<LedgerEntry> ledgerEntries = groupedLedgerEntries.entrySet().stream()
            .flatMap(entry -> getReleaseRemainingReservationLedgerEntry(entry.getValue(), sequenceNumber).stream())
            .filter(ledgerEntry -> isNonZero(ledgerEntry.getQuantity()))
            .toList();

        return bookingService.book(ledgerEntries, stateInput)
            .addSequenceNumber(sequenceNumber)
            .setReservationRelease(reservationReference)
            .buildSuccessful();
    }

    private List<LedgerEntry> getReleaseRemainingReservationLedgerEntry(Collection<LedgerEntry> ledgerEntries, long sequenceNumber) {
        if (ledgerEntries == null || ledgerEntries.isEmpty()) {
            return List.of();
        }

        LedgerEntry anyLedgerEntry = ledgerEntries.stream().findFirst().orElseThrow();
        String transactionId = anyLedgerEntry.getTransactionId();
        String reservationRef = anyLedgerEntry.getReservationRef();
        String portfolioId = anyLedgerEntry.getPortfolioId();
        String accountId = anyLedgerEntry.getAccountId();
        String symbol = anyLedgerEntry.getSymbol();

        BigDecimal outstandingReservationValue = getOutstandingReservationValue(ledgerEntries);

        LedgerEntry release =
            new LedgerEntry(
                outstandingReservationValue.negate(),
                null,
                NO_FEES,
                LedgerEntryType.RESERVATION_RELEASE,
                portfolioId,
                accountId,
                symbol,
                reservationRef,
                transactionId,
                true,
                sequenceNumber
            );

        LedgerEntry releaseRemaining = new LedgerEntry(
            BigDecimal.ZERO,
            null,
            NO_FEES,
            LedgerEntryType.RESERVATION_RELEASE_REMAINING,
            portfolioId,
            accountId,
            symbol,
            reservationRef,
            transactionId,
            true,
            sequenceNumber
        );

        return List.of(release, releaseRemaining);
    }

    private BigDecimal getOutstandingReservationValue(Collection<LedgerEntry> ledgerEntries) {
        return ledgerEntries.stream()
            .filter(le -> le.getType().isReservationOrRelease() && le.getQuantity() != null)
            .map(LedgerEntry::getQuantity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
