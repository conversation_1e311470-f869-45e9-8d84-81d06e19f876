import { OrderStateResponse } from '@wyden/services/graphql/generated/graphql';
import * as Sentry from '@sentry/react';

export interface OrderStateResponseWithSubOrders extends OrderStateResponse {
  subOrders: string[];
}

const recursivelyAttachParent = (
  orderStates: OrderStateResponse[],
  parentOrderId: string | undefined | null,
  subOrders: string[],
): string[] => {
  const parentOrder = orderStates.find((order) => order.orderId === parentOrderId);
  const isCorruptedParent = parentOrder && parentOrder.parentOrderId === parentOrderId;

  if (isCorruptedParent) {
    Sentry.captureException(
      new Error(`Order has same orderId and parentOrderId: ${parentOrderId}`),
    );
    throw new Error('Order has same orderId and parentOrderId');
  }

  if (parentOrder?.parentOrderId) {
    return recursivelyAttachParent(orderStates, parentOrder.parentOrderId, [
      parentOrder.orderId as string,
      ...subOrders,
    ]);
  } else if (parentOrder) {
    return [parentOrder.orderId as string, ...subOrders];
  } else {
    return subOrders;
  }
};

export const addSubOrders = (
  orderStates: OrderStateResponse[],
): OrderStateResponseWithSubOrders[] => {
  const result: OrderStateResponseWithSubOrders[] = [];

  orderStates.forEach((orderState) => {
    const subOrders = recursivelyAttachParent(orderStates, orderState.parentOrderId, [
      orderState.orderId as string,
    ]);

    result.push({
      ...orderState,
      subOrders,
    });
  });
  return result;
};
