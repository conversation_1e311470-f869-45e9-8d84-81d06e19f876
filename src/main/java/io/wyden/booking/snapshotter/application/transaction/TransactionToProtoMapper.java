package io.wyden.booking.snapshotter.application.transaction;

import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.booking.snapshotter.domain.transaction.TransactionFeeType;
import io.wyden.booking.snapshotter.domain.transaction.payment.Deposit;
import io.wyden.booking.snapshotter.domain.transaction.payment.Withdrawal;
import io.wyden.booking.snapshotter.domain.transaction.trade.ClientAssetTrade;
import io.wyden.booking.snapshotter.domain.transaction.trade.ClientCashTrade;
import io.wyden.booking.snapshotter.domain.transaction.trade.StreetAssetTrade;
import io.wyden.booking.snapshotter.domain.transaction.trade.StreetCashTrade;
import io.wyden.booking.snapshotter.domain.transaction.transfer.AccountCashTransfer;
import io.wyden.booking.snapshotter.domain.transaction.transfer.PortfolioAssetTransfer;
import io.wyden.booking.snapshotter.domain.transaction.transfer.PortfolioCashTransfer;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientAssetTradeSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioAssetTransferSnapshot;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.StreetAssetTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import io.wyden.published.common.Metadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;

import static io.wyden.booking.snapshotter.domain.common.Identifiers.randomIdentifier;
import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public final class TransactionToProtoMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionToProtoMapper.class);

    private TransactionToProtoMapper() {
    }

    public static TransactionSnapshot map(Transaction transaction) {
        String createdAt = transaction.getCreatedAt() != null ?
            DateUtils.toIsoUtcTime(transaction.getCreatedAt())
            : DateUtils.toIsoUtcTime(ZonedDateTime.now());

        String updatedAt = transaction.getUpdatedAt() != null ?
            DateUtils.toIsoUtcTime(transaction.getUpdatedAt())
            : createdAt;

        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(createdAt)
            .setUpdatedAt(updatedAt)
            .setResponseId(randomIdentifier())
            .build();

        if (transaction instanceof ClientCashTrade trade) {
            ClientCashTradeSnapshot.Builder snapshot = ClientCashTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(trade.isLive());

            ZonedDateTime dateTime = trade.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = trade.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = trade.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = trade.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = trade.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = trade.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = trade.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal leavesQuantity = trade.getLeavesQuantity();
            if (leavesQuantity != null) {
                snapshot.setLeavesQuantity(toProtoString(leavesQuantity));
            }

            BigDecimal price = trade.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = trade.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String intOrderId = trade.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = trade.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String orderId = trade.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String parentOrderId = trade.getParentOrderId();
            if (parentOrderId != null) {
                snapshot.setParentOrderId(parentOrderId);
            }

            String rootOrderId = trade.getRootOrderId();
            if (rootOrderId != null) {
                snapshot.setRootOrderId(rootOrderId);
            }

            String clientRootOrderId = trade.getClientRootOrderId();
            if (clientRootOrderId != null) {
                snapshot.setClientRootOrderId(clientRootOrderId);
            }

            String baseCurrency = trade.getBaseCurrency();
            if (baseCurrency != null) {
                snapshot.setBaseCurrency(baseCurrency);
            }

            String portfolioId = trade.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String counterPortfolioId = trade.getCounterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                snapshot.setCounterPortfolio(counterPortfolioId);
            }

            Collection<Fee> fees = map(trade.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            String underlyingExecutionId = trade.getUnderlyingExecutionId();
            if (underlyingExecutionId != null) {
                snapshot.setUnderlyingExecutionId(underlyingExecutionId);
            }

            String rootExecutionId = trade.getRootExecutionId();
            if (rootExecutionId != null) {
                snapshot.setRootExecutionId(rootExecutionId);
            }

            snapshot.setSettled(trade.isSettled());

            ZonedDateTime settledDateTime = trade.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setClientCashTrade(snapshot)
                .build();
        }

        if (transaction instanceof StreetCashTrade trade) {
            StreetCashTradeSnapshot.Builder snapshot = StreetCashTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(trade.isLive());

            ZonedDateTime dateTime = trade.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = trade.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = trade.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = trade.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = trade.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = trade.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = trade.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal leavesQuantity = trade.getLeavesQuantity();
            if (leavesQuantity != null) {
                snapshot.setLeavesQuantity(toProtoString(leavesQuantity));
            }

            BigDecimal price = trade.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = trade.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String orderId = trade.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String parentOrderId = trade.getParentOrderId();
            if (parentOrderId != null) {
                snapshot.setParentOrderId(parentOrderId);
            }

            String rootOrderId = trade.getRootOrderId();
            if (rootOrderId != null) {
                snapshot.setRootOrderId(rootOrderId);
            }

            String clientRootOrderId = trade.getClientRootOrderId();
            if (clientRootOrderId != null) {
                snapshot.setClientRootOrderId(clientRootOrderId);
            }

            String intOrderId = trade.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = trade.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String baseCurrency = trade.getBaseCurrency();
            if (baseCurrency != null) {
                snapshot.setBaseCurrency(baseCurrency);
            }

            String portfolioId = trade.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = trade.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setVenueAccount(accountId);
            }

            String underlyingExecutionId = trade.getUnderlyingExecutionId();
            if (underlyingExecutionId != null) {
                snapshot.setUnderlyingExecutionId(underlyingExecutionId);
            }

            String rootExecutionId = trade.getRootExecutionId();
            if (rootExecutionId != null) {
                snapshot.setRootExecutionId(rootExecutionId);
            }

            Collection<Fee> fees = map(trade.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(trade.isSettled());

            ZonedDateTime settledDateTime = trade.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            Long settlementId = trade.getSettlementId();
            if (settlementId != null) {
                snapshot.setSettlementId(String.valueOf(settlementId));
            }

            return TransactionSnapshot.newBuilder()
                .setStreetCashTrade(snapshot)
                .build();
        }

        if (transaction instanceof ClientAssetTrade trade) {
            ClientAssetTradeSnapshot.Builder snapshot = ClientAssetTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(trade.isLive());

            ZonedDateTime dateTime = trade.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = trade.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = trade.getReservationRef();
            if (isNotBlank(reservationRef)) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = trade.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = trade.getVenueExecutionId();
            if (isNotBlank(venueExecutionId)) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = trade.getDescription();
            if (isNotBlank(description)) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = trade.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = trade.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = trade.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String intOrderId = trade.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = trade.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String orderId = trade.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String asset = trade.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String portfolioId = trade.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String counterPortfolioId = trade.getCounterPortfolioId();
            if (isNotBlank(counterPortfolioId)) {
                snapshot.setCounterPortfolio(counterPortfolioId);
            }

            Collection<Fee> fees = map(trade.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(trade.isSettled());

            ZonedDateTime settledDateTime = trade.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setClientAssetTrade(snapshot)
                .build();
        }

        if (transaction instanceof StreetAssetTrade trade) {
            StreetAssetTradeSnapshot.Builder snapshot = StreetAssetTradeSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(trade.isLive());

            ZonedDateTime dateTime = trade.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = trade.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = trade.getReservationRef();
            if (isNotBlank(reservationRef)) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = trade.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = trade.getVenueExecutionId();
            if (isNotBlank(venueExecutionId)) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = trade.getDescription();
            if (isNotBlank(description)) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = trade.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            BigDecimal price = trade.getPrice();
            if (price != null) {
                snapshot.setPrice(toProtoString(price));
            }

            String currency = trade.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String intOrderId = trade.getIntOrderId();
            if (intOrderId != null) {
                snapshot.setIntOrderId(intOrderId);
            }

            String extOrderId = trade.getExtOrderId();
            if (extOrderId != null) {
                snapshot.setExtOrderId(extOrderId);
            }

            String orderId = trade.getOrderId();
            if (orderId != null) {
                snapshot.setOrderId(orderId);
            }

            String asset = trade.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String portfolioId = trade.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = trade.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setVenueAccount(accountId);
            }

            Collection<Fee> fees = map(trade.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(trade.isSettled());

            ZonedDateTime settledDateTime = trade.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setStreetAssetTrade(snapshot)
                .build();
        }

        if (transaction instanceof Deposit deposit) {
            DepositSnapshot.Builder snapshot = DepositSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(deposit.isLive());

            ZonedDateTime dateTime = deposit.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = deposit.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = deposit.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = deposit.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = deposit.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = deposit.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = deposit.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = deposit.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = deposit.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = deposit.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccount(accountId);
            }

            String feePortfolioId = deposit.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = deposit.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            Collection<Fee> fees = map(deposit.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(deposit.isSettled());

            ZonedDateTime settledDateTime = deposit.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setDeposit(snapshot)
                .build();
        }

        if (transaction instanceof Withdrawal withdrawal) {
            WithdrawalSnapshot.Builder snapshot = WithdrawalSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(withdrawal.isLive());

            ZonedDateTime dateTime = withdrawal.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = withdrawal.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = withdrawal.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = withdrawal.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = withdrawal.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = withdrawal.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = withdrawal.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = withdrawal.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String portfolioId = withdrawal.getPortfolioId();
            if (isNotBlank(portfolioId)) {
                snapshot.setPortfolio(portfolioId);
            }

            String accountId = withdrawal.getAccountId();
            if (isNotBlank(accountId)) {
                snapshot.setAccount(accountId);
            }

            String feePortfolioId = withdrawal.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            String feeAccountId = withdrawal.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            Collection<Fee> fees = map(withdrawal.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(withdrawal.isSettled());

            ZonedDateTime settledDateTime = withdrawal.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setWithdrawal(snapshot)
                .build();
        }

        if (transaction instanceof PortfolioCashTransfer transfer) {
            PortfolioCashTransferSnapshot.Builder snapshot = PortfolioCashTransferSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transfer.isLive());

            ZonedDateTime dateTime = transfer.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = transfer.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transfer.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transfer.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transfer.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transfer.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transfer.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = transfer.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String fromPortfolioId = transfer.getFromPortfolioId();
            if (isNotBlank(fromPortfolioId)) {
                snapshot.setFromPortfolioId(fromPortfolioId);
            }

            String toPortfolioId = transfer.getToPortfolioId();
            if (isNotBlank(toPortfolioId)) {
                snapshot.setToPortfolioId(toPortfolioId);
            }

            Collection<Fee> fees = map(transfer.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transfer.isSettled());

            ZonedDateTime settledDateTime = transfer.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            String feePortfolioId = transfer.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            return TransactionSnapshot.newBuilder()
                .setPortfolioCashTransfer(snapshot)
                .build();
        }

        if (transaction instanceof AccountCashTransfer transfer) {
            AccountCashTransferSnapshot.Builder snapshot = AccountCashTransferSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transfer.isLive());

            ZonedDateTime dateTime = transfer.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = transfer.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transfer.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transfer.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transfer.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transfer.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transfer.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String currency = transfer.getCurrency();
            if (currency != null) {
                snapshot.setCurrency(currency);
            }

            String fromAccountId = transfer.getFromAccountId();
            if (isNotBlank(fromAccountId)) {
                snapshot.setFromAccountId(fromAccountId);
            }

            String toAccountId = transfer.getToAccountId();
            if (isNotBlank(toAccountId)) {
                snapshot.setToAccountId(toAccountId);
            }

            String feeAccountId = transfer.getFeeAccountId();
            if (isNotBlank(feeAccountId)) {
                snapshot.setFeeAccountId(feeAccountId);
            }

            String feePortfolioId = transfer.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            Collection<Fee> fees = map(transfer.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transfer.isSettled());

            ZonedDateTime settledDateTime = transfer.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            return TransactionSnapshot.newBuilder()
                .setAccountCashTransfer(snapshot)
                .build();
        }

        if (transaction instanceof PortfolioAssetTransfer transfer) {
            PortfolioAssetTransferSnapshot.Builder snapshot = PortfolioAssetTransferSnapshot.newBuilder()
                .setMetadata(metadata)
                .setIsLive(transfer.isLive());

            ZonedDateTime dateTime = transfer.getDateTime();
            if (dateTime != null) {
                snapshot.setDateTime(DateUtils.toIsoUtcTime(dateTime));
            }

            String uuid = transfer.getUuid();
            if (uuid != null) {
                snapshot.setUuid(uuid);
            }

            String reservationRef = transfer.getReservationRef();
            if (reservationRef != null) {
                snapshot.setReservationRef(reservationRef);
            }

            String executionId = transfer.getExecutionId();
            if (executionId != null) {
                snapshot.setExecutionId(executionId);
            }

            String venueExecutionId = transfer.getVenueExecutionId();
            if (venueExecutionId != null) {
                snapshot.setVenueExecutionId(venueExecutionId);
            }

            String description = transfer.getDescription();
            if (description != null) {
                snapshot.setDescription(description);
            }

            BigDecimal quantity = transfer.getQuantity();
            if (quantity != null) {
                snapshot.setQuantity(toProtoString(quantity));
            }

            String asset = transfer.getAsset();
            if (asset != null) {
                snapshot.setInstrument(asset);
            }

            String fromPortfolioId = transfer.getFromPortfolioId();
            if (isNotBlank(fromPortfolioId)) {
                snapshot.setFromPortfolioId(fromPortfolioId);
            }

            String toPortfolioId = transfer.getToPortfolioId();
            if (isNotBlank(toPortfolioId)) {
                snapshot.setToPortfolioId(toPortfolioId);
            }

            Collection<Fee> fees = map(transfer.getFees());
            if (fees != null) {
                snapshot.addAllTransactionFee(fees);
            }

            snapshot.setSettled(transfer.isSettled());

            ZonedDateTime settledDateTime = transfer.getSettledDateTime();
            if (settledDateTime != null) {
                snapshot.setSettledDateTime(DateUtils.toIsoUtcTime(settledDateTime));
            }

            String feePortfolioId = transfer.getFeePortfolioId();
            if (isNotBlank(feePortfolioId)) {
                snapshot.setFeePortfolioId(feePortfolioId);
            }

            return TransactionSnapshot.newBuilder()
                .setPortfolioAssetTransfer(snapshot)
                .build();
        }

        LOGGER.warn("Unknown transaction type. Cannot convert to proto, returning empty proto: {}", transaction);

        return TransactionSnapshot.newBuilder()
            .build();
    }

    public static Collection<Fee> map(Collection<io.wyden.booking.snapshotter.domain.transaction.TransactionFee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> Fee.newBuilder()
                .setAmount(toProtoString(fee.getAmount()))
                .setCurrency(fee.getCurrency())
                .setFeeType(map(fee.getFeeType()))
                .build())
            .toList();
    }

    private static FeeType map(TransactionFeeType feeType) {
        if (feeType == null) {
            return null;
        }

        return switch (feeType) {
            case EXCHANGE_FEE -> FeeType.EXCHANGE_FEE;
            case FIXED_FEE -> FeeType.FIXED_FEE;
            case TRANSACTION_FEE -> FeeType.TRANSACTION_FEE;
            default -> FeeType.FEE_TYPE_UNSPECIFIED;
        };
    }
}
