import { ERRORS } from '@wyden/constants';
import { useEventLogs } from '@wyden/features/error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '@wyden/features/error-indicators/network-indicators/events';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import {
  PortfolioResponse,
  PortfolioSearchInput,
  usePortfolioSearchQuery,
} from '@wyden/services/graphql/generated/graphql';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  onDataFetched?: (data: PortfolioResponse[]) => void;
  search?: Partial<PortfolioSearchInput>;
  // skips the query execution
  skip?: boolean;
  fetchPolicy?: 'network-only';
}

export const usePortfolioSearch = ({ onDataFetched, search, skip, fetchPolicy }: Props = {}) => {
  const { upsertRequest } = useNetworkStore();
  const { addEventLog } = useEventLogs();
  const { t } = useTranslation();

  const {
    data: portfoliosData,
    error: loadingPortfoliosError,
    refetch: portfoliosRefetch,
    loading: portfoliosLoading,
    fetchMore: portfoliosFetchMore,
  } = usePortfolioSearchQuery({
    onCompleted: (data) => {
      onDataFetched && onDataFetched(data.portfolioSearch?.edges.map(({ node }) => node) ?? []);
    },
    variables: {
      search: {
        first: 50,
        ...search,
      },
    },
    fetchPolicy: fetchPolicy,
    skip,
    onError: (err) => {
      onDataFetched && onDataFetched([]);
      upsertRequest('portfolioSearch', {
        pending: false,
        error: ERRORS.CLIENT_ERROR,
        err,
      });
      addEventLog({
        type: NetworkEvents.PORTFOLIOS_QUERY,
        message: t('eventLogs.requestFailed', {
          name: t('portfolios.portfolioQuery'),
        }),
        timestamp: new Date().getTime(),
      });
    },
  });

  const portfolios = useMemo(
    () => portfoliosData?.portfolioSearch?.edges.map(({ node }) => node) ?? [],
    [portfoliosData],
  );

  return {
    portfolios,
    portfoliosRefetch,
    portfoliosLoading,
    loadingPortfoliosError,
    portfoliosFetchMore,
    pageInfo: portfoliosData?.portfolioSearch?.pageInfo,
  };
};
