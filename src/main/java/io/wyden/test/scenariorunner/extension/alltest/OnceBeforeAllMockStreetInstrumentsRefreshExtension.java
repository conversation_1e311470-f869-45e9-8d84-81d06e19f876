package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.test.scenariorunner.config.Configuration;
import io.wyden.test.scenariorunner.config.HealthObserverSettings;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.extension.common.OnceBeforeAllExtension;
import io.wyden.test.scenariorunner.integration.service.ReferenceDataClient;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.integration.service.ServiceHealthObserver;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * This extension:<br>
 * - once before all tests refreshes street side instruments by venue [WydenMock] via reference-data
 */
public class OnceBeforeAllMockStreetInstrumentsRefreshExtension extends OnceBeforeAllExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(OnceBeforeAllMockStreetInstrumentsRefreshExtension.class);

    private static final HealthObserverSettings HEALTH_OBSERVER_SETTINGS = Configuration.getHealthObserverSettings();

    private static final int minMockInstrumentsCount = 1500;

    // mock venue will be refreshed by default
    private static final List<String> venues = List.of(
        VenueAccountConstants.MOCK_VENUE
    );

    public OnceBeforeAllMockStreetInstrumentsRefreshExtension() {
        LOGGER.debug("Initialized with venues: " + venues);
    }

    @Override
    public void onceBeforeAll(ExtensionContext context) {
        if (HEALTH_OBSERVER_SETTINGS.healthCheckEnabled()) {
            ServiceHealthObserver serviceHealthObserver = new ServiceHealthObserver();
            serviceHealthObserver.waitServiceIsUp(Service.REFERENCE_DATA, Timeouts.WAIT_FOR_CONDITION_XL);
        }

        ReferenceDataClient referenceDataClient = new ReferenceDataClient();

        if (allMockInstrumentsLoaded(referenceDataClient)) {
            LOGGER.info("Mock instruments refresh not needed. Most-likely it was done before");
            return;
        }

        for (String venue : venues) {

            referenceDataClient.refreshStreetSideInstrumentsByVenue(venue);

            WaitUtils.waitUntilIgnoringExceptions("all mock instruments loaded",
                () -> allMockInstrumentsLoaded(referenceDataClient),
                Timeouts.WAIT_FOR_CONDITION,
                Timeouts.WAIT_FOR_CONDITION_XL.multipliedBy(3));
        }
    }

    private static boolean allMockInstrumentsLoaded(ReferenceDataClient referenceDataClient) {
        LOGGER.info("Check if all mock instruments loaded");
        return referenceDataClient.getAllInstruments().getInstrumentList()
            .stream()
            .filter(i -> i.getBaseInstrument().getVenueName().equals(VenueAccountConstants.MOCK_VENUE))
            .count() >= minMockInstrumentsCount;
    }

}
