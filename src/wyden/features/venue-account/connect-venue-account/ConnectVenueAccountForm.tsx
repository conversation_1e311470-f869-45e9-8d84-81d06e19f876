import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import { Select } from '@wyden/features/form/Select';
import { TextField } from '@wyden/features/form/TextField';
import { z } from 'zod';
import { DialogForm, DialogFormProps } from '../../form/DialogForm';
import { KeyValuesField, ConfigFieldEntry } from '../../config-field/ConfigFieldEntry';
import { useVenueAccountStore } from '../useVenueAccountStore';
import { EditAccountFormComponent } from '../edit-account/EditAccountForm';

const VenueField = createUniqueFieldSchema(
  z.string({
    required_error: 'venueAccounts.accountForm.venueIsRequired',
  }),
  'venue',
);

const VenueAccountFormSchemaZObject = {
  venue: VenueField,
  venueAccountName: z.string({
    required_error: 'venueAccounts.accountForm.venueAccountIsRequired',
  }),
  keyValues: KeyValuesField,
};

export const VenueAccountFormSchema = z
  .object(VenueAccountFormSchemaZObject)
  .superRefine((values, ctx) => {
    values.keyValues.forEach((val, index) => {
      const allOtherValueKeys = values.keyValues
        .filter((_val, cbIndex) => cbIndex !== index)
        .map((val) => val.key);

      if (allOtherValueKeys.includes(val.key)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '',
          path: ['keyValues', index, 'key'],
        });
      }
    });
  });

export const mapping = [
  [VenueField, Select],
  [z.string(), TextField],
  [KeyValuesField, ConfigFieldEntry],
] as const;

const ConnectDialogForm = (props: DialogFormProps) => {
  const { toggleConnectVenueAccountDialog } = useVenueAccountStore();
  return (
    <DialogForm
      {...props}
      toggleDialog={toggleConnectVenueAccountDialog}
      titleTranslationId="venueAccounts.connectVenueAccount"
      submitButtonTranslationId="venueAccounts.accountForm.connectSubmit"
    />
  );
};

export const ConnectVenueAccountForm = createTsForm(mapping, {
  FormComponent: ConnectDialogForm,
});

export const EditAccountForm = createTsForm(mapping, {
  FormComponent: EditAccountFormComponent,
});
