package io.wyden.apiserver.rest.venueaccount;

import com.google.protobuf.TextFormat;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class VenueAccountCreateRequestEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountCreateRequestEmitter.class);

    private final RabbitExchange<VenueAccountCreateRequest> venueAccountCreateRequestExchange;

    public VenueAccountCreateRequestEmitter(final RabbitExchange<VenueAccountCreateRequest> venueAccountCreateRequestExchange) {
        this.venueAccountCreateRequestExchange = venueAccountCreateRequestExchange;
    }

    public void emit(VenueAccountCreateRequest request) {
        LOGGER.info("Emitting venue account creation request: {}", TextFormat.shortDebugString(request));
        venueAccountCreateRequestExchange.publish(request, StringUtils.EMPTY);
    }
}
