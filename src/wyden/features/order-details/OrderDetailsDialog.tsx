import { Button } from '@ui/Button';
import { Dialog } from '@ui/Dialog';
import { DialogActions } from '@ui/DialogActions';
import { DialogContent } from '@ui/DialogContent';
import { DialogTitle } from '@ui/DialogTitle';
import { styled } from '@ui/styled';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { Header } from '@ui/Typography/Header';
import { Paragraph } from '@ui/Typography/Paragraph';
import { Timeline } from '@wyden/features/order-details/Timeline';
import { getSpacing } from '@wyden/utils/styles';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderDetailsInfo } from './OrderDetailsInfo';
import { useOrderDetailsStore } from './useOrderDetails';

export const OrderDetailsDialog = () => {
  const { t } = useTranslation();
  const [tab, setTab] = useState('terms');
  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => setTab(newValue);
  const { isOrderHistoryDetailsDialogOpen, order, closeOrderDetailsDialog } =
    useOrderDetailsStore();
  const handleCloseOrderDetailsDialog = () => {
    closeOrderDetailsDialog();
    setTab('terms');
  };

  return (
    <StyledDialog
      open={isOrderHistoryDetailsDialogOpen}
      onClose={handleCloseOrderDetailsDialog}
      maxWidth="lg"
    >
      <DialogTitle onClose={handleCloseOrderDetailsDialog}>
        <StyledTitle>
          <Header variant="h4">{t('ordersHistory.orderDetails.orderDetails')}</Header>
        </StyledTitle>
      </DialogTitle>
      <StyledDialogContent>
        <StyledParagraph>
          {t('common.id')}: {order?.orderId}
        </StyledParagraph>
        <DetailsHeaderParagraph variant="big">
          {t('ordersHistory.orderDetails.details')}
        </DetailsHeaderParagraph>
        <Tabs withDivider value={tab} onChange={handleTabChange}>
          <Tab label={t('ordersHistory.orderDetails.terms')} value="terms" />
          <Tab label={t('ordersHistory.orderDetails.timeline')} value="timeline" />
        </Tabs>
        {tab === 'terms' && <OrderDetailsInfo order={order} />}
        {tab === 'timeline' && <Timeline order={order} />}
      </StyledDialogContent>
      <DialogActions>
        <Button onClick={handleCloseOrderDetailsDialog} size="lg">
          {t('ordersHistory.closeDetails')}
        </Button>
      </DialogActions>
    </StyledDialog>
  );
};

const StyledTitle = styled('div')`
  display: flex;
  align-items: center;
  gap: ${getSpacing(2)};
`;

const DetailsHeaderParagraph = styled(Paragraph)`
  color: ${({ theme }) => theme.palette.text.primary};
  margin: ${getSpacing(4)} 0 ${getSpacing(2)} 0;
  font-size: 14px;
  font-weight: 500;
`;

const StyledParagraph = styled(Paragraph)`
  font-size: 12px;
  font-weight: 400;
`;

const StyledDialog = styled(Dialog)`
  .MuiDialog-container {
    & > .MuiPaper-root {
      width: 100%;
      height: 100%;
    }
  }
`;

const StyledDialogContent = styled(DialogContent)`
  display: flex;
  flex-direction: column;
`;
