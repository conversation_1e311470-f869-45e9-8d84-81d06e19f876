package io.wyden.cloudutils.rabbitmq.queue;

import com.google.common.base.Strings;
import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import jakarta.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;

import static java.util.Objects.nonNull;

public class RabbitQueueBuilder<T extends Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RabbitQueueBuilder.class);

    private final RabbitIntegrator rabbitIntegrator;

    private String queueName;

    private String consumerName;

    /**
     * Prefer cleaning queues from RabbitMQ by setting x-expires (queue TTL) over autoDelete.
     * The latter may introduce errors that are hard to find - e.g. we may be losing queues if the client temporarily disconnects
     * <a href="https://www.rabbitmq.com/ttl.html#queue-ttl">https://www.rabbitmq.com/ttl.html#queue-ttl</a>
     * <a href="https://stackoverflow.com/questions/61517133/rabbitmq-temporary-queue-dies-periodically">https://stackoverflow.com/questions/61517133/rabbitmq-temporary-queue-dies-periodically</a>
     */
    private boolean autoDelete = false;

    private boolean durable = true;
    private boolean quorum = true;

    private boolean singleActiveConsumer;
    @Nullable private Duration messageTTL;
    @Nullable private Duration queueTTL;
    @Nullable private Predicate<AMQP.BasicProperties> filter;

    private RabbitExchange<T> deadLetterExchange;

    public RabbitQueueBuilder(RabbitIntegrator rabbitIntegrator) {
        this.rabbitIntegrator = rabbitIntegrator;
    }

    public RabbitQueueBuilder<T> setQueueName(String queueName) {
        this.queueName = queueName;
        return this;
    }

    public RabbitQueueBuilder<T> setConsumerName(String consumerName) {
        this.consumerName = consumerName;
        return this;
    }

    public RabbitQueueBuilder<T> setSingleActiveConsumer(boolean singleActiveConsumer) {
        this.singleActiveConsumer = singleActiveConsumer;
        return this;
    }

    public RabbitQueueBuilder<T> setDeadLetterExchange(RabbitExchange<T> deadLetterExchange) {
        this.deadLetterExchange = deadLetterExchange;
        return this;
    }

    public RabbitQueueBuilder<T> setMessageTTL(Duration messageTTL) {
        this.messageTTL = messageTTL;
        return this;
    }

    public RabbitQueueBuilder<T> setQueueTTL(Duration queueTTL) {
        this.queueTTL = queueTTL;
        return this;
    }

    public RabbitQueueBuilder<T> setQuorum(boolean quorum) {
        this.quorum = quorum;
        if (quorum) {
            this.durable = true;
        }
        return this;
    }

    public RabbitQueueBuilder<T> setDurable(boolean durable) {
        this.durable = durable;
        return this;
    }

    public RabbitQueueBuilder<T> setFilter(Predicate<AMQP.BasicProperties> filter) {
        this.filter = filter;
        return this;
    }

    void verify() {
        boolean expiringCheck = autoDelete && nonNull(queueTTL);
        if (expiringCheck) {
            throw new IllegalArgumentException("Expiring queue cannot be autoDelete and expires at same time");
        }

        if (quorum && !durable) {
            throw new IllegalArgumentException("Quorum queue can't be non durable");
        }
    }

    public RabbitQueue<T> declare() {
        verify();

        if (Strings.isNullOrEmpty(queueName)) {
            queueName = "common-" + UUID.randomUUID();
            LOGGER.warn("Queue name not assigned. Auto-assigning UUID name: {}", queueName);
        }

        RabbitQueue<T> queue = new RabbitQueue<>(
            new RawRabbitQueue(rabbitIntegrator, queueName, consumerName, singleActiveConsumer, quorum, durable, deadLetterExchange, messageTTL, queueTTL));

        queue.declare();

        Optional.ofNullable(filter).ifPresent(queue::setFilter);

        return queue;
    }

}
