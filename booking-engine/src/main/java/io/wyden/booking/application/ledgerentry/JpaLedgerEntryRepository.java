package io.wyden.booking.application.ledgerentry;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.application.SimpleJpaRepository;
import io.wyden.booking.application.querylimit.QueryLimitValidator;
import io.wyden.booking.domain.instrument.Currency_;
import io.wyden.booking.domain.instrument.Security;
import io.wyden.booking.domain.instrument.Security_;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.domain.ledgerentry.LedgerEntry_;
import io.wyden.booking.domain.position.AccountReference;
import io.wyden.booking.domain.position.AccountReference_;
import io.wyden.booking.domain.position.CashPosition_;
import io.wyden.booking.domain.position.PortfolioReference;
import io.wyden.booking.domain.position.PortfolioReference_;
import io.wyden.booking.domain.position.ReferenceTypesMapper;
import io.wyden.booking.infrastructure.telemetry.Meters;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.interfaces.rest.RequestModel.LedgerEntrySearch;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.DateUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.metamodel.SingularAttribute;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static io.wyden.booking.infrastructure.telemetry.Meters.LedgerQueryType.COUNT_BY_PROPERTIES;
import static io.wyden.booking.infrastructure.telemetry.Meters.LedgerQueryType.DELETE_BY_PROPERTIES;
import static io.wyden.booking.infrastructure.telemetry.Meters.LedgerQueryType.FIND_BY_PROPERTIES;
import static io.wyden.booking.infrastructure.telemetry.Meters.LedgerQueryType.SAVE;
import static io.wyden.booking.infrastructure.telemetry.Meters.LedgerQueryType.SUM_RESERVATIONS;
import static io.wyden.booking.infrastructure.telemetry.Meters.bookingLedgerQueryLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.transaction.annotation.Propagation.REQUIRED;

@Repository
public class JpaLedgerEntryRepository extends SimpleJpaRepository<LedgerEntry, Long> implements LedgerEntryRepository {

    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;
    private final QueryLimitValidator queryLimitValidator;

    public JpaLedgerEntryRepository(EntityManager entityManager,
                                    Telemetry telemetry,
                                    QueryLimitValidator queryLimitValidator) {
        super(entityManager, LedgerEntry.class);
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.queryLimitValidator = queryLimitValidator;
    }

    @Override
    public Collection<LedgerEntry> findByReservationRef(String reservationRef) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<LedgerEntry> query = builder.createQuery(LedgerEntry.class);
        Root<LedgerEntry> ledgerEntryRoot = query.from(LedgerEntry.class);
    
        Predicate reservationPredicate = builder.equal(ledgerEntryRoot.get(LedgerEntry_.reservationRef), reservationRef);
    
        query.select(ledgerEntryRoot)
             .where(reservationPredicate);
    
        return entityManager.createQuery(query).getResultList();
    }

    private List<Predicate> getPredicates(LedgerEntrySearch request,
                                                 CriteriaBuilder builder,
                                                 Root<LedgerEntry> ledgerEntries) {

        queryLimitValidator.validate(request);

        Join<LedgerEntry, Security> security = ledgerEntries.join(LedgerEntry_.security, JoinType.LEFT);

        List<Predicate> predicates = new LinkedList<>();

        // TODO SPL AC-2427 - separate symbol and currency lookup (Position, LedgerEntry, Transaction)

        SingularAttribute currencyAttribute = CashPosition_.currency;

        if (!request.symbol().isEmpty()) {
            Predicate securityPredicate = security.get(Security_.symbol).in(request.symbol());
            Predicate currencyPredicate = ledgerEntries.get(currencyAttribute).get(Currency_.currency).in(request.symbol());
            Predicate symbolPredicate = builder.or(securityPredicate, currencyPredicate);
            predicates.add(symbolPredicate);
        }

        if (!request.currency().isEmpty()) {
            Predicate currencyPredicate = ledgerEntries.get(currencyAttribute).get(Currency_.currency).in(request.currency());
            predicates.add(currencyPredicate);
        }

        predicates.add(builder.or(
            createPortfolioPredicates(request, builder, ledgerEntries),
            createAccountPredicates(request, builder, ledgerEntries)));

        if (!request.reference().isEmpty()) {
            Predicate portfolioPredicate = builder.treat(ledgerEntries.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioId)
                .in(request.reference());
            Predicate venueAccountPredicate = builder.treat(ledgerEntries.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountId)
                .in(request.reference());
            Predicate referencePredicate = builder.or(portfolioPredicate, venueAccountPredicate);
            predicates.add(referencePredicate);
        }

        if (!request.ledgerEntryType().isEmpty()) {
            Predicate[] ledgerEntryTypePredicates = request.ledgerEntryType().stream()
                .flatMap(ledgerEntryType -> matchLedgerEntryType(ledgerEntryType).stream())
                .map(ledgerEntryType -> builder.equal(ledgerEntries.get(LedgerEntry_.type), ledgerEntryType))
                .toArray(Predicate[]::new);
            predicates.add(builder.or(ledgerEntryTypePredicates));
        }

        if (isNotBlank(request.transactionId())) {
            Predicate transactionIdPredicate = builder.equal(ledgerEntries.get(LedgerEntry_.transactionId), request.transactionId());
            predicates.add(transactionIdPredicate);
        }

        if (isNotBlank(request.orderId())) {
            Predicate orderIdPredicate = builder.equal(ledgerEntries.get(LedgerEntry_.reservationRef), request.orderId());
            predicates.add(orderIdPredicate);
        }

        if (isNotBlank(request.from())) {
            Timestamp after = DateUtils.epochMillisToSqlTimestamp(request.from());
            Predicate fromPredicate = builder.greaterThanOrEqualTo(ledgerEntries.get(LedgerEntry_.createdAt), after);
            predicates.add(fromPredicate);
        }

        if (isNotBlank(request.to())) {
            Timestamp before = DateUtils.epochMillisToSqlTimestamp(request.to());
            Predicate toPredicate = builder.lessThan(ledgerEntries.get(LedgerEntry_.createdAt), before);
            predicates.add(toPredicate);
        }

        return predicates;
    }

    private Predicate createAccountPredicates(LedgerEntrySearch request, CriteriaBuilder builder, Root<LedgerEntry> root) {
        List<Predicate> predicates = new LinkedList<>();

        if (RequestModel.AccountType.NONE.equals(request.accountType())) {

            predicates.add(builder.disjunction());

        } else if (RequestModel.AccountType.ALL.equals(request.accountType())) {

            Predicate accountPredicate = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountId)
                .isNotNull();
            predicates.add(accountPredicate);

        } else if (request.accountType() != null){

            Predicate emptyAccountTypePredicate = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountType).isNull();
            Predicate accountTypePredicate = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountType).in(ReferenceTypesMapper.map(request.accountType()).name());
            Predicate isAccountType = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountId)
                .isNotNull();

            if (RequestModel.WalletType.VOSTRO.equals(request.walletType()) || RequestModel.WalletType.NOSTRO.equals(request.walletType())) {
                Predicate walletTypePredicate = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                    .get(AccountReference_.walletType).in(ReferenceTypesMapper.map(request.walletType()).name());
                Predicate emptywalletTypePredicate = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                    .get(AccountReference_.walletType).isNull();

                predicates.add(builder.and(
                    builder.or(accountTypePredicate, emptyAccountTypePredicate),
                    builder.or(walletTypePredicate, emptywalletTypePredicate),
                    isAccountType));
            } else {
                predicates.add(builder.and(builder.or(accountTypePredicate, emptyAccountTypePredicate), isAccountType));
            }
        }

        if (!request.accountId().isEmpty()) {
            Predicate venueAccountPredicate = builder.treat(root.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountId)
                .in(request.accountId());
            predicates.add(venueAccountPredicate);
        }

        return builder.and(predicates.toArray(Predicate[]::new));
    }

    private Predicate createPortfolioPredicates(LedgerEntrySearch request, CriteriaBuilder builder, Root<LedgerEntry> root) {
        List<Predicate> predicates = new LinkedList<>();
        if (RequestModel.PortfolioType.ALL.equals(request.portfolioType())) {

            predicates.add(builder.treat(root.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioId)
                .isNotNull());

        } else if (RequestModel.PortfolioType.NONE.equals(request.portfolioType())) {

            predicates.add(builder.disjunction());

        } else if (RequestModel.PortfolioType.NOSTRO.equals(request.portfolioType()) || RequestModel.PortfolioType.VOSTRO.equals(request.portfolioType())) {

            Predicate portfolioTypePredicate1 = builder.treat(root.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioType)
                .in(request.portfolioType().name());
            Predicate portfolioTypePredicate2 = builder.treat(root.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioType)
                .isNull();
            Predicate portfolioTypePredicate3 = builder.treat(root.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioId)
                .isNotNull();
            predicates.add(builder.and(builder.or(portfolioTypePredicate1, portfolioTypePredicate2), portfolioTypePredicate3));
        }

        if (!request.portfolio().isEmpty()) {
            Predicate portfolioPredicate = builder.treat(root.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioId)
                .in(request.portfolio());
            predicates.add(portfolioPredicate);
        }

        return builder.and(predicates.toArray(Predicate[]::new));
    }

    private static List<LedgerEntryType> matchLedgerEntryType(String ledgerEntryTypeStr) {
        return Arrays.stream(LedgerEntryType.values())
            .filter(ledgerEntryType -> ledgerEntryType.name().equalsIgnoreCase(ledgerEntryTypeStr))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = REQUIRED)
    public <S extends LedgerEntry> S save(S entity) {
        try (var ignored = otlTracing.createSpan("booking.save.ledgerentry", SpanKind.CLIENT)) {
            return recordLatencyIn(latencyTimer(SAVE)).of(() -> super.save(entity));
        }
    }

    @Override
    public long countByProperties(LedgerEntrySearch request) {
        return recordLatencyIn(latencyTimer(COUNT_BY_PROPERTIES)).of(() -> countByPropertiesInner(request, StringUtils.EMPTY));
    }

    @Override
    public long countByProperties(LedgerEntrySearch request, String after) {
        return recordLatencyIn(latencyTimer(COUNT_BY_PROPERTIES)).of(() -> countByPropertiesInner(request, after));
    }

    private long countByPropertiesInner(LedgerEntrySearch request, String after) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
        Root<LedgerEntry> ledgerEntries = countQuery.from(LedgerEntry.class);

        List<Predicate> predicates = getPredicates(request, builder, ledgerEntries);

        if (isNotBlank(after)) {
            long cursor = Long.parseLong(after);
            Predicate laterPredicate = builder.greaterThan(ledgerEntries.get(LedgerEntry_.id), cursor);
            Predicate earlierPredicate = builder.lessThan(ledgerEntries.get(LedgerEntry_.id), cursor);
            Predicate afterPredicate = request.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        countQuery
            .select(builder.count(ledgerEntries))
            .where(predicates.toArray(Predicate[]::new));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    @Override
    public Collection<LedgerEntry> findByProperties(LedgerEntrySearch request) {
        try (var ignored = otlTracing.createSpan("booking.lookup.ledgerentry.findbyproperties", SpanKind.CLIENT)) {
            return recordLatencyIn(latencyTimer(FIND_BY_PROPERTIES)).of(() -> getLedgerEntries(request));
        }
    }

    private List<LedgerEntry> getLedgerEntries(LedgerEntrySearch request) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<LedgerEntry> searchQuery = builder.createQuery(LedgerEntry.class);
        Root<LedgerEntry> ledgerEntries = searchQuery.from(LedgerEntry.class);

        List<Predicate> predicates = getPredicates(request, builder, ledgerEntries);

        if (isNotBlank(request.after())) {
            long cursor = Long.parseLong(request.after());
            Predicate laterPredicate = builder.greaterThan(ledgerEntries.get(LedgerEntry_.id), cursor);
            Predicate earlierPredicate = builder.lessThan(ledgerEntries.get(LedgerEntry_.id), cursor);
            Predicate afterPredicate = request.sortingOrder() == RequestModel.SortingOrder.ASC ? laterPredicate : earlierPredicate;
            predicates.add(afterPredicate);
        }

        searchQuery
            .select(ledgerEntries)
            .where(predicates.toArray(Predicate[]::new));

        if (request.sortingOrder() != null) {
            // TODO SPL change sorting to datetime once avaialble
            searchQuery.orderBy(request.sortingOrder() == RequestModel.SortingOrder.ASC ?
                builder.asc(ledgerEntries.get(LedgerEntry_.updatedAt)) : builder.desc(ledgerEntries.get(LedgerEntry_.updatedAt)));
        }

        TypedQuery<LedgerEntry> typedQuery = entityManager.createQuery(searchQuery);

        if (request.first() != null) {
            typedQuery.setMaxResults(request.first());
        }

        return typedQuery.getResultList();
    }

    @Override
    public BigDecimal sumReservationsByInstrumentAndReferenceAndReservationRef(String currency, String portfolioId, String accountId, String reservationRef) {
        return recordLatencyIn(latencyTimer(SUM_RESERVATIONS))
            .of(() -> sumReservationsByInstrumentAndReferenceAndReservationRefInner(currency, portfolioId, accountId, reservationRef));
    }

    private BigDecimal sumReservationsByInstrumentAndReferenceAndReservationRefInner(String currency, String portfolioId, String accountId, String reservationRef) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<BigDecimal> searchQuery = builder.createQuery(BigDecimal.class);

        Root<LedgerEntry> ledgerEntries = searchQuery.from(LedgerEntry.class);
        Join<LedgerEntry, Security> ledgerEntryInstrument = ledgerEntries.join(LedgerEntry_.security, JoinType.LEFT);

        // TODO SPL AC-2427 - separate symbol and currency lookup (Position, LedgerEntry, Transaction)
        Predicate securityPredicate = builder.equal(ledgerEntryInstrument.get(Security_.symbol), currency);
        Predicate currencyPredicate = builder.equal(ledgerEntries.get(LedgerEntry_.currency).get(Currency_.currency), currency);
        Predicate instrumentPredicate = builder.or(securityPredicate, currencyPredicate);

        Predicate referencePredicate;
        if (isNotBlank(portfolioId)) {
            referencePredicate = builder.equal(builder.treat(ledgerEntries.get(LedgerEntry_.reference), PortfolioReference.class)
                .get(PortfolioReference_.portfolioId), portfolioId);
        } else {
            referencePredicate = builder.equal(builder.treat(ledgerEntries.get(LedgerEntry_.reference), AccountReference.class)
                .get(AccountReference_.accountId), accountId);
        }

        Predicate reservationRefPredicate = builder.equal(ledgerEntries.get(LedgerEntry_.reservationRef), reservationRef);
        Predicate reservationPredicate = ledgerEntries.get(LedgerEntry_.type).in(LedgerEntryType.getReservationOrReleaseTypes());
        Predicate predicates = builder.and(instrumentPredicate, referencePredicate, reservationRefPredicate, reservationPredicate);

        searchQuery
            .select(builder.sum(ledgerEntries.get(LedgerEntry_.quantity)))
            .where(predicates);

        TypedQuery<BigDecimal> typedQuery = entityManager.createQuery(searchQuery);
        List<BigDecimal> resultList = typedQuery.getResultList();
        return Optional.ofNullable(resultList.get(0))
            .orElse(BigDecimal.ZERO);
    }

    @Transactional
    @Override
    public void deleteByProperties(LedgerEntrySearch request) {
        recordLatencyIn(latencyTimer(DELETE_BY_PROPERTIES)).of(() ->
            findByProperties(request)
                .forEach(entityManager::remove));
    }

    private Timer latencyTimer(Meters.LedgerQueryType queryType) {
        try {
            return bookingLedgerQueryLatencyTimer(meterRegistry, queryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
