syntax = "proto3";
option java_multiple_files = true;

package io.wyden.published.reporting;

message OrderState {

  string id = 20;
  string order_id = 1;
  string client_id = 2;
  string cl_order_id = 3;
  string orig_cl_order_id = 4;
  string portfolio_id = 5;
  OrderStatus order_status = 6;
  string order_qty = 7;
  string limit_price = 8;
  string stop_price = 9;
  TIF tif = 10;
  string filled_qty = 11;
  string remaining_qty = 12;
  string last_qty = 13;
  string avg_price = 14;
  string last_price = 15;
  string reason = 16;
  Side side = 17;
  string instrumentId = 18;
  string venue_account_id = 19;
  string venue_timestamp = 21;
  string created_at = 22; // ISO 8601 format, UTC
  string updated_at = 23; // ISO 8601 format, UTC
  string last_request_result = 24;
  int32 sequence_number = 25;
  // DateTime format compatible with FIX expireTime: yyyyMMdd-HH:mm:ss.SSS
  string expire_time = 26;
  OrderCategory order_category = 27;
  string parent_order_id = 28;
  OrderType order_type = 29;
  string symbol = 30;
  AssetClass instrument_type = 31;
  repeated string venue_accounts = 32;
  string underlying_venue_account = 33;
  string root_order_id = 34;
  string currency = 35;
  string counter_portfolio_id = 36;
  string ext_order_id = 37;
}

message OrderStateList {
  repeated OrderState items = 1;
}

enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;
  NEW = 1;
  PARTIALLY_FILLED = 2;
  FILLED = 3;
  DONE_FOR_DAY = 4;
  CANCELED = 5;
  REPLACED = 6;
  PENDING_CANCEL = 7;
  STOPPED = 8;
  REJECTED = 9;
  SUSPENDED = 10;
  PENDING_NEW = 11;
  CALCULATED = 12;
  EXPIRED = 13;
  ACCEPTED_FOR_BIDDING = 14;
  PENDING_REPLACE = 15;
}

enum TIF {
  TIF_UNSPECIFIED = 0;
  GTC = 1;
  GTD = 2;
  IOC = 3;
  FOK = 4;
  DAY = 5;
}

enum Side {
  SIDE_UNDETERMINED = 0;
  BUY = 1;
  SELL = 2;
  REDUCE_SHORT = 3;
  SELL_SHORT = 4;
}

enum OrderCategory {
  ORDER_CATEGORY_UNSPECIFIED = 0;
  SIMPLE = 1 [deprecated = true];
  PARENT = 2 [deprecated = true];
  CHILD = 3 [deprecated = true];
  DIRECT_MARKET_ACCESS_ORDER = 4;
  SOR_ORDER = 5;
  SOR_CHILD_ORDER = 6;
  AGENCY_ORDER = 7;
  AGENCY_STREET_ORDER = 8;
  AGENCY_SOR_ORDER = 9;
  AGENCY_CLOB_ORDER = 10;
  CLOB_QUOTING_ORDER = 11;
  CLOB_EXTERNAL_HEDGE_ORDER = 12;
  AUTO_HEDGING_ORDER = 13;
}

enum OrderType {
  ORDER_TYPE_UNSPECIFIED = 0;
  MARKET = 1;
  LIMIT = 2;
  STOP = 3;
  STOP_LIMIT = 4;
}

enum AssetClass {
  ASSET_CLASS_UNDETERMINED = 0;
  FOREX = 1;
}
