package io.wyden.sor.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.ScheduledRabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.published.smartrecommendationengine.ExecutionRecommendations;
import io.wyden.published.smartrecommendationengine.SORCancellationCheckEvent;
import io.wyden.published.smartrecommendationengine.SORCandidateSuspensionTimeoutEvent;
import io.wyden.published.smartrecommendationengine.SORLimitOrderExecutionCheckEvent;
import io.wyden.published.smartrecommendationengine.SOROrderExpiredEvent;
import io.wyden.published.smartrecommendationengine.SOROrderRetryEvent;
import io.wyden.published.smartrecommendationengine.SORSubmissionCheckEvent;
import org.springframework.stereotype.Component;

@Component
public class RabbitDestinations {

    private final RabbitExchange<Message> tradingIngressExchange;
    private final RabbitExchange<Message> tradingUnroutedExchange;
    private final RabbitExchange<Message> tradingRouterExchange;
    private final RabbitExchange<Message> tradingDLX;
    private final RabbitExchange<BestExecutionRequest> bestExecutionRequestExchange;
    private final RabbitExchange<ExecutionRecommendations> bestExecutionRecommendationsExchange;
    private final RabbitExchange<Message> connectorStateExchange;
    private final RabbitExchange<SOROrderExpiredEvent> orderExpiredExchange;
    private final RabbitExchange<SORSubmissionCheckEvent> submissionCheckExchange;
    private final RabbitExchange<SORLimitOrderExecutionCheckEvent> executionCheckExchange;
    private final RabbitExchange<SORCancellationCheckEvent> cancellationCheckExchange;
    private final RabbitExchange<SOROrderRetryEvent> orderRetryExchange;
    private final ScheduledRabbitExchange scheduledRabbitExchange;
    private final RabbitExchange<SORCandidateSuspensionTimeoutEvent> candidateSuspensionExchange;

    public RabbitDestinations(RabbitIntegrator rabbitIntegrator) {
        tradingIngressExchange = OemsExchange.Trading.declareIngressExchange(rabbitIntegrator);
        tradingUnroutedExchange = OemsExchange.Trading.declareUnroutedExchange(rabbitIntegrator);
        tradingRouterExchange = OemsExchange.Trading.declareRouterExchange(rabbitIntegrator);
        tradingDLX = OemsExchange.Trading.declareDLX(rabbitIntegrator);
        bestExecutionRequestExchange = OemsExchange.SmartRecommendationEngine.bestExecutionRequestExchange(rabbitIntegrator);
        bestExecutionRecommendationsExchange = OemsExchange.SmartRecommendationEngine.bestExecutionRecommendationsExchange(rabbitIntegrator);
        connectorStateExchange = OemsExchange.DiagnosticEvents.declareConnectorDiagnosticExchange(rabbitIntegrator);
        orderExpiredExchange = OemsExchange.SmartOrderRouter.sorOrderExpiredExchange(rabbitIntegrator);
        submissionCheckExchange = OemsExchange.SmartOrderRouter.sorSubmissionCheckExchange(rabbitIntegrator);
        cancellationCheckExchange = OemsExchange.SmartOrderRouter.sorCancellationCheckExchange(rabbitIntegrator);
        scheduledRabbitExchange = OemsExchange.MessageScheduler.declareScheduledExchange(rabbitIntegrator);
        candidateSuspensionExchange = OemsExchange.SmartOrderRouter.sorCandidateSuspensionTimeoutExchange(rabbitIntegrator);
        executionCheckExchange = OemsExchange.SmartOrderRouter.sorExecutionCheckExchange(rabbitIntegrator);
        orderRetryExchange = OemsExchange.SmartOrderRouter.sorOrderRetryExchange(rabbitIntegrator);
    }

    public RabbitExchange<Message> getTradingIngressExchange() {
        return tradingIngressExchange;
    }

    public RabbitExchange<Message> getTradingUnroutedExchange() {
        return tradingUnroutedExchange;
    }

    public RabbitExchange<Message> getTradingRouterExchange() {
        return tradingRouterExchange;
    }

    public RabbitExchange<Message> getTradingDLX() {
        return tradingDLX;
    }

    public RabbitExchange<BestExecutionRequest> getBestExecutionRequestExchange() {
        return bestExecutionRequestExchange;
    }

    public RabbitExchange<ExecutionRecommendations> getBestExecutionRecommendationsExchange() {
        return bestExecutionRecommendationsExchange;
    }

    public RabbitExchange<Message> getConnectorStateExchange() {
        return connectorStateExchange;
    }

    public RabbitExchange<SOROrderExpiredEvent> getOrderExpiredExchange() {
        return orderExpiredExchange;
    }

    public RabbitExchange<SORSubmissionCheckEvent> getSubmissionCheckExchange() {
        return submissionCheckExchange;
    }

    public RabbitExchange<SORLimitOrderExecutionCheckEvent> getExecutionCheckExchange() {
        return executionCheckExchange;
    }

    public RabbitExchange<SORCancellationCheckEvent> getCancellationCheckExchange() {
        return cancellationCheckExchange;
    }

    public RabbitExchange<SOROrderRetryEvent> getOrderRetryExchange() {
        return orderRetryExchange;
    }

    public ScheduledRabbitExchange getScheduledRabbitExchange() {
        return scheduledRabbitExchange;
    }

    public RabbitExchange<SORCandidateSuspensionTimeoutEvent> getCandidateSuspensionTimeoutExchange() {
        return candidateSuspensionExchange;
    }
}
