package io.wyden.smartrecommendationengine;

import io.wyden.smartrecommendationengine.config.SmartRecommendationEngineTestConfiguration;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = SmartRecommendationEngineTestConfiguration.class)
class SmartRecommendationEngineApplicationTests {

    @Test
    void contextLoads() {
    }

}
