package io.wyden.rest.management.portfolio;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioOnboardingPositionCreatedEvent;
import io.wyden.published.referencedata.PortfolioOnboardingStatus;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.rest.management.domain.OnboardingModel;
import io.wyden.rest.management.domain.PortfolioModel;

import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.isBlank;

public final class PortfolioFromProtoMapper {

    private PortfolioFromProtoMapper() {
    }

    public static PortfolioModel.Portfolio map(Portfolio portfolio) {
        if (portfolio == null) {
            return null;
        }

        return new PortfolioModel.Portfolio(
            portfolio.getId(),
            portfolio.getName(),
            map(portfolio.getCreatedAt()),
            map(portfolio.getPortfolioType()),
            portfolio.getPortfolioCurrency(),
            map(portfolio.getTagsMap()));
    }

    public static Collection<OnboardingModel.PortfolioOnboardingResponse> map(PortfolioOnboardingPositionCreatedEvent onboardingEvent) {
        if (onboardingEvent == null) {
            return Collections.emptyList();
        }

        return onboardingEvent.getItemList().stream()
            .map(event -> {
                String portfolioId = event.getPortfolio().getId();
                String portfolioName = event.getPortfolio().getName();

                if (isBlank(portfolioName)) {
                    portfolioName = event.getRequest().getPortfolioToModify().getName();
                }

                return new OnboardingModel.PortfolioOnboardingResponse(
                    portfolioId,
                    portfolioName,
                    map(event.getStatus()),
                    event.getReason());
            })
            .toList();
    }

    private static Long map(String isoDateTime) {
        if (isBlank(isoDateTime)) {
            return null;
        }

        Instant instant = DateUtils.isoUtcTimeToInstant(isoDateTime);
        if (instant == null) {
            return null;
        }

        return instant.toEpochMilli();
    }

    private static Collection<PortfolioModel.Tag> map(Map<String, String> tags) {
        if (tags == null) {
            return null;
        }

        return tags.entrySet().stream()
            .map(entry -> new PortfolioModel.Tag(entry.getKey(), entry.getValue()))
            .toList();
    }

    private static PortfolioModel.PortfolioType map(PortfolioType portfolioType) {
        if (portfolioType == null) {
            return null;
        }

        return switch (portfolioType) {
            case NOSTRO -> PortfolioModel.PortfolioType.NOSTRO;
            case VOSTRO -> PortfolioModel.PortfolioType.VOSTRO;
            case UNRECOGNIZED, PORTFOLIO_TYPE_UNSPECIFIED -> PortfolioModel.PortfolioType.UNSPECIFIED;
        };
    }

    private static OnboardingModel.PortfolioOnboardingStatus map(PortfolioOnboardingStatus status) {
        if (status == null) {
            return OnboardingModel.PortfolioOnboardingStatus.UNKNOWN;
        }

        return switch (status) {
            case PORTFOLIO_ONBOARDING_STATUS_OK -> OnboardingModel.PortfolioOnboardingStatus.OK;
            case PORTFOLIO_ONBOARDING_STATUS_FAILED -> OnboardingModel.PortfolioOnboardingStatus.FAILED;
            default -> OnboardingModel.PortfolioOnboardingStatus.UNKNOWN;
        };
    }
}
