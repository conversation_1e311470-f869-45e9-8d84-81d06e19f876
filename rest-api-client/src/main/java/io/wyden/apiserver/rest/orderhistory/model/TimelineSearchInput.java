package io.wyden.apiserver.rest.orderhistory.model;

import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;

import java.util.EnumSet;
import javax.annotation.Nullable;

public record TimelineSearchInput(
    String orderId,
    @Nullable String rootOrderId,
    @Nullable Boolean includeRelated,
    @Nullable EnumSet<TimelineEventType> eventType,
    @Nullable Integer first,
    @Nullable String after,
    SortingOrder sortingOrder
) {
    public TimelineSearchInput {
        includeRelated = includeRelated != null && includeRelated;
        eventType = eventType == null ? EnumSet.allOf(TimelineEventType.class) : eventType;
    }
}
