package io.wyden.messagescheduler.service.scheduling;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.messagescheduler.domain.map.DelayedMessageMapConfig;
import io.wyden.sor.model.DelayedMessage;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

@Component
public class DelayedMessageRepository {
    private final Map<String, DelayedMessage> delayedMessageMap;

    public DelayedMessageRepository(HazelcastInstance hazelcastInstance) {
        this.delayedMessageMap = DelayedMessageMapConfig.getMap(hazelcastInstance);
    }

    public void save(DelayedMessage task) {
        delayedMessageMap.put(task.getMessageId(), task);
    }

    public Collection<DelayedMessage> findAll() {
        return delayedMessageMap.values();
    }

    public DelayedMessage remove(String messageId) {
        return delayedMessageMap.remove(messageId);
    }
}
