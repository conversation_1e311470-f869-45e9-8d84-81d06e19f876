package io.wyden.apiserver.rest.settlement.configuration;

import java.util.Collection;

public record SettlementAccountConfiguration(boolean automationEnabled,
                                             Collection<SettlementSchedulePoint> schedule,
                                             Collection<String> daysExcluded, // Calendar day to exclude MM-DD format
                                             Collection<WalletByAsset> assetToWalletMap,
                                             SettlementDirectionPriority directionPriority,
                                             SettlementLegPriority legPriority,
                                             boolean treasuryManagementAutomationEnabled,
                                             Collection<TreasuryThreshold> treasuryThresholds
) {
}