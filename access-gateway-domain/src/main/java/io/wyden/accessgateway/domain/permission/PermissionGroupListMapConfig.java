package io.wyden.accessgateway.domain.permission;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.CompoundKeyUtils;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import jakarta.annotation.Nullable;

import java.util.Set;

/*
 * This is read-only map with short TTL used to list all dynamic permissions for given user
 */
public class PermissionGroupListMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "permission-group-list_v0.1";
    private static final int TTL_SECONDS = 5;

    public static IMap<String, Set<String>> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
    }

    @Override
    protected void addMapConfig(Config config, @Nullable MapStoreConfig mapStoreConfig) {
        super.addMapConfig(config, mapStoreConfig);
        config.getMapConfig(MAP_NAME).setTimeToLiveSeconds(TTL_SECONDS);
    }

    public static String toKey(String aclId, String resource, String scope, int limit) {
        return CompoundKeyUtils.toKey(4, aclId, resource, scope, String.valueOf(limit));
    }

    public static PermissionQuery fromKey(String key) {
        String[] parts = CompoundKeyUtils.fromKey(4, key);
        return new PermissionQuery(parts[0], parts[1], parts[2], Integer.parseInt(parts[3]));
    }

    public record PermissionQuery(String aclId, String resource, String scope, int limit) {}
}
