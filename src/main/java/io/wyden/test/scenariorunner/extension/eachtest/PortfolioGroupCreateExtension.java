package io.wyden.test.scenariorunner.extension.eachtest;

import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.PortfolioGroupConfiguration;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.PortfolioGroupConfigurationInput;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.PortfolioType;
import io.wyden.test.scenariorunner.data.accounting.PortfolioGroupFactory;
import io.wyden.test.scenariorunner.extension.ExtensionUtils;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.ParameterContext;
import org.junit.jupiter.api.extension.ParameterResolutionException;
import org.junit.jupiter.api.extension.ParameterResolver;

import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.gqlActorKey;
import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.throwInvalidExtensionConfigExceptionIfNull;

public class PortfolioGroupCreateExtension implements BeforeEachCallback, ParameterResolver {

    private final PortfolioType portfolioType;
    private final String clientId;
    private PortfolioGroupConfiguration portfolioGroup;

    public PortfolioGroupCreateExtension(String clientId, PortfolioType portfolioType) {
        this.clientId = clientId;
        this.portfolioType = portfolioType;
    }

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        GraphQLActor actor = ExtensionUtils.getGlobalStore(context).get(gqlActorKey(clientId), GraphQLActor.class);

        throwInvalidExtensionConfigExceptionIfNull(actor);

        portfolioGroup = createRandomPortfolioGroupAndGet(actor, portfolioType);
    }

    @Override
    public boolean supportsParameter(ParameterContext parameterContext, ExtensionContext extensionContext) throws ParameterResolutionException {
        return parameterContext.getParameter().getType() == PortfolioGroupConfiguration.class;
    }

    @Override
    public PortfolioGroupConfiguration resolveParameter(ParameterContext parameterContext, ExtensionContext extensionContext) throws ParameterResolutionException {
        return portfolioGroup;
    }

    public String getPortfolioGroupId(){
        return this.portfolioGroup.id();
    }

    private PortfolioGroupConfiguration createRandomPortfolioGroupAndGet(GraphQLActor actor, PortfolioType portfolioType) {
        PortfolioGroupConfigurationInput portfolioGroupUpdate = PortfolioGroupFactory.randomPortfolioGroupInput(portfolioType);
        return actor.brokerDesk().updateAndGetPortfolioGroup(portfolioGroupUpdate.id(), portfolioGroupUpdate);
    }

}
