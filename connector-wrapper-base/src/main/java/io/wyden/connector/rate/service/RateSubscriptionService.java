package io.wyden.connector.rate.service;

import ch.algotrader.api.connector.application.Connector;
import ch.algotrader.api.connector.metadata.RateCapabilities;

import com.google.common.collect.Sets;
import com.google.protobuf.Message;
import io.wyden.connector.rate.io.RateEventEmitter;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * Service responsible for managing rate subscriptions and handling rate requests.
 */
public class RateSubscriptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RateSubscriptionService.class);

    private final RateRepository rateRepository;
    private final Connector connector;
    private final RateEventEmitter rateEventEmitter;
    private final int heartbeatInterval;
    private final Duration rateFetchInterval;
    private final ThreadPoolTaskScheduler heartbeatScheduler;
    private final ThreadPoolTaskScheduler fetchRateScheduler;
    private final Map<SubscriptionKey, RateSubscription> rateSubscriptionsMap = new ConcurrentHashMap<>();
    private final RateSinkSubscriptionManager rateSinkSubscriptionManager;

    public RateSubscriptionService(RateRepository rateRepository,
                                   Connector connector,
                                   RateEventEmitter rateEventEmitter,
                                   RateSinkSubscriptionManager rateSinkSubscriptionManager,
                                   int heartbeatInterval,
                                   Duration rateFetchInterval,
                                   int fetchRateSchedulerPoolSize) {
        this.rateRepository = rateRepository;
        this.connector = connector;
        this.rateEventEmitter = rateEventEmitter;
        this.rateSinkSubscriptionManager = rateSinkSubscriptionManager;
        this.fetchRateScheduler = fetchRateScheduler(fetchRateSchedulerPoolSize);
        this.heartbeatScheduler = heartbeatScheduler();
        this.heartbeatInterval = heartbeatInterval;
        this.rateFetchInterval = rateFetchInterval;

        startHeartBeat();
    }

    public void handleRateRequest(RateRequest request) {
        // Ensure the RateSink subscription is started on first request
        rateSinkSubscriptionManager.ensureStarted();

        // Process the request
        storeRequest(request);
    }

    public Map<SubscriptionKey, RateSubscription> getRateSubscriptionsMap() {
        return rateSubscriptionsMap;
    }

    private ThreadPoolTaskScheduler heartbeatScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(1);
        scheduler.setThreadNamePrefix("HeartbeatTask-");
        scheduler.initialize();
        return scheduler;
    }

    private ThreadPoolTaskScheduler fetchRateScheduler(int poolSize) {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(poolSize);
        scheduler.setThreadNamePrefix("FetchRateTask-");
        scheduler.initialize();
        return scheduler;
    }

    private void startHeartBeat() {
        LOGGER.info("Starting rate subscription heart beating with interval: {} seconds", heartbeatInterval);
        try {
            heartbeatScheduler.scheduleWithFixedDelay(createHeartBeatTask(), Instant.now().plusSeconds(1), Duration.ofSeconds(heartbeatInterval));
        } catch (Exception e) {
            LOGGER.error("Error when trying to start heart beating: {}", e.toString());
        }
    }

    @NotNull
    private Runnable createHeartBeatTask() {
        return () -> {
            LOGGER.trace("Running rate heart beat");
            rateSubscriptionsMap.forEach((subscriptionKey, rateSubscription) -> {
                try {
                    if (!rateSubscription.rateRequests().isEmpty()) {
                        handleHeartBeat(subscriptionKey, rateSubscription);
                    } else {
                        unsubscribe(subscriptionKey);
                    }
                } catch (Exception e) {
                    LOGGER.error("Exception during rate heart beat", e);
                }
            });
        };
    }

    private void handleHeartBeat(SubscriptionKey subscriptionKey, RateSubscription rateSubscription) {
        LOGGER.trace("Handling heart beat for subscriptionKey: {}", subscriptionKey);
        rateSubscription.rateRequests().clear();
    }

    private void storeRequest(RateRequest request) {
        SubscriptionKey subscriptionKey = new SubscriptionKey(request.getBaseCurrency(), request.getQuoteCurrency());
        RateSubscription rateSubscription = rateSubscriptionsMap.computeIfAbsent(subscriptionKey, key -> createNewSubscriptionTask(subscriptionKey));
        rateSubscription.rateRequests().add(request);
        LOGGER.trace("rateSubscriptionsMap keySet: {}", rateSubscriptionsMap.keySet());
    }

    private RateSubscription createNewSubscriptionTask(SubscriptionKey subscriptionKey) {
        try {
            ScheduledFuture<?> subscriptionTask = fetchRateScheduler.scheduleAtFixedRate(() -> {
                try {
                    Optional<RateCapabilities> rateCapabilities = connector.getCapabilities().getRateCapabilities();
                    if (rateCapabilities.isEmpty() || !rateCapabilities.get().isExchangeRateSupported()) {
                        LOGGER.error("Rate capabilities are not available for subscription: {}", subscriptionKey);
                        return;
                    }

                    Message message = getRateEvent(subscriptionKey);
                    rateEventEmitter.emit(subscriptionKey.baseCurrency(), subscriptionKey.quoteCurrency(), message);
                } catch (Exception e) {
                    LOGGER.error("Error in fetchRate task for subscription: {}", subscriptionKey, e);
                }
            }, rateFetchInterval);
            return new RateSubscription(Sets.newHashSet(), subscriptionTask);
        } catch (Exception e) {
            LOGGER.error("Exception during creation of new fetchRate task");
            throw e;
        }
    }

    private Message getRateEvent(SubscriptionKey subscriptionKey) {
        Optional<Rate> rate = rateRepository.find(subscriptionKey.baseCurrency(), subscriptionKey.quoteCurrency());
        return rate.orElseThrow(() -> new IllegalStateException("No rate found for base currency %s and quote currency %s".formatted(subscriptionKey.baseCurrency(), subscriptionKey.quoteCurrency())));
    }

    private void unsubscribe(SubscriptionKey subscriptionKey) {
        LOGGER.info("Unsubscribe for: {}", subscriptionKey);
        RateSubscription rateSubscription = rateSubscriptionsMap.get(subscriptionKey);
        rateSubscription.subscriptionTask().cancel(true);
        rateSubscriptionsMap.remove(subscriptionKey);
    }

    /**
     * Shuts down all schedulers and clears subscriptions.
     * This method should be called when the service is no longer needed
     * to properly release resources.
     */
    public void shutdown() {
        LOGGER.info("Shutting down RateSubscriptionService");

        // Cancel all subscription tasks
        rateSubscriptionsMap.forEach((key, subscription) -> {
            subscription.subscriptionTask().cancel(true);
        });

        // Clear the subscriptions map
        rateSubscriptionsMap.clear();

        // Shutdown schedulers
        if (heartbeatScheduler != null) {
            heartbeatScheduler.shutdown();
        }

        if (fetchRateScheduler != null) {
            fetchRateScheduler.shutdown();
        }
    }

    private record SubscriptionKey(String baseCurrency, String quoteCurrency) {
    }

    private record RateSubscription(Set<RateRequest> rateRequests, ScheduledFuture<?> subscriptionTask) {
    }
}