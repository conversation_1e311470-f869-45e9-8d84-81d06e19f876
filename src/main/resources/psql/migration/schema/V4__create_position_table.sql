CREATE TABLE position
(
    id                                INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    created_at                        TIMESTAMP(6),
    updated_at                        TIMESTAMP(6),
    version                           BIGINT NOT NULL DEFAULT 0,
    quantity                          DECIMAL(19, 8),
    net_cost                          DECIMAL(19, 8),
    gross_cost                        DECIMAL(19, 8),
    net_realized_pl                   DECIMAL(19, 8),
    gross_realized_pl                 DECIMAL(19, 8),
    last_applied_ledger_entry_id      BIGINT,
    reference_id                      INTEGER,
    instrument_id                     INTEGER
);

-- Add foreign key constraints
ALTER TABLE position
    ADD CONSTRAINT fk_position_reference FOREIGN KEY (reference_id) REFERENCES generic_reference (id);
ALTER TABLE position
    ADD CONSTRAINT fk_position_instrument FOREIGN KEY (instrument_id) REFERENCES instrument (id);

COMMENT ON TABLE position IS 'Table for storing position data';
COMMENT ON COLUMN position.id IS 'Unique identifier for the position';
COMMENT ON COLUMN position.quantity IS 'The current quantity (positive or negative) of the position';
COMMENT ON COLUMN position.net_cost IS 'The cost paid to open the position (after fees)';
COMMENT ON COLUMN position.gross_cost IS 'The cost paid to open the position (before fees)';
COMMENT ON COLUMN position.net_realized_pl IS 'The realized profit and loss made so far from this position (after fees)';
COMMENT ON COLUMN position.gross_realized_pl IS 'The realized profit and loss made so far from this position (before fees)';
COMMENT ON COLUMN position.last_applied_ledger_entry_id IS 'Tracks the most recent LedgerEntry applied to this Position';
COMMENT ON COLUMN position.reference_id IS 'Reference to the generic_reference table';
COMMENT ON COLUMN position.instrument_id IS 'Reference to the instrument table';

-- Add indexes to improve query performance
CREATE INDEX idx_position_reference_id ON position (reference_id);
CREATE INDEX idx_position_instrument_id ON position (instrument_id);
