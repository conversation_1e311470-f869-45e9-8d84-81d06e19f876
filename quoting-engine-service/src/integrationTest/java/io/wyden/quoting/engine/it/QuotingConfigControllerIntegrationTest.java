package io.wyden.quoting.engine.it;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.InvalidProtocolBufferException;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfigValidation;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.reactive.server.EntityExchangeResult;
import org.springframework.test.web.reactive.server.WebTestClient;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

public class QuotingConfigControllerIntegrationTest extends NoAeronIntegrationTestBase {

    @Autowired
    private WebTestClient webClient;

    @MockBean
    private PortfoliosCacheFacade portfoliosCacheFacade;

    @BeforeEach
    void setUp() {
        when(portfoliosCacheFacade.find("nostro")).thenReturn(Optional.of(Portfolio.newBuilder().setId("nostro").setMatchingEngineUid(101).build()));
        when(portfoliosCacheFacade.find("nostro2")).thenReturn(Optional.of(Portfolio.newBuilder().setId("nostro2").setMatchingEngineUid(102).build()));
    }

    @Test
    void shouldPutUpdateGet() {
        QuotingConfig quotingConfig = QuotingConfig.newBuilder()
            .setNostroPortfolio("nostro")
            .setQuotingEnabled(true)
            .setThrottlingPeriod(1)
            .setMaximumDepth("1")
            .setQuantityIncrement("0.1")
            .setMinQuantityFactor("0.1")
            .setMaxQuantityFactor("1")
            .build();

        AtomicLong assignedClobUid = new AtomicLong();
        // create
        webClient.post()
            .uri("/configuration")
            .bodyValue(quotingConfig)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectBody().consumeWith(res -> {
                try {
                    QuotingConfigValidation quotingConfigValidation = QuotingConfigValidation.parseFrom(res.getResponseBody());
                    assertThat(quotingConfigValidation.getClobUid()).isNotBlank();
                    assignedClobUid.set(Long.parseLong(quotingConfigValidation.getClobUid()));
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
            });

        // get
        webClient.get()
            .uri("/configuration")
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk()
            .expectBody().consumeWith(bytes -> {
                try {
                    assertAsExpected(bytes, quotingConfig.toBuilder().setClobUid(assignedClobUid.get()).build());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });

        // update
        QuotingConfig update = QuotingConfig.newBuilder()
            .setClobUid(assignedClobUid.get())
            .setNostroPortfolio("nostro")
            .setQuotingEnabled(true)
            .setThrottlingPeriod(11)
            .setMinQuantityFactor("0.1")
            .setMaxQuantityFactor("1")
            .build();

        webClient.put()
            .uri("/configuration/" + assignedClobUid)
            .bodyValue(update)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk();

        // get
        webClient.get()
            .uri("/configuration")
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isOk()
            .expectBody().consumeWith(bytes -> {
                try {
                    assertAsExpected(bytes, update);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    private static void assertAsExpected(EntityExchangeResult<byte[]> bytes, QuotingConfig quotingConfig) throws IOException {
        byte[] responseBody = bytes.getResponseBody();
        if (responseBody != null) {
            CodedInputStream input = CodedInputStream.newInstance(responseBody);
            List<QuotingConfig> retrievedList = new ArrayList<>();
            while (!input.isAtEnd()) {
                retrievedList.add(QuotingConfig.parseFrom(input.readBytes()));
            }

            QuotingConfig quotingConfigRetrieved = retrievedList.get(0);
            assertThat(quotingConfigRetrieved.getUpdatedAt()).isNotBlank();
            QuotingConfig quotingConfigRetrievedFieldsSaved = quotingConfigRetrieved.toBuilder()
                .clearUpdatedAt()
                .build();
            assertThat(quotingConfigRetrievedFieldsSaved).isEqualTo(quotingConfig);
        }
    }

    @Test
    @DirtiesContext(methodMode = DirtiesContext.MethodMode.BEFORE_METHOD)
    void absentConfigReturns404() {
        // get
        webClient.get()
            .uri("/configuration/9876")
            .accept(MediaType.APPLICATION_PROTOBUF)
            .exchange()
            .expectStatus().isNotFound();
    }
}
