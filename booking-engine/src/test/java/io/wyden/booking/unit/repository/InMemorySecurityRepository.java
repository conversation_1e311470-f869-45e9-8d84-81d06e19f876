package io.wyden.booking.unit.repository;

import io.wyden.booking.domain.instrument.Security;
import io.wyden.booking.domain.instrument.SecurityRepository;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public class InMemorySecurityRepository extends InMemoryRepositoryBase<Security, Long> implements SecurityRepository {

    public InMemorySecurityRepository(Map<Long, Security> cache) {
        super(cache);
    }

    @Override
    public Optional<Security> findBySymbol(String symbol) {
        return cache.values().stream()
            .filter(instrument -> Objects.equals(instrument.getSymbol(), symbol))
            .findFirst();
    }
}
