import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import EditIcon from '@mui/icons-material/Edit';

import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { Badge } from '@ui/Badge';
import { TextField } from '@ui/TextField';
import { Tooltip, Typography } from '@mui/material';
import { useWindow } from '@nest-react/hooks/useWindow';
import { useNestReact } from '@nest-react/hooks/useNestReact';
import { getSpacing } from '@wyden/utils/styles';
import { BORDER_RADIUS } from '@wyden/constants';
import { useWorkspaces } from '@wyden/features/useWorkspaces';
import { useWidgetDynamicData } from './hooks/useWidgetDynamicData';
import { EditLabel, WidgetTab } from '@wyden/features/widgets-renderer/widget/WidgetTab';
import { useHeaderUpdate } from '@wyden/features/widgets-renderer/widget/hooks/useHeaderUpdate';
import { useDesignModeStore } from '@wyden/features/app-header/useDesignModeStore';
import { MainWidgetDragHandler } from '@wyden/features/widgets-renderer/widget/MainWidgetDragHandler';
import { MainWidgetCloseButton } from '@wyden/features/widgets-renderer/widget/MainWidgetCloseButton';
import { useFullscreenMode } from '@wyden/hooks/useFullScreenMode';
import { Label } from '@ui/Typography/Label';

type WidgetHeaderProps = {
  children?: ReactNode;
  customName?: ReactNode;
};

export const WidgetHeader = ({ children, customName }: WidgetHeaderProps) => {
  const { t } = useTranslation();
  const { tabTargetHeight } = useNestReact();
  const { window, stackedComponents } = useWindow();
  const { isDesignModeActive } = useDesignModeStore();
  const { isCurrentWorkspacePredefined } = useWorkspaces();
  const { widgetData } = useWidgetDynamicData(window.id);
  const { isSomeComponentFullScreen } = useFullscreenMode(window.id);
  const shouldRenderTabs = !isSomeComponentFullScreen && stackedComponents.length > 0;
  const isDraggable = window.flavours.includes('DraggableComponent');
  const { toggleFormOpen, handleFormBlur, handleChange, formOpen, name } = useHeaderUpdate(window);
  const nameContent = formOpen ? (
    <TextField autoFocus={true} onBlur={handleFormBlur} value={name} onChange={handleChange} />
  ) : (
    <>
      {isDesignModeActive && (
        <Tooltip arrow title={t('widget.edit')}>
          <EditLabel onClick={toggleFormOpen}>
            <EditIcon fontSize="small" />
          </EditLabel>
        </Tooltip>
      )}
      <StyledSubtitle variant="subtitle1">
        <StyledBadge
          color="primary"
          badgeContent={widgetData?.itemsCount}
          max={999}
          isActive={true}
        >
          <Label>{customName || window.displayName}</Label>
        </StyledBadge>
      </StyledSubtitle>
    </>
  );

  return (
    <StyledWidgetHeader $withTabs={shouldRenderTabs} $height={tabTargetHeight}>
      {shouldRenderTabs ? (
        <>
          <Tabs>
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any*/}
            {stackedComponents.map((c: any) => (
              <WidgetTab
                key={c.id}
                component={c}
                dataTestId={`${c.name.replaceAll('_', '-').toLowerCase()}-button`}
              />
            ))}
          </Tabs>
          <NoTabbedCloseContainer id="no-tabbed-close-container">{children}</NoTabbedCloseContainer>
        </>
      ) : (
        <>
          <NoTabbedHeader>
            {isDraggable && isDesignModeActive && <MainWidgetDragHandler />}
            {nameContent}
          </NoTabbedHeader>
          <NoTabbedCloseContainer id="no-tabbed-close-container">
            {children}
            {isCurrentWorkspacePredefined ? null : <MainWidgetCloseButton />}
          </NoTabbedCloseContainer>
        </>
      )}
    </StyledWidgetHeader>
  );
};

const StyledBadge = styled(Badge)`
  vertical-align: baseline;
  .MuiBadge-badge {
    right: -19px;
    top: 11px;
    padding-top: 1px;
  }
`;

const Tabs = styled('div')`
  display: flex;
  gap: 4px;
`;

export const NoTabbedCloseContainer = styled('div')`
  display: flex;
  align-items: center;
  opacity: 0;
`;

const NoTabbedHeader = styled('div')`
  display: flex;
  text-wrap: nowrap;
  font-size: 12px;
  align-items: center;
  position: relative;
  font-weight: bold;

  &:hover {
    ${EditLabel} {
      visibility: visible;
    }
  }
`;

const StyledSubtitle = styled(Typography)`
  padding-left: ${getSpacing(2)};
`;

export const StyledWidgetHeader = styled('div')<{ $withTabs?: boolean; $height: number }>`
  display: flex;
  container-type: inline-size;
  min-height: ${({ $height }) => $height}px;
  max-height: ${({ $height }) => $height}px;
  container-name: widget-header;
  align-items: center;
  user-select: none;
  overflow: hidden;
  border-top-left-radius: ${BORDER_RADIUS};
  border-top-right-radius: ${BORDER_RADIUS};
  border-bottom: 1px solid ${({ theme }) => color[theme.palette.mode].borderElementsBorderWeak};
  border-color: ${({ theme, $withTabs }) =>
    $withTabs ? color[theme.palette.mode].borderElementsBorderWeak : 'transparent'};
  justify-content: space-between;
  padding: ${getSpacing(2)} 0;
`;
