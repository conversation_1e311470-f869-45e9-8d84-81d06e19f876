package io.wyden.apiserver.rest.apiui;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.core.HazelcastJsonValue;
import com.hazelcast.map.IMap;
import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.VenueTypeDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentsSearchInput;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.updateinstrument.InstrumentsRefreshRequestInput;
import io.wyden.apiserver.rest.referencedata.symbols.model.VenueAccountNamesPerVenue;
import io.wyden.apiserver.rest.referencedata.symbols.model.dto.symbolsquery.SymbolPredicate;
import io.wyden.apiserver.rest.referencedata.symbols.model.dto.symbolsquery.SymbolPredicateType;
import io.wyden.apiserver.rest.referencedata.symbols.model.dto.symbolsquery.SymbolResponseDto;
import io.wyden.apiserver.rest.referencedata.symbols.model.dto.symbolsquery.SymbolSearchInput;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDesc;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.GetInstrumentsRequest;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.InstrumentMapConfig;
import io.wyden.referencedata.domain.InstrumentSymbolMapConfig;
import io.wyden.referencedata.domain.model.InstrumentSymbol;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

class InstrumentsGraphQLControllerTest extends PermissionsIntegrationTestNoDb {

    private static final String BINANCE_ACCOUNT = "BinanceAccount";

    private static final List<String> INSTRUMENT_IDS = List.of(
        "BTCUSD@FOREX@Kraken",
        "BTCUSD@FOREX@Bitstamp",
        "BTCUSDT@FOREX@Kraken",
        "BTCUSDT@FOREX@Bitstamp"
    );

    @Autowired
    private RabbitIntegrator rabbitIntegrator;

    @Autowired
    private RabbitExchange<GetInstrumentsRequest> requestRabbitExchange;

    @Autowired
    private HazelcastInstance hazelcast;

    @MockBean
    private VenueAccountCacheFacade venueAccountCacheFacade;

    private ExchangeObserver<GetInstrumentsRequest> exchangeObserver;
    private IMap<String, HazelcastJsonValue> instrumentSymbolMap;
    private IMap<String, Instrument> instrumentMap;

    @BeforeEach
    void setUp() {
        exchangeObserver = ExchangeObserver.newBuilder(
                rabbitIntegrator,
                requestRabbitExchange,
                (bytes, ps) -> GetInstrumentsRequest.parseFrom(bytes),
                "it-instrument-req"
            )
            .withHeaders(Map.of())
            .build()
            .attach();

        instrumentMap = InstrumentMapConfig.getMap(hazelcast);

        instrumentSymbolMap = InstrumentSymbolMapConfig.getMap(hazelcast);
        new InstrumentSymbolMapConfig().setupMemberInstance(hazelcast);

        for (String instrumentId : INSTRUMENT_IDS) {
            String[] split = instrumentId.split("@");
            String symbol = split[0];
            AssetClass assetClass = AssetClass.valueOf(split[1]);
            String venueName = split[2];
            Instrument instrument = Instrument.newBuilder()
                .setBaseInstrument(BaseInstrument.newBuilder()
                    .setSymbol(symbol)
                    .setAssetClass(assetClass)
                    .setVenueName(venueName))
                .build();
            InstrumentSymbol instrumentSymbol = InstrumentSymbol.from(instrument);
            HazelcastJsonValue json = new HazelcastJsonValue(instrumentSymbol.toJson());

            instrumentSymbolMap.put(instrumentId, json);

            instrumentMap.put(instrumentId, instrument);
        }

        doAnswer(invocationOnMock -> {
            Set<String> venueAccountNames = invocationOnMock.getArgument(0);
            return venueAccountNames.stream().map(name -> VenueAccount.newBuilder().setVenueName(name).setId(name).build()).collect(Collectors.toSet());
        }).when(venueAccountCacheFacade).venueAccountDetails(anySet());
        doAnswer(invocationOnMock -> true).when(venueAccountCacheFacade).exists(any());
        when(venueAccountCacheFacade.venueAccountDetails()).thenReturn(List.of(
            VenueAccount.newBuilder().setId("Bitstamp").setVenueName("Bitstamp").build(),
            VenueAccount.newBuilder().setId("Kraken").setVenueName("Kraken").build()
        ));
    }

    @AfterEach
    void tearDown() {
        exchangeObserver.detach();
        instrumentSymbolMap.clear();
        instrumentMap.clear();
    }

    @Test
    @WithMockCustomUser
    void instrumentRefreshRequestShouldBeSentToRabbit() {
        // given
        grant("venue.account", "read", BINANCE_ACCOUNT);
        // when
        String correlationObject = "correlation64";
        executeFile(
            "instruments-refresh",
            Map.of("request", new InstrumentsRefreshRequestInput(BINANCE_ACCOUNT, correlationObject))
        )
            .errors().verify();

        // then
        GetInstrumentsRequest emittedRequest = exchangeObserver.awaitMessage(m -> true);
        assertThat(emittedRequest.getVenueAccount()).isEqualTo(BINANCE_ACCOUNT);
        assertThat(emittedRequest.getMetadata().getCorrelationObject()).isEqualTo(correlationObject);
        assertThat(emittedRequest.getMetadata().getRequesterId()).isEqualTo("TEST_USER");
    }

    @Test
    @WithMockCustomUser
    void instrumentRefreshRequestShouldBeRejectedWhenUnauthorized() {
        // when
        String correlationObject = "correlation64";
        executeFile(
            "instruments-refresh",
            Map.of("request", new InstrumentsRefreshRequestInput(BINANCE_ACCOUNT, correlationObject))
        )
            // then
            .errors().expect(responseError -> responseError.getMessage().equals("Access Denied"));
    }

    @Test
    @WithMockCustomUser
    void symbolSearch_oneVenuePermitted() {
        grant("venue.account", "read", "Kraken");
        executeFile("instrument-symbol-search",
            Map.of("request", new SymbolSearchInput(new SymbolPredicate(SymbolPredicateType.CONTAINS, List.of("BTC")), null, SharedModel.SortingOrder.ASC, 10, null)))
            .errors().verify()
            .path("symbolSearch.edges[*].node").entityList(SymbolResponseDto.class)
            .hasSize(2)
            .containsExactly(
                new SymbolResponseDto("BTCUSD", AssetClassDto.FOREX, Set.of(
                    new VenueAccountNamesPerVenue("Kraken", List.of(vad("Kraken")), null)
                )),
                new SymbolResponseDto("BTCUSDT", AssetClassDto.FOREX, Set.of(
                    new VenueAccountNamesPerVenue("Kraken", List.of(vad("Kraken")), null)
                ))
            );
    }

    @Test
    @WithMockCustomUser
    void searchInstruments_permittedOnlyIfClientInstrumentRead() {
        grant("client.instrument", "read");
        InstrumentsSearchInput v1 = new InstrumentsSearchInput(List.of(), null, VenueTypeDto.CLIENT, false, false, null, null, 200, null);
        executeFile("instrument-search",
            Map.of("search", v1))
            .errors().verify();
    }

    @Test
    @WithMockCustomUser
    void searchInstruments_forbiddenWithoutClientInstrumentRead() {
        InstrumentsSearchInput v1 = new InstrumentsSearchInput(List.of(), null, VenueTypeDto.CLIENT, false, false, null, null, 200, null);
        executeFile("instrument-search",
            Map.of("search", v1))
            .errors().expect(responseError -> responseError.getMessage().equals("Access Denied")).verify();
    }

    @Test
    @WithMockCustomUser
    void searchInstruments_permittedForOtherVenueTypeWithoutPermissions() {
        InstrumentsSearchInput request = new InstrumentsSearchInput(List.of(), null, VenueTypeDto.CLOB, false, false, null, null, 200, null);
        executeFile("instrument-search",
            Map.of("search", request))
            .errors().verify();
        request = new InstrumentsSearchInput(List.of(), null, VenueTypeDto.STREET, false, false, null, null, 200, null);
        executeFile("instrument-search",
            Map.of("search", request))
            .errors().verify();
        request = new InstrumentsSearchInput(List.of(), null, null, false, false, null, null, 200, null);
        executeFile("instrument-search",
            Map.of("search", request))
            .errors().verify();
    }

    @Test
    @WithMockCustomUser
    void symbolSearch_twoVenuePermitted() {
        grant("venue.account", "read", "Kraken");
        grant("venue.account", "read", "Bitstamp");
        executeFile("instrument-symbol-search",
            Map.of("request", new SymbolSearchInput(new SymbolPredicate(SymbolPredicateType.CONTAINS, List.of("BTC")), null, SharedModel.SortingOrder.ASC, 10, null)))
            .errors().verify()
            .path("symbolSearch.edges[*].node").entityList(SymbolResponseDto.class)
            .hasSize(2)
            .containsExactly(
                new SymbolResponseDto("BTCUSD", AssetClassDto.FOREX, Set.of(
                    new VenueAccountNamesPerVenue("Kraken", List.of(vad("Kraken")), null),
                    new VenueAccountNamesPerVenue("Bitstamp", List.of(vad("Bitstamp")), null)
                )),
                new SymbolResponseDto("BTCUSDT", AssetClassDto.FOREX, Set.of(
                    new VenueAccountNamesPerVenue("Kraken", List.of(vad("Kraken")), null),
                    new VenueAccountNamesPerVenue("Bitstamp", List.of(vad("Bitstamp")), null)
                ))
            );
    }

    @Test
    @WithMockCustomUser
    void symbolSearch_twoVenuePermitted_filterByVenueName() {
        grant("venue.account", "read", "Kraken");
        grant("venue.account", "read", "Bitstamp");
        executeFile("instrument-symbol-search",
            Map.of("request", new SymbolSearchInput(new SymbolPredicate(SymbolPredicateType.CONTAINS, List.of("BTC")), List.of("Bitstamp"), SharedModel.SortingOrder.ASC, 10, null)))
            .errors().verify()
            .path("symbolSearch.edges[*].node").entityList(SymbolResponseDto.class)
            .hasSize(2)
            .containsExactly(
                new SymbolResponseDto("BTCUSD", AssetClassDto.FOREX, Set.of(
                    new VenueAccountNamesPerVenue("Bitstamp", List.of(vad("Bitstamp")), null)
                )),
                new SymbolResponseDto("BTCUSDT", AssetClassDto.FOREX, Set.of(
                    new VenueAccountNamesPerVenue("Bitstamp", List.of(vad("Bitstamp")), null)
                ))
            );
    }

    @Test
    @WithMockCustomUser
    void symbolSearch_filterByVenueNameThatIsNotPermitted() {
        grant("venue.account", "read", "Kraken");
        executeFile("instrument-symbol-search",
            Map.of("request", new SymbolSearchInput(null, List.of("Bitstamp"), SharedModel.SortingOrder.ASC, 10, null)))
            .errors().expect(responseError -> responseError.getMessage().equals("User has no access to one of passed venue names"));

    }

    private VenueAccountDesc vad(String venueAccountId) {
        return new VenueAccountDesc(venueAccountId, venueAccountId);
    }
}
