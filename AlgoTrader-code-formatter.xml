<code_scheme name="AlgoTrader" version="173">
  <option name="RIGHT_MARGIN" value="150" />
  <option name="FORMATTER_TAGS_ENABLED" value="true" />
  <JavaCodeStyleSettings>
    <option name="ANNOTATION_PARAMETER_WRAP" value="1" />
    <option name="ALIGN_MULTILINE_ANNOTATION_PARAMETERS" value="true" />
    <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
    <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
    <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
      <value />
    </option>
    <option name="IMPORT_LAYOUT_TABLE">
      <value>
        <package name="ch.algotrader" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="java" withSubpackages="true" static="false" />
        <package name="javax" withSubpackages="true" static="false" />
        <emptyLine />
        <package name="" withSubpackages="true" static="true" />
      </value>
    </option>
  </JavaCodeStyleSettings>
  <editorconfig>
    <option name="ENABLED" value="false" />
  </editorconfig>
  <codeStyleSettings language="JAVA">
    <option name="RIGHT_MARGIN" value="150" />
    <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
    <option name="ALIGN_MULTILINE_RESOURCES" value="false" />
    <option name="ALIGN_MULTILINE_FOR" value="false" />
    <option name="ARRAY_INITIALIZER_WRAP" value="1" />
    <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
    <option name="WRAP_ON_TYPING" value="0" />
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>