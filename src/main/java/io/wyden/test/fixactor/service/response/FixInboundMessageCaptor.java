package io.wyden.test.fixactor.service.response;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import quickfix.Message;
import quickfix.SessionID;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.time.Duration;

public class FixInboundMessageCaptor {

    private static final Logger LOGGER = LoggerFactory.getLogger(FixInboundMessageCaptor.class);

    private final SessionID sessionId;
    private final Sinks.Many<String> replaySink = Sinks.many().replay().limit(10_000, Duration.ofMinutes(30));
    private final Sinks.Many<String> directSink = Sinks.many().multicast().directBestEffort();

    public FixInboundMessageCaptor(SessionID sessionId) {
        this.sessionId = sessionId;
    }

    void capture(Message message) {
        LOGGER.info("Captured {} (session: {}): {}", message.getClass().getSimpleName(), sessionId, message);
        Sinks.EmitResult emitResult = replaySink.tryEmitNext(message.toRawString());

        if (emitResult.isFailure()) {
            LOGGER.error("Failed to emit captured message. Reason: {} , Message: {}", emitResult, message);
        }

        directSink.tryEmitNext(message.toRawString());
    }

    Flux<String> streamWithBuffer() {
        return replaySink.asFlux();
    }

    Flux<String> streamDirect() {
        return directSink.asFlux();
    }

    public void terminate() {
        replaySink.tryEmitComplete();
        directSink.tryEmitComplete();
    }
}
