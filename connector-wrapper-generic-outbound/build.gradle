
dependencies {
    implementation project(":connector-wrapper-base")
    implementation dependencyCatalog.connector.generic
}

bootRun {
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9102"]
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev",
            "SPRING_CLOUD_VAULT_ENABLED": "false",
            "ACCOUNT_NAME": "simulator",
            "GENERICCONNECTOR.EXCHANGENAME": "Simulator",
            "GENERICCONNECTOR.INSTRUMENTTYPESSUPPORTED": "ForexDTO",
            "GENERICCONNECTOR.REQUESTSSUPPORTED": "MarketOrderDTO=Submit,LimitOrderDTO=Submit&Cancel",
            "GENERICCONNECTOR.TIFSSUPPORTED": "MarketOrderDTO=GTC&IOC,LimitOrderDTO=GTC&IOC",
            "GENERICCONNECTOR.BALANCESSUPPORTED": "false",
            "GENERICCONNECTOR_FIX_SESSION_MD_SOCKETCONNECTHOST": "localhost",
            "GENERICCONNECTOR.FIX.SESSION.MD.SOCKETCONNECTPORT": "10001",
            "GENERICCONNECTOR.FIX.SESSION.MD.TARGETCOMPID": "WYDEN_EXCHANGE_MARKET_DATA",
            "GENERICCONNECTOR.FIX.SESSION.MD.SENDERCOMPID": "CLIENT_MARKET_DATA",
            "GENERICCONNECTOR.FIX.SESSION.MD.USERNAME": "simulator",
            "GENERICCONNECTOR.FIX.SESSION.MD.PASSWORD": "password",
            "GENERICCONNECTOR.FIX.SESSION.MD.SOCKETUSESSL": "N",
            "GENERICCONNECTOR_FIX_SESSION_T_SOCKETCONNECTHOST": "localhost",
            "GENERICCONNECTOR.FIX.SESSION.T.SOCKETCONNECTPORT": "10001",
            "GENERICCONNECTOR.FIX.SESSION.T.TARGETCOMPID": "WYDEN_EXCHANGE_TRADING",
            "GENERICCONNECTOR.FIX.SESSION.T.SENDERCOMPID": "CLIENT_TRADING",
            "GENERICCONNECTOR.FIX.SESSION.T.USERNAME": "simulator",
            "GENERICCONNECTOR.FIX.SESSION.T.PASSWORD": "password",
            "GENERICCONNECTOR.FIX.SESSION.T.SOCKETUSESSL": "N",
            "GENERICCONNECTOR.FIX.SESSION.T.RESETONLOGON": "Y"
    ])
}

// workaround for AC-703

bootJar {
    archiveClassifier = "bootjar"
}

task patchBootJar(type: Zip) {
    def genericPath = project.configurations.compileClasspath.find { it.name.startsWith("connector-generic-") }

    entryCompression = org.gradle.api.tasks.bundling.ZipEntryCompression.STORED

    archiveName = "${baseName}-${version}.jar"
    destinationDirectory = file("${buildDir}/libs")

    from(zipTree("${buildDir}/libs/${baseName}-${version}-bootjar.jar")) {
        include("**/*")
    }

    from(zipTree(genericPath)) {
        include("genericconnector-fix.cfg")
    }

    duplicatesStrategy = "FAIL"
    dependsOn(bootJar)
}

tasks['bootJar'].finalizedBy 'patchBootJar'

// workaround for AC-703 end
