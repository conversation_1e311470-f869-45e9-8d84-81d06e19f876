package io.wyden.brokerconfig.domain.resolver;

import io.wyden.brokerconfig.domain.ConfigFactory;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.PricingConfig;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * This test verifies that GenericConfigResolver can "get" all properties from PricingConfig/ExecutionConfig messages. Since resolver is based on reflection,
 * we need to make sure that no change in messages property names can break configuration service.
 * This test will fail if resolver tries to resolve a property that is no longer in the proto message.
 */
class GenericConfigResolverTest {

    @Test
    void shouldHaveAllPricingProperties() {
        PricingConfig pricingConfig = ConfigFactory.createPricingConfig()
            .build();

        PricingConfig.Builder builder = PricingConfig.newBuilder();

        assertDoesNotThrow(() -> {
            ConfigResolvers.pricingResolvers()
                .forEach(configResolver -> configResolver.resolve(builder, pricingConfig));
        });
    }

    @Test
    void shouldHaveAllExecutionProperties() {
        ExecutionConfig executionConfig = ConfigFactory.createExecutionConfig()
            .build();

        ExecutionConfig.Builder builder = ExecutionConfig.newBuilder();

        assertDoesNotThrow(() -> {
            ConfigResolvers.executionResolvers()
                .forEach(configResolver -> configResolver.resolve(builder, executionConfig));
        });
    }
}