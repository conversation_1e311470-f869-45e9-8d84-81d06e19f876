package io.wyden.cloudutils.hazelcast;

import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import com.hazelcast.config.SerializerConfig;

public final class Serializers {

    private Serializers() {
    }

    public static <M extends Message> SerializerConfig protobufSerializer(Class<M> clazz, Parser<M> parser) {
        return new SerializerConfig()
                .setImplementation(new ProtobufSerializer<>(clazz, parser))
                .setTypeClass(clazz);
    }
}
