package io.wyden.settlement.server.settlement.transaction;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing booking sequence numbers for settlement transactions.
 * Note: Booking service assigns sequence numbers to both transactions and reservations,
 * but settlement service only tracks sequence numbers for transactions it processes.
 */
@Repository
public class BookingSequenceNumberRepository {

    private final JdbcTemplate jdbcTemplate;

    public BookingSequenceNumberRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public long save(BookingSequenceNumberEntity entity) {
        String sql = """
            INSERT INTO booking_sequence_number (
                transaction_id, sequence_number, created_at, updated_at
            ) VALUES (?, ?, ?, ?)
            """;

        KeyHolder keyHolder = new GeneratedKeyHolder();
        Timestamp now = Timestamp.from(Instant.now());

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
            ps.setLong(1, entity.transactionId());
            ps.setLong(2, entity.sequenceNumber());
            ps.setTimestamp(3, entity.createdAt() != null ? entity.createdAt() : now);
            ps.setTimestamp(4, entity.updatedAt() != null ? entity.updatedAt() : now);
            return ps;
        }, keyHolder);

        return keyHolder.getKey().longValue();
    }

    public Optional<BookingSequenceNumberEntity> findByTransactionId(Long transactionId) {
        String sql = """
            SELECT id, transaction_id, sequence_number, created_at, updated_at
            FROM booking_sequence_number
            WHERE transaction_id = ?
            """;

        List<BookingSequenceNumberEntity> results = jdbcTemplate.query(sql, new BookingSequenceNumberRowMapper(), transactionId);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    public Optional<BookingSequenceNumberEntity> findBySequenceNumber(Long sequenceNumber) {
        String sql = """
            SELECT id, transaction_id, sequence_number, created_at, updated_at
            FROM booking_sequence_number
            WHERE sequence_number = ?
            """;

        List<BookingSequenceNumberEntity> results = jdbcTemplate.query(sql, new BookingSequenceNumberRowMapper(), sequenceNumber);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    public List<BookingSequenceNumberEntity> findBySequenceNumberRange(Long minSequenceNumber, Long maxSequenceNumber) {
        String sql = """
            SELECT id, transaction_id, sequence_number, created_at, updated_at
            FROM booking_sequence_number
            WHERE sequence_number BETWEEN ? AND ?
            ORDER BY sequence_number
            """;

        return jdbcTemplate.query(sql, new BookingSequenceNumberRowMapper(), minSequenceNumber, maxSequenceNumber);
    }

    public List<Long> findMissingSequenceNumbers(Long minSequenceNumber, Long maxSequenceNumber) {
        String sql = """
            SELECT generate_series(?, ?) AS seq_num
            EXCEPT
            SELECT sequence_number FROM booking_sequence_number
            WHERE sequence_number BETWEEN ? AND ?
            ORDER BY seq_num
            """;

        return jdbcTemplate.queryForList(sql, Long.class, minSequenceNumber, maxSequenceNumber, minSequenceNumber, maxSequenceNumber);
    }

    public Optional<Long> findMinSequenceNumber() {
        String sql = "SELECT MIN(sequence_number) FROM booking_sequence_number";
        Long result = jdbcTemplate.queryForObject(sql, Long.class);
        return Optional.ofNullable(result);
    }

    public Optional<Long> findMaxSequenceNumber() {
        String sql = "SELECT MAX(sequence_number) FROM booking_sequence_number";
        Long result = jdbcTemplate.queryForObject(sql, Long.class);
        return Optional.ofNullable(result);
    }

    public long count() {
        String sql = "SELECT COUNT(*) FROM booking_sequence_number";
        return jdbcTemplate.queryForObject(sql, Long.class);
    }

    public void deleteByTransactionId(Long transactionId) {
        String sql = "DELETE FROM booking_sequence_number WHERE transaction_id = ?";
        jdbcTemplate.update(sql, transactionId);
    }

    private static class BookingSequenceNumberRowMapper implements RowMapper<BookingSequenceNumberEntity> {
        @Override
        public BookingSequenceNumberEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            return BookingSequenceNumberEntity.builder()
                .id(rs.getLong("id"))
                .transactionId(rs.getLong("transaction_id"))
                .sequenceNumber(rs.getLong("sequence_number"))
                .createdAt(rs.getTimestamp("created_at"))
                .updatedAt(rs.getTimestamp("updated_at"))
                .build();
        }
    }
}
