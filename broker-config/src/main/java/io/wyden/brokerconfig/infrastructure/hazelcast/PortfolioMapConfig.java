package io.wyden.brokerconfig.infrastructure.hazelcast;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.brokerdesk.PortfolioConfig;
import org.springframework.stereotype.Component;

import static io.wyden.brokerconfig.PortfolioBrokerConfigMapConfig.BROKER_CONFIG_PORTFOLIO_V_0_1;
import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

@Component
public class PortfolioMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = BROKER_CONFIG_PORTFOLIO_V_0_1;

    public static IMap<String, PortfolioConfig> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(PortfolioConfig.class, PortfolioConfig.parser()));
    }
}