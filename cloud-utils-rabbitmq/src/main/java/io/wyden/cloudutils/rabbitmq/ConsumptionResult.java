package io.wyden.cloudutils.rabbitmq;

public record ConsumptionResult(boolean complete, boolean requeue) {

    private static final ConsumptionResult COMPLETE = new ConsumptionResult(true, false);
    private static final ConsumptionResult FAILURE_NEEDS_RETRY = new ConsumptionResult(false, true);
    private static final ConsumptionResult FAILURE_NON_RECOVERABLE = new ConsumptionResult(false, false);

    public static ConsumptionResult consumed() {
        return COMPLETE;
    }

    public static ConsumptionResult failureNeedsRequeue() {
        return FAILURE_NEEDS_RETRY;
    }

    public static ConsumptionResult failureNonRecoverable() {
        return FAILURE_NON_RECOVERABLE;
    }
}
