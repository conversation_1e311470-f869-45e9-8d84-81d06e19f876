spring.application.name=lmax-global-connector
spring.profiles.active = prod

server.port=8109

tracing.resource.service=connector-wrapper-lmax-global
tracing.resource.scope=io.wyden.connector.lmax-global
management.metrics.tags.wyden_service=connector-wrapper-lmax-global

account.name=lmax-global
venue.name=LMAX Global
venue-override={FX_BROKER: "LMAX Global"}
lmaxglobal.fix.keystore.path=classpath:/identity.jks
# See Psono "API keys: Manual testing:"
#
# for values.
lmaxglobal.username=
lmaxglobal.password=
lmaxglobal.fix.senderCompId=
lmaxglobal.fix.session.t.targetCompId=
lmaxglobal.fix.session.t.socketConnectHost=
lmaxglobal.fix.session.t.socketConnectPort=
lmaxglobal.fix.session.md.targetCompId=
lmaxglobal.fix.session.md.socketConnectHost=
lmaxglobal.fix.session.md.socketConnectPort=
lmaxglobal.fix.session.md.resetOnLogon=Y
