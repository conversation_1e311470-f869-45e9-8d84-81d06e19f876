package io.wyden.rest.management.contract;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.SettlementSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.settlement.SettlementSearch;
import io.wyden.rest.management.SpringTestBase;
import io.wyden.rest.management.contract.client.SettlementApi;
import io.wyden.rest.management.contract.model.Error;
import io.wyden.rest.management.contract.model.Settlement;
import io.wyden.rest.management.contract.model.SettlementClientSideMappingEntry;
import io.wyden.rest.management.contract.model.SettlementConfiguration;
import io.wyden.rest.management.contract.model.SettlementConnection;
import io.wyden.rest.management.contract.model.SettlementLeg;
import io.wyden.rest.management.contract.model.SettlementMappingTarget;
import io.wyden.rest.management.contract.model.SettlementRequest;
import io.wyden.rest.management.contract.model.SettlementResponse;
import io.wyden.rest.management.contract.model.SettlementStreetSideMappingEntry;
import io.wyden.rest.management.contract.model.SortingOrder;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Instant;
import java.util.List;

import static io.wyden.rest.management.contract.AccountContractTest.createTransactionConnection;
import static io.wyden.rest.management.contract.AccountContractTest.validatePageInfo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.slf4j.LoggerFactory.getLogger;

@Tag("contract-tests")
public class SettlementContractTest extends SpringTestBase {

    private static final Logger LOGGER = getLogger(SettlementContractTest.class);

    /**
     * Search Settlements (with paging)
     */
    @Test
    public void settlementsGetTest() {
        String transactionExecutionId = "execution-id";
        String clientSettlementId = "client-settlement-id";
        Long from = Instant.now().toEpochMilli() - 10000L;
        Long to = Instant.now().toEpochMilli() + 10000L;
        Long first = 99L;
        String after = "after-cursor";
        SortingOrder sortingOrder = SortingOrder.ASC;

        String path = UriComponentsBuilder.fromPath("/api/v1/settlements")
            .queryParam("transactionExecutionId", transactionExecutionId)
            .queryParam("clientSettlementId", clientSettlementId)
            .queryParam("from", from)
            .queryParam("to", to)
            .queryParam("first", first)
            .queryParam("after", after)
            .queryParam("sortingOrder", sortingOrder)
            .build()
            .toString();

        SettlementApi settlementApi = new SettlementApi(configureApi(path, "GET", ""));

        SettlementSnapshot settlement = createSettlement();

        Mockito.when(bookingEngineHttpClient.getSettlements(any()))
            .thenAnswer(invocation -> {
                SettlementSearch search = invocation.getArgument(0, SettlementSearch.class);
                LOGGER.info("Searching settlements using filter: {}", search);

                assertThat(search.getTransactionExecutionId()).isEqualTo(transactionExecutionId);
                assertThat(search.getClientSettlementId()).isEqualTo(clientSettlementId);
                assertThat(search.getFrom()).isEqualTo(from.toString());
                assertThat(search.getTo()).isEqualTo(to.toString());

                assertThat(search.getClientId()).isEqualTo(username);
                assertThat(search.getFirst()).isEqualByComparingTo(Math.toIntExact(first));
                assertThat(search.getAfter()).isEqualTo(after);

                return createTransactionConnection(TransactionSnapshot.newBuilder()
                    .setSettlement(settlement)
                    .build());
            });

        SettlementConnection response = settlementApi.retrieveSettlementsMeetingCertainCriteria(transactionExecutionId, clientSettlementId, from, to, first, after, sortingOrder);
        LOGGER.info("Response: {}", response);

        assertThat(response).isNotNull();
        validatePageInfo(response.getPageInfo());

        assertThat(response.getEdges()).hasSize(1);
        Settlement settlementResponse = response.getEdges().get(0).getNode();
        validateSettlement(settlementResponse, settlement);
    }

    /**
     * Create new Settlement
     */
    @Test
    public void settlementsPostWithoutTransactionIdsTest() throws JsonProcessingException {
        SettlementRequest settlementRequest = new SettlementRequest()
            .clientSettlementId("client-settlement-id")
            .settlementDateTime(System.currentTimeMillis())
            .description("description");

        String path = "/api/v1/settlements?forceSettlement=false";
        SettlementApi settlementApi = new SettlementApi(configureApi(path, "POST", objectMapper.writeValueAsString(settlementRequest)));

        failWithMessage(() -> settlementApi.createANewSettlement(settlementRequest, false), "transactionExecutionIds cannot be empty");
    }

    @Test
    public void settlementsPostTest() throws JsonProcessingException {
        SettlementLeg streetSideLeg = new SettlementLeg()
            .sourceId("source-id")
            .targetId("target-id")
            .asset("BTC")
            .quantity(10.0);

        SettlementLeg clientSideLeg = new SettlementLeg()
            .sourceId("source-id")
            .targetId("target-id")
            .asset("USD")
            .quantity(250000.0);

        SettlementConfiguration settlementConfiguration = new SettlementConfiguration()
            .addStreetSideMappingItem(new SettlementStreetSideMappingEntry()
                .accountId("street-account-id")
                .addTargetsItem(new SettlementMappingTarget()
                    .target("street-target-id")
                    .type(SettlementMappingTarget.TypeEnum.CRYPTO)))
            .addClientSideMappingItem(new SettlementClientSideMappingEntry()
                .walletId("client-wallet-id")
                .addTargetsItem(new SettlementMappingTarget()
                    .target("client-target-id")
                    .type(SettlementMappingTarget.TypeEnum.FIAT)));

        SettlementRequest settlementRequest = new SettlementRequest()
            .clientSettlementId("client-settlement-id")
            .settlementDateTime(System.currentTimeMillis())
            .description("description")
            .transactionExecutionIds(List.of("transaction-execution-id"))
            .addStreetSideLegsItem(streetSideLeg)
            .addClientSideLegsItem(clientSideLeg)
            .settlementConfiguration(settlementConfiguration);

        String path = "/api/v1/settlements?forceSettlement=false";
        SettlementApi settlementApi = new SettlementApi(configureApi(path, "POST", objectMapper.writeValueAsString(settlementRequest)));

        SettlementResponse settlementResponse = settlementApi.createANewSettlement(settlementRequest, false);
        System.out.println(settlementResponse);

        assertThat(settlementResponse.getClientSettlementId()).isEqualTo(settlementRequest.getClientSettlementId());
        assertThat(settlementResponse.getSettlementDateTime()).isEqualTo(settlementRequest.getSettlementDateTime());
        assertThat(settlementResponse.getDescription()).isEqualTo(settlementRequest.getDescription());
        assertThat(settlementResponse.getTransactionExecutionIds()).isEqualTo(settlementRequest.getTransactionExecutionIds());
        assertThat(settlementResponse.getSettlementConfiguration()).isNotNull();
        assertThat(settlementResponse.getRequestedStreetSideLegs()).isNotNull();
        assertThat(settlementResponse.getRequestedClientSideLegs()).isNotNull();
        assertThat(settlementResponse.getSettlementStatus()).isEqualTo(SettlementResponse.SettlementStatusEnum.FAILURE);
        assertThat(settlementResponse.getSettlementStatusReason()).isEqualTo("Did not get confirmation from settlement engine");
    }

    private static SettlementSnapshot createSettlement() {
        return SettlementSnapshot.newBuilder()
            .setUuid("uuid")
            .setDescription("description")
            .setDateTime(DateUtils.toIsoUtcTime())
            .setClientSettlementId("client-settlement-id")
            .addSettledTransactionIds("execution-id")
            .build();
    }

    private static void validateSettlement(Settlement actual, SettlementSnapshot expected) {
        assertThat(actual.getId()).isEqualTo(expected.getUuid());
        assertThat(actual.getClientSettlementId()).isEqualTo(expected.getClientSettlementId());
        assertThat(actual.getTransactionType()).isEqualTo(Settlement.TransactionTypeEnum.SETTLEMENT);
        assertThat(actual.getSettlementDateTime()).isEqualTo(DateUtils.isoUtcTimeToInstant(expected.getDateTime()).toEpochMilli());
        assertThat(actual.getDescription()).isEqualTo(expected.getDescription());
        assertThat(actual.getTransactionExecutionIds()).isEqualTo(expected.getSettledTransactionIdsList());
    }

    private void failWithMessage(ThrowableAssert.ThrowingCallable throwingCallable, String errorMessage) {
        assertThatThrownBy(throwingCallable)
            .satisfies(e -> {
                LOGGER.info("Caught exception: {}", e.getMessage());

                if (e instanceof HttpClientErrorException.BadRequest badRequest) {
                    Error error = badRequest.getResponseBodyAs(Error.class);
                    if (error == null) {
                        fail("Response body is null");
                    }

                    assertThat(error.getStatus()).isEqualTo(400);
                    assertThat(error.getMessage()).isEqualTo(errorMessage);
                    assertThat(error.getError()).isEqualTo("Bad Request");
                    assertThat(error.getTimestamp()).isNotBlank();
                    assertThat(error.getPath()).isNotBlank();
                } else {
                    fail("Should have thrown HttpClientErrorException.BadRequest");
                }
            });
    }
}
