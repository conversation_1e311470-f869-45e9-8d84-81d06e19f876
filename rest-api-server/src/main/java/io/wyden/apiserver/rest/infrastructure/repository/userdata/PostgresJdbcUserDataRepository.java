package io.wyden.apiserver.rest.infrastructure.repository.userdata;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import static java.sql.Types.VARCHAR;

@Repository
@ConditionalOnProperty(name = "db.engine", havingValue = "psql")
public class PostgresJdbcUserDataRepository implements UserDataRepository {

    private static final String QUERY = "SELECT data FROM user_data WHERE id = ?";
    private static final String UPSERT = "INSERT INTO user_data (id, data, sequence_num) VALUES (?, ?, 1) ON CONFLICT (id) DO UPDATE SET data = ?, updated_at = NOW(), " +
        "sequence_num = (SELECT sequence_num FROM user_data WHERE id = ?) + 1";

    private final JdbcTemplate jdbcTemplate;

    public PostgresJdbcUserDataRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public String findById(String id) {
        return jdbcTemplate.queryForObject(QUERY, new String[]{id}, new int[]{VARCHAR}, String.class);
    }

    public void save(String id, String data) {
        jdbcTemplate.update(UPSERT, new String[]{id, data, data, id}, new int[]{VARCHAR, VARCHAR, VARCHAR, VARCHAR});
    }
}
