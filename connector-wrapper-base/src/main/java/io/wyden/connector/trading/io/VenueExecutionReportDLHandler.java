package io.wyden.connector.trading.io;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.published.venue.VenueResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class VenueExecutionReportDLHandler implements MessageConsumer<VenueResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger(VenueExecutionReportDLHandler.class);

    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public VenueExecutionReportDLHandler(Telemetry telemetry) {
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public ConsumptionResult consume(VenueResponse response, AMQP.BasicProperties properties) {
        try (var ignored = otlTracing.createSpan("venueexecutionreport.dlq", SpanKind.INTERNAL)) {
            return consumeInner(response);
        }
    }

    private ConsumptionResult consumeInner(VenueResponse response) {
        LOGGER.error("Response not delivered: {}", response);
        updateMetrics(response);
        return ConsumptionResult.consumed();
    }

    private void updateMetrics(VenueResponse response) {
        try {
            String instrumentId = response.getVenueTicker();
            String messageType = response.getResponseType().name();
            this.meterRegistry.counter("wyden.trading.request.incoming.count",
                "instrumentId", instrumentId,
                "messageType", messageType,
                "orderType", "",
                "handlerType", "dlq").increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
