package io.wyden.oems.storage.config.referencedata.instrumentsymbol;

import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.config.StoredMapConfigBean;
import io.wyden.oems.storage.web.StringHazelcastJsonValueMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.referencedata.domain.InstrumentSymbolMapConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static io.wyden.oems.storage.config.MapStores.defaultEagerMapLoaderConfig;

@Configuration
public class InstrumentSymbolMapConfiguration {

    @Bean
    HazelcastMapConfigBean instrumentSymbolMapConfig(InstrumentSymbolMapLoader mapLoader) {
        HazelcastMapConfig hazelcastMapConfig = new InstrumentSymbolMapConfig();
        MapStoreConfig mapStoreConfig = defaultEagerMapLoaderConfig(mapLoader, true);
        return new StoredMapConfigBean(hazelcastMapConfig, mapStoreConfig);
    }

    @Bean
    WebAccess instrumentSymbolMapWebAccess(HazelcastInstance hazelcast, InstrumentSymbolMapLoader mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new InstrumentSymbolMapConfig();
        return new StringHazelcastJsonValueMapWebAccess(hazelcast, hazelcastMapConfig, mapStore);
    }
}
