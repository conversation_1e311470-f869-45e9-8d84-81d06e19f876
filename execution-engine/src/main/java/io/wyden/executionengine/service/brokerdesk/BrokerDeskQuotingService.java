package io.wyden.executionengine.service.brokerdesk;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.executionengine.domain.StreetSideInstrument;
import io.wyden.executionengine.service.referencedata.InstrumentsRepository;
import io.wyden.published.marketdata.Ask;
import io.wyden.published.marketdata.Bid;
import io.wyden.published.marketdata.BidAskQuote;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import io.wyden.published.marketdata.Trade;
import io.wyden.published.referencedata.Instrument;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

import static io.wyden.executionengine.service.utils.ProtobufUtils.addPriceMarkup;
import static org.apache.commons.lang3.BooleanUtils.isFalse;

public class BrokerDeskQuotingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrokerDeskQuotingService.class);

    private final StreetSideInstrument streetSideInstrument;
    private final MarketDataObserver marketDataObserver;
    private final InstrumentsRepository instrumentsRepository;

    private final Sinks.Many<MarketDataEvent> marketDataSink = Sinks.many().multicast().directBestEffort();
    private Disposable subscription;
    private final String bankVenueAccount;
    private final BigDecimal priceMarkup;

    public BrokerDeskQuotingService(StreetSideInstrument streetSideInstrument,
                                    MarketDataObserver marketDataObserver,
                                    InstrumentsRepository instrumentsRepository,
                                    String bankVenueAccount,
                                    BigDecimal priceMarkup) {
        this.streetSideInstrument = streetSideInstrument;
        this.marketDataObserver = marketDataObserver;
        this.instrumentsRepository = instrumentsRepository;
        this.bankVenueAccount = bankVenueAccount;
        this.priceMarkup = priceMarkup;
        subscribe(streetSideInstrument);
    }

    private void subscribe(StreetSideInstrument streetSidestreetSideInstrumentty) {
        LOGGER.info("Starting MD subscription for streetSidestreetSideInstrumentty: {}", streetSidestreetSideInstrumentty.getInstrumentId());
        subscription = marketDataObserver.subscribe(streetSidestreetSideInstrumentty)
            .doOnNext(marketDataEvent -> {
                try {
                    Boolean consumptionResult;
                    LOGGER.trace("Consuming new MarketDataEvent ({}): {}", marketDataEvent.getClass().getSimpleName(), marketDataEvent);
                    if (marketDataEvent.hasBid()) {
                        consumptionResult = consumeBid(marketDataEvent.getBid());
                    } else if (marketDataEvent.hasAsk()) {
                        consumptionResult = consumeAsk(marketDataEvent.getAsk());
                    } else if (marketDataEvent.hasBidAskQuote()) {
                        consumptionResult = consumeBidAskQuote(marketDataEvent.getBidAskQuote());
                    } else if (marketDataEvent.hasTrade()) {
                        consumptionResult = consumeTrade(marketDataEvent.getTrade());
                    } else if (marketDataEvent.hasOrderBook()) {
                        consumptionResult = consumeOrderBook(marketDataEvent.getOrderBook());
                    } else {
                        consumptionResult = Boolean.FALSE;
                    }

                    if (isFalse(consumptionResult)) {
                        LOGGER.error("Failed to emit market data event of type {} to flux: {}, event: {}", marketDataEvent.getClass().getSimpleName(), consumptionResult, marketDataEvent);
                    }
                } catch (Exception ex) {
                    LOGGER.error("Exception when processing market data event", ex);
                }
            }).subscribe();
    }

    public void unsubscribe() {
        subscription.dispose();
        marketDataObserver.unsubscribe(streetSideInstrument);
    }

    public Flux<MarketDataEvent> getMarketDataFlux() {
        return marketDataSink.asFlux();
    }

    private Boolean consumeBid(Bid bid) {
        LOGGER.trace("Consuming new market data event of type BID: {}", bid);

        String streetSideInstrumentId = bid.getIdentifier().getInstrumentId();
        MarketDataIdentifier marketDataIdentifier;
        try {
            marketDataIdentifier = buildClientSideMarketDataIdentifier(streetSideInstrumentId);
        } catch (NoSuchElementException e) {
            LOGGER.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
        Bid newBid = bid.toBuilder()
            .setPrice(addPriceMarkup(bid.getPrice(), bidMarkup()))
            .setIdentifier(marketDataIdentifier)
            .build();

        LOGGER.trace("Publishing BID event with added priceMarkup: {}", newBid);
        emitBid(newBid);
        return Boolean.TRUE;
    }

    private Boolean consumeAsk(Ask ask) {
        LOGGER.trace("Consuming new market data event of type ASK: {}", ask);

        String streetSideInstrumentId = ask.getIdentifier().getInstrumentId();
        MarketDataIdentifier marketDataIdentifier;
        try {
            marketDataIdentifier = buildClientSideMarketDataIdentifier(streetSideInstrumentId);
        } catch (NoSuchElementException e) {
            LOGGER.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
        Ask newAsk = ask.toBuilder()
            .setPrice(addPriceMarkup(ask.getPrice(), askMarkup()))
            .setIdentifier(marketDataIdentifier)
            .build();

        LOGGER.trace("Publishing ASK event with added priceMarkup: {}", newAsk);
        emitAsk(newAsk);
        return Boolean.TRUE;
    }

    private Boolean consumeBidAskQuote(BidAskQuote bidAskQuote) {
        LOGGER.trace("Consuming new market data event of type BIDASK: {}", bidAskQuote);

        String streetSideInstrumentId = bidAskQuote.getIdentifier().getInstrumentId();
        MarketDataIdentifier marketDataIdentifier;
        try {
            marketDataIdentifier = buildClientSideMarketDataIdentifier(streetSideInstrumentId);
        } catch (NoSuchElementException e) {
            LOGGER.error(e.getMessage(), e);
            return Boolean.FALSE;
        }
        BidAskQuote newBidAsk = bidAskQuote.toBuilder()
            .setBidPrice(addPriceMarkup(bidAskQuote.getBidPrice(), bidMarkup()))
            .setAskPrice(addPriceMarkup(bidAskQuote.getAskPrice(), askMarkup()))
            .setIdentifier(marketDataIdentifier)
            .build();

        LOGGER.trace("Publishing BIDASK event with added priceMarkup: {}", newBidAsk);
        emitBidAskQuote(newBidAsk);
        return Boolean.TRUE;
    }

    private Boolean consumeTrade(Trade trade) {
        // Regarding OG skipping trades from synthetic stream production
        return Boolean.TRUE;
    }

    private Boolean consumeOrderBook(OrderBook orderBook) {
        LOGGER.trace("Consuming new market data event of type ORDERBOOK: {}", orderBook);

        String streetSideInstrumentId = orderBook.getIdentifier().getInstrumentId();
        MarketDataIdentifier marketDataIdentifier;
        try {
            marketDataIdentifier = buildClientSideMarketDataIdentifier(streetSideInstrumentId);
        } catch (NoSuchElementException e) {
            LOGGER.error(e.getMessage(), e);
            return Boolean.FALSE;
        }

        Map<String, OrderBookLevel> bidsMapWithPriceMarkup = addPriceMarkupToOrderBookEntriesPrices(orderBook.getBidsMap(), bidMarkup());
        Map<String, OrderBookLevel> asksMapWithPriceMarkup = addPriceMarkupToOrderBookEntriesPrices(orderBook.getAsksMap(), askMarkup());
        OrderBookLevel topBid = orderBook.getTopBid();
        OrderBookLevel topAsk = orderBook.getTopAsk();

        OrderBook newOrderBook = OrderBook.newBuilder()
            .setIdentifier(marketDataIdentifier)
            .putAllBids(bidsMapWithPriceMarkup)
            .putAllAsks(asksMapWithPriceMarkup)
            .setTopBid(OrderBookLevel.newBuilder(topBid).setPrice(addPriceMarkup(topBid.getPrice(), bidMarkup())).build())
            .setTopAsk(OrderBookLevel.newBuilder(topAsk).setPrice(addPriceMarkup(topAsk.getPrice(), askMarkup())).build())
            .build();

        LOGGER.trace("Publishing ORDERBOOK event with added priceMarkup: {}", newOrderBook);
        emitOrderBook(bankVenueAccount, newOrderBook);
        return Boolean.TRUE;
    }

    private BigDecimal bidMarkup() {
        return BigDecimal.ONE.subtract(priceMarkup);
    }

    private BigDecimal askMarkup() {
        return BigDecimal.ONE.add(priceMarkup);
    }

    @NotNull
    private Map<String, OrderBookLevel> addPriceMarkupToOrderBookEntriesPrices(Map<String, OrderBookLevel> orderBookLevelMap, BigDecimal markup) {
        return orderBookLevelMap.entrySet().stream()
            .collect(Collectors.toMap(entry -> addPriceMarkup(entry.getValue().getPrice(), markup)
                , entry -> OrderBookLevel.newBuilder(entry.getValue()).setPrice(addPriceMarkup(entry.getValue().getPrice(), markup)).build()));
    }

    @NotNull
    private MarketDataIdentifier buildClientSideMarketDataIdentifier(String streetSideInstrumentId) {
        LOGGER.trace("Building clientSide MarkedDataIdentifier for instrumentId: {}", streetSideInstrumentId);

        Instrument clientSideInstrument = instrumentsRepository.findClientInstrumentsForStreetSideInstrument(streetSideInstrumentId).stream().findFirst()
            .orElseThrow(() -> new NoSuchElementException("None client side instrument found for street side instrumentId: " + streetSideInstrumentId));
        String clientSideInstrumentId = clientSideInstrument.getInstrumentIdentifiers().getInstrumentId();

        LOGGER.trace("ClientSide instrumentId: {}", clientSideInstrumentId);

        return MarketDataIdentifier.newBuilder()
            .setInstrumentId(clientSideInstrumentId)
            .setTicker(clientSideInstrument.getInstrumentIdentifiers().getAdapterTicker())
            .setVenue(bankVenueAccount)
            .setVenueAccount(bankVenueAccount)
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();
    }

    private void emitBid(Bid bid) {
        MarketDataEvent marketDataEvent = MarketDataEvent.newBuilder().setBid(bid).build();
        marketDataSink.tryEmitNext(marketDataEvent);
    }

    private void emitAsk(Ask ask) {
        MarketDataEvent marketDataEvent = MarketDataEvent.newBuilder().setAsk(ask).build();
        marketDataSink.tryEmitNext(marketDataEvent);
    }

    private void emitBidAskQuote(BidAskQuote bidAskQuote) {
        MarketDataEvent marketDataEvent = MarketDataEvent.newBuilder().setBidAskQuote(bidAskQuote).build();
        marketDataSink.tryEmitNext(marketDataEvent);
    }

    private void emitOrderBook(String venueAccount, OrderBook orderBook) {
        MarketDataEvent marketDataEvent = MarketDataEvent.newBuilder().setOrderBook(orderBook).build();
        marketDataSink.tryEmitNext(marketDataEvent);
    }
}
