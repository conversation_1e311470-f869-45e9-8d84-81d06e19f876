package io.wyden.booking.snapshotter.application.state;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.booking.snapshotter.domain.common.AuditedEntity;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.snapshotter.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.snapshotter.domain.position.PositionRepository;
import io.wyden.booking.snapshotter.domain.reservation.ReservationRepository;
import io.wyden.booking.snapshotter.domain.state.SequencedStateOutput;
import io.wyden.booking.snapshotter.domain.state.StateOutput;
import io.wyden.booking.snapshotter.domain.state.tracking.SnapshotterTrackingRepositoryFacade;
import io.wyden.booking.snapshotter.domain.transaction.TransactionRepository;
import io.wyden.cloudutils.telemetry.Telemetry;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Function;

import static io.wyden.booking.snapshotter.infrastructure.telemetry.Meters.BOOKING_STATE_DBDUMP_COUNT;
import static io.wyden.booking.snapshotter.infrastructure.telemetry.Meters.bookingStateDbDumpLatencyTimer;
import static io.wyden.cloud.utils.rest.pagination.PaginationWrapper.getLast;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;

@Component
public class StatePersister {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatePersister.class);

    private final TransactionRepository transactionRepository;
    private final ReservationRepository reservationRepository;
    private final LedgerEntryRepository ledgerEntryRepository;
    private final PositionRepository positionRepository;
    private final SnapshotterTrackingRepositoryFacade snapshotterTrackingRepositoryFacade;
    private final MeterRegistry meterRegistry;

    public StatePersister(TransactionRepository transactionRepository,
                          ReservationRepository reservationRepository,
                          LedgerEntryRepository ledgerEntryRepository,
                          PositionRepository positionRepository,
                          SnapshotterTrackingRepositoryFacade snapshotterTrackingRepositoryFacade,
                          Telemetry telemetry) {
        this.transactionRepository = transactionRepository;
        this.reservationRepository = reservationRepository;
        this.ledgerEntryRepository = ledgerEntryRepository;
        this.positionRepository = positionRepository;
        this.snapshotterTrackingRepositoryFacade = snapshotterTrackingRepositoryFacade;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    private <T extends AuditedEntity> T saveOrUpdate(T entity, Function<T, Optional<T>> finder, Function<T, T> saver) {
        finder.apply(entity).ifPresentOrElse(
            existing -> {
                entity.setVersion(existing.getVersion());
                entity.setId(existing.getId());
                entity.setCreatedAt(existing.getCreatedAt());
            },
            () -> { }
        );

        return saver.apply(entity);
    }

    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    public void flush(SequencedStateOutput sequencedStateOutput) {
        recordLatencyIn(bookingStateDbDumpLatencyTimer(meterRegistry)).of(() -> flushInner(sequencedStateOutput));
        updateMetrics();
    }

    private void flushInner(SequencedStateOutput sequencedStateOutput) {
        LOGGER.info("Persisting state changed after Command: {}", sequencedStateOutput.sequenceNumber());
        StateOutput stateOutput = sequencedStateOutput.stateOutput();

        stateOutput.processedReservations()
            .forEach(entity -> saveOrUpdate(entity, e -> reservationRepository.findByReservationRef(e.getReservationRef()), reservationRepository::save));

        stateOutput.processedTransactions()
            .forEach(entity -> saveOrUpdate(entity, e -> transactionRepository.findByUuid(e.getUuid()), transactionRepository::save));

        // TODO SPL check and simplify LE persistence?
        stateOutput.processedLedgerEntries().forEach(entity -> {
            // below steps are needed because LEs are mutable due to Settlement flag change
            if (entity.getId() != null) {
                ledgerEntryRepository
                    .findById(entity.getId())
                    .ifPresent(existing -> refreshVersion(entity, existing));
            } else {
                // todo-md we may need an uuid on LE too...
            }

            ledgerEntryRepository.save(entity);
        });

        Optional<LedgerEntry> latestLedgerEntry = getLast(stateOutput.processedLedgerEntries());

        stateOutput.processedPositions().values().forEach(position -> {
            latestLedgerEntry.ifPresent(latest -> position.setLastAppliedLedgerEntryId(latest.getId()));
            saveOrUpdate(position, positionRepository::findByReference, positionRepository::save);
        });

        snapshotterTrackingRepositoryFacade.updateLastDurablyProcessedCommand(sequencedStateOutput.sequenceNumber());
        LOGGER.info("Persisted state output after Command: {}", sequencedStateOutput.sequenceNumber());
    }

    private static void refreshVersion(AuditedEntity toSave, AuditedEntity existing) {
        toSave.setVersion(existing.getVersion());
    }

    private void updateMetrics() {
        try {
            meterRegistry.counter(BOOKING_STATE_DBDUMP_COUNT).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
