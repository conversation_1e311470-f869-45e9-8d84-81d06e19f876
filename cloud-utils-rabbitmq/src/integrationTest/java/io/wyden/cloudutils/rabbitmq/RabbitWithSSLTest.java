package io.wyden.cloudutils.rabbitmq;

import org.junit.jupiter.api.Test;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.testcontainers.utility.MountableFile.forClasspathResource;

@Testcontainers
public class RabbitWithSSLTest {

    public static final String TLSV_1_2 = "TLSv1.2";

    @Test
    void shouldWorkWithSSL() {
        try (RabbitMQContainer container = new RabbitMQContainer("rabbitmq:3.12-management")) {
            container
                .withNetwork(Network.newNetwork())
                .withCopyFileToContainer(forClasspathResource("/certs/server_certificate.pem", 0644), "/etc/rabbitmq/rabbitmq_cert.pem")
                .withCopyFileToContainer(forClasspathResource("/certs/ca_certificate.pem", 0644), "/etc/rabbitmq/ca_cert.pem")
                .withCopyFileToContainer(forClasspathResource("/certs/server_key.pem", 0644), "/etc/rabbitmq/rabbitmq_key.pem")
                .withRabbitMQConfig(forClasspathResource("/rabbitmq-ssl.conf"))
            ;

            container.start();

            RabbitIntegrator rabbitIntegrator = new RabbitIntegrator(
                container.getAdminUsername(),
                container.getAdminPassword(),
                "/",
                container.getHost(),
                container.getMappedPort(5671),
                TLSV_1_2
            );

            rabbitIntegrator.checkHealth();
        }
    }
}
