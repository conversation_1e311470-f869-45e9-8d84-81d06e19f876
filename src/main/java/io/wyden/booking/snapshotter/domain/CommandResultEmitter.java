package io.wyden.booking.snapshotter.domain;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.common.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

import static io.wyden.booking.snapshotter.infrastructure.telemetry.Meters.BOOKING_SNAPSHOTTER_RESULT_OUTGOING;
import static io.wyden.booking.snapshotter.infrastructure.telemetry.TelemetryConfig.SERVICE;

@Component
public class CommandResultEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommandResultEmitter.class);

    private final RabbitExchange<Result> commandResultExchange;
    private final MeterRegistry meterRegistry;

    public CommandResultEmitter(RabbitExchange<Result> commandResultExchange,
                                Telemetry telemetry) {
        this.commandResultExchange = commandResultExchange;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    public void emit(Result result) {
        commandResultExchange.publishWithHeaders(
            result,
            Map.of(
                OemsHeader.SERVICE.getHeaderName(), SERVICE
            ));
        updateMetrics(result);
        LOGGER.debug("Emitted command result: {}", result);
    }

    private void updateMetrics(Result result) {
        try {
            Tags tags = Tags.of(
                "resultType", result.getResultCase().name(),
                "successType", result.hasSuccess() ? result.getSuccess().getSuccessCase().name() : "",
                "failureType", result.getError().getCategory(),
                "target", result.getMetadata().getTarget()
            );
            meterRegistry.counter(BOOKING_SNAPSHOTTER_RESULT_OUTGOING, tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
