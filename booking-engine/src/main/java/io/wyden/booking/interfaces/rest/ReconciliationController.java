package io.wyden.booking.interfaces.rest;

import io.wyden.booking.application.transaction.TransactionQueryService;
import io.wyden.published.booking.TransactionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/reconciliation/transactions")
public class ReconciliationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationController.class);

    private final JdbcTemplate jdbcTemplate;
    private final TransactionQueryService transactionQueryService;

    public ReconciliationController(JdbcTemplate jdbcTemplate, TransactionQueryService transactionQueryService) {
        this.jdbcTemplate = jdbcTemplate;
        this.transactionQueryService = transactionQueryService;
    }

    @GetMapping("/sequence-numbers")
    public ResponseEntity<SequenceNumberInfo> getSequenceNumberInfo(
            @RequestParam(required = false) Long min,
            @RequestParam(required = false) Long max,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "1000") int size) {
        try {
            if (min != null && max != null) {
                // Return sequence numbers in range with pagination
                int offset = page * size;
                String sql = """
                    SELECT sequence_number
                    FROM transaction
                    WHERE sequence_number BETWEEN ? AND ?
                    ORDER BY sequence_number
                    LIMIT ? OFFSET ?
                    """;
                List<Long> sequenceNumbers = jdbcTemplate.queryForList(sql, Long.class, min, max, size, offset);

                // Get total count for this range
                String countSql = "SELECT COUNT(*) FROM transaction WHERE sequence_number BETWEEN ? AND ?";
                Long totalCount = jdbcTemplate.queryForObject(countSql, Long.class, min, max);

                return ResponseEntity.ok(new SequenceNumberInfo(null, null, totalCount, sequenceNumbers, page, size));
            } else {
                // Return min, max and count
                String minSql = "SELECT MIN(sequence_number) FROM transaction WHERE sequence_number IS NOT NULL";
                String maxSql = "SELECT MAX(sequence_number) FROM transaction WHERE sequence_number IS NOT NULL";
                String countSql = "SELECT COUNT(*) FROM transaction WHERE sequence_number IS NOT NULL";

                Long minSeq = jdbcTemplate.queryForObject(minSql, Long.class);
                Long maxSeq = jdbcTemplate.queryForObject(maxSql, Long.class);
                Long count = jdbcTemplate.queryForObject(countSql, Long.class);

                return ResponseEntity.ok(new SequenceNumberInfo(minSeq, maxSeq, count, null, 0, 0));
            }
        } catch (Exception e) {
            LOGGER.error("Error getting sequence number info", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/by-sequence")
    public ResponseEntity<List<TransactionWithSnapshot>> getTransactionsBySequenceNumbers(
            @RequestParam List<Long> sequenceNumbers,
            @RequestParam(defaultValue = "1000") int maxResults) {
        try {
            if (sequenceNumbers.isEmpty()) {
                return ResponseEntity.ok(List.of());
            }

            // Limit batch size to prevent memory issues
            List<Long> limitedSequenceNumbers = sequenceNumbers.stream()
                .limit(maxResults)
                .toList();

            String placeholders = String.join(",", limitedSequenceNumbers.stream().map(s -> "?").toList());
            String sql = String.format("""
                SELECT id, uuid, sequence_number, transaction_type, created_at, updated_at
                FROM transaction
                WHERE sequence_number IN (%s)
                ORDER BY sequence_number
                """, placeholders);

            List<TransactionInfo> basicInfo = jdbcTemplate.query(sql, (rs, rowNum) -> new TransactionInfo(
                rs.getLong("id"),
                UUID.fromString(rs.getString("uuid")),
                rs.getLong("sequence_number"),
                rs.getString("transaction_type"),
                rs.getTimestamp("created_at").toString(),
                rs.getTimestamp("updated_at").toString()
            ), limitedSequenceNumbers.toArray());

            // Get full transaction snapshots
            List<TransactionWithSnapshot> results = basicInfo.stream()
                .map(info -> {
                    try {
                        TransactionSnapshot snapshot = transactionQueryService.getTransactionSnapshot(info.getUuid());
                        return new TransactionWithSnapshot(info.getSequenceNumber(), snapshot);
                    } catch (Exception e) {
                        LOGGER.error("Error getting transaction snapshot for UUID: {}", info.getUuid(), e);
                        return null;
                    }
                })
                .filter(result -> result != null)
                .toList();

            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error getting transactions by sequence numbers", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/by-uuid")
    public ResponseEntity<List<UuidSequenceNumberPair>> getSequenceNumbersByUuids(
            @RequestParam List<UUID> uuids,
            @RequestParam(defaultValue = "1000") int maxResults) {
        try {
            if (uuids.isEmpty()) {
                return ResponseEntity.ok(List.of());
            }

            // Limit batch size to prevent memory issues
            List<UUID> limitedUuids = uuids.stream()
                .limit(maxResults)
                .toList();

            String placeholders = String.join(",", limitedUuids.stream().map(u -> "?").toList());
            String sql = String.format("""
                SELECT uuid, sequence_number
                FROM transaction
                WHERE uuid IN (%s) AND sequence_number IS NOT NULL
                ORDER BY sequence_number
                """, placeholders);

            List<UuidSequenceNumberPair> results = jdbcTemplate.query(sql, (rs, rowNum) -> new UuidSequenceNumberPair(
                UUID.fromString(rs.getString("uuid")),
                rs.getLong("sequence_number")
            ), limitedUuids.toArray());

            return ResponseEntity.ok(results);
        } catch (Exception e) {
            LOGGER.error("Error getting sequence numbers by UUIDs", e);
            return ResponseEntity.internalServerError().build();
        }
    }



    public static class SequenceNumberInfo {
        private Long min;
        private Long max;
        private Long count;
        private List<Long> sequenceNumbers;
        private Integer page;
        private Integer size;
        private Boolean hasMore;

        public SequenceNumberInfo() {}

        public SequenceNumberInfo(Long min, Long max, Long count, List<Long> sequenceNumbers, Integer page, Integer size) {
            this.min = min;
            this.max = max;
            this.count = count;
            this.sequenceNumbers = sequenceNumbers;
            this.page = page;
            this.size = size;
            this.hasMore = sequenceNumbers != null && count != null && ((page + 1) * size < count);
        }

        // Getters and setters
        public Long getMin() { return min; }
        public void setMin(Long min) { this.min = min; }

        public Long getMax() { return max; }
        public void setMax(Long max) { this.max = max; }

        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }

        public List<Long> getSequenceNumbers() { return sequenceNumbers; }
        public void setSequenceNumbers(List<Long> sequenceNumbers) { this.sequenceNumbers = sequenceNumbers; }

        public Integer getPage() { return page; }
        public void setPage(Integer page) { this.page = page; }

        public Integer getSize() { return size; }
        public void setSize(Integer size) { this.size = size; }

        public Boolean getHasMore() { return hasMore; }
        public void setHasMore(Boolean hasMore) { this.hasMore = hasMore; }
    }

    public static class TransactionInfo {
        private Long id;
        private UUID uuid;
        private Long sequenceNumber;
        private String transactionType;
        private String createdAt;
        private String updatedAt;

        public TransactionInfo() {}

        public TransactionInfo(Long id, UUID uuid, Long sequenceNumber, String transactionType, String createdAt, String updatedAt) {
            this.id = id;
            this.uuid = uuid;
            this.sequenceNumber = sequenceNumber;
            this.transactionType = transactionType;
            this.createdAt = createdAt;
            this.updatedAt = updatedAt;
        }

        // Getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public UUID getUuid() { return uuid; }
        public void setUuid(UUID uuid) { this.uuid = uuid; }

        public Long getSequenceNumber() { return sequenceNumber; }
        public void setSequenceNumber(Long sequenceNumber) { this.sequenceNumber = sequenceNumber; }

        public String getTransactionType() { return transactionType; }
        public void setTransactionType(String transactionType) { this.transactionType = transactionType; }

        public String getCreatedAt() { return createdAt; }
        public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }

        public String getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }
    }

    public static class TransactionWithSnapshot {
        private Long sequenceNumber;
        private TransactionSnapshot transactionSnapshot;

        public TransactionWithSnapshot() {}

        public TransactionWithSnapshot(Long sequenceNumber, TransactionSnapshot transactionSnapshot) {
            this.sequenceNumber = sequenceNumber;
            this.transactionSnapshot = transactionSnapshot;
        }

        public Long getSequenceNumber() { return sequenceNumber; }
        public void setSequenceNumber(Long sequenceNumber) { this.sequenceNumber = sequenceNumber; }

        public TransactionSnapshot getTransactionSnapshot() { return transactionSnapshot; }
        public void setTransactionSnapshot(TransactionSnapshot transactionSnapshot) { this.transactionSnapshot = transactionSnapshot; }
    }

    public static class UuidSequenceNumberPair {
        private UUID uuid;
        private Long sequenceNumber;

        public UuidSequenceNumberPair() {}

        public UuidSequenceNumberPair(UUID uuid, Long sequenceNumber) {
            this.uuid = uuid;
            this.sequenceNumber = sequenceNumber;
        }

        public UUID getUuid() { return uuid; }
        public void setUuid(UUID uuid) { this.uuid = uuid; }

        public Long getSequenceNumber() { return sequenceNumber; }
        public void setSequenceNumber(Long sequenceNumber) { this.sequenceNumber = sequenceNumber; }
    }
}
