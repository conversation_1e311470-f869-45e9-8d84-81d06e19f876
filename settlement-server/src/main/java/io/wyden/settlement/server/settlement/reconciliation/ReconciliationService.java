package io.wyden.settlement.server.settlement.reconciliation;

import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberEntity;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberRepository;
import io.wyden.settlement.server.settlement.transaction.StreetCashTradeMapper;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class ReconciliationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationService.class);

    private final TransactionRepository transactionRepository;
    private final BookingSequenceNumberRepository bookingSequenceNumberRepository;
    private final TransactionFeeRepository transactionFeeRepository;
    private final BookingTransactionClient bookingTransactionClient;

    public ReconciliationService(TransactionRepository transactionRepository,
                                 BookingSequenceNumberRepository bookingSequenceNumberRepository,
                                 TransactionFeeRepository transactionFeeRepository,
                                 BookingTransactionClient bookingTransactionClient) {
        this.transactionRepository = transactionRepository;
        this.bookingSequenceNumberRepository = bookingSequenceNumberRepository;
        this.transactionFeeRepository = transactionFeeRepository;
        this.bookingTransactionClient = bookingTransactionClient;
    }

    public ReconciliationResult performReconciliation() {
        LOGGER.info("Starting reconciliation procedure");

        ReconciliationResult.Builder resultBuilder = ReconciliationResult.builder();

        try {
            // Step 1: Get sequence number ranges first to understand scale
            BookingTransactionClient.SequenceNumberRange bookingRange = bookingTransactionClient.getSequenceNumberRange();

            if (bookingRange.min() == null || bookingRange.max() == null) {
                LOGGER.warn("No sequence numbers found in booking service");
                return resultBuilder.success(false).errorMessage("No sequence numbers found in booking service").build();
            }

            LOGGER.info("Booking sequence range: {} - {}, total count: {}", bookingRange.min(), bookingRange.max(), bookingRange.count());
            resultBuilder.bookingSequenceRange(new SequenceRange(bookingRange.min(), bookingRange.max()));

            // Step 2: Backfill sequence numbers for existing transactions (in batches)
            int backfilledCount = backfillSequenceNumbersForExistingTransactions();
            resultBuilder.backfilledTransactions(backfilledCount);
            LOGGER.info("Backfilled sequence numbers for {} existing transactions", backfilledCount);

            Optional<Long> settlementMinSeq = bookingSequenceNumberRepository.findMinSequenceNumber();
            Optional<Long> settlementMaxSeq = bookingSequenceNumberRepository.findMaxSequenceNumber();
            if (settlementMinSeq.isPresent() && settlementMaxSeq.isPresent()) {
                resultBuilder.settlementSequenceRange(new SequenceRange(settlementMinSeq.get(), settlementMaxSeq.get()));
            }

            // Step 3: Find discrepancies (process in chunks to handle millions of records)
            List<Long> discrepancies = findDiscrepanciesInChunks(bookingRange.min(), bookingRange.max());
            resultBuilder.missingSequenceNumbers(discrepancies);
            LOGGER.info("Found {} missing sequence numbers in settlement", discrepancies.size());

            // Step 4: Pull missing transactions (in batches)
            int pulledCount = pullMissingTransactionsInBatches(discrepancies);
            resultBuilder.pulledTransactions(pulledCount);
            LOGGER.info("Pulled {} missing transactions from booking", pulledCount);

            resultBuilder.success(true);

        } catch (Exception e) {
            LOGGER.error("Error during reconciliation", e);
            resultBuilder.success(false).errorMessage(e.getMessage());
        }

        ReconciliationResult result = resultBuilder.build();
        LOGGER.info("Reconciliation completed: {}", result);
        return result;
    }

    private int backfillSequenceNumbersForExistingTransactions() {
        // Get all transactions that don't have sequence numbers yet
        List<TransactionEntity> transactionsWithoutSeqNum = transactionRepository.findTransactionsWithoutSequenceNumbers();

        if (transactionsWithoutSeqNum.isEmpty()) {
            return 0;
        }

        LOGGER.info("Found {} transactions without sequence numbers, processing in batches", transactionsWithoutSeqNum.size());

        int backfilledCount = 0;
        int batchSize = 1000;

        // Process in batches to handle large datasets
        for (int i = 0; i < transactionsWithoutSeqNum.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, transactionsWithoutSeqNum.size());
            List<TransactionEntity> batch = transactionsWithoutSeqNum.subList(i, endIndex);

            // Extract UUIDs from the batch
            List<UUID> uuids = batch.stream()
                .map(TransactionEntity::transactionUuid)
                .toList();

            try {
                List<BookingTransactionClient.UuidSequenceNumberPair> pairs = bookingTransactionClient.getSequenceNumbersByUuids(uuids);

                Map<UUID, Long> uuidToSequenceNumber = pairs.stream()
                    .collect(Collectors.toMap(
                        BookingTransactionClient.UuidSequenceNumberPair::uuid,
                        BookingTransactionClient.UuidSequenceNumberPair::sequenceNumber
                    ));

                for (TransactionEntity transaction : batch) {
                    Long sequenceNumber = uuidToSequenceNumber.get(transaction.transactionUuid());
                    if (sequenceNumber != null) {
                        createSequenceNumberEntity(transaction.id(), sequenceNumber);
                        backfilledCount++;
                        LOGGER.debug("Backfilled sequence number {} for transaction UUID: {}", sequenceNumber, transaction.transactionUuid());
                    } else {
                        LOGGER.warn("No sequence number found for transaction UUID: {}", transaction.transactionUuid());
                    }
                }

            } catch (Exception e) {
                LOGGER.error("Error getting sequence numbers for batch, batch size: {}", batch.size(), e);
            }

            LOGGER.info("Processed batch {}/{}, backfilled {} so far",
                (i / batchSize) + 1, (transactionsWithoutSeqNum.size() + batchSize - 1) / batchSize, backfilledCount);
        }

        return backfilledCount;
    }

    private List<Long> findDiscrepanciesInChunks(Long minSequenceNumber, Long maxSequenceNumber) {
        List<Long> allDiscrepancies = new ArrayList<>();
        long chunkSize = 10000; // Process 10k sequence numbers at a time

        LOGGER.info("Finding discrepancies in range {} - {} using chunks of {}", minSequenceNumber, maxSequenceNumber, chunkSize);

        for (long start = minSequenceNumber; start <= maxSequenceNumber; start += chunkSize) {
            long end = Math.min(start + chunkSize - 1, maxSequenceNumber);

            try {
                // Get booking sequence numbers for this chunk
                List<Long> bookingSequenceNumbers = bookingTransactionClient.getSequenceNumbersInRange(start, end);
                Set<Long> bookingSequenceSet = new HashSet<>(bookingSequenceNumbers);

                // Get settlement sequence numbers for this chunk
                List<BookingSequenceNumberEntity> settlementSequenceNumbers =
                    bookingSequenceNumberRepository.findBySequenceNumberRange(start, end);
                Set<Long> settlementSequenceSet = settlementSequenceNumbers.stream()
                    .map(BookingSequenceNumberEntity::sequenceNumber)
                    .collect(Collectors.toSet());

                // Find discrepancies in this chunk
                List<Long> chunkDiscrepancies = bookingSequenceSet.stream()
                    .filter(seqNum -> !settlementSequenceSet.contains(seqNum))
                    .sorted()
                    .toList();

                allDiscrepancies.addAll(chunkDiscrepancies);

                LOGGER.info("Processed chunk {} - {}: found {} discrepancies", start, end, chunkDiscrepancies.size());

            } catch (Exception e) {
                LOGGER.error("Error processing chunk {} - {}", start, end, e);
            }
        }

        return allDiscrepancies;
    }

    int pullMissingTransactionsInBatches(List<Long> missingSequenceNumbers) {
        if (missingSequenceNumbers.isEmpty()) {
            return 0;
        }

        LOGGER.info("Pulling {} missing transactions in batches", missingSequenceNumbers.size());

        int pulledCount = 0;
        int batchSize = 1000;

        for (int i = 0; i < missingSequenceNumbers.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, missingSequenceNumbers.size());
            List<Long> batch = missingSequenceNumbers.subList(i, endIndex);

            try {
                List<BookingTransactionClient.TransactionWithSnapshot> transactionsWithSnapshots =
                    bookingTransactionClient.getTransactionsWithSnapshotsBySequenceNumbers(batch);

                // Process each transaction with its snapshot
                for (BookingTransactionClient.TransactionWithSnapshot transactionWithSnapshot : transactionsWithSnapshots) {
                    if (transactionWithSnapshot.transactionSnapshot() != null && transactionWithSnapshot.sequenceNumber() != null) {
                        try {
                            // Create and save transaction entity directly from snapshot
                            long transactionId = createTransactionFromSnapshot(transactionWithSnapshot.transactionSnapshot());

                            // Create and save sequence number entity
                            createSequenceNumberEntity(transactionId, transactionWithSnapshot.sequenceNumber());

                            pulledCount++;
                            LOGGER.debug("Successfully pulled transaction with sequence number: {}", transactionWithSnapshot.sequenceNumber());
                        } catch (Exception e) {
                            LOGGER.error("Error pulling transaction with sequence number {}", transactionWithSnapshot.sequenceNumber(), e);
                        }
                    }
                }

                LOGGER.info("Processed missing transactions batch {}/{}, pulled {} so far",
                    (i / batchSize) + 1, (missingSequenceNumbers.size() + batchSize - 1) / batchSize, pulledCount);

            } catch (Exception e) {
                LOGGER.error("Error pulling missing transactions batch", e);
            }
        }
        return pulledCount;
    }

    private long createTransactionFromSnapshot(TransactionSnapshot snapshot) {
        if (snapshot.hasStreetCashTrade()) {
            // Map StreetCashTradeSnapshot to TransactionEntity using existing mapper
            TransactionEntity transactionEntity = StreetCashTradeMapper.mapToTransactionEntity(snapshot.getStreetCashTrade(), null);
            long transactionId = transactionRepository.save(transactionEntity);

            // Save transaction fees
            List<TransactionFeeEntity> transactionFees = StreetCashTradeMapper.mapToTransactionFeesEntity(snapshot.getStreetCashTrade(), transactionId);
            transactionFees.forEach(transactionFeeRepository::save);

            return transactionId;
        } else {
            // Handle other transaction types if needed
            LOGGER.warn("Unsupported transaction type in snapshot: {}", snapshot.getTransactionCase());
            throw new IllegalArgumentException("Unsupported transaction type: " + snapshot.getTransactionCase());
        }
    }

    private void createSequenceNumberEntity(long transactionId, long sequenceNumber) {
        BookingSequenceNumberEntity sequenceNumberEntity = BookingSequenceNumberEntity.builder()
            .transactionId(transactionId)
            .sequenceNumber(sequenceNumber)
            .createdAt(Timestamp.from(Instant.now()))
            .updatedAt(Timestamp.from(Instant.now()))
            .build();

        bookingSequenceNumberRepository.save(sequenceNumberEntity);
    }

    public ReconciliationStatus getReconciliationStatus() {
        long settlementTransactionCount = transactionRepository.count();
        long settlementSequenceNumberCount = bookingSequenceNumberRepository.count();

        BookingTransactionClient.SequenceNumberRange bookingRange = bookingTransactionClient.getSequenceNumberRange();
        Optional<Long> settlementMinSeq = bookingSequenceNumberRepository.findMinSequenceNumber();
        Optional<Long> settlementMaxSeq = bookingSequenceNumberRepository.findMaxSequenceNumber();

        return ReconciliationStatus.builder()
            .settlementTransactionCount(settlementTransactionCount)
            .settlementSequenceNumberCount(settlementSequenceNumberCount)
            .bookingTransactionCount(bookingRange.count() != null ? bookingRange.count() : 0)
            .bookingSequenceRange(bookingRange.min() != null && bookingRange.max() != null ?
                new SequenceRange(bookingRange.min(), bookingRange.max()) : null)
            .settlementSequenceRange(settlementMinSeq.isPresent() && settlementMaxSeq.isPresent() ?
                new SequenceRange(settlementMinSeq.get(), settlementMaxSeq.get()) : null)
            .build();
    }

    public record SequenceRange(Long min, Long max) {

        @Override
        public String toString() {
            return String.format("[%d, %d]", min, max);
        }
    }
}
