package io.wyden.booking.common;

import io.wyden.booking.domain.reservation.NoopReservation;
import io.wyden.booking.domain.reservation.Reservation;
import io.wyden.booking.domain.reservation.ReservationFee;
import io.wyden.booking.domain.reservation.ReservationFeeType;
import io.wyden.booking.domain.reservation.payment.DepositReservation;
import io.wyden.booking.domain.reservation.payment.WithdrawalReservation;
import io.wyden.booking.domain.reservation.trade.ClientCashTradeReservation;
import io.wyden.booking.domain.reservation.trade.StreetCashTradeReservation;
import io.wyden.booking.domain.transaction.Transaction;
import io.wyden.booking.domain.transaction.TransactionFee;
import io.wyden.booking.domain.transaction.TransactionFeeType;
import io.wyden.booking.domain.transaction.payment.Deposit;
import io.wyden.booking.domain.transaction.payment.Withdrawal;
import io.wyden.booking.domain.transaction.trade.ClientCashTrade;
import io.wyden.booking.domain.transaction.trade.StreetCashTrade;

import java.util.Collection;
import java.util.List;

public final class ReservationFactory {

    private ReservationFactory() {
    }

    public static Reservation create(Transaction transaction) {
        if (transaction instanceof ClientCashTrade trade) {
            return create(trade);
        }

        if (transaction instanceof StreetCashTrade trade) {
            return create(trade);
        }

        if (transaction instanceof Deposit deposit) {
            return create(deposit);
        }

        if (transaction instanceof Withdrawal withdrawal) {
            return create(withdrawal);
        }

        return new NoopReservation();
    }

    public static Reservation create(ClientCashTrade trade) {
        return new ClientCashTradeReservation(
            trade.getUuid(),
            trade.getReservationRef(),
            trade.getDateTime(),
            trade.getQuantity(),
            trade.getPrice(),
            null,
            trade.getBaseCurrencyToPortfolioCurrencyRate(),
            trade.getBaseCurrencyToCounterPortfolioCurrencyRate(),
            trade.getCurrency(),
            trade.getBaseCurrency(),
            trade.getPortfolio(),
            trade.getCounterPortfolio(),
            map(trade.getFees()));
    }

    public static Reservation create(StreetCashTrade trade) {
        return new StreetCashTradeReservation(
            trade.getUuid(),
            trade.getReservationRef(),
            trade.getDateTime(),
            trade.getQuantity(),
            trade.getPrice(),
            null,
            trade.getBaseCurrencyToPortfolioCurrencyRate(),
            trade.getBaseCurrencyToAccountCurrencyRate(),
            trade.getCurrency(),
            trade.getBaseCurrency(),
            trade.getPortfolio(),
            trade.getAccount(),
            map(trade.getFees()));
    }

    public static Reservation create(Deposit deposit) {
        return new DepositReservation(
            deposit.getUuid(),
            deposit.getReservationRef(),
            deposit.getDateTime(),
            deposit.getCurrency(),
            deposit.getQuantity(),
            deposit.getPortfolio(),
            deposit.getAccount(),
            deposit.getFeePortfolio(),
            deposit.getFeeAccount(),
            map(deposit.getFees()));
    }

    public static Reservation create(Withdrawal withdrawal) {
        return new WithdrawalReservation(
            withdrawal.getUuid(),
            withdrawal.getReservationRef(),
            withdrawal.getDateTime(),
            withdrawal.getCurrency(),
            withdrawal.getQuantity(),
            withdrawal.getPortfolio(),
            withdrawal.getAccount(),
            withdrawal.getFeePortfolio(),
            withdrawal.getFeeAccount(),
            map(withdrawal.getFees()));
    }

    public static Collection<ReservationFee> map(Collection<TransactionFee> fees) {
        if (fees == null) {
            return List.of();
        }

        return fees.stream()
            .map(fee -> new ReservationFee(fee.getAmount(), fee.getCurrency(), fee.getDescription(), map(fee.getFeeType())))
            .toList();
    }

    private static ReservationFeeType map(TransactionFeeType feeType) {
        if (feeType == null) {
            return ReservationFeeType.FEE_TYPE_UNSPECIFIED;
        }

        return switch (feeType) {
            case FIXED_FEE -> ReservationFeeType.FIXED_FEE;
            case EXCHANGE_FEE -> ReservationFeeType.EXCHANGE_FEE;
            case TRANSACTION_FEE -> ReservationFeeType.TRANSACTION_FEE;
            default -> ReservationFeeType.FEE_TYPE_UNSPECIFIED;
        };
    }
}
