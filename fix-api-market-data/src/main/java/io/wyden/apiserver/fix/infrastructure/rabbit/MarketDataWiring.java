package io.wyden.apiserver.fix.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.wyden.apiserver.fix.common.marketdata.infrastructure.MarketDataEventConsumers;
import io.wyden.apiserver.fix.common.marketdata.infrastructure.MarketDataQueueBinder;
import io.wyden.apiserver.fix.common.marketdata.infrastructure.MarketDataRequester;
import io.wyden.cloud.utils.spring.util.ExclusiveNameGenerator;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.audit.EventLogEvent;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MarketDataWiring {

    private final RabbitIntegrator rabbitIntegrator;
    private final ExclusiveNameGenerator exclusiveQueueNameGenerator;
    private final String queueTemplatePrefix;
    private final String eventLogQueueTemplatePrefix;
    private final String consumerName;

    public MarketDataWiring(@Value("${marketdata.client.queueTemplatePrefix}") String queueTemplatePrefix,
                            @Value("${event.log.queueTemplatePrefix}") String eventLogQueueTemplatePrefix,
                            @Value("${spring.application.name}") String consumerName,
                            RabbitIntegrator rabbitIntegrator,
                            ExclusiveNameGenerator exclusiveQueueNameGenerator) {
        this.queueTemplatePrefix = queueTemplatePrefix;
        this.eventLogQueueTemplatePrefix = eventLogQueueTemplatePrefix;
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
        this.exclusiveQueueNameGenerator = exclusiveQueueNameGenerator;
    }

    @Bean
    public RabbitExchange<MarketDataRequest> marketDataRequestExchange() {
        return OemsExchange.MarketData.declareMarketDataRequest(rabbitIntegrator);
    }

    @Bean
    public RabbitExchange<MarketDataEvent> marketDataEventExchange() {
        return OemsExchange.MarketData.declareMarketDataAll(rabbitIntegrator);
    }

    private <T extends Message> RabbitQueue<T> simpleMarketDataQueue(String queueNameTemplate) {
        return new ExpiringRabbitQueueBuilder<T>(rabbitIntegrator)
                .setQueueName(exclusiveQueueNameGenerator.getQueueName(queueNameTemplate))
                .setSingleActiveConsumer(false)
                .setConsumerName(consumerName)
                .declare();
    }

    @Bean
    public RabbitQueue<MarketDataEvent> marketDataEventQueue() {
        return simpleMarketDataQueue(queueTemplatePrefix);
    }

    @Bean
    public MarketDataQueueBinder<MarketDataEvent> marketDataEventObserver(RabbitExchange<MarketDataEvent> exchange, RabbitQueue<MarketDataEvent> queue, MarketDataEventConsumers consumer) {
        return new MarketDataQueueBinder<>(exchange, queue, MarketDataEvent.parser(), consumer.marketDataEventConsumer());
    }

    @Bean
    MarketDataRequester mdSubscriptionRequester(RabbitExchange<MarketDataRequest> marketDataRequestExchange, @Value("${marketdata.clientside.renewViaConfigService:true}") boolean renewViaConfigService) {
        return new MarketDataRequester(marketDataRequestExchange, renewViaConfigService);
    }

    @Bean
    public MarketDataEventConsumers marketDataConsumers(Telemetry telemetry) {
        return new MarketDataEventConsumers(telemetry);
    }

    @Bean
    public RabbitQueue<EventLogEvent> eventLogEventQueue(RabbitIntegrator rabbitIntegrator) {
        return new ExpiringRabbitQueueBuilder<EventLogEvent>(rabbitIntegrator)
            .setQueueName(exclusiveQueueNameGenerator.getQueueName(eventLogQueueTemplatePrefix))
            .setConsumerName(consumerName)
            .declare();
    }
}
