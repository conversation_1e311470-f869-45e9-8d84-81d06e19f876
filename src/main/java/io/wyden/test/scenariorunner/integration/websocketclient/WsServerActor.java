package io.wyden.test.scenariorunner.integration.websocketclient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import io.qameta.allure.Step;
import io.wyden.test.scenariorunner.config.Configuration;
import io.wyden.test.scenariorunner.config.HostSettings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import org.springframework.web.reactive.socket.client.ReactorNettyWebSocketClient;
import org.springframework.web.reactive.socket.client.WebSocketClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

public class WsServerActor implements WebSocketHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(WsServerActor.class);

    private static final String POSITION = "position";
    private static final String CONNECTOR_STATE = "connector-state";

    private final HostSettings hostSettings = Configuration.getHostSettings();

    private final WebSocketClient wsClient;
    private final String key;
    private final String secret;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final CountDownLatch connectionLatch = new CountDownLatch(1);

    private WebSocketSession session;
    private Flux<String> messageFlux;

    private final JsonObjectMapper mapper = new JsonObjectMapper();

    public WsServerActor(String key, String secret) {
        this.wsClient = new ReactorNettyWebSocketClient();
        this.key = key;
        this.secret = secret;
    }

    @Step
    public void connect() {
        if (isConnected.get()) {
            LOGGER.info("Already connected.");
            return;
        }

        wsClient.execute(URI.create(hostSettings.websocketServerHost() + "/ws"), this)
            .doOnSubscribe(sub -> LOGGER.info("Connecting to WebSocket..."))
            .doOnError(err -> {
                LOGGER.error("Error connecting: " + err.getMessage());
                connectionLatch.countDown();
            })
            .subscribe();

        waitUntilSessionConnected();
    }

    private void waitUntilSessionConnected() {
        try {
            connectionLatch.await();
            if (!isConnected.get()) {
                throw new IllegalStateException("Failed to connect to WebSocketServer.");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("Connection establish wait interrupted. Error: ", e);
            throw new IllegalStateException("Failed to connect to WebSocketServer - interrupted");
        }
    }

    @Step
    public void disconnect() {
        if (session != null && session.isOpen()) {
            session.close()
                .doOnTerminate(() -> {
                    isConnected.set(false);
                    LOGGER.info("WebSocket session closed.");
                })
                .subscribe();
        } else {
            LOGGER.info("No active WebSocket session to close.");
        }
    }

    @Step
    public void login() {
        String loginMsg = mapper.write(Command.login(key, secret));
        LOGGER.info("Sending login msg: " + loginMsg);
        session.send(Mono.just(session.textMessage(loginMsg)))
            .subscribe();
    }

    @Step
    public void subscribePositions(boolean snapshot) {
        subscribe(POSITION, snapshot);
    }

    private void subscribe(String event, boolean snapshot) {
        String subscriptionMsg = mapper.write(Command.subscribe(event, snapshot));
        LOGGER.info("Sending subscribe msg: " + subscriptionMsg);
        session.send(Mono.just(session.textMessage(subscriptionMsg)))
            .subscribe();
    }

    @Step
    public void subscribeConnectorState(boolean snapshot) {
        subscribe(CONNECTOR_STATE, snapshot);
    }

    @Step
    public void unsubscribeConnectorState() {
        unsubscribe(CONNECTOR_STATE);
    }

    private void unsubscribe(String event) {
        String msg = mapper.write(Command.unsubscribe(event));
        LOGGER.info("Sending unsubscribe msg: " + msg);
        session.send(Mono.just(session.textMessage(msg)))
            .subscribe();
    }

    @Step
    public void unsubscribePositions() {
        unsubscribe(POSITION);
    }

    public Flux<String> messages() {
        if (messageFlux != null) {
            return messageFlux;
        }
        return Flux.empty();
    }

    @Override
    public Mono<Void> handle(WebSocketSession session) {
        this.session = session;
        LOGGER.info("Connected to WebSocket!");
        this.isConnected.set(true);
        this.connectionLatch.countDown();

        // Create a Flux for incoming messages
        messageFlux = session.receive()
            .subscribeOn(Schedulers.boundedElastic())
            .map(WebSocketMessage::getPayloadAsText)
            .doOnNext(msg -> LOGGER.info("Received message: " + msg))
            .doOnError(err -> LOGGER.error("Error receiving message: " + err.getMessage()))
            .doOnComplete(() -> LOGGER.info("WebSocket session completed."))
            .filter(msg -> !msg.contains("\"success\":true"));

        return Mono.never(); // Keep the connection open
    }

    private static String calculateSignature(String apiNonce, String apiSecret) {
        byte[] secretBytes = apiSecret.getBytes(StandardCharsets.UTF_8);

        HashCode hash = Hashing.hmacSha256(secretBytes)
            .hashString(apiNonce, StandardCharsets.UTF_8);

        return hash.toString();
    }

    public record Command(String type, String event, String id, Map<String, Object> params) {
        public Command {
            params = params == null ? Map.of() : params;
        }

        public static Command login(String key, String secret) {
            String nonce = String.valueOf(System.currentTimeMillis());
            String signature = calculateSignature(nonce, secret);
            return new Command("login", null, "1", Map.of(
                "key", key,
                "nonce", nonce,
                "signature", signature
            ));
        }

        public static Command subscribe(String event, boolean snapshot) {
            return new Command("subscribe", event, "2", Map.of(
                "snapshot", snapshot
            ));
        }

        public static Command unsubscribe(String event) {
            return new Command("unsubscribe", event, "3", null);
        }

    }

    private static class JsonObjectMapper {

        private static final Logger LOGGER = LoggerFactory.getLogger(JsonObjectMapper.class);

        private final ObjectMapper objectMapper;

        public JsonObjectMapper() {
            this.objectMapper = new ObjectMapper();
        }

        public <T> T read(String json, Class<T> type) {
            try {
                return objectMapper.readValue(json, type);
            } catch (JsonProcessingException e) {
                LOGGER.error("Error reading json: " + json);
                throw new RuntimeException(e);
            }
        }

        public String write(Object object) {
            try {
                return objectMapper.writeValueAsString(object);
            } catch (JsonProcessingException e) {
                LOGGER.error("Error writing json: " + object.toString());
                throw new RuntimeException(e);
            }
        }

    }

//    public static void main(String[] args) throws InterruptedException {
//        WsServerActor actor = new WsServerActor("48e4d332-1714-4bc7-984b-2e572172568e", "wykJki&vc4UMAaPX");
//        actor.connect();
//        actor.login();
//        actor.subscribePositions(true);
//        List<String> msges = new ArrayList<>();
//        actor.messages().subscribe(msges::add);
//        Thread.sleep(1000);
//        System.out.println(msges);
//        actor.unsubscribePositions();
//        msges.clear();
//        actor.subscribeConnectorState(true);
//        Thread.sleep(30000);
//        System.out.println(msges);
//        actor.unsubscribeConnectorState();
//        actor.disconnect();
//    }

}
