package io.wyden.apiserver.rest.utils;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelReplaceRequest;
import io.wyden.apiserver.rest.trading.model.OrderCancelRequest;
import io.wyden.apiserver.rest.trading.model.OrderStatusRequest;
import io.wyden.published.client.ClientRequestType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class MetricsUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetricsUtils.class);

    private MetricsUtils() {
        // Empty
    }

    public static void updateMetrics(MeterRegistry meterRegistry, NewOrderSingleRequest request) {
        try {
            String instrumentId = request.getInstrumentId();
            String orderType = request.getOrderType().name();
            String messageType = ClientRequestType.ORDER_SINGLE.name();
            meterRegistry.counter("wyden.trading.request.incoming.count",
                "instrumentId", instrumentId,
                "orderType", orderType,
                "messageType", messageType,
                "handlerType", "standard").increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    public static void updateMetrics(MeterRegistry meterRegistry, OrderCancelRequest request) {
        try {
            String messageType = ClientRequestType.CANCEL.name();
            meterRegistry.counter("wyden.trading.request.incoming.count",
                "instrumentId", "",
                "orderType", "",
                "messageType", messageType,
                "handlerType", "standard").increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    public static void updateMetrics(MeterRegistry meterRegistry, OrderCancelReplaceRequest request) {
        try {
            String instrumentId = request.replacingOrder().getInstrumentId();
            String orderType = request.replacingOrder().getOrderType().name();
            String messageType = ClientRequestType.CANCEL_REPLACE.name();
            meterRegistry.counter("wyden.trading.request.incoming.count",
                "instrumentId", instrumentId,
                "orderType", orderType,
                "messageType", messageType,
                "handlerType", "standard").increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    public static void updateMetrics(MeterRegistry meterRegistry, OrderStatusRequest request) {
        try {
            String messageType = ClientRequestType.ORDER_STATUS_REQUEST.name();
            meterRegistry.counter("wyden.trading.request.incoming.count",
                "instrumentId", "",
                "orderType", "",
                "messageType", messageType,
                "handlerType", "standard").increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
