package io.wyden.apiserver.fix.common.marketdata;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.apiserver.fix.common.eventlog.EventLogConsumer;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataDepth;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataEvent;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataSubscription;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataSubscriptionKey;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataSubscriptionMap;
import io.wyden.apiserver.fix.common.marketdata.model.SessionScopedMDRequest;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.audit.EventLogEvent;
import io.wyden.published.marketdata.MarketDataRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import quickfix.field.MDReqID;
import quickfix.field.Text;
import quickfix.fix44.MarketDataRequestReject;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.util.NoSuchElementException;
import java.util.Optional;

public abstract class MarketDataSubscriptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataSubscriptionService.class);
    protected final MarketDataAdapter marketDataAdapter;
    private final MeterRegistry meterRegistry;
    protected final MarketDataSubscriptionMap subscriptionMap;
    private final FixSessionWrapper fixSessionWrapper;

    public MarketDataSubscriptionService(MarketDataAdapter marketDataAdapter,
                                         Telemetry telemetry,
                                         String side,
                                         EventLogConsumer eventLogConsumer,
                                         FixSessionWrapper fixSessionWrapper,
                                         String applicationName) {
        this.marketDataAdapter = marketDataAdapter;
        this.meterRegistry = telemetry.getMeterRegistry();
        this.fixSessionWrapper = fixSessionWrapper;
        this.subscriptionMap = new MarketDataSubscriptionMap(telemetry, side);

        eventLogConsumer.streamResults()
            .filter(e -> e.getMetadata().getInResponseToRequesterId().equals(applicationName)) // filter out requests originated outside of FIX API
            .filter(EventLogEvent::hasMarketDataSubscriptionCreated)
            .subscribe(this::onMarketDataSubscriptionCreated);

        eventLogConsumer.streamResults()
            .filter(e -> e.getMetadata().getInResponseToRequesterId().equals(applicationName)) // filter out requests originated outside of FIX API
            .filter(EventLogEvent::hasMarketDataSubscriptionRejected)
            .subscribe(this::onMarketDataSubscriptionRejected);
    }

    protected void onMarketDataSubscriptionCreated(EventLogEvent event) {
    }

    protected void onMarketDataSubscriptionRejected(EventLogEvent event) {
        try {
            findByRequestId(event.getMetadata().getInResponseToRequestId())
                .ifPresent(subscription -> {
                    subscription.getClientRequests()
                        .forEach(clientRequest -> fixSessionWrapper.send(
                            clientRequest.sessionID(),
                            createMarketDataRequestReject(clientRequest.requestId(), event.getDescription())));


                    terminateSubscription(event, subscription);
                });
        } catch (Exception e) {
            LOGGER.warn("Exception when closing client side market data subscription", e);
        }
    }

    private void terminateSubscription(EventLogEvent event, MarketDataSubscription subscription) {
        String msg = "Market data subscription was rejected: %s".formatted(event.getDescription());
        LOGGER.warn(msg);
        subscription.terminate(new RuntimeException(msg));

        stopObservingAndDetachConsumer(subscription);
        subscriptionMap.remove(subscription.getKey());
    }

    private MarketDataRequestReject createMarketDataRequestReject(String mdReqIDValue, String description) {
        MDReqID mdReqID = new MDReqID(mdReqIDValue);
        MarketDataRequestReject reject = new MarketDataRequestReject(mdReqID);
        reject.set(new Text(description));
        return reject;
    }

    public Optional<MarketDataSubscription> findByRequestId(String requestId) {
        return subscriptionMap.findByRequestId(requestId);
    }

    public void reset() {
        subscriptionMap.clear();
    }

    public synchronized void unsubscribe(MarketDataSubscriptionKey marketDataSubscriptionKey, SessionScopedMDRequest sessionScopedMDRequest) {
        LOGGER.debug("Unsubscribe marketDataSubscriptionKey: {}. MarketData subscriptions size: {}", marketDataSubscriptionKey, subscriptionMap.getSubscriptionMap().size());
        LOGGER.trace("MarketData subscriptions {}", subscriptionMap.getSubscriptionMap());
        updateMetrics("wyden.market-data.unsubscribe-incoming", marketDataSubscriptionKey);
        MarketDataSubscription marketDataSubscription = Optional.ofNullable(subscriptionMap.get(marketDataSubscriptionKey))
            .orElseThrow(() -> new NoSuchElementException("No subscription found for subscriptionKey: " + marketDataSubscriptionKey));

        marketDataSubscription.removeClientRequest(sessionScopedMDRequest);

        int refCount = marketDataSubscription.getClientRequestsCount();
        if (refCount == 0) {
            LOGGER.debug("Unsubscribing and removing subscription from map");
            stopObservingAndDetachConsumer(marketDataSubscription);
            subscriptionMap.remove(marketDataSubscriptionKey);
        } else {
            LOGGER.debug("RefCount after decrementing = {}. Keeping subscription in map.", refCount);
        }
        LOGGER.debug("MarketData subscriptions size: {}", subscriptionMap.getSubscriptionMap().size());
    }

    public synchronized MarketDataSubscription subscribe(MarketDataSubscriptionKey marketDataSubscriptionKey, SessionScopedMDRequest sessionScopedMDRequest) {
        LOGGER.debug("Subscribe marketDataSubscriptionKey: {}. MarketData subscriptions size: {}", marketDataSubscriptionKey, subscriptionMap.getSubscriptionMap().size());
        LOGGER.trace("MarketData subscriptions {}", subscriptionMap.getSubscriptionMap());
        updateMetrics("wyden.market-data.subscribe-incoming", marketDataSubscriptionKey);

        Optional.ofNullable(subscriptionMap.get(marketDataSubscriptionKey)).ifPresentOrElse(
            marketDataConnectorSubscription -> marketDataConnectorSubscription.addClientRequest(sessionScopedMDRequest),
            () -> createNewSubscription(marketDataSubscriptionKey, sessionScopedMDRequest));

        LOGGER.debug("MarketData subscriptions size: {}", subscriptionMap.getSubscriptionMap().size());
        return subscriptionMap.get(marketDataSubscriptionKey);
    }

    private void createNewSubscription(MarketDataSubscriptionKey marketDataSubscriptionKey, SessionScopedMDRequest sessionScopedMDRequest) {
        LOGGER.debug("Creating Market Data subscription for marketDataSubscriptionKey: {}. MarketData subscriptions size: {}", marketDataSubscriptionKey, subscriptionMap.getSubscriptionMap().size());
        MarketDataSubscription marketDataSubscription = new MarketDataSubscription(marketDataSubscriptionKey, sessionScopedMDRequest);

        observeAndAttachConsumer(marketDataSubscription);
        updateMetrics("wyden.market-data.subscribe-outgoing", marketDataSubscriptionKey);
        sendMarketDataRequest(marketDataSubscription).orElseThrow(() -> new IllegalStateException("Connector subscribe not successful"));
        subscriptionMap.put(marketDataSubscriptionKey, marketDataSubscription);
    }

    protected Optional<MarketDataRequest> sendMarketDataRequest(MarketDataSubscription subscription) {
        int refCount = subscription.getClientRequestsCount();

        if (refCount > 0) {
            return Optional.ofNullable(marketDataAdapter.request(subscription));
        }

        LOGGER.warn("No client demand for {} - market data request won't be sent", subscription.getKey());
        return Optional.empty();
    }

    synchronized void observeAndAttachConsumer(MarketDataSubscription marketDataSubscription) {
        attachConsumer(marketDataSubscription);
    }

    private synchronized void stopObservingAndDetachConsumer(MarketDataSubscription marketDataSubscription) {
        detachConsumer(marketDataSubscription);
    }

    private void attachConsumer(MarketDataSubscription marketDataSubscription) {
        if (marketDataSubscription.getUnderlyingSubscription() != null) {
            LOGGER.debug("Subscription exist in MarketDataSubscriptionService");
            return;
        }
        Disposable subscription = attachConsumerToMarketDataEventFlux(marketDataSubscription);
        marketDataSubscription.setUnderlyingSubscription(subscription);
        LOGGER.info("Attaching consumer for subscription: {}, {}", marketDataSubscription.getKey(), marketDataSubscription.getUnderlyingSubscription());
    }

    private Disposable attachConsumerToMarketDataEventFlux(MarketDataSubscription marketDataSubscription) {
        Flux<? extends MarketDataEvent> flux = Flux.empty();
        MarketDataSubscriptionKey subscriptionKey = marketDataSubscription.getKey();

        if (subscriptionKey.depth().equals(MarketDataDepth.L1)) {
            flux = marketDataAdapter.streamMarketDataL1(marketDataSubscription);
        } else if (subscriptionKey.depth().equals(MarketDataDepth.L2)) {
            flux = marketDataAdapter.streamMarketDataL2(marketDataSubscription);
        }
        return flux
            .onBackpressureDrop()
            .publishOn(Schedulers.boundedElastic())
            .subscribe(marketDataEvent -> marketDataSubscription.getSink().tryEmitNext(marketDataEvent));
    }

    private void detachConsumer(MarketDataSubscription marketDataSubscription) {
        LOGGER.debug("Removing subscription in MarketDataSubscriptionService");
        if (marketDataSubscription.getUnderlyingSubscription() != null) {
            marketDataSubscription.getUnderlyingSubscription().dispose();
        }
        marketDataSubscription.setUnderlyingSubscription(null);
    }

    protected void updateMetrics(String name, MarketDataSubscriptionKey marketDataSubscriptionKey) {
        try {
            this.meterRegistry.counter(name, Tags.of("instrumentId", marketDataSubscriptionKey.instrumentId(), "depth", marketDataSubscriptionKey.depth().name())).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
