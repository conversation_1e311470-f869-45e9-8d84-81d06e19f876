package io.wyden.rest.management.account;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import io.wyden.published.referencedata.WalletType;
import io.wyden.published.referencedata.account.AccountOnboardingRequest;
import io.wyden.rest.management.domain.OnboardingModel;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;

import static io.wyden.rest.management.portfolio.PortfolioToProtoMapper.mapGrants;

public final class AccountToProtoMapper {

    private AccountToProtoMapper() {}

    public static AccountOnboardingRequest map(Collection<OnboardingModel.AccountOnboardingRequest> onboardingRequests, String clientId, String correlationId) {
        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt(DateUtils.toIsoUtcTime())
            .setCorrelationObject(correlationId)
            .build();

        List<VenueAccountCreateRequest> createRequests = onboardingRequests.stream()
            .map(request -> VenueAccountCreateRequest.newBuilder()
                .setVenueAccountId(ObjectUtils.firstNonNull(request.id(), StringUtils.EMPTY))
                .setVenueAccountName(request.name())
                .setOwnerUsername(clientId)
                .setAccountType(AccountType.WALLET)
                .setWalletType(map(request.walletType()))
                .addAllGrants(mapGrants(request.grants()))
                .build())
            .toList();

        return AccountOnboardingRequest.newBuilder()
            .setMetadata(metadata)
            .addAllAccountOnboardingRequest(createRequests)
            .build();
    }

    public static WalletType map(OnboardingModel.WalletType walletType) {
        if (walletType == null) {
            return WalletType.WALLET_TYPE_UNSPECIFIED;
        }
        return switch (walletType) {
            case VOSTRO -> WalletType.WALLET_VOSTRO;
            case NOSTRO -> WalletType.WALLET_NOSTRO;
        };
    }
}
