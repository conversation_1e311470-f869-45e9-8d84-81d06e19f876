import { describe, expect, it } from 'vitest';
import { sortDescByUpdatedAt } from './gridHelpers';

describe('sortDescByUpdatedAt', () => {
  it('should return 1 if a.updatedAt is greater than b.updatedAt', () => {
    const a = { updatedAt: 2000 };
    const b = { updatedAt: 1000 };
    expect(sortDescByUpdatedAt(a, b)).toBe(1);
  });

  it('should return -1 if a.updatedAt is less than b.updatedAt', () => {
    const a = { updatedAt: 1000 };
    const b = { updatedAt: 2000 };
    expect(sortDescByUpdatedAt(a, b)).toBe(-1);
  });

  it('should return 1 if a.updatedAt is equal to b.updatedAt', () => {
    const a = { updatedAt: 1000 };
    const b = { updatedAt: 1000 };
    expect(sortDescByUpdatedAt(a, b)).toBe(1);
  });
});
