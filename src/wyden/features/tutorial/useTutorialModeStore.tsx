import { Step } from 'intro.js-react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { tradingDashboardSteps } from './tutorialData';

interface TutorialModeStore {
  tutorialSteps: Step[];
  isTutorialModeActive: boolean;
  setTutorialSteps: (newSteps?: Step[], filterSteps?: boolean) => void;
  setIsTutorialModeActive: (newValue?: boolean) => void;
}

const TUTORIAL_MODE_ACTIONS = {
  SET_TUTORIAL_STEPS: 'setTutorialSteps',
  SET_IS_TUTORIAL_MODE_ACTIVE: 'setIsTutorialModeActive',
} as const;

const filterStepsInViewport = (steps: Step[] = []): Step[] =>
  steps.filter((step) => {
    const element = typeof step.element === 'string' && document.querySelector(step.element);
    if (step instanceof HTMLDivElement) {
      return true;
    }
    if (!element) {
      return false;
    }

    const { top, bottom } = element.getBoundingClientRect();
    const isInViewport = top >= 0 && bottom <= window.innerHeight;
    return isInViewport;
  });

export const useTutorialModeStore = create<TutorialModeStore>()(
  devtools(
    (set) => ({
      isTutorialModeActive: false,
      tutorialSteps: tradingDashboardSteps,
      setIsTutorialModeActive: (newValue?: boolean) =>
        set(
          () => ({
            isTutorialModeActive: newValue,
          }),
          false,
          TUTORIAL_MODE_ACTIONS.SET_IS_TUTORIAL_MODE_ACTIVE,
        ),
      setTutorialSteps: (newSteps?: Step[], filterSteps = true) =>
        set(
          () => ({
            tutorialSteps: filterSteps ? filterStepsInViewport(newSteps) : newSteps,
            isTutorialModeActive: true,
          }),
          false,
          TUTORIAL_MODE_ACTIONS.SET_TUTORIAL_STEPS,
        ),
    }),
    { name: 'TutorialModeStore' },
  ),
);
