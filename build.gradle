plugins {
	id 'java'
	alias dependencyCatalog.plugins.sonarqube
}

subprojects {
	apply plugin: 'idea'
	apply plugin: 'jacoco'
	sourceCompatibility = '17'

	ext {
		repository_username = System.env.NEXUS_DEPLOY_USERNAME
		repository_password = System.env.NEXUS_DEPLOY_PASSWORD
		buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
	}

	repositories {
		mavenLocal()
		maven {
			name 'nexus-releases'
			url 'https://repo.wyden.io/nexus/repository/releases/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		maven {
			name 'nexus-main'
			url 'https://repo.wyden.io/nexus/repository/main/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		maven {
			name 'nexus-snapshot'
			url 'https://repo.wyden.io/nexus/repository/snapshots/'
			credentials {
				username repository_username
				password repository_password
			}
		}
		mavenCentral()
		maven { url 'https://repo.spring.io/milestone' }
	}
}

sonarqube {
	properties {
		property "sonar.projectKey", "target-registry"
		property "sonar.projectName", "Target Registry"
	}
}

allprojects {
    ext {
        buildVersion = System.env.BUILD_VERSION ? System.env.BUILD_VERSION : '-SNAPSHOT'
    }
	group = 'io.wyden'
	version = "0.8.0$buildVersion"
}
