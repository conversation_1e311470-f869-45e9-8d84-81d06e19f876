package io.wyden.hedger.service.model;

import io.wyden.hedger.model.RetryStatus;

import java.util.concurrent.atomic.AtomicInteger;

public class AutoHedgerRetryStatus {
    private AtomicInteger retryAttempts = new AtomicInteger(0);
    private boolean underRetry;

    AutoHedgerRetryStatus() {
    }

    AutoHedgerRetryStatus(RetryStatus autoHedgerRetryStatus) {
        retryAttempts.set(autoHedgerRetryStatus.getRetryAttempts());
        underRetry = autoHedgerRetryStatus.getUnderRetry();
    }

    RetryStatus toRetryStatus() {
        return RetryStatus.newBuilder()
            .setRetryAttempts(retryAttempts.get())
            .setUnderRetry(underRetry)
            .build();
    }

    int incrementRetryAttempts() {
        return retryAttempts.incrementAndGet();
    }

    void resetRetryAttempts() {
        retryAttempts.set(0);
    }

    boolean isUnderRetry() {
        return underRetry;
    }

    void setUnderRetry(boolean underRetry) {
        this.underRetry = underRetry;
    }

    @Override
    public String toString() {
        return "RetryStatus{" +
            "retryAttempts=" + retryAttempts +
            ", underRetry=" + underRetry +
            '}';
    }
}
