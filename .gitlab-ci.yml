include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

variables:
  IMAGE_NAME: rest-api
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: rest-api

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification
  stage: version

changes-verify:
  extends: .verify-locked-files
  stage: version

version-check-domain:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - rest-api-domain/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - rest-api-domain/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1

version-check-client:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - rest-api-client/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - rest-api-client/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  stage: test
  interruptible: true
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  script:
    - ./gradlew sonarqube --stacktrace --info
  allow_failure: true
  only:
    - main
  except:
    - schedules

unit-test:
  stage: test
  extends: .unit_test
  variables:
    REPORTS_BASE_DIRECTORY: "rest-api-server/"
  except:
    - tags
    - schedules

integration-test:
  stage: test
  extends: .integration_test
  variables:
    REPORTS_BASE_DIRECTORY: "rest-api-server/"
  except:
    - tags
    - schedules

build:
  stage: build
  interruptible: true
  script:
    - ./gradlew assemble
  artifacts:
    paths:
      - rest-api-domain/build/libs/
      - rest-api-client/build/libs/
      - rest-api-server/build/libs/
    expire_in: 1 days
  except:
    - tags
    - schedules

publish-lib:
  stage: publish
  interruptible: true
  only:
    - main
  script:
    - ./gradlew publish
  dependencies:
    - build
  except:
    - schedules

publish:
  extends:
    - .publish_image
  variables:
    DOCKERFILE_PATH: rest-api-server/Dockerfile
    DOCKER_BUILD_CONTEXT: rest-api-server/.
  dependencies:
    - build
  only:
    - main
  except:
    - schedules

container-scan:
  extends:
    - .docker_scan
  dependencies: []
  artifacts:
    when: always
    paths:
      - ./results-container.html
    expire_in: 1 days
  allow_failure: true
  only:
    - main

retag:
  extends:
    - .retag_image
  stage: publish
  dependencies: []
  only:
    - tags

audit:
  extends:
    - .audit_trails
  stage: publish
  only:
    - tags

deploy-dev:
  extends:
    - .deploy_helm
  only:
    - main
  except:
    - schedules

tag_rc:
  extends: .tag_repository
  stage: tag
  only:
    - main
  except:
    - schedules

tag_rc_scheduled:
  extends: .tag_repository_scheduled
  stage: tag
