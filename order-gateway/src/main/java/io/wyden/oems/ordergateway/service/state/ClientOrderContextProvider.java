package io.wyden.oems.ordergateway.service.state;

import io.vavr.control.Try;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.referencedata.InstrumentsRepository;
import io.wyden.oems.ordergateway.service.referencedata.VenueAccountsRepository;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.oems.OemsResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
public class ClientOrderContextProvider {

    private final ClientOrderCache cache;
    private final InstrumentsRepository instrumentsRepository;
    private final VenueAccountsRepository venueAccountsRepository;

    public ClientOrderContextProvider(ClientOrderCache cache, InstrumentsRepository instrumentsRepository, VenueAccountsRepository venueAccountsRepository) {
        this.cache = cache;
        this.instrumentsRepository = instrumentsRepository;
        this.venueAccountsRepository = venueAccountsRepository;
    }

    public Try<ClientOrderContext> initialize(ClientRequest clientNewOrderRequest) {
        return Try.of(() -> ClientOrderContext.initialize(clientNewOrderRequest, cache, instrumentsRepository, venueAccountsRepository));
    }

    public Try<ClientOrderContext> initialize(ClientRequest clientNewOrderRequest, ClientOrderContext previousInChain) {
        return Try.of(() -> ClientOrderContext.initialize(clientNewOrderRequest, cache, previousInChain, instrumentsRepository, venueAccountsRepository));
    }

    public Try<ClientOrderContext> retrieveByOemsOrder(OemsResponse oemsResponse) {
        return cache.find(oemsResponse.getOrderId())
            .map(ps -> Try.of(() -> fromPersistentState(ps, cache, instrumentsRepository)))
            .orElse(Try.failure(new IllegalArgumentException("Order not found")));
    }

    public Try<ClientOrderContext> retrieveContext(ClientRequest clientRequest) {
        return findOrderState(clientRequest)
            .map(ps -> Try.of(() -> fromPersistentState(ps, cache, instrumentsRepository)))
            .orElse(Try.failure(new IllegalArgumentException("Order not found")));
    }

    private Optional<ClientOrderState> findOrderState(ClientRequest clientRequest) {
        String orderId = switch (clientRequest.getRequestType()) {
            case ORDER_SINGLE, ORDER_STATUS_REQUEST -> clientRequest.getOrderId();
            case CANCEL, CANCEL_REPLACE -> clientRequest.getOrigOrderId();
            default -> throw new IllegalArgumentException("Request type not supported for Order state retrieval: " + clientRequest);
        };

        if (!orderId.isBlank()) {
            return cache.find(orderId);
        }
        return Optional.empty();
    }

    public Try<ClientOrderContext> retrieveByOrderId(String orderId) {
        return cache.find(orderId)
            .map(os -> Try.of(() -> fromPersistentState(os, cache, instrumentsRepository)))
            .orElse(Try.failure(new IllegalStateException("not found")));
    }

    public static ClientOrderContext fromPersistentState(ClientOrderState orderState, ClientOrderCache clientOrderCache, InstrumentsRepository instrumentsRepository) {
        ClientOrderStatus currentStatus = orderState.getCurrentStatus();

        State state = switch (currentStatus) {
            case PENDING_NEW -> fromPendingNew(orderState);
            case NEW, PARTIALLY_FILLED -> new New();
            case PENDING_CANCEL -> fromPendingCancel(orderState);
            case PENDING_REPLACE -> fromPendingReplace(orderState);
            case FILLED, REPLACED -> new Filled();
            case CANCELED -> new Cancelled();
            case REJECTED -> new Rejected();
            default -> throw new IllegalStateException("Order status unknown to state machine: " + currentStatus);
        };

        return ClientOrderContext.rebuild(state, clientOrderCache, orderState, instrumentsRepository);
    }

    @NotNull
    private static State fromPendingReplace(ClientOrderState orderState) {
        boolean oemsCancelRequested = orderState.getOemsCancelRequested();
        return oemsCancelRequested ?
            new PendingCancelReplaceAndPendingOemsCancel() : new PendingCancelReplace();
    }

    @NotNull
    private static State fromPendingCancel(ClientOrderState orderState) {
        boolean oemsCancelRequested = orderState.getOemsCancelRequested();
        return oemsCancelRequested ?
            new PendingCancelAndPendingOemsCancel() :
            new PendingCancel();
    }

    @NotNull
    private static State fromPendingNew(ClientOrderState orderState) {
        String oemsOrderId = orderState.getOemsOrderId();
        return isNotBlank(oemsOrderId) ?
            new PendingNewAndPendingOemsNew() : new PendingNew();
    }
}
