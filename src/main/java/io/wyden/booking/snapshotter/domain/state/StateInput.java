package io.wyden.booking.snapshotter.domain.state;

/**
 * Current booking state view required to perform a Command processing.
 * Will contain only a small subset of required application state (e.g. Positions for Portfolios/Accounts taking part in a Transaction)
 * Different CommandProcessors may require different StateInput content (see concrete implementations of StateInput interface)
 */
public interface StateInput {
}
