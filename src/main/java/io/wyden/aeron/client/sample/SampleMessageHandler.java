package io.wyden.aeron.client.sample;

import io.wyden.aeron.client.MessageHandler;
import io.wyden.aeron.client.pojo.Order;
import io.wyden.aeron.client.pojo.SymbolSpecification;
import io.wyden.aeron.client.sample.SampleStateTracker.State;

import java.util.List;

public class SampleMessageHandler extends MessageHandler {

    private final SampleStateTracker stateTracker;

    public SampleMessageHandler(SampleStateTracker stateTracker) {
        this.stateTracker = stateTracker;
    }

    @Override
    public void symbolSpecification(long correlation, List<SymbolSpecification> symbols) {
        if (!symbols.isEmpty()) {
            stateTracker.setState(State.READY);
        } else {
            stateTracker.setState(State.SYMBOLS_EMPTY);
        }
    }

    @Override
    public void allOrdersReport(long correlation, List<Order> orders) {
        stateTracker.setState(State.ORDERS_INITIALIZED);
    }

}
