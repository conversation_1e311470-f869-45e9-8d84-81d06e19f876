package io.wyden.booking.snapshotter.domain.ledgerentry;

import java.util.Arrays;
import java.util.Collection;

public enum LedgerEntryType {
    RESERVATION,
    WITHDRAWAL_RESERVATION,
    ASSET_TRADE_BUY,
    ASSET_TRADE_SELL,
    CASH_TRADE_CREDIT,
    CASH_TRADE_DEBIT,
    ASSET_TRADE_PROCEEDS,
    DEPOSIT,
    WITHD<PERSON>WAL,
    TRANSFER,
    FEE,
    TRADING_FEE,
    DEPOSIT_FEE,
    WITHDRAWAL_FEE,
    TRANSFER_FEE,
    RESERVATION_RELEASE,
    RESERVATION_RELEASE_REMAINING;

    public boolean isNonReservationOrRelease() {
        return !isReservationOrRelease();
    }

    public boolean isReservationOrRelease() {
        return isReservation() || isReservationRelease();
    }

    public boolean isReservation() {
        return RESERVATION == this
            || WITHDRAWAL_RESERVATION == this;
    }

    public boolean isReservationRelease() {
        return RESERVATION_RELEASE == this
            || RESERVATION_RELEASE_REMAINING == this;
    }

    public static Collection<LedgerEntryType> getReservationOrReleaseTypes() {
        return Arrays.stream(values())
            .filter(LedgerEntryType::isReservationOrRelease)
            .toList();
    }
}
