package io.wyden.test.scenariorunner.model.booking.trade;

import java.util.Objects;

public class ClientCashTrade extends CashTrade {

    private String portfolioId;
    private String counterPortfolioId;

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getCounterPortfolioId() {
        return counterPortfolioId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClientCashTrade that = (ClientCashTrade) o;
        return super.equals(that) && Objects.equals(portfolioId, that.portfolioId) && Objects.equals(counterPortfolioId, that.counterPortfolioId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), portfolioId, counterPortfolioId);
    }

    @Override
    public String toString() {
        return "ClientCashTrade{" +
            "portfolioId='" + portfolioId + '\'' +
            ", counterPortfolioId='" + counterPortfolioId + '\'' +
            ", baseCurrency='" + baseCurrency + '\'' +
            ", underlyingExecutionId='" + underlyingExecutionId + '\'' +
            ", quantity=" + quantity +
            ", price=" + price +
            ", currency='" + currency + '\'' +
            ", fee=" + fee +
            ", feeCurrency='" + feeCurrency + '\'' +
            ", intOrderId='" + intOrderId + '\'' +
            ", extOrderId='" + extOrderId + '\'' +
            ", orderId='" + orderId + '\'' +
            ", uuid='" + uuid + '\'' +
            ", updatedAt=" + updatedAt +
            ", executionId='" + executionId + '\'' +
            ", venueExecutionId='" + venueExecutionId + '\'' +
            ", description='" + description + '\'' +
            ", dateTime=" + dateTime +
            ", settled='" + settled + '\'' +
            ", settledDateTime=" + settledDateTime +
            ", rootExecution=" + rootExecution +
            '}';
    }

}
