include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: booking-snapshotter
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: booking-snapshotter

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification
  stage: version

changes-verify:
  extends: .verify-locked-files
  stage: version

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  stage: test
  interruptible: true
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  script:
    - ./gradlew sonarqube --stacktrace
  allow_failure: true
  only:
    - main
  except:
    - schedules
  tags:
    - kubernetes-aws

unit-test:
  stage: test
  extends: .unit_test
  except:
    - tags
    - schedules

integration-test:
  stage: test
  extends: .integration_test
  except:
    - tags
    - schedules

build:
  stage: build
  interruptible: true
  script:
    - ./gradlew assemble
  artifacts:
    paths:
      - build/libs/
    expire_in: 1 days
  except:
    - tags
    - schedules
  tags:
    - kubernetes-aws

publish:
  extends:
    - .publish_image
  stage: publish
  dependencies:
    - build
  only:
    - main
  except:
    - schedules

container-scan:
  extends:
    - .docker_scan
  dependencies: []
  artifacts:
    when: always
    paths:
      - ./results-container.html
    expire_in: 1 days
  allow_failure: true
  tags:
    - kubernetes-aws
  only:
    - main

retag:
  extends:
    - .retag_image
  stage: publish
  dependencies: []
  only:
    - tags

deploy-dev:
  extends:
    - .deploy_helm
  only:
    - main
  except:
    - schedules

tag_rc:
  extends: .tag_repository
  stage: tag
  only:
    - main
  except:
    - schedules

tag_rc_scheduled:
  extends: .tag_repository_scheduled
  stage: tag
