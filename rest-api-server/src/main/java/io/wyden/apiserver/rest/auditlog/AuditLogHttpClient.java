package io.wyden.apiserver.rest.auditlog;

import io.wyden.apiserver.rest.audit.model.AuditEventDto;
import io.wyden.apiserver.rest.audit.model.HedgeSearchInput;
import io.wyden.apiserver.rest.audit.model.MatchesSearchInput;
import io.wyden.apiserver.rest.audit.model.PayloadType;
import io.wyden.apiserver.rest.audit.model.PreTradeCheckAuditLogSearch;
import io.wyden.apiserver.rest.infrastructure.webclient.WebClientInstance;
import io.wyden.published.audit.AuditEventPayload;
import io.wyden.published.audit.MatchPayload;
import io.wyden.published.audit.MatchPayloadList;
import io.wyden.published.common.CursorConnection;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.ResolvableType;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AuditLogHttpClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuditLogHttpClient.class);
    private static final String MATCH = "match";
    private static final String HEDGE = "hedge";

    private final WebClient webClient;
    private final String auditServerHost;

    public AuditLogHttpClient(final WebClientInstance webClientInstance,
                              final @Value("${audit.server.host}") String auditServerHost) {
        this.webClient = webClientInstance.getWebClient();
        this.auditServerHost = auditServerHost;
    }

    public Mono<CursorConnection> getPreTradeCheckAuditLogs(PreTradeCheckAuditLogSearch search) {
        LOGGER.trace("Calling audit server to get PreTradeCheckAuditLogs");
        return webClient
            .post()
            .uri(auditServerHost + "/audit-event/ptc")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .bodyValue(search)
            .retrieve()
            .bodyToMono(CursorConnection.class)
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())))
            .onErrorResume(throwable -> {throw new RuntimeException("Exception: " + throwable);});
    }

    public Mono<List<AuditEventDto>> getAuditEventByType(PayloadType type) {
        LOGGER.trace("Calling audit server to get AuditEventLogs of type: {}", type);
        ParameterizedTypeReference<List<AuditEventDto>> typeReference = ParameterizedTypeReference.forType(ResolvableType.forClassWithGenerics(List.class, AuditEventDto.class).getType());
        return fetchAuditEventOfGivenType(auditServerHost + "/audit-event", toQueryParamsMap(Map.of("type", type.name())), MediaType.APPLICATION_JSON, typeReference);
    }

    public Mono<MatchPayloadList> getMatchesBySearchParams(String orderId, String rootOrderId, String matchId) {
        Map<String, String> params = new HashMap<>();

        addParamIfNotEmpty(params, "orderId", orderId);
        addParamIfNotEmpty(params, "rootOrderId", rootOrderId);
        addParamIfNotEmpty(params, "matchId", matchId);

        if (params.isEmpty()) {
            return Mono.empty();
        }

        LOGGER.debug("Calling audit server to get Matches for params: {}", params);
        return fetchMatches(params);
    }

    public Mono<MatchPayload> getMatchByOrderIdAndMatchId(String orderId, String matchId) {
        LOGGER.debug("Calling audit server to get Matches for orderId: {} and matchId: {}", orderId, matchId);
        return fetchMatches(Map.of("orderId", orderId, "matchId", matchId))
            .map(MatchPayloadList::getItemsList)
            .map(matchPayloads -> matchPayloads.stream().findFirst().orElseThrow());
    }

    public Mono<CursorConnection> getMatchesPaged(MatchesSearchInput matchesSearchInput) {
        LOGGER.debug("Calling audit server to get paged Matches with matchesSearchInput: {}", matchesSearchInput);
        return fetchMatchesPaged(matchesSearchInput);
    }

    public Mono<CursorConnection> getHedgesPaged(HedgeSearchInput hedgeSearchInput) {
        LOGGER.debug("Calling audit server to get Hedge with hedgeSearchInput: {}", hedgeSearchInput);
        return fetchHedgesPaged(hedgeSearchInput);
    }

    private Mono<MatchPayloadList> fetchMatches(Map<String, String> params) {
        return fetchAuditEventOfGivenType(auditServerHost + "/audit-event/" + AuditLogHttpClient.MATCH, toQueryParamsMap(params), MatchPayloadList.class)
            .doOnNext(matchPayloadList -> LOGGER.debug("Received {} matches for {}", matchPayloadList.getItemsList().size(), params));
    }

    private Mono<CursorConnection> fetchMatchesPaged(MatchesSearchInput matchesSearchInput) {
        return fetchPagedAuditEventOfGivenType(auditServerHost + "/audit-event/" + AuditLogHttpClient.MATCH, matchesSearchInput);
    }

    private Mono<AuditEventPayload> fetchHedges(Map<String, String> params) {
        return fetchAuditEventOfGivenType(auditServerHost + "/audit-event/" + AuditLogHttpClient.HEDGE, toQueryParamsMap(params), AuditEventPayload.class)
            .doOnNext(payload -> LOGGER.debug("Received hedge for {}", params));
    }

    private Mono<CursorConnection> fetchHedgesPaged(HedgeSearchInput hedgeSearchInput) {
        return fetchPagedAuditEventOfGivenType(auditServerHost + "/audit-event/" + AuditLogHttpClient.HEDGE, hedgeSearchInput);
    }

    private <T> Mono<T> fetchAuditEventOfGivenType(String url, LinkedMultiValueMap<String, String> queryParamsMap, Class<T> clazz) {
        ParameterizedTypeReference<T> parameterizedTypeReference = ParameterizedTypeReference.forType(ResolvableType.forClass(clazz).getType());
        return fetchAuditEventOfGivenType(url, queryParamsMap, MediaType.APPLICATION_PROTOBUF, parameterizedTypeReference);
    }

    private <T> Mono<T> fetchAuditEventOfGivenType(String auditServerHost, LinkedMultiValueMap<String, String> queryParamsMap, MediaType mediaType, ParameterizedTypeReference<T> parameterizedTypeReference) {
        return webClient
            .get()
            .uri(auditServerHost, uriBuilder -> uriBuilder.queryParams(queryParamsMap).build())
            .accept(mediaType)
            .retrieve()
            .bodyToMono(parameterizedTypeReference)
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())))
            .onErrorResume(throwable -> {throw new RuntimeException("Exception: " + throwable);});
    }

    private Mono<CursorConnection> fetchPagedAuditEventOfGivenType(String auditServerHost, Object searchInput) {
        return webClient
            .post()
            .uri(auditServerHost)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_PROTOBUF)
            .bodyValue(searchInput)
            .retrieve()
            .bodyToMono(CursorConnection.class)
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())))
            .onErrorResume(throwable -> {throw new RuntimeException("Exception: " + throwable);});
    }

    private LinkedMultiValueMap<String, String> toQueryParamsMap(Map<String, String> params) {
        LinkedMultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        params.forEach((key, val) -> {
            if (StringUtils.isNotEmpty(val))
                multiValueMap.add(key, val);
        });
        return multiValueMap;
    }

    private void addParamIfNotEmpty(Map<String, String> params, String paramName, String paramValue) {
        if (StringUtils.isNotEmpty(paramValue)) {
            params.put(paramName, paramValue);
        }
    }
}
