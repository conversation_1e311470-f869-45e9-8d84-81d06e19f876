{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "baseUrl": "./", "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false, "paths": {"@wyden/*": ["src/wyden/*"], "@ui/*": ["src/ui/*"], "@nest-react/*": ["src/nest-react/*"]}}, "include": ["src", "main.d.ts", "vite-env.d.ts", "./vite-env.d.ts", "mocks", "./setupTest.ts"], "exclude": ["src/**/*.cy.tsx"], "references": [{"path": "./tsconfig.node.json"}]}