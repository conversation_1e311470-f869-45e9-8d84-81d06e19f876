package io.wyden.agencytrading.infrastructure.rabbit;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.agencytrading.service.fsm.OrderService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.OemsTarget;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTargetType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import javax.annotation.Nullable;

import static io.wyden.agencytrading.service.oems.outbound.OemsResponseFactory.ORDER_GATEWAY_TARGET;
import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.failureNeedsRequeue;

@Component
public class TradingMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradingMessageConsumer.class);
    private static final String BROKERDESK_SOURCE_TYPE = Metadata.ServiceType.BROKER_DESK.name();
    private static final String AGENCY = OemsTarget.AGENCY.getTarget();

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations rabbitDestinations;
    private final OrderService orderService;
    private final Tracing otlTracing;
    private final String queueName;

    private RabbitQueue<Message> queue;

    TradingMessageConsumer(RabbitIntegrator rabbitIntegrator,
                           RabbitDestinations rabbitDestinations,
                           OrderService orderService,
                           Tracing otlTracing,
                           @Value("${rabbitmq.trading-agency-trading-service-queue}") String queueName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitDestinations = rabbitDestinations;
        this.orderService = orderService;
        this.otlTracing = otlTracing;
        this.queueName = queueName;

        declareQueue();
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("request.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(message, properties);
            }
        }
    }

    private ConsumptionResult consumeInner(@Nullable Message data, AMQP.BasicProperties properties) {
        LOGGER.debug("Received new Trading message. Properties: {}", properties);
        Object source = properties.getHeaders().get(OemsHeader.SOURCE.getHeaderName());
        Object sourceType = properties.getHeaders().get(OemsHeader.SOURCE_TYPE.getHeaderName());
        try {
            if (data == null) {
                LOGGER.error("Message parsing failed");
                Span.current().setStatus(StatusCode.ERROR, "Message parsing failed");
                return ConsumptionResult.failureNonRecoverable();
            } else if (data instanceof OemsRequest oemsRequest) {
                if (Objects.equals(sourceType, BROKERDESK_SOURCE_TYPE) && Objects.equals(source, AGENCY)) {
                    orderService.onOrderNotDelivered(oemsRequest);
                } else {
                    orderService.onOemsRequest(oemsRequest);
                }
                return ConsumptionResult.consumed();
            } else if (data instanceof OemsResponse oemsResponse) {
                switch (oemsResponse.getResponseType()) {
                    case EXECUTION_REPORT -> orderService.onExecutionReport(oemsResponse);
                    case CANCEL_REJECT -> orderService.onCancelReject(oemsResponse);
                    default -> {
                        LOGGER.error("Unknown OemsResponse type, requeuing");
                        Span.current().setStatus(StatusCode.ERROR, "Unknown OemsResponse type, requeuing");
                        return failureNeedsRequeue();
                    }
                }
                return ConsumptionResult.consumed();
            } else {
                LOGGER.error("Message type not supported: {}", data.getClass().getSimpleName());
                Span.current().setStatus(StatusCode.ERROR, "Message type not supported");
                return ConsumptionResult.failureNonRecoverable();
            }
        } catch (Exception ex) {
            LOGGER.error("Failed to process message, dropping", ex);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(ex);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private void declareQueue() {
        queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setSingleActiveConsumer(true)
            .declare();

        queue.attachConsumer(TradingMessageParser.parser(), this);

        bindQueueToAgencyRequests(rabbitDestinations.getTradingIngressExchange(), OemsRequest.OemsPTCStatus.APPROVED);
        bindQueueToAgencyRequests(rabbitDestinations.getTradingIngressExchange(), OemsRequest.OemsPTCStatus.NOT_REQUIRED);
        bindQueueToAgencyChildResponses(rabbitDestinations.getTradingUnroutedExchange());
        bindQueueToDeadletteredAgencyRequests(rabbitDestinations.getTradingDLX());
    }

    private <T extends Message> void bindQueueToAgencyRequests(RabbitExchange<Message> exchange, OemsRequest.OemsPTCStatus ptcStatus) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.SOURCE.getHeaderName(), ORDER_GATEWAY_TARGET,
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.BROKER_DESK.name(),
            OemsHeader.TARGET.getHeaderName(), AGENCY,
            OemsHeader.PTC.getHeaderName(), ptcStatus.name()
        );
        bindQueue(exchange, headers);
    }

    private void bindQueueToAgencyChildResponses(RabbitExchange<Message> exchange) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.BROKER_DESK.name(),
            OemsHeader.TARGET.getHeaderName(), AGENCY
        );
        bindQueue(exchange, headers);
    }

    private void bindQueueToDeadletteredAgencyRequests(RabbitExchange<Message> tradingDLX) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), OemsTargetType.BROKER_DESK.name(),
            OemsHeader.SOURCE.getHeaderName(), AGENCY
        );
        bindQueue(tradingDLX, headers);
    }

    private void bindQueue(RabbitExchange<Message> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }
}
