package io.wyden.executionengine.service.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

import static java.util.Objects.requireNonNull;

public record ExposureTarget(BigDecimal target, BigDecimal lowThreshold, BigDecimal highThreshold) {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExposureTarget.class);

    public ExposureTarget {
        requireNonNull(target, "target cannot be null");
        requireNonNull(lowThreshold, "lowThreshold cannot be null");
        requireNonNull(highThreshold, "highThreshold cannot be null");

        if (lowThreshold.compareTo(target) > 0) {
            throw new IllegalArgumentException(
                "low threshold cannot be greater than target, but got: lowThreshold=%s , target=%s"
                    .formatted(lowThreshold, target));
        }

        if (highThreshold.compareTo(target) < 0) {
            throw new IllegalArgumentException(
                "high threshold cannot be smaller than target, but got: highThreshold=%s , target=%s"
                    .formatted(highThreshold, target));
        }
    }

    /**
     *
     * @param currentExposure given how much we currently hold (quantity)
     * @param buyClientDemand how much clients want to buy from us
     * @param sellClientDemand how much client want to sell us
     * @return limited amount to sell to clients
     */
    public BigDecimal findMaxPossibleToSell(BigDecimal currentExposure, BigDecimal buyClientDemand, BigDecimal sellClientDemand) {
        BigDecimal minExposure = lowThreshold.subtract(sellClientDemand);
        BigDecimal availableToBuyFromBank = BigDecimal.ZERO.max(currentExposure.subtract(minExposure));
        BigDecimal maxPossibleToSellToClients = buyClientDemand.min(availableToBuyFromBank);

        LOGGER.debug("Max possible to sell: {}. Details: exposure: {}, pending buys: {}, pending sells: {}, threshold: min:{}  max:{}",
                maxPossibleToSellToClients, currentExposure, buyClientDemand, sellClientDemand, lowThreshold, highThreshold);

        return maxPossibleToSellToClients;
    }

    /**
     *
     * @param currentExposure given how much we currently hold (quantity)
     * @param buyClientDemand how much clients want to buy from us
     * @param sellClientDemand how much client want to sell us
     * @return limited amount to buy from clients
     */
    public BigDecimal findMaxPossibleToBuy(BigDecimal currentExposure, BigDecimal buyClientDemand, BigDecimal sellClientDemand) {
        BigDecimal maxExposure = highThreshold.add(buyClientDemand);
        BigDecimal availableToSellToBank = BigDecimal.ZERO.max(maxExposure.subtract(currentExposure));
        BigDecimal maxPossibleToBuyFromClients = availableToSellToBank.min(sellClientDemand);

        LOGGER.debug("Max possible to buy from clients: {}. Details: exposure: {}, pending buys: {}, pending sells: {}, threshold: min:{}  max:{}",
                maxPossibleToBuyFromClients, currentExposure, buyClientDemand, sellClientDemand, lowThreshold, highThreshold);

        return maxPossibleToBuyFromClients;
    }
}
