package io.wyden.audit.infrastructure.health;

import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.health.indicators.RabbitHealthCheckConfiguration;
import org.springframework.boot.actuate.autoconfigure.health.ConditionalOnEnabledHealthIndicator;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HealthIndicators {

    @Bean
    @ConditionalOnEnabledHealthIndicator("rabbit")
    AbstractHealthIndicator rabbit(RabbitIntegrator rabbitIntegrator) {
        return new RabbitHealthCheckConfiguration(rabbitIntegrator);
    }

}
