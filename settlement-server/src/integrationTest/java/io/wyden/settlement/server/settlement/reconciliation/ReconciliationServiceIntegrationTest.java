package io.wyden.settlement.server.settlement.reconciliation;

import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.TransactionType;
import io.wyden.published.common.Metadata;
import io.wyden.settlement.server.settlement.TestContainersIntegrationBase;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberEntity;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionFeeRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class ReconciliationServiceIntegrationTest extends TestContainersIntegrationBase {

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private BookingSequenceNumberRepository bookingSequenceNumberRepository;

    @Autowired
    private TransactionFeeRepository transactionFeeRepository;

    @Mock
    private BookingTransactionClient bookingTransactionClient;

    @Mock
    private RestTemplate restTemplate;

    private ReconciliationService reconciliationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        reconciliationService = new ReconciliationService(
            transactionRepository,
            bookingSequenceNumberRepository,
            transactionFeeRepository,
            bookingTransactionClient
        );
    }

    @Test
    void shouldPullMissingTransactionSuccessfully() {
        // Given
        UUID transactionUuid = UUID.randomUUID();
        Long sequenceNumber = 12345L;
        
        // Mock full transaction snapshot
        TransactionSnapshot transactionSnapshot = createMockTransactionSnapshot(transactionUuid);

        // Mock TransactionWithSnapshot
        BookingTransactionClient.TransactionWithSnapshot transactionWithSnapshot =
            new BookingTransactionClient.TransactionWithSnapshot(sequenceNumber, transactionSnapshot);

        // Setup mocks
        when(bookingTransactionClient.getTransactionsWithSnapshotsBySequenceNumbers(any()))
            .thenReturn(List.of(transactionWithSnapshot));

        // When
        int result = reconciliationService.pullMissingTransactionsInBatches(List.of(sequenceNumber));

        // Then
        assertEquals(1, result);
        
        // Verify transaction was saved
        List<TransactionEntity> transactions = transactionRepository.findAll();
        assertEquals(1, transactions.size());
        assertEquals(transactionUuid, transactions.get(0).transactionUuid());
        
        // Verify sequence number was saved
        List<BookingSequenceNumberEntity> sequenceNumbers =
            bookingSequenceNumberRepository.findBySequenceNumberRange(sequenceNumber, sequenceNumber);
        assertEquals(1, sequenceNumbers.size());
        assertEquals(sequenceNumber, sequenceNumbers.get(0).sequenceNumber());
    }

    private TransactionSnapshot createMockTransactionSnapshot(UUID transactionUuid) {
        Fee fee = Fee.newBuilder()
            .setAmount("10.00")
            .setCurrency("USD")
            .setFeeType(FeeType.EXCHANGE_FEE)
            .build();

        Metadata metadata = Metadata.newBuilder()
            .setCreatedAt("2023-01-01T10:00:00Z")
            .setUpdatedAt("2023-01-01T10:00:00Z")
            .setResponseId(UUID.randomUUID().toString())
            .build();

        StreetCashTradeSnapshot streetCashTrade = StreetCashTradeSnapshot.newBuilder()
            .setMetadata(metadata)
            .setUuid(transactionUuid.toString())
            .setReservationRef("reservation-123")
            .setDateTime("2023-01-01T10:00:00Z")
            .setExecutionId(UUID.randomUUID().toString())
            .setVenueExecutionId(UUID.randomUUID().toString())
            .setDescription("Test transaction")
            .setQuantity("100.00")
            .setLeavesQuantity("0.00")
            .setPrice("50.00")
            .setCurrency("USD")
            .setIntOrderId(UUID.randomUUID().toString())
            .setExtOrderId(UUID.randomUUID().toString())
            .setOrderId(UUID.randomUUID().toString())
            .setBaseCurrency("USD")
            .setPortfolio(UUID.randomUUID().toString())
            .setVenueAccount("test-venue-account")
            .setParentOrderId(UUID.randomUUID().toString())
            .setRootOrderId(UUID.randomUUID().toString())
            .setRootExecutionId(UUID.randomUUID().toString())
            .addTransactionFee(fee)
            .build();

        return TransactionSnapshot.newBuilder()
            .setStreetCashTrade(streetCashTrade)
            .build();
    }

    @Test
    void shouldHandleEmptyMissingSequenceNumbers() {
        // When
        int result = reconciliationService.pullMissingTransactionsInBatches(List.of());

        // Then
        assertEquals(0, result);
    }

    @Test
    void shouldCreateSequenceNumberEntityCorrectly() {
        // Given
        TransactionEntity transaction = createTestTransaction();
        long transactionId = transactionRepository.save(transaction);
        Long sequenceNumber = 98765L;

        // When
        BookingSequenceNumberEntity sequenceNumberEntity = BookingSequenceNumberEntity.builder()
            .transactionId(transactionId)
            .sequenceNumber(sequenceNumber)
            .createdAt(Timestamp.from(Instant.now()))
            .updatedAt(Timestamp.from(Instant.now()))
            .build();

        bookingSequenceNumberRepository.save(sequenceNumberEntity);

        // Then
        List<BookingSequenceNumberEntity> sequenceNumbers =
            bookingSequenceNumberRepository.findBySequenceNumberRange(sequenceNumber, sequenceNumber);
        assertEquals(1, sequenceNumbers.size());
        assertEquals(transactionId, sequenceNumbers.get(0).transactionId());
        assertEquals(sequenceNumber, sequenceNumbers.get(0).sequenceNumber());
    }

    private TransactionEntity createTestTransaction() {
        return TransactionEntity.builder()
            .orderId(UUID.randomUUID())
            .transactionType(TransactionType.TRANSACTION_TYPE_STREET_CASH_TRADE)
            .portfolioId(UUID.randomUUID())
            .transactionUuid(UUID.randomUUID())
            .createdAt(Timestamp.from(Instant.now()))
            .updatedAt(Timestamp.from(Instant.now()))
            .transactionDatetime(Timestamp.from(Instant.now()))
            .venueExecutionId(UUID.randomUUID())
            .description("Test transaction")
            .quantity(new BigDecimal("100.00"))
            .leavesQuantity(new BigDecimal("0.00"))
            .price(new BigDecimal("50.00"))
            .currency("USD")
            .intOrderId(UUID.randomUUID())
            .extOrderId(UUID.randomUUID())
            .baseCurrency("USD")
            .venueAccount("test-venue-account")
            .parentOrderId(UUID.randomUUID())
            .rootOrderId(UUID.randomUUID())
            .rootExecutionId(UUID.randomUUID())
            .executionId(UUID.randomUUID())
            .build();
    }
}
