package io.wyden.referencedata.configuration;

import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccountCreateRequest;
import io.wyden.published.referencedata.VenueType;
import io.wyden.referencedata.service.venue.VenueService;
import io.wyden.referencedata.service.venueaccount.VenueAccountService;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;

import java.util.EnumSet;
import java.util.List;
import java.util.stream.Stream;

import static io.wyden.referencedata.configuration.InitialVenueAccountsConfig.VenueAccountConfig;
import static io.wyden.referencedata.configuration.InitialVenueAccountsConfig.account;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

class InitialVenueAccountsConfigTest {

    @ParameterizedTest
    @MethodSource
    void shouldCreateInitialVenueAccounts(VenueAccountConfig.VenueType venueType, AccountType accountType) throws Exception {
        //given
        String vaId = "testName";
        String venueName = "testVenueName";
        String owner = "trader";
        VenueAccountCreateRequest va = VenueAccountCreateRequest.newBuilder()
            .setVenueAccountId(vaId)
            .setVenueAccountName(vaId)
            .setVenueName(venueName)
            .setOwnerUsername(owner)
            .setAccountType(accountType)
            .build();

        List<VenueAccountConfig> config = List.of(account(vaId, venueName, owner, venueType));
        VenueService venueService = mock(VenueService.class);
        VenueAccountService venueAccountService = mock(VenueAccountService.class);

        InitialVenueAccountsConfig initialVenueAccountsConfig = new InitialVenueAccountsConfig(venueAccountService, venueService);
        //when
        initialVenueAccountsConfig.initializeVenueAccounts(config).run();
        //then
        ArgumentCaptor<VenueAccountCreateRequest> vaCaptor = ArgumentCaptor.forClass(VenueAccountCreateRequest.class);
        verify(venueService).createOrUpdate(eq(venueName), eq(VenueType.valueOf(venueType.name())));
        verify(venueAccountService).createFromRequest(vaCaptor.capture());
        assertThat(vaCaptor.getValue()).isEqualTo(va);
    }

    static Stream<Arguments> shouldCreateInitialVenueAccounts() {
        return EnumSet.allOf(VenueAccountConfig.VenueType.class).stream()
            .map(configuredVenueType -> {
                AccountType expectedAccountType = switch (configuredVenueType) {
                    case STREET -> AccountType.EXCHANGE;
                    case CLOB -> AccountType.ACCOUNT_TYPE_CLOB;
                };
                return Arguments.of(configuredVenueType, expectedAccountType);
            });
    }
}
