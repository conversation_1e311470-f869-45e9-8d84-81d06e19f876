package io.wyden.brokerconfig.interfaces.rabbit;

import com.google.protobuf.Message;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.InfrastructureException;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsTargetType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class OemsRequestEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OemsRequestEmitter.class);

    private final RabbitExchange<Message> tradingIngressExchange;
    private final Tracing otlTracing;

    public OemsRequestEmitter(RabbitExchange<Message> tradingIngressExchange,
                              Tracing otlTracing) {
        this.tradingIngressExchange = tradingIngressExchange;
        this.otlTracing = otlTracing;
    }

    public OemsRequest emit(OemsRequest request) throws InfrastructureException {
        try (var ignored = otlTracing.createSpan("oemsrequest.emit", SpanKind.PRODUCER)) {
            return emitInner(request);
        }
    }

    private OemsRequest emitInner(OemsRequest request) throws InfrastructureException {
        Map<String, String> routingHeaders = Map.of(
                OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
                OemsHeader.SOURCE.getHeaderName(), request.getMetadata().getSource(),
                OemsHeader.SOURCE_TYPE.getHeaderName(), request.getMetadata().getSourceType().name(),
                OemsHeader.INSTRUMENT_ID.getHeaderName(), request.getInstrumentId(),
                OemsHeader.PTC.getHeaderName(), request.getPtc().name(),
                OemsHeader.TARGET.getHeaderName(), request.getMetadata().getTarget(),
                OemsHeader.TARGET_TYPE.getHeaderName(), request.getMetadata().getTargetType().name()
            );

        Map<String, String> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(routingHeaders), RabbitHeadersPropagator.setter())
            .getHeaders();

        LOGGER.info("Emitting {} to {}, headers: {}\n{}", request.getClass().getSimpleName(), tradingIngressExchange.getName(), headers, request);
        tradingIngressExchange.publishWithHeaders(request, headers);

        return request;
    }
}
