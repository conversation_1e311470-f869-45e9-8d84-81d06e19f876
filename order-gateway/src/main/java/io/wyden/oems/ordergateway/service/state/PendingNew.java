package io.wyden.oems.ordergateway.service.state;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Instrument;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import javax.annotation.Nullable;

import static org.apache.commons.lang3.Validate.isTrue;

/**
 * Initial state. Order was requested by Client and registered in the system.
 * Now system should either reject it early or delegate execution to OEMS by placing OemsRequest.
 */
class PendingNew extends AbstractState {

    @Override
    public Rejected onOemsRequestSendingFailure(ClientOrderContext ctx) {
        ClientOrderState.Builder updated = ctx.getPersistentState()
            .toBuilder()
            .setDescription("Connectivity issue")
            .setClosed(true)
            .setCurrentStatus(ClientOrderStatus.REJECTED)
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
        updateMessages(updated);
        ctx.persistState(updated.build());

        return new Rejected();
    }

    @Override
    public PendingNewAndPendingOemsNew onOemsRequestSent(OemsRequest oemsRequest, ClientOrderContext ctx) {
        ClientOrderState.Builder updated = ctx.getPersistentState()
            .toBuilder()
            .setCurrentStatus(ClientOrderStatus.PENDING_NEW)
            .setOemsOrderId(oemsRequest.getOrderId())
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
        updateMessages(updated);
        ctx.persistState(updated.build());

        return new PendingNewAndPendingOemsNew();
    }

    @Override
    public State onOemsResponse(OemsResponse oemsResponse, ClientOrderContext ctx) {
        throw new IllegalStateException("OEMS Order not sent yet. OemsResponse not compliant.");
    }

    @Override
    public boolean hasToBeBackedByNewOemsOrder(ClientOrderContext ctx) {
        ClientOrderState persistentState = ctx.getPersistentState();
        ClientRequestType triggeredBy = persistentState.getOrder().getRequestType();
        return triggeredBy == ClientRequestType.ORDER_SINGLE
            || triggeredBy == ClientRequestType.CANCEL_REPLACE && !persistentState.getBlocked();
    }

    @Override
    public Cancelled onClientCancelRequest(ClientRequest cancelRequest, ClientOrderContext ctx) {
        ClientOrderState.Builder updatedState = ctx.getPersistentState().toBuilder()
            .setClosed(true)
            .setCurrentStatus(ClientOrderStatus.CANCELED)
            .setRemainingQuantity("0")
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
        updateMessages(updatedState, cancelRequest);
        ctx.persistState(updatedState.build());

        return new Cancelled();
    }

    @Override
    public Cancelled onClientCancelReplaceRequest(ClientRequest cancelReplaceRequest, ClientOrderContext ctx) {
        ctx.setPendingReplacement(cancelReplaceRequest);

        ClientOrderState.Builder updatedState = ctx.getPersistentState()
            .toBuilder()
            .setClosed(true)
            .setCurrentStatus(ClientOrderStatus.CANCELED)
            .setRemainingQuantity("0")
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
        updateMessages(updatedState, cancelReplaceRequest);
        ctx.persistState(updatedState.build());

        return new Cancelled();
    }

    public static ClientOrderState createPersistentState(ClientRequest clientRequest, @Nullable Instrument instrument) {
        isTrue(clientRequest.getRequestType() == ClientRequestType.ORDER_SINGLE, "Only suitable for acting on ORDER_SINGLE");

        String createdAt = clientRequest.getCreatedAt();
        ClientOrderState.Builder builder = ClientOrderState.newBuilder()
            .setOrder(clientRequest)
            .setCurrentStatus(ClientOrderStatus.PENDING_NEW)
            .setQuantity(clientRequest.getQuantity())
            .setRemainingQuantity(clientRequest.getQuantity())
            .setFilledQuantity("0")
            .setCreatedAt(createdAt)
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));

        if (instrument != null) {
            builder.setInstrumentId(instrument.getInstrumentIdentifiers().getInstrumentId());
        }

        return builder.build();
    }

    public static ClientOrderState createPersistentState(ClientRequest clientRequest, ClientOrderContext previousInChain, @Nullable Instrument instrument) {
        isTrue(clientRequest.getRequestType() == ClientRequestType.CANCEL_REPLACE, "Only suitable for acting on CANCEL_REPLACE");

        String filledQuantity = previousInChain.getPersistentState().getFilledQuantity();
        String quantity = clientRequest.getQuantity();
        BigDecimal remainingQty = new BigDecimal(quantity).subtract(new BigDecimal(filledQuantity));

        ClientOrderState.Builder builder = ClientOrderState.newBuilder()
            .setOrder(clientRequest)
            .setPreReplaceOrderId(previousInChain.getPersistentState().getOrder().getOrderId())
            .setCurrentStatus(ClientOrderStatus.PENDING_NEW)
            .setQuantity(quantity)
            .setRemainingQuantity(remainingQty.toString())
            .setFilledQuantity(filledQuantity)
            .setPreReplaceFilledQty(filledQuantity)
            .setBlocked(true)
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));

        if (instrument != null) {
            builder.setInstrumentId(instrument.getInstrumentIdentifiers().getInstrumentId());
        }

        return builder.build();
    }
}
