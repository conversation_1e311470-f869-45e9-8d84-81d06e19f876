package io.wyden.smartrecommendationengine.service.client.inbound;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import io.wyden.smartrecommendationengine.utils.HostIdUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;

/**
 * Accepts best execution requests.
 */
@Component
public class BestExecutionRequestConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(BestExecutionRequestConsumer.class);

    public BestExecutionRequestConsumer(RabbitQueue<BestExecutionRequest> bestExecutionRequestBroadcastQueue,
                                        RabbitExchange<BestExecutionRequest> bestExecutionRequestExchange,
                                        RabbitQueue<BestExecutionRequest> bestExecutionRequestPrivateQueue,
                                        BestExecutionRequestHandler bestExecutionRequestHandler,
                                        @Value("${rabbitmq.best-execution-request-broadcast-header}") String broadcastHeader,
                                        HostIdUtils hostIdUtils) {

        declareBroadcastQueue(bestExecutionRequestBroadcastQueue, bestExecutionRequestExchange, bestExecutionRequestHandler, broadcastHeader);
        declarePrivateQueue(bestExecutionRequestPrivateQueue, bestExecutionRequestExchange, bestExecutionRequestHandler, hostIdUtils);
    }

    private void declareBroadcastQueue(RabbitQueue<BestExecutionRequest> bestExecutionRequestBroadcastQueue,
                                       RabbitExchange<BestExecutionRequest> bestExecutionRequestExchange,
                                       BestExecutionRequestHandler bestExecutionRequestHandler,
                                       String broadcastHeader) {

        Map<String, Object> headers = getBestExecutionExchangeHeaders(broadcastHeader);

        LOGGER.info("Binding exchange {} and queue {} with headers {}", bestExecutionRequestExchange, bestExecutionRequestBroadcastQueue, headers);
        bestExecutionRequestBroadcastQueue.bindWithHeaders(bestExecutionRequestExchange, MatchingCondition.ALL, headers);

        bestExecutionRequestBroadcastQueue.attachConsumer(BestExecutionRequest.parser(), (request, params) -> {
            LOGGER.info("Handling Best execution broadcast request: {} for order id {}", request, request.getOrderId());
            bestExecutionRequestHandler.handleBestExecutionBroadcastRequest(request, params);
            return consumed();
        });
    }

    private void declarePrivateQueue(RabbitQueue<BestExecutionRequest> bestExecutionRequestPrivateQueue,
                                     RabbitExchange<BestExecutionRequest> bestExecutionRequestExchange,
                                     BestExecutionRequestHandler bestExecutionRequestHandler,
                                     HostIdUtils hostIdUtils) {

        String hostId = hostIdUtils.getHostId();
        Map<String, Object> headers = getBestExecutionExchangeHeaders(hostId);

        LOGGER.info("Binding exchange {} and queue {} with headers {}", bestExecutionRequestExchange, bestExecutionRequestPrivateQueue, headers);
        bestExecutionRequestPrivateQueue.bindWithHeaders(bestExecutionRequestExchange, MatchingCondition.ALL, headers);

        bestExecutionRequestPrivateQueue.attachConsumer(BestExecutionRequest.parser(), (request, params) -> {
            LOGGER.info("Handling Best execution private request: {} for order id {}", request, request.getOrderId());
            bestExecutionRequestHandler.handleBestExecutionPrivateRequest(request, params);
            return consumed();
        });
    }


    private static Map<String, Object> getBestExecutionExchangeHeaders(String sreDestination) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), BestExecutionRequest.class.getSimpleName(),
            OemsHeader.SRE_DESTINATION.getHeaderName(), sreDestination
        );
        return headers;
    }

}
