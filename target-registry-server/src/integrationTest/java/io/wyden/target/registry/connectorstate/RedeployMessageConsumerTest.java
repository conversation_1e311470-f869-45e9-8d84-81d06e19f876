package io.wyden.target.registry.connectorstate;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.target.registry.k8s.K8sIntegrationTest;
import io.wyden.target.registry.statemachine.state.TargetStateUndeployed;

import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1Pod;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.time.ZonedDateTime;
import java.util.concurrent.TimeUnit;

class RedeployMessageConsumerTest extends K8sIntegrationTest {

    @Test
    void should_recreate_connector() throws ApiException {
        //given
        requestDefaultConnector();
        awaitConnectorIsAlive();
        //when
        String previousConnectorName = getConnectorPodName();
        requestRedeployOfConnector();
        //then new name will be different
        Awaitility.await().atMost(180, TimeUnit.SECONDS).until(() -> !previousConnectorName.equalsIgnoreCase(getConnectorPodName()));
    }


    @Test
    void should_recreate_connector_even_undeployed_state() throws ApiException {
        //given
        requestDefaultConnector();
        awaitConnectorIsAlive();
        //force invalid undeployed state
        connectorStateCache.persist(new TargetStateUndeployed(targetStateContext).getStatus(), DateUtils.toIsoUtcTime(ZonedDateTime.now()), CONNECTOR_ID);
        //when
        String previousConnectorName = getConnectorPodName();
        requestRedeployOfConnector();
        //then new name will be different
        Awaitility.await().atMost(180, TimeUnit.SECONDS).until(() -> !previousConnectorName.equalsIgnoreCase(getConnectorPodName()));
    }

    private String getConnectorPodName() throws ApiException {
        return k8sOpsHelper.getPods().stream().findFirst()
            .map(V1Pod::getMetadata)
            .map(V1ObjectMeta::getName)
            .orElse(StringUtils.EMPTY);
    }
}
