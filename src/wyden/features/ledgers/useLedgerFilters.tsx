import { useUserStoreSynchronizer } from '@wyden/features/user-data/useUserStoreSynchronizerStore';
import { VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import {
  PortfolioResponse,
  PortfolioResponseSchema,
  VenueAccountSchema,
} from '@wyden/services/graphql/generated/graphql';
import { z } from 'zod';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface LedgerFilter {
  accounts: VenueAccountWithVenue[];
  wallets: VenueAccountWithVenue[];
  portfolios: PortfolioResponse[];
  transactionId: string;
  orderId: string;
  currency: string[];
  createdFrom: Date | null;
  createdTo: Date | null;
  ledgerEntryType: string[];
}

export const LedgerFiltersSchema = z.object({
  accounts: z.array(VenueAccountSchema().extend({ venue: z.string() })),
  wallets: z.array(VenueAccountSchema().extend({ venue: z.string() })),
  portfolios: z.array(PortfolioResponseSchema()),
  transactionId: z.string().optional(),
  orderId: z.string().optional(),
  currency: z.array(z.string()).optional(),
  createdFrom: z.date().nullable().optional(),
  createdTo: z.date().nullable().optional(),
  ledgerEntryType: z.array(z.string()).optional(),
});

export enum LedgerFilterLists {
  COLUMN_LIST = 'columnList',
  ACCOUNTS_LIST = 'accounts',
  WALLETS_LIST = 'wallets',
  PORTFOLIOS_LIST = 'portfolios',
  TRANSACTION_ID_INPUT = 'transactionId',
  ORDER_ID_INPUT = 'orderId',
  CURRENCY_INPUT = 'currency',
  TRANSACTION_TYPES_LIST = 'ledgerEntryType',
  CREATED_FROM_INPUT = 'createdFrom',
  CREATED_TO_INPUT = 'createdTo',
}

interface LedgerFiltersStore {
  getUserStoreSynchronizer: () => ReturnType<typeof useUserStoreSynchronizer.getState>;
  selectedList: LedgerFilterLists;
  filters: LedgerFilter;
  setSelectedList: (list: LedgerFilterLists) => void;
  setAccounts: (accounts: VenueAccountWithVenue[]) => void;
  setWallets: (wallets: VenueAccountWithVenue[]) => void;
  setPortfolios: (portfolios: PortfolioResponse[]) => void;
  setCurrency: (currency: string[]) => void;
  setCreatedFrom: (createdFrom: Date | null) => void;
  setCreatedTo: (createdTo: Date | null) => void;
  setTransactionId: (intTransactionId: string) => void;
  setOrderId: (orderId: string) => void;
  setLedgerEntryType: (ledgerEntryType: string[]) => void;
  toggleLedgerType: (type: string) => void;
  onRemoveFilter: (filter: keyof LedgerFilter) => void;
  clear: () => void;
  columnListSelected: () => void;
  setInitialState: (
    portfolios: PortfolioResponse[],
    accounts: VenueAccountWithVenue[],
    wallets: VenueAccountWithVenue[],
  ) => void;
  reset: () => void;
  isInitialized?: boolean;
}

const initialValues: LedgerFilter = {
  transactionId: '',
  orderId: '',
  accounts: [],
  wallets: [],
  portfolios: [],
  currency: [],
  createdFrom: null,
  createdTo: null,
  ledgerEntryType: [],
};

export const useLedgerFilters = create<LedgerFiltersStore>()(
  devtools(
    (set) => ({
      getUserStoreSynchronizer: () => useUserStoreSynchronizer.getState(),
      selectedList: LedgerFilterLists.COLUMN_LIST,
      filters: initialValues,
      isInitialized: false,
      setSelectedList: (list) => set(() => ({ selectedList: list })),
      setAccounts: (accounts) =>
        set((state) => ({
          filters: {
            ...state.filters,
            accounts,
          },
        })),
      setWallets: (wallets) =>
        set((state) => ({
          filters: {
            ...state.filters,
            wallets,
          },
        })),
      setPortfolios: (portfolios) =>
        set((state) => ({
          filters: {
            ...state.filters,
            portfolios,
          },
        })),
      setCurrency: (currency) =>
        set((state) => ({
          filters: {
            ...state.filters,
            currency,
          },
        })),
      setCreatedTo: (createdTo) =>
        set((state) => ({
          filters: {
            ...state.filters,
            createdTo,
          },
        })),

      setCreatedFrom: (createdFrom) =>
        set((state) => ({
          filters: {
            ...state.filters,
            createdFrom,
          },
        })),

      setLedgerEntryType: (ledgerEntryType) =>
        set((state) => ({
          filters: {
            ...state.filters,
            ledgerEntryType,
          },
        })),
      toggleLedgerType: (type) =>
        set((state) => {
          const ledgerEntryType = [...(state.filters.ledgerEntryType || [])];
          const index = ledgerEntryType.indexOf(type);
          if (index > -1) {
            ledgerEntryType.splice(index, 1);
          } else {
            ledgerEntryType.push(type);
          }
          return {
            filters: {
              ...state.filters,
              ledgerEntryType,
            },
          };
        }),
      setTransactionId: (transactionId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            transactionId,
          },
        })),
      setOrderId: (orderId) =>
        set((state) => ({
          filters: {
            ...state.filters,
            orderId,
          },
        })),
      clear: () => {
        return set(() => ({ filters: { ...initialValues } }));
      },
      columnListSelected: () => set(() => ({ selectedList: LedgerFilterLists.COLUMN_LIST })),
      onRemoveFilter: (filter) => {
        set((state) => {
          return {
            ...state,
            filters: {
              ...state.filters,
              [filter]: filter in initialValues ? initialValues[filter] : null,
            },
          };
        });
      },
      setInitialState: (portfolios, accounts, wallets) => {
        set((state) => {
          if (state.isInitialized) return state;

          return {
            filters: {
              ...initialValues,
              portfolios,
              accounts,
              wallets,
            },
            isInitialized: true,
          };
        });
      },
      reset: () => set(() => ({ filters: initialValues, isInitialized: false })),
    }),
    { name: 'LedgerFilters' },
  ),
);
