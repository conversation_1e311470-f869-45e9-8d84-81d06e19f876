package io.wyden.clob.gateway.service.fsm;

import io.micrometer.common.util.StringUtils;
import io.wyden.clob.gateway.model.PlaceOrderCommand;
import io.wyden.clob.gateway.service.aeron.IdGenerator;
import io.wyden.clob.gateway.service.rate.RateCache;
import io.wyden.clob.gateway.service.referencedata.InstrumentsRepository;
import io.wyden.clob.gateway.utils.MathUtils;
import io.wyden.clob.gateway.utils.TradingMessageUtils;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Portfolio;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;

import static io.wyden.clob.gateway.utils.BigDecimalUtils.bd;
import static io.wyden.clob.gateway.utils.TradingMessageUtils.hasAvailableBalance;

@Component
public class PlaceOrderCommandFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlaceOrderCommandFactory.class);
    public static final int NANOS_IN_MILLI = 1_000_000;

    private final IdGenerator idGenerator;
    private final InstrumentsRepository instrumentsRepository;
    private final RateCache rateCache;

    public PlaceOrderCommandFactory(IdGenerator idGenerator, InstrumentsRepository instrumentsRepository, RateCache rateCache) {
        this.idGenerator = idGenerator;
        this.instrumentsRepository = instrumentsRepository;
        this.rateCache = rateCache;
    }

    PlaceOrderCommand createPlaceOrderCommand(OemsRequest request, Portfolio clientPortfolio) {
        long orderId = idGenerator.nextId();
        Instrument instrument = instrumentsRepository.get(request.getInstrumentId());
        int symbol = instrument.getInstrumentIdentifiers().getMatchingEngineId();

        final long price = switch (request.getOrderType()) {
            case MARKET -> request.getSide() == OemsSide.BUY ? Long.MAX_VALUE : 0;
            case LIMIT -> MathUtils.convertPrice(bd(request.getPrice()), instrument);
            case STOP, STOP_LIMIT, UNRECOGNIZED, ORDER_TYPE_UNSPECIFIED ->
                throw new IllegalArgumentException("Unsupported order type " + request.getOrderType());
        };

        ZonedDateTime expireTime = DateUtils.fromFixUtcTime(request.getExpireTime());
        final String expiryEpochMillis = expireTime == null ? null : DateUtils.zonedDateTimeToEpochMillis(expireTime);
        final long expiry = StringUtils.isBlank(expiryEpochMillis) ? 0 : Long.parseLong(expiryEpochMillis) * NANOS_IN_MILLI;

        PlaceOrderCommand.Builder builder = PlaceOrderCommand.newBuilder()
            .setSymbol(symbol)
            .setUid(clientPortfolio.getMatchingEngineUid())
            .setOrderId(orderId)
            .setOrderUuid(request.getOrderId())
            .setPrice(price)
            .setAction(request.getSide() == OemsSide.BUY ? io.wyden.clob.gateway.model.OrderAction.BID : io.wyden.clob.gateway.model.OrderAction.ASK)
            .setOrderType(switch (request.getTif()) {
                case TIF_UNSPECIFIED, UNRECOGNIZED, DAY -> throw new IllegalArgumentException("Unsupported TIF: " + request.getTif());
                case GTC -> io.wyden.clob.gateway.model.OrderType.GTC;
                case IOC -> io.wyden.clob.gateway.model.OrderType.IOC;
                case FOK -> io.wyden.clob.gateway.model.OrderType.FOK;
                case GTD -> io.wyden.clob.gateway.model.OrderType.GTD;
            })
            .setExpiry(expiry);

        BigDecimal requestQuantity = bd(request.getQuantity());

        if (TradingMessageUtils.isCashOrderWithAvailableBalanceInBaseCurrency(request, instrument)) {
            // It is important to use CLOB rate in case available balance is specified in base currency
            // (external FX rate will almost never be 100% the same, which will produce Order overfills or underfills).
            // Place it as BaseOrder with available balance as qty and quantity as amount, so exchange-core can reduce it using order book rate
            BigDecimal qty = bd(request.getAvailableBalanceAmount());
            long size = MathUtils.convertVolume(qty, instrument);
            builder.setSize(size);

            long availableBalance = MathUtils.convertCashVolume(requestQuantity, instrument);
            builder.setAmount(availableBalance);

        } else if (TradingMessageUtils.isCashOrder(request)) {
            long amount = MathUtils.convertCashVolume(requestQuantity, instrument);

            if (hasAvailableBalance(request)) {
                BigDecimal availableBalanceQuoteCcy = getAvailableBalanceInQuoteCurrency(request, instrument);
                long availableBalance = MathUtils.convertCashVolume(availableBalanceQuoteCcy, instrument);

                if (availableBalance <= 0) {
                    throw new IllegalStateException("Unable to convert available balance to matching engine amount, orderId %s".formatted(request.getOrderId()));
                }

                if (amount > availableBalance) {
                    LOGGER.info("Requested amount {} exceeds available balance {}, order {} will be reduced", amount, availableBalance, request.getOrderId());
                    amount = availableBalance;
                }
            }

            builder.setAmount(amount);
            builder.setSize(Long.MAX_VALUE);

        } else { // Base Currency Order:
            if (hasAvailableBalance(request)) {
                adjustSizeAndAmount(request, instrument, builder, requestQuantity);
            } else {
                builder.setSize(MathUtils.convertVolume(requestQuantity, instrument));
            }
        }

        return builder.build();
    }

    private void adjustSizeAndAmount(OemsRequest request, Instrument instrument, PlaceOrderCommand.Builder builder, BigDecimal requestQuantity) {
        if (isAvailableBalanceInBaseCurrency(request, instrument)) {
            BigDecimal effectiveQty = requestQuantity.min(new BigDecimal(request.getAvailableBalanceAmount()));
            long size = MathUtils.convertVolume(effectiveQty, instrument);
            builder.setSize(size);
        } else if (isAvailableBalanceInQuoteCurrency(request, instrument)) {
            long availableBalance = MathUtils.convertCashVolume(new BigDecimal(request.getAvailableBalanceAmount()), instrument);
            builder.setAmount(availableBalance);
            long size = MathUtils.convertVolume(requestQuantity, instrument);
            builder.setSize(size);
        } else {
            BigDecimal availableBalanceAmount = getAvailableBalanceInQuoteCurrency(request, instrument);
            long availableBalance = MathUtils.convertCashVolume(availableBalanceAmount, instrument);
            builder.setAmount(availableBalance);
            long size = MathUtils.convertVolume(requestQuantity, instrument);
            builder.setSize(size);
        }
    }

    private boolean isAvailableBalanceInQuoteCurrency(OemsRequest request, Instrument instrument) {
        return request.getAvailableBalanceCurrency().equals(instrument.getBaseInstrument().getQuoteCurrency());
    }

    private static boolean isAvailableBalanceInBaseCurrency(OemsRequest request, Instrument instrument) {
        return request.getAvailableBalanceCurrency().equals(instrument.getForexSpotProperties().getBaseCurrency());
    }

    private BigDecimal getAvailableBalanceInQuoteCurrency(OemsRequest request, Instrument instrument) {
        BigDecimal availableBalance = bd(request.getAvailableBalanceAmount());
        String availableBalanceCurrency = request.getAvailableBalanceCurrency();
        String targetCurrency = request.getQuoteCurrency();
        return convertAvailableBalance(availableBalance, availableBalanceCurrency, targetCurrency, instrument.getTradingConstraints().getQuoteQtyIncr());
    }

    private BigDecimal convertAvailableBalance(BigDecimal value, String sourceCurrency, String targetCurrency, String incr) {
        int decimals = MathUtils.convertIncrToScale(incr);
        BigDecimal conversionRate = rateCache.find(sourceCurrency, targetCurrency)
            .map(rate -> bd(rate.getValue()))
            .orElseThrow(() -> new IllegalStateException("Conversion rate not found (from availableBalanceCurrency %s to %s)".formatted(sourceCurrency, targetCurrency)));
        return value.multiply(conversionRate).setScale(decimals, RoundingMode.HALF_UP);
    }
}
