package io.wyden.booking.wal.api;

import io.wyden.booking.wal.domain.service.WalService;
import io.wyden.published.booking.WalEvent;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
public class EventQueryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventQueryController.class);

    private final WalService walEventService;

    public EventQueryController(WalService walEventService) {
        this.walEventService = walEventService;
    }

    @GetMapping("/wal-events")
    public CursorConnection getWalEvents(
        @RequestParam(defaultValue = "${wal.events.default.first:1000}") long first,
        @RequestParam(defaultValue = "${wal.events.default.after:0}") long after) {

        LOGGER.info("Requested WAL events with first: {}, after: {}", first, after);

        // Fetch WAL events from the service
        List<WalEvent> found = walEventService.findWalEvents(first + 1, after);

        // Create edges for each WAL event
        List<CursorEdge> edges = found.stream()
            .limit(first)
            .filter(walEvent -> walEvent.getEventCase() != WalEvent.EventCase.EVENT_NOT_SET)
            .peek(this::debug)
            .map(this::createCursorEdge)
            .collect(Collectors.toList());

        // Create page info
        PageInfo pageInfo = PageInfo.newBuilder()
            .setHasNextPage(found.size() > first)
            .setTotalSizeStatus(PageInfo.TotalSizeStatus.SIZE_UNKNOWN)
            .setEndCursor(edges.isEmpty() ? "" : edges.get(edges.size() - 1).getCursor())
            .build();

        // Build and return the CursorConnection
        return CursorConnection.newBuilder()
            .setPageInfo(pageInfo)
            .addAllEdges(edges)
            .build();
    }

    private void debug(WalEvent walEvent) {
        LOGGER.debug("Returning: {}", walEvent);
    }

    private CursorEdge createCursorEdge(WalEvent walEvent) {
        // Create CursorNode with WAL event
        CursorNode node = CursorNode.newBuilder()
            .setWalEvent(walEvent)
            .build();

        // Create CursorEdge with node and cursor (using sequence number as cursor)
        return CursorEdge.newBuilder()
            .setNode(node)
            .setCursor(String.valueOf(walEvent.getSequenceNumber()))
            .build();
    }
}
