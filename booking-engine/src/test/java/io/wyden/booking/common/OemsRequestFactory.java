package io.wyden.booking.common;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import static io.wyden.booking.common.TestUtils.randomString;
import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;

public final class OemsRequestFactory {

    private OemsRequestFactory() {
    }

    public static OemsRequest.Builder withLimit(OemsRequest.Builder builder, BigDecimal limit, BigDecimal quantity) {
        return builder
            .setQuantity(toProtoString(quantity))
            .setOrderType(OemsOrderType.LIMIT)
            .setPrice(toProtoString(limit));
    }

    public static OemsRequest.Builder withMarket(OemsRequest.Builder builder, BigDecimal quantity) {
        return builder
            .setQuantity(toProtoString(quantity))
            .setOrderType(OemsOrderType.MARKET);
    }

    public static OemsRequest.Builder createOemsRequestLimit(BigDecimal price, BigDecimal quantity) {
        return withLimit(createOemsRequestToBrokerDesk(), price, quantity);
    }

    public static OemsRequest.Builder createOemsRequestMarket(BigDecimal quantity) {
        return withMarket(createOemsRequestToBrokerDesk(), quantity);
    }

    public static OemsRequest.Builder createOemsRequestToBrokerDesk() {
        String orderId = randomString();

        Metadata.Builder metadata = Metadata.newBuilder()
            .setTargetType(Metadata.ServiceType.BROKER_DESK)
            .setTarget("Agency")
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setRequestId(orderId);

        return OemsRequest.newBuilder()
            .setMetadata(metadata)
            .setExecutionConfig(ExecutionConfig.newBuilder()
                .setCounterPortfolio("Bank")
                .build())
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setPortfolioId("Client 1")
            .setCurrency("BTC")
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .setSide(OemsSide.BUY);
    }

    public static OemsRequest.Builder createOemsRequestToVenue() {
        String orderId = randomString();

        Metadata.Builder metadata = Metadata.newBuilder()
            .setTargetType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT)
            .setTarget("BitMEX")
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setRequestId(orderId);

        return OemsRequest.newBuilder()
            .setMetadata(metadata)
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setOrderId(orderId)
            .setPortfolioId("Client 1")
            .setVenueAccount("BitMEX-testnet")
            .setCurrency("BTC")
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .setSide(OemsSide.BUY);
    }
}
