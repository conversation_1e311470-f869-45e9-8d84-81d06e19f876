package io.wyden.referencedata.client;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.referencedata.Currency;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.Venue;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.domain.CurrencyMapConfig;
import io.wyden.referencedata.domain.InstrumentMapConfig;
import io.wyden.referencedata.domain.PortfolioMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import io.wyden.referencedata.domain.VenueMapConfig;


public class ReferenceDataProvider {

    public InstrumentsCacheFacade getInstrumentsCacheFacade(HazelcastInstance hazelcast, Tracing otlTracing) {
        IMap<String, Instrument> map = InstrumentMapConfig.getMap(hazelcast);
        return new InstrumentsCacheFacade(map, otlTracing);
    }

    public InstrumentSymbolsCacheFacade getInstrumentSymbolsCacheFacade(VenueAccountCacheFacade venueAccountCacheFacade,
                                                                        InstrumentsCacheFacade instrumentsCacheFacade,
                                                                        HazelcastInstance hazelcast,
                                                                        Tracing otlTracing) {
        return new InstrumentSymbolsCacheFacade(venueAccountCacheFacade, instrumentsCacheFacade, hazelcast, otlTracing);
    }

    public VenuesCacheFacade getVenuesCacheFacade(HazelcastInstance hazelcastInstance, Tracing otlTracing) {
        IMap<String, Venue> map = VenueMapConfig.getMap(hazelcastInstance);
        return new VenuesCacheFacade(map, otlTracing);
    }

    public static VenueAccountCacheFacade getVenueAccountCacheFacade(HazelcastInstance hazelcastInstance, Tracing otlTracing) {
        IMap<String, VenueAccount> map = VenueAccountMapConfig.getMap(hazelcastInstance);
        return new VenueAccountCacheFacade(map, otlTracing);
    }

    public PortfoliosCacheFacade getPortfoliosCacheFacade(HazelcastInstance hazelcastInstance, Tracing otlTracing) {
        IMap<String, Portfolio> map = PortfolioMapConfig.getMap(hazelcastInstance);
        return new PortfoliosCacheFacade(map, otlTracing);
    }

    public CurrencyCacheFacade getCurrencyCacheFacade(HazelcastInstance hazelcastInstance, Tracing otlTracing) {
        IMap<String, Currency> map = CurrencyMapConfig.getMap(hazelcastInstance);
        return new CurrencyCacheFacade(map, otlTracing);
    }
}
