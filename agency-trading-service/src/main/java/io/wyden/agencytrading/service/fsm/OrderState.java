package io.wyden.agencytrading.service.fsm;

import io.wyden.agencytrading.model.AgencyTradingOrderState;
import io.wyden.agencytrading.model.Execution;
import io.wyden.cloudutils.tools.MathUtils;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import org.apache.commons.collections4.list.UnmodifiableList;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.PriorityQueue;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.EMPTY;

// TODO: Make it immune to EMPTY
public class OrderState {

    private final PriorityQueue<OrderStatus> orderStatuses = new PriorityQueue<>(Collections.reverseOrder());
    private BigDecimal quantity;
    private BigDecimal filledQuantity;
    private BigDecimal remainingQuantity;
    private BigDecimal avgPrice;
    private BigDecimal avgPriceWithMarkup;
    private BigDecimal totalFeeCharged;
    private BigDecimal totalPercFee;
    private Boolean fixedFeeCharged;
    private String errorCode;
    private String description;
    private final List<Execution> executions = new LinkedList<>();
    private String pendingCancelRequestId;
    private String pendingCancelRequesterId;
    private final String clientId;
    private final OemsRequest oemsRequest;
    private int sequenceNumber;
    private boolean forceCancel;
    private boolean isDirty;
    private OemsRequest childOrderRequest = emptyOemsRequest();

    OrderState(OemsRequest oemsRequest) {
        this.orderStatuses.add(StatusStart.create());
        this.quantity = new BigDecimal(oemsRequest.getQuantity());
        this.filledQuantity = BigDecimal.ZERO;
        this.remainingQuantity = new BigDecimal(oemsRequest.getQuantity());
        this.avgPrice = BigDecimal.ZERO;
        this.avgPriceWithMarkup = BigDecimal.ZERO;
        this.totalFeeCharged = BigDecimal.ZERO;
        this.totalPercFee = BigDecimal.ZERO;
        this.fixedFeeCharged = false;
        this.errorCode = EMPTY;
        this.description = EMPTY;
        this.pendingCancelRequestId = EMPTY;
        this.pendingCancelRequesterId = EMPTY;
        this.clientId = ObjectUtils.firstNonNull(oemsRequest.getClientId(), EMPTY);
        this.oemsRequest = oemsRequest;
        this.sequenceNumber = 0;
        this.forceCancel = oemsRequest.getForceCancel();
    }

    OrderState(AgencyTradingOrderState orderState) {
        this.orderStatuses.addAll(OrderStatus.toOrderStates(orderState));
        this.quantity = new BigDecimal(orderState.getQuantity());
        this.filledQuantity = new BigDecimal(orderState.getFilledQuantity());
        this.remainingQuantity = new BigDecimal(orderState.getRemainingQuantity());
        this.avgPrice = new BigDecimal(orderState.getAvgPrice());
        this.avgPriceWithMarkup = new BigDecimal(orderState.getAvgPriceWithMarkup());
        this.totalFeeCharged = new BigDecimal(orderState.getTotalFeeCharged());
        this.totalPercFee = new BigDecimal(orderState.getTotalPercFee());
        this.fixedFeeCharged = orderState.getFixedFeeCharged();
        this.errorCode = orderState.getErrorCode();
        this.description = orderState.getDescription();
        this.executions.addAll(orderState.getExecutionList());
        this.pendingCancelRequestId = orderState.getPendingCancelRequestId();
        this.pendingCancelRequesterId = orderState.getPendingCancelRequesterId();
        this.clientId = orderState.getClientId();
        this.oemsRequest = orderState.getRequest();
        this.sequenceNumber = orderState.getSequenceNumber() + 1;
        this.childOrderRequest = orderState.getChildOrderRequest();
        this.forceCancel = orderState.getForceCancel();
    }

    AgencyTradingOrderState toAgencyTradingOrderState() {
        AgencyTradingOrderState.Builder builder = AgencyTradingOrderState.newBuilder()
            .addAllCurrentStatus(orderStatuses.stream().map(OrderStatus::toOemsOrderStatus).collect(Collectors.toSet()))
            .setQuantity(quantity.toPlainString())
            .setFilledQuantity(filledQuantity.toPlainString())
            .setRemainingQuantity(remainingQuantity.toPlainString())
            .setAvgPrice(avgPrice.toPlainString())
            .setAvgPriceWithMarkup(avgPriceWithMarkup.toPlainString())
            .setTotalFeeCharged(totalFeeCharged.toPlainString())
            .setTotalPercFee(totalPercFee.toPlainString())
            .setFixedFeeCharged(fixedFeeCharged)
            .setErrorCode(errorCode)
            .setDescription(description)
            .addAllExecution(executions)
            .setPendingCancelRequestId(pendingCancelRequestId)
            .setPendingCancelRequesterId(pendingCancelRequesterId)
            .setClosed(isClosed())
            .setClientId(clientId)
            .setSequenceNumber(sequenceNumber)
            .setRequest(oemsRequest)
            .setChildOrderRequest(childOrderRequest)
            .setForceCancel(forceCancel);
        return builder.build();
    }

    public OrderStatus getOrderStatus() {
        return orderStatuses.peek();
    }

    public OemsOrderStatus getOemsOrderStatus() {
        return getOrderStatus().toOemsOrderStatus();
    }

    public boolean hasOrderStatuses(OrderStatus... statuses) {
        return orderStatuses.containsAll(Arrays.asList(statuses));
    }

    void addOrderStatus(OrderStatus orderStatus) {
        if (!orderStatuses.contains(orderStatus)) {
            orderStatuses.add(orderStatus);
            isDirty = true;
        }
    }

    void removeOrderStatus(OrderStatus orderStatus) {
        if (orderStatuses.contains(orderStatus)) {
            orderStatuses.remove(orderStatus);
            isDirty = true;
        }
    }

    void setOrderStatus(OrderStatus orderStatus) {
        if (orderStatuses.size() != 1 || !orderStatuses.contains(orderStatus)) {
            orderStatuses.clear();
            orderStatuses.add(orderStatus);
            isDirty = true;
        }
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    void setQuantity(BigDecimal quantity) {
        if (this.quantity.compareTo(quantity) != 0) {
            this.quantity = quantity;
            isDirty = true;
        }
    }

    public BigDecimal getFilledQuantity() {
        return filledQuantity;
    }

    void setFilledQuantity(BigDecimal filledQuantity) {
        if (this.filledQuantity.compareTo(filledQuantity) != 0) {
            this.filledQuantity = filledQuantity;
            isDirty = true;
        }
    }

    public BigDecimal getRemainingQuantity() {
        return remainingQuantity;
    }

    void setRemainingQuantity(BigDecimal remainingQuantity) {
        if (this.remainingQuantity.compareTo(remainingQuantity) != 0) {
            this.remainingQuantity = remainingQuantity;
            isDirty = true;
        }
    }

    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    void setAvgPrice(BigDecimal avgPrice) {
        if (this.avgPrice.compareTo(avgPrice) != 0) {
            this.avgPrice = avgPrice;
            isDirty = true;
        }
    }

    public BigDecimal getAvgPriceWithMarkup() {
        return avgPriceWithMarkup;
    }

    public void setAvgPriceWithMarkup(BigDecimal avgPriceWithMarkup) {
        if (this.avgPriceWithMarkup.compareTo(avgPriceWithMarkup) != 0) {
            this.avgPriceWithMarkup = avgPriceWithMarkup;
            isDirty = true;
        }
    }

    public BigDecimal getTotalFeeCharged() {
        return totalFeeCharged;
    }

    public void setTotalFeeCharged(BigDecimal totalFeeCharged) {
        this.totalFeeCharged = totalFeeCharged;
        isDirty = true;
    }

    public BigDecimal getTotalPercFee() {
        return totalPercFee;
    }

    public void setTotalPercFee(BigDecimal totalPercFee) {
        this.totalPercFee = totalPercFee;
        isDirty = true;
    }

    public Boolean getFixedFeeCharged() {
        return fixedFeeCharged;
    }

    public void setFixedFeeCharged(Boolean fixedFeeCharged) {
        this.fixedFeeCharged = fixedFeeCharged;
        isDirty = true;
    }

    public String getErrorCode() {
        return errorCode;
    }

    void setErrorCode(String errorCode) {
        if (!StringUtils.equals(this.errorCode, errorCode)) {
            this.errorCode = errorCode;
            isDirty = true;
        }
    }

    public String getDescription() {
        return description;
    }

    void setDescription(String description) {
        if (!StringUtils.equals(this.description, description)) {
            this.description = description;
            isDirty = true;
        }
    }

    public List<Execution> getExecutions() {
        return UnmodifiableList.unmodifiableList(executions);
    }

    public void addExecution(Execution execution) {
        executions.add(execution);
        isDirty = true;
    }

    public String getPendingCancelRequestId() {
        return pendingCancelRequestId;
    }

    public String getPendingCancelRequesterId() {
        return pendingCancelRequesterId;
    }

    private void setPendingCancelRequestId(String pendingCancelRequestId) {
        if (!StringUtils.equals(this.pendingCancelRequestId, pendingCancelRequestId)) {
            this.pendingCancelRequestId = pendingCancelRequestId;
            isDirty = true;
        }
    }

    private void setPendingCancelRequesterId(String pendingCancelRequesterId) {
        if (!StringUtils.equals(this.pendingCancelRequesterId, pendingCancelRequesterId)) {
            this.pendingCancelRequesterId = pendingCancelRequesterId;
            isDirty = true;
        }
    }

    public boolean isClosed() {
        return orderStatuses.stream()
            .map(OrderStatus::isTerminal)
            .reduce(false, (acc, val) -> acc || val);
    }

    public String getClientId() {
        return clientId;
    }

    public OemsRequest getOemsRequest() {
        return oemsRequest;
    }

    public OemsRequest getChildOrderRequest() {
        return childOrderRequest;
    }

    public void setChildOrderRequest(OemsRequest childOrderRequest) {
        this.childOrderRequest = childOrderRequest;
        isDirty = true;
    }

    String removeStatusPendingCancel() {
        String pendingCancelRequestId = getPendingCancelRequestId();
        setPendingCancelRequestId(EMPTY);
        setPendingCancelRequesterId(EMPTY);
        removeOrderStatus(StatusPendingCancel.create());
        return pendingCancelRequestId;
    }

    void setStatusPendingCancel(String requestId, String requesterId) {
        setPendingCancelRequestId(requestId);
        setPendingCancelRequesterId(requesterId);
        addOrderStatus(StatusPendingCancel.create());
    }

    void setStatusCancelled() {
        setRemainingQuantity(BigDecimal.ZERO);
        setPendingCancelRequestId(EMPTY);
        setPendingCancelRequesterId(EMPTY);
        setOrderStatus(StatusCancelled.create());
    }

    void setStatusExpired() {
        setRemainingQuantity(BigDecimal.ZERO);
        setPendingCancelRequestId(EMPTY);
        setPendingCancelRequesterId(EMPTY);
        setOrderStatus(StatusExpired.create());
    }

    void setStatusRejected() {
        setRemainingQuantity(BigDecimal.ZERO);
        setOrderStatus(StatusRejected.create());
    }

    void incSequenceNum() {
        sequenceNumber = sequenceNumber + 1;
        isDirty = true;
    }

    public boolean isDirty() {
        return isDirty;
    }

    public void setDirty(boolean dirty) {
        isDirty = dirty;
    }

    public boolean isForceCancel() {
        return forceCancel;
    }

    public void setForceCancel(boolean forceCancel) {
        this.forceCancel = forceCancel;
        this.isDirty = true;
    }

    @Override
    public String toString() {
        return "OrderState{" +
            "\n  venueOrder{" +
            "\n  orderStatuses=" + orderStatuses +
            "\n  quantity=" + quantity +
            "\n  filledQuantity=" + filledQuantity +
            "\n  remainingQuantity=" + remainingQuantity +
            "\n  avgPrice=" + avgPrice +
            "\n  avgPriceWithMarkup=" + avgPriceWithMarkup +
            "\n  totalFeeCharged=" + totalFeeCharged +
            "\n  totalPercFee=" + totalPercFee +
            "\n  fixedFeeCharged=" + fixedFeeCharged +
            "\n  errorCode='" + errorCode + '\'' +
            "\n  description='" + description + '\'' +
            "\n  executions=" + executions.size() +
            "\n  pendingCancelRequestId='" + pendingCancelRequestId + '\'' +
            "\n  pendingCancelRequesterId='" + pendingCancelRequesterId + '\'' +
            "\n  clientId='" + clientId + '\'' +
            "\n  sequenceNumber=" + sequenceNumber +
            "\n  forceCancel=" + forceCancel +
            "\n  oemsRequest{" +
            "\n" + oemsRequest.toString().indent(4) + "  }" +
            "\n  childOrderRequest{" +
            "\n" + childOrderRequest.toString().indent(4) + "  }" +
            "\n}";
    }

    BigDecimal recalculateAvgPrice() {
        List<Execution> executionList = getExecutions();
        if (executionList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return executionList.stream()
            .collect(MathUtils.averagingWeighted(Execution::getPrice, Execution::getQuantity));
    }

    BigDecimal recalculateAvgPriceWithMarkup() {
        List<Execution> executionList = getExecutions();
        if (executionList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return executionList.stream()
            .collect(MathUtils.averagingWeighted(Execution::getPriceWithMarkup, Execution::getQuantity));
    }

    private OemsRequest emptyOemsRequest() {
        return OemsRequest.newBuilder().build();
    }

}
