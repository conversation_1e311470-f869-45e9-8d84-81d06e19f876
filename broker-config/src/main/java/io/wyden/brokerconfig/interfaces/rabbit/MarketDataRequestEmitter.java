package io.wyden.brokerconfig.interfaces.rabbit;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.marketdata.MarketDataRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class MarketDataRequestEmitter {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataRequestEmitter.class);
    private static final String PRICING_SERVICE_ROUTING_KEY = "pricing-service";

    private final MeterRegistry meterRegistry;
    private final RabbitExchange<MarketDataRequest> mdRequestExchange;
    private final Tracing otlTracing;

    public MarketDataRequestEmitter(Telemetry telemetry, RabbitExchange<MarketDataRequest> mdRequestExchange) {
        this.mdRequestExchange = mdRequestExchange;
        this.meterRegistry = telemetry.getMeterRegistry();
        this.otlTracing = telemetry.getTracing();
    }

    public void emit(MarketDataRequest request) {
        LOGGER.info("Publishing market data request (routing key = {}): {}", PRICING_SERVICE_ROUTING_KEY, request);
        try (var ignored = otlTracing.createSpan("broker-config-service.md-request.produce", SpanKind.PRODUCER)) {
            mdRequestExchange.publish(request, PRICING_SERVICE_ROUTING_KEY);
            updateMetrics(request);
        }
    }

    private void updateMetrics(MarketDataRequest marketDataRequest) {
        try {
            Tags tags = Tags.of(
                "instrumentId", marketDataRequest.getInstrumentKey().getInstrumentId(),
                "venueAccount", marketDataRequest.getInstrumentKey().getVenueAccount(),
                "portfolioId", marketDataRequest.getPortfolioId(),
                "depth", String.valueOf(marketDataRequest.getMarketDepth())
            );
            meterRegistry.counter("wyden.broker-config-service.md-request.outgoing", tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
