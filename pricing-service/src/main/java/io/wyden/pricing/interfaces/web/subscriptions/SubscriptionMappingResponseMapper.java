package io.wyden.pricing.interfaces.web.subscriptions;

import io.wyden.published.pricing.ClientSideSubscriptionKey;

public class SubscriptionMappingResponseMapper {

    public static SubscriptionMappingResponse toDto(ClientSideSubscriptionKey clientSideSubscriptionKey, String streamId) {
        return new SubscriptionMappingResponse(clientSideSubscriptionKey.getInstrumentId(), clientSideSubscriptionKey.getPortfolioId(), streamId);
    }
}
