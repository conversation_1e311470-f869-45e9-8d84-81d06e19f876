package io.wyden.apiserver.fix.common.marketdata.infrastructure;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.apiserver.fix.common.marketdata.model.L1Event;
import io.wyden.apiserver.fix.common.marketdata.model.L2Event;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataDepth;
import io.wyden.apiserver.fix.common.marketdata.model.MarketDataParser;
import io.wyden.cloudutils.rabbitmq.queue.AutoAckMessageConsumer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.marketdata.Ask;
import io.wyden.published.marketdata.Bid;
import io.wyden.published.marketdata.BidAskQuote;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.Trade;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

public class MarketDataEventConsumers {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataEventConsumers.class);

    private final Sinks.Many<L1Event> l1Sink;
    private final Sinks.Many<L2Event> l2Sink;

    private final MeterRegistry meterRegistry;

    public MarketDataEventConsumers(Telemetry telemetry) {
        meterRegistry = telemetry.getMeterRegistry();
        l1Sink = Sinks.many().multicast().directBestEffort();
        l2Sink = Sinks.many().multicast().directBestEffort();
    }

    public AutoAckMessageConsumer<io.wyden.published.marketdata.MarketDataEvent> marketDataEventConsumer() {
        return (marketDataEvent, properties) -> {
            try {
                Sinks.EmitResult emitResult;

                LOGGER.trace("Consuming new MarketDataEvent: {}", marketDataEvent);

                MarketDataIdentifier identifier = marketDataEvent.getIdentifier();

                if (marketDataEvent.hasBid()) {
                    Bid bid = marketDataEvent.getBid();
                    updateMetrics(identifier.getInstrumentId(), MarketDataDepth.L1);
                    emitResult = l1Sink.tryEmitNext(MarketDataParser.parseL1Event(bid, identifier));
                } else if (marketDataEvent.hasAsk()) {
                    Ask ask = marketDataEvent.getAsk();
                    updateMetrics(identifier.getInstrumentId(), MarketDataDepth.L1);
                    emitResult = l1Sink.tryEmitNext(MarketDataParser.parseL1Event(ask, identifier));
                } else if (marketDataEvent.hasBidAskQuote()) {
                    BidAskQuote bidAskQuote = marketDataEvent.getBidAskQuote();
                    updateMetrics(identifier.getInstrumentId(), MarketDataDepth.L1);
                    emitResult = l1Sink.tryEmitNext(MarketDataParser.parseL1Event(bidAskQuote, identifier));
                } else if (marketDataEvent.hasTrade()) {
                    Trade trade = marketDataEvent.getTrade();
                    updateMetrics(identifier.getInstrumentId(), MarketDataDepth.L1);
                    emitResult = l1Sink.tryEmitNext(MarketDataParser.parseL1Event(trade, identifier));
                } else if (marketDataEvent.hasOrderBook()) {
                    OrderBook orderBook = marketDataEvent.getOrderBook();
                    updateMetrics(identifier.getInstrumentId(), MarketDataDepth.L2);
                    emitResult = l2Sink.tryEmitNext(MarketDataParser.parseL2Event(orderBook, identifier));
                } else {
                    emitResult = Sinks.EmitResult.FAIL_CANCELLED;
                }

                if (emitResult.isFailure()) {
                    if (emitResult == Sinks.EmitResult.FAIL_ZERO_SUBSCRIBER) {
                        LOGGER.trace("Failed to emit market data event of type {} to flux, result: {}, event: {}", marketDataEvent.getClass().getSimpleName(), emitResult, marketDataEvent);
                    } else {
                        LOGGER.warn("Failed to emit market data event of type {} to flux, result: {}, event: {}", marketDataEvent.getClass().getSimpleName(), emitResult, marketDataEvent);
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Exception when processing market data event", ex);
            }
        };
    }

    public Flux<L1Event> getL1Flux() {
        return l1Sink.asFlux()
            .doOnSubscribe(s -> LOGGER.info("L1Event flux subscribed"));
    }

    public Flux<L2Event> getL2Flux() {
        return l2Sink.asFlux()
            .doOnSubscribe(s -> LOGGER.info("L2Event flux subscribed"));
    }

    private void updateMetrics(String instrumentId, MarketDataDepth depth) {
        try {
            meterRegistry.counter("wyden.market-data.events-incoming", Tags.of("instrumentId", instrumentId, "depth", depth.name()))
                .increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
