package io.wyden.sor.utils;

import com.github.javafaker.Faker;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.*;
import org.jetbrains.annotations.NotNull;
import org.testcontainers.shaded.org.checkerframework.checker.units.qual.C;

import java.util.UUID;

import static io.wyden.sor.it.SORIntegrationTestBase.*;
import static io.wyden.sor.it.exchangeotcmix.SORMixIntegrationTestBase.BINANCE;
import static io.wyden.sor.it.exchangeotcmix.SORMixIntegrationTestBase.OTC_BROKER;
import static io.wyden.sor.it.otc.SOROtcIntegrationTestBase.B2C2;
import static io.wyden.sor.it.otc.SOROtcIntegrationTestBase.CF;

public class TestingData {

    private static final Faker FAKER = new Faker();

    // incoming from client:
    public final String orderId;
    public final String clientId;
    public final String symbol;
    public final String ethSymbol;
    public final String quantity;
    public final String halfQuantity;
    public final String quantityReplaced;
    public final String limit;
    public final String stop;
    public final String cancelRequestId;

    public final String executionPrice;
    public final OemsSide side;
    public final String expireTime;
    public final OemsTIF tif;

    public TestingData() {
        orderId = UUID.randomUUID().toString();
        clientId = "%s %s".formatted(FAKER.dog().breed(), FAKER.dog().name());
        symbol = BTCUSD_SYMBOL;
        ethSymbol = ETHUSD_SYMBOL;
        quantity = "10";
        halfQuantity = "5";
        quantityReplaced = "15";
        limit = "41000";
        stop = "39000";
        cancelRequestId = UUID.randomUUID().toString();
        executionPrice = "40000";
        side = OemsSide.BUY;
        expireTime = "20341212-12:12:12.222";
        tif = OemsTIF.GTD;
    }

    public OemsRequest clientSmartOrderRequest(OemsOrderType orderType) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, BITMEX, KRAKEN);
    }

    public OemsRequest clientSmartOrderRequestOtc(OemsOrderType orderType) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, B2C2, CF);
    }

    public OemsRequest clientSmartOrderRequestMix(OemsOrderType orderType) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, OTC_BROKER, BINANCE);
    }

    public OemsRequest clientSmartOrderRequestNoTradingConstraints(OemsOrderType orderType) {
        return getSmartOrderOemsRequestForOneVenue(orderType, quantity, ethSymbol, BITMEX);
    }

    public OemsRequest clientReplacingSmartOrderRequestOtc(OemsOrderType orderType, String replacedSmartOrderId) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, replacedSmartOrderId, B2C2, CF);
    }

    public OemsRequest clientReplacingSmartOrderRequestMix(OemsOrderType orderType, String replacedSmartOrderId) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, replacedSmartOrderId, OTC_BROKER, BINANCE);
    }

    public OemsRequest clientReplacingSmartOrderRequest(OemsOrderType orderType, String replacedSmartOrderId) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, replacedSmartOrderId, BITMEX, KRAKEN);
    }

    public OemsRequest clientReplacingSmartOrderRequestForOneVenueOtc(OemsOrderType orderType, String replacedSmartOrderId) {
        return getSmartOrderOemsRequestForOneVenue(orderType, quantity, symbol, replacedSmartOrderId, B2C2);
    }

    public OemsRequest clientReplacingSmartOrderRequestForOneVenueOtcMix(OemsOrderType orderType, String replacedSmartOrderId) {
        return getSmartOrderOemsRequestForOneVenue(orderType, quantity, symbol, replacedSmartOrderId, OTC_BROKER);
    }

    public OemsRequest clientReplacingSmartOrderRequestForOneVenue(OemsOrderType orderType, String replacedSmartOrderId) {
        return getSmartOrderOemsRequestForOneVenue(orderType, quantity, symbol, replacedSmartOrderId, BITMEX);
    }

    public OemsRequest clientCancelOrderRequest(OemsOrderType orderType) {
        return getCancelOrderOemsRequest(orderType, quantity, symbol, KRAKEN, BITMEX);
    }

    public OemsRequest clientCancelOrderRequestOtc(OemsOrderType orderType) {
        return getCancelOrderOemsRequest(orderType, quantity, symbol, B2C2, CF);
    }

    public OemsRequest clientCancelOrderRequestMix(OemsOrderType orderType) {
        return getCancelOrderOemsRequest(orderType, quantity, symbol, OTC_BROKER, BINANCE);
    }

    public OemsRequest clientSmartOrderRequest(OemsOrderType orderType, String symbol) {
        return getSmartOrderOemsRequest(orderType, quantity, symbol, BITMEX, KRAKEN);
    }

    @NotNull
    private OemsRequest getSmartOrderOemsRequest(OemsOrderType orderType, String quantity, String symbol, String replacedSmartOrderId, String candidate1, String candidate2) {
        OemsRequest smartOrderOemsRequest = getSmartOrderOemsRequest(orderType, quantity, symbol, candidate1, candidate2);
        return smartOrderOemsRequest.toBuilder()
                    .setOrderId(UUID.randomUUID().toString())
                    .setPreReplaceOrderId(replacedSmartOrderId)
                    .build();
    }

    @NotNull
    private OemsRequest getSmartOrderOemsRequestForOneVenue(OemsOrderType orderType, String quantity, String symbol, String replacedSmartOrderId, String candidate) {
        OemsRequest smartOrderOemsRequest = getSmartOrderOemsRequestForOneVenue(orderType, quantity, symbol, candidate);
        return smartOrderOemsRequest.toBuilder()
                .setOrderId(UUID.randomUUID().toString())
                .setPreReplaceOrderId(replacedSmartOrderId)
                .build();
    }

    private OemsRequest getSmartOrderOemsRequestForOneVenue(OemsOrderType orderType, String quantity, String symbol, String candidate) {
        Metadata metadata = Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .setRequesterId("ORDER_GATEWAY")
                .setSource("ORDER_GATEWAY")
                .setTarget("SOR")
                .setTargetType(Metadata.ServiceType.SOR)
                .build();

        OemsRequest.Builder builder = OemsRequest.newBuilder()
                .setMetadata(metadata)
                .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
                .setClientId(clientId)
                .setOrderId(orderId)
                .setClientRootOrderId(UUID.randomUUID().toString())
                .setRootOrderId(UUID.randomUUID().toString())
                .setOrderType(orderType)
                .setTif(OemsTIF.GTC)
                .setQuantity(quantity)
                .setSide(side)
                .setPtc(OemsRequest.OemsPTCStatus.NOT_REQUIRED)
                .setSymbol(symbol)
                .setInstrumentType(OemsInstrumentType.FOREX)
                .addVenueAccounts(candidate)
                .setMetadata(Metadata.newBuilder()
                        .setTargetType(Metadata.ServiceType.SOR)
                        .build());

        if (orderType == OemsOrderType.LIMIT || orderType == OemsOrderType.STOP_LIMIT) {
            builder.setPrice(limit);
        }

        if (orderType == OemsOrderType.STOP || orderType == OemsOrderType.STOP_LIMIT) {
            builder.setStopPrice(stop);
        }

        return builder.build();
    }

    @NotNull
    private OemsRequest getSmartOrderOemsRequest(OemsOrderType orderType, String quantity, String symbol, String candidate1, String candidate2) {
        Metadata metadata = Metadata.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setRequesterId("ORDER_GATEWAY")
            .setSource("ORDER_GATEWAY")
            .setTarget("SOR")
            .setTargetType(Metadata.ServiceType.SOR)
            .build();

        OemsRequest.Builder builder = OemsRequest.newBuilder()
            .setMetadata(metadata)
            .setRequestType(OemsRequest.OemsRequestType.ORDER_SINGLE)
            .setClientId(clientId)
            .setOrderId(orderId)
            .setClientRootOrderId(UUID.randomUUID().toString())
            .setRootOrderId(UUID.randomUUID().toString())
            .setOrderType(orderType)
            .setTif(OemsTIF.GTC)
            .setQuantity(quantity)
            .setSide(side)
            .setPtc(OemsRequest.OemsPTCStatus.NOT_REQUIRED)
            .setSymbol(symbol)
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setExpireTime(expireTime)
            .setTif(tif)
            .addVenueAccounts(candidate1)
            .addVenueAccounts(candidate2)
            .setMetadata(Metadata.newBuilder()
                .setTargetType(Metadata.ServiceType.SOR)
                .build());

        if (orderType == OemsOrderType.LIMIT || orderType == OemsOrderType.STOP_LIMIT) {
            builder.setPrice(limit);
        }

        if (orderType == OemsOrderType.STOP || orderType == OemsOrderType.STOP_LIMIT) {
            builder.setStopPrice(stop);
        }

        return builder.build();
    }

    @NotNull
    private OemsRequest getCancelOrderOemsRequest(OemsOrderType orderType, String quantity, String symbol, String candidate1, String candidate2) {
        Metadata metadata = Metadata.newBuilder()
            .setTarget("SOR")
            .setTargetType(Metadata.ServiceType.SOR)
            .build();

        OemsRequest.Builder builder = OemsRequest.newBuilder()
            .setMetadata(metadata)
            .setRequestType(OemsRequest.OemsRequestType.CANCEL)
            .setClientId(clientId)
            .setOrderId(orderId)
            .addVenueAccounts(candidate1)
            .addVenueAccounts(candidate2)
            .setOrderType(orderType)
            .setTif(OemsTIF.GTC)
            .setQuantity(quantity)
            .setSide(side)
            .setPtc(OemsRequest.OemsPTCStatus.NOT_REQUIRED)
            .setSymbol(symbol)
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setMetadata(Metadata.newBuilder()
                .setTargetType(Metadata.ServiceType.SOR)
                .setRequestId(cancelRequestId)
                .build());

        if (orderType == OemsOrderType.LIMIT || orderType == OemsOrderType.STOP_LIMIT) {
            builder.setPrice(limit);
        }

        if (orderType == OemsOrderType.STOP || orderType == OemsOrderType.STOP_LIMIT) {
            builder.setStopPrice(stop);
        }

        return builder.build();
    }
}
