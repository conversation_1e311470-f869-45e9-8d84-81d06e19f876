#  Wyden Exchange - Exchange Simulator

## Overview

**Description:**
Wyden Exchange offers a way to connect the Wyden platform to a paper trading exchange. 
This exchange can be configured to offer various instruments and market data from real crypto exchanges. This is thanks to integration with the cryptocurrency data provider CoinAPI. This integration provides real-like instruments and real-time market prices. In addition, the exchange's trading capability can be used to accept incoming orders that can be executed internally and mimic the behavior of a real exchange counterparty. Following this, the execution reports of the paper trades are forwarded to the connected counterparty.


The project is based on the already available Wyden connectivity and internal libraries. Including integration via the
[Generic Outbound FIX Connector](https://gitlab.wyden.io/development/algotrader-adapters/-/tree/develop/connector-generic-outbound),
which gives free seamless connection with Wyden platform through the existing connector. Then
the [Connectors API](https://gitlab.wyden.io/development/algotrader-api) is used internally by this project to easily
re-use the standard connectors, and to provide the data for this simulator. The obvious choices for connectors are the
ones which offer wide range of aggregated market data for many instruments from multiple popular venues,
like [CoinAPI Connector](https://gitlab.wyden.io/development/algotrader-adapters/-/tree/develop/connector-coinapi).


**Owner/Team:**
- Team/Name: `Blue Team`

## Architecture

### High-Level Architecture
![Architecture diagram](high-overview-architecture.png "High overview of Wyden Exchange architecture")

[//]: # (https://mermaid.live/edit)
[//]: # (```mermaid)
[//]: # (graph LR)
[//]: # (    subgraph platform[Wyden Platform])
[//]: # (        direction TB)
[//]: # (        wydenapi&#40;Connectors API&#41; --- outbound[Outbound FIX])
[//]: # (    end)
[//]: # ()
[//]: # (    subgraph exchange[Wyden Exchange])
[//]: # (        direction TB)
[//]: # (        core&#40;&#40;Exchange Simulator&#41;&#41; --- db[&#40;Orders&#41;])
[//]: # (        exchapi&#40;Connectors API&#41; --- coinapi[CoinAPI])
[//]: # (        exchapi --->|RD & MD| core)
[//]: # (        core --- server[FIX server])
[//]: # (    end)
[//]: # (    exchange <--->|FIX| platform)
[//]: # (```)

### Data Flow

Market data flow with synthetic mode disabled:
```mermaid
sequenceDiagram
participant FIX
participant Exchange-Simulator
participant Connector-API

FIX->>Exchange-Simulator : subscribe
Exchange-Simulator->>Connector-API : subscribe
Connector-API-->>Exchange-Simulator : market data
Exchange-Simulator->>Exchange-Simulator : put data in cache
Exchange-Simulator->>FIX : market data

```

Market data flow with synthetic mode enabled:
```mermaid
sequenceDiagram
participant Client
participant FIX
participant Exchange-Simulator
participant Connector-API

Note over Client,Connector-API: Enable synthetic mode per session (fixedDelayInMillis > 0)
FIX->>Exchange-Simulator : subscribe instrument
Client->>Exchange-Simulator : enable synthetic mode
Exchange-Simulator ->> Exchange-Simulator : clear cache
Client->>Exchange-Simulator : set synthetic data (fixedDelayInMillis > 0)
Exchange-Simulator ->> Exchange-Simulator : schedule sending market data
Exchange-Simulator ->> FIX : send market data
Note over Exchange-Simulator : Send data every fixedDelayInMillis
Exchange-Simulator ->> Exchange-Simulator : schedule sending market data
Exchange-Simulator ->> FIX : send market data

Note over Client,Connector-API: Enable synthetic mode per session (fixedDelayInMillis = 0)
FIX->>Exchange-Simulator : subscribe instrument
Client->>Exchange-Simulator : enable synthetic mode
Exchange-Simulator ->> Exchange-Simulator : clear cache
Client->>Exchange-Simulator : set synthetic data (fixedDelayInMillis = 0)
Exchange-Simulator ->> FIX : send market data only once
Exchange-Simulator ->> Exchange-Simulator : disable scheduler for selected instrument
```

Trade flow with synthetic mode disabled:
```mermaid
sequenceDiagram
participant Client
participant FIX
participant Exchange-Simulator

Note over Client,Exchange-Simulator: Fill order at once
FIX->>Exchange-Simulator : NewSingleOrder
Exchange-Simulator->>FIX : ExecutionReport
Exchange-Simulator->>Exchange-Simulator : process order at scheduled trade time event
Exchange-Simulator->>FIX : ExecutionReport(filled)


Note over Client,Exchange-Simulator: Fill order with partial fills count > execution.maxFillsCount = 2 
FIX->>Exchange-Simulator : NewSingleOrder
Exchange-Simulator->>FIX : ExecutionReport
Exchange-Simulator->>Exchange-Simulator : process order by scheduled trade time event
Exchange-Simulator->>FIX : ExecutionReport(1st partial fill)
Exchange-Simulator->>Exchange-Simulator : process order by scheduled trade time event
Exchange-Simulator->>FIX : ExecutionReport(2nd partial fill)
Note over Exchange-Simulator : execution.maxFillsCount reached
Exchange-Simulator->>Exchange-Simulator : override trade logic and fill order 
Exchange-Simulator->>FIX : ExecutionReport(filled)

Note over Client,Exchange-Simulator: Fill order when last market data is too old
FIX->>Exchange-Simulator : NewSingleOrder
Exchange-Simulator->>FIX : ExecutionReport
Note over Exchange-Simulator: Market data gap
Exchange-Simulator->>Exchange-Simulator : process order by scheduled trade time event with market data from cache
Exchange-Simulator->>FIX : ExecutionReport(filled)
```

Trade flow with synthetic mode enabled:

In synthetic mode, the market data cache is not used. 
Consequently, if no order book is sent to the exchange simulator, the trade logic will never be overridden and no trades will be executed.

```mermaid
sequenceDiagram
participant Client
participant FIX
participant Exchange-Simulator

FIX->>Exchange-Simulator : subscribe instrument
Client->>Exchange-Simulator : enable synthetic mode
Exchange-Simulator ->> Exchange-Simulator : clear cache
FIX->>Exchange-Simulator : NewSingleOrder(1BTC)
Exchange-Simulator->>FIX : ExecutionReport
Client->>Exchange-Simulator : set synthetic OrderBook (0.1BTC, fixedDelayInMillis = 0)
Exchange-Simulator->>Exchange-Simulator : process order by scheduled trade time event
Exchange-Simulator->>FIX : ExecutionReport(patrial fill 0.1BTC)
Note over Exchange-Simulator: Wait for the new order book.
Client->>Exchange-Simulator : set synthetic OrderBook (0.2BTC, fixedDelayInMillis = 0)
Exchange-Simulator->>Exchange-Simulator : process order by scheduled trade time event
Exchange-Simulator->>FIX : ExecutionReport(patrial fill 0.2BTC)
Note over Exchange-Simulator: Wait for the new order book.
```


### Domain and API

Domain objects:
- `NONE`

## Functional Description

- **Primary Responsibilities:** Exchange simulation with real (from 3rd party exchange) or synthetic sata
  - lifelike reference data streaming: example instruments instrumentId - `BTCUSDT`, description - `BINANCE_SPOT_BTC_USDT@PickYourOwnNameOfTheExchange`, adapter
        ticker id - `BINANCE_SPOT_BTC_USDT`
  - lifelike market data streaming
  - simulated trading with Market orders, and Limit orders
      - both order types supports IOC and GTC Time In Force (TIF) settings
      - orders can be submitted and later canceled if they are not executed immediately
      - orders are filled based on the incoming market data
      - there is no concept of balances
    
- **APIs Exposed:**
    - Instrument REST - Enable and manage synthetic data transmission.  

- **Event-Driven Behavior:**
    - `NONE`
- **Dependencies:**
    - Hard dependency on: 3rd party exchange (by default CoinAPI)
    - Upstream: `NONE`
  
- **Test Harness:**  
  This project can act as a testing venue for our Generic Outbound FIX Connector. The connector is set up according to the above configuration.

## Technical Description

- **Service Ports:**
    - `HTTP: 10000`
    - `FIX session acceptor port: 10001`


- **Configuration:**

  - Market data connectivity properties  
    `referenceData.exchangeFilterRegex` - CoinAPI offers variety of exchanges, this property allows to filter the final         set of instruments available via Wyden Exchange
          - All **Binance** instruments offering `BINANCE|BINANCEFTS|BINANCEFTSC|BINANCEOPT`
          - All **Okx** instruments offering `OKEX`  
    `referenceData.maxNumOfInstruments` - limits number of instruments, offers only first N instruments  
    `referenceData.onlyActiveInstruments` - filters the non-tradable instruments, and expired instruments

  - Connector configuration  
    `coinapi.apiKey` - sets the API key

  - FIX server properties  
    `fix.server.store` - path of FIX sessions store
    `fix.server.settings` - path to the detailed FIX server configuration (QuickFIX/J)
    `fix.server.port` - specifies the server's acceptor port
    messages are stored in build/data/log

  - Exchange behaviour properties  
    `execution.iocOrderTimeoutSeconds` - seconds to wait till order with IOC Time In Force setting is canceled  
    `execution.maxMarketDataAgeSeconds` - specifies max duration in seconds between receiving the market data events and
  using them to generate the simulated fills of processed orders  
    `execution.maxFillsCount` - maximal number of executions per order, when the maximum is reached the remaining quantity
  of the order is filled regardless the size of the currently used market price level  
  FIX Logon message property `fillsCount` - overrides the logic of simulated executions which use real market data,
  instead when the count is set all the orders are executed with the number of fills specified, at the market price if
  available, or at a price equal to the limit price (only for limit and stop limit orders), or at a price equal to
  stop price (only for stop orders) or at a random price. This value has the following characteristics:
    - is stored in RawData<96> field as `property-name` and `value` pair, used delimiter `_`
    - can be set per session, each session can have a different number of set fills which are generated for its orders
    - value needs to be int, e.g. `fillsCount_10`  

  - Example of config overriders for connecting to the exchange  
    The following generic connector settings need to be set:
        ```
        "genericconnector.exchangeName" : "PickYourOwnNameOfTheExchange",
    
        "genericconnector.fix.session.md.senderCompId" : "DEMO",
        "genericconnector.fix.session.md.targetCompId" : "WYDEN_EXCHANGE_MARKET_DATA",
        "genericconnector.fix.session.md.socketConnectHost" : "exchange-simulator.wyden.io",
        "genericconnector.fix.session.md.socketConnectPort" : "10001",
        "genericconnector.fix.session.md.socketUseSSL" : "N",
    
        "genericconnector.fix.session.t.senderCompId" : "DEMO_TRADING",
        "genericconnector.fix.session.t.targetCompId" : "WYDEN_EXCHANGE_TRADING",
        "genericconnector.fix.session.t.socketConnectHost" : "exchange-simulator.wyden.io",
        "genericconnector.fix.session.t.socketConnectPort" : "10001",
        "genericconnector.fix.session.t.socketUseSSL" : "N",
    
        "genericconnector.requestsSupported" : "MarketOrderDTO=Submit&Cancel,LimitOrderDTO=Submit&Cancel",
        "genericconnector.securityTypesSupported" : "ForexDTO,FutureDTO,OptionDTO,PerpetualSwapDTO,IndexDTO",
        "genericconnector.tifsSupported" : "MarketOrderDTO=IOC&GTC,LimitOrderDTO=IOC&GTC",
        "genericconnector.postOnlyOrderTypesAndTifsSupported" : "",
        "genericconnector.balancesSupported" : "false",
        ```
    
## Service Dashboard

- **Dashboard URL:**
    - local env: http://localhost:10000/swagger-ui/index.html#/
    - kubernetes env: firstly, the port needs to be redirected:  
      `kubectl port-forward -n <NAMESPACE> deployment/exchange-simulator 10000:10000 &`  
      and later use url: http://localhost:10000/swagger-ui/index.html

## Deployment & Operations

### HA/DR Mode
`SRM` - Service is not Resilient nor Highly Available. 
Can work only in Single Replica Mode. 
Service can be restarted, while messages are accumulating in queues and continue working after restart (no app operation downtime)

### Resource Requests & Limits
| Resource  | Request  | Limit    |
|-----------|----------|----------|
| CPU       | `100m`  | `300m`   |
| Memory    | `1024Mi` | `2048Mi` |

### Deployment Notes
- **Helm Chart:** `<HELM_CHART_NAME>`
- **Kubernetes Namespace:** `<NAMESPACE>`
- **Secrets Required:** `NONE`
- **Persistent Volume Claims (PVCs):** `NONE`
- **Rolling Update Strategy:** `NONE`

## Building the Service

### Prerequisites
- Install `Gradle`
- Clone the repository: `ssh://*******************:2222/atcloud/exchange-simulator.git`

### Build Commands
- Build the application: `/gradlew assemble`
- Run tests: `/gradlew check`
- Create Docker image: `docker build -t exchange-simulator .`

## Monitoring & Logging

- **Metrics:**
    - Prometheus Endpoint: `<METRICS_ENDPOINT>`
    - Key Metrics: `<CPU Usage, Latency>`

- **Logging:**
    - Log Format: `<JSON/Plain>`
    - Log Storage: `<ELK/Splunk/Loki>`
    - Log Levels: `<DEBUG/INFO/WARN/ERROR>`

## Troubleshooting & FAQs

### Common/known Issues
- no authentication implemented, all session logon requests are satisfied if the target comp id is set correctly
- SSL not supported

### Xwiki

[Wyden Exchange ‒ Exchange Simulator](https://xwiki.wyden.io/bin/view/Wyden%20Exchange%20-%20Exchange%20Simulator)
