package io.wyden.clob.gateway.service.fsm;

import io.wyden.published.oems.OemsOrderStatus;

/**
 * Terminal state, doesn't accept any messages, never transits to another state.
 * Order moves to Cancelled state when order in PendingCancel state receives ExecutionReport
 * with matching pendingCancelId and ExecType == CANCELED
 */
class StatusCancelled extends OrderStatus {

    private static final OrderStatus instance = new StatusCancelled();

    static OrderStatus create() {
        return instance;
    }

    @Override
    Precedence getPrecedence() {
        return Precedence.CANCELLED;
    }

    @Override
    boolean isTerminal() {
        return true;
    }

    @Override
    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.STATUS_CANCELED;
    }
}
