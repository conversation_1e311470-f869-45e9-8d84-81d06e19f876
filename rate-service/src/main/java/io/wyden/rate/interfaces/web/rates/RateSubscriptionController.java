package io.wyden.rate.interfaces.web.rates;

import io.swagger.v3.oas.annotations.Hidden;
import io.wyden.published.rate.RateSubscription;
import io.wyden.published.rate.RateSubscriptionCreateRequest;
import io.wyden.published.rate.RateSubscriptionKey;
import io.wyden.published.rate.RateSubscriptionList;
import io.wyden.rate.domain.map.RateSubscriptionMapConfig;
import io.wyden.rate.service.RateSubscriptionManagementService;
import io.wyden.rate.service.RateSubscriptionRepository;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

@Hidden
@RestController
@RequestMapping("/subscriptions")
public class RateSubscriptionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RateSubscriptionController.class);

    private final RateSubscriptionRepository rateSubscriptionRepository;
    private final RateSubscriptionManagementService rateSubscriptionManagementService;

    public RateSubscriptionController(RateSubscriptionRepository rateSubscriptionRepository,
                                      RateSubscriptionManagementService rateSubscriptionManagementService) {
        this.rateSubscriptionRepository = rateSubscriptionRepository;
        this.rateSubscriptionManagementService = rateSubscriptionManagementService;
    }

    @PostMapping
    public ResponseEntity<Void> create(@RequestBody RateSubscriptionCreateRequest request) {
        request = toUpperCase(request);
        LOGGER.debug("Creating rate subscription for baseCurrency: {}, quoteCurrency: {}", request.getBaseCurrency(), request.getQuoteCurrency());
        rateSubscriptionManagementService.create(request.getBaseCurrency(), request.getQuoteCurrency());
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    @GetMapping
    public RateSubscriptionList findAll() {
        LOGGER.debug("Getting all rate subscriptions");
        Collection<RateSubscription> rateSubscriptions = rateSubscriptionRepository.findAll();
        return RateSubscriptionList.newBuilder()
            .addAllRateSubscription(rateSubscriptions)
            .build();
    }

    @DeleteMapping("/{key}")
    public ResponseEntity<Void> delete(@PathVariable String key) {
        RateSubscriptionKey rateSubscriptionKey = toUpperCase(RateSubscriptionMapConfig.fromKey(key));
        LOGGER.debug("Deleting rate subscription for baseCurrency: {}, quoteCurrency: {}", rateSubscriptionKey.getBaseCurrency(), rateSubscriptionKey.getQuoteCurrency());
        rateSubscriptionManagementService.delete(rateSubscriptionKey.getBaseCurrency(), rateSubscriptionKey.getQuoteCurrency());
        return ResponseEntity.noContent().build();
    }

    private static @NotNull RateSubscriptionKey toUpperCase(RateSubscriptionKey rateSubscriptionKey) {
        return rateSubscriptionKey.toBuilder()
            .setBaseCurrency(rateSubscriptionKey.getBaseCurrency().toUpperCase())
            .setQuoteCurrency(rateSubscriptionKey.getQuoteCurrency().toUpperCase())
            .build();
    }

    private static @NotNull RateSubscriptionCreateRequest toUpperCase(RateSubscriptionCreateRequest request) {
        return request.toBuilder()
            .setBaseCurrency(request.getBaseCurrency().toUpperCase())
            .setQuoteCurrency(request.getQuoteCurrency().toUpperCase())
            .build();
    }
}


