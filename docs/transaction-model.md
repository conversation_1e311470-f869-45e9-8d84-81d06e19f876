# Transaction model

## Transaction fields and their descriptions

### Base Class: Transaction

| Field Name          | Description                                                   |
|---------------------|---------------------------------------------------------------|
| uuid                | Unique identifier of the transaction                          |
| reservationRef      | Reference identifier of the related reservation               |
| dateTime            | Date of transaction                                           |
| executionId         | Internal ID of the execution                                  |
| venueExecutionId    | External/custody/venue ID of the execution                    |
| leftReference       | Portfolio / Account reference (depending on transaction type) |
| rightReference      | Portfolio / Account reference (depending on transaction type) |
| leftReferencePrice  | Rate of transaction currency to left reference                |
| rightReferencePrice | Rate of transaction currency to right reference               |
| description         | Arbitrary description of the transaction                      |
| settled             | Settlement status flag                                        |
| settledDateTime     | Time when transaction was settled                             |
| isLive              | Live status flag                                              |
| settlementType      | Type of settlement (INSTANT or DEFERRED)                      |
| transactionType     | Type of transaction                                           |
| fees                | Collection of transaction fees                                |

### Trade Class

| Field Name            | Description                                                               |
|-----------------------|---------------------------------------------------------------------------|
| quantity              | The quantity that was executed                                            |
| leavesQuantity        | The quantity that is still pending                                        |
| price                 | The unit price at which the trade was executed                            |
| currency              | The transaction/quote currency                                            |
| intOrderId            | Internal order identifier generated by the connector                      |
| extOrderId            | External order identifier assigned by the trading venue/exchange          |
| orderId               | Internal OEMS order identifier                                            |
| parentOrderId         | Direct parent order identifier                                            |
| rootOrderId           | The execution ID of the parent/underlying trade                           |
| clientRootOrderId     | References the original order ID from the client space                    |
| underlyingExecutionId | The execution ID of the parent/underlying trade that triggered this trade |
| rootExecutionId       | The execution ID of the original/root trade in the execution chain        |
| execType              | Execution type of the trade                                               |

### CashTrade Class

| Field Name   | Description                                            |
|--------------|--------------------------------------------------------|
| baseCurrency | The currency that represents the quantity of the trade |

### AssetTrade Class

| Field Name | Description           |
|------------|-----------------------|
| security   | The Instrument traded |

### Payment Class

| Field Name   | Description                                          |
|--------------|------------------------------------------------------|
| currency     | The currency of the payment                          |
| quantity     | The quantity that was paid                           |
| portfolio    | The portfolio the payment was received or sent       |
| account      | The custody account the payment was received or sent |
| feeAccount   | The account payment fees will be charged to          |
| feePortfolio | The portfolio payment fees will be charged to        |

### Transfer Class

| Field Name    | Description                                             |
|---------------|---------------------------------------------------------|
| quantity      | The quantity that is transferred                        |
| intTransferId | Internal ID of the corresponding transfer order         |
| extTransferId | External/custody ID of the corresponding transfer order |

### CashTransfer Class

| Field Name | Description              |
|------------|--------------------------|
| currency   | The currency transferred |

### AssetTransfer Class

| Field Name    | Description                                        |
|---------------|----------------------------------------------------|
| security      | The Instrument transferred                         |
| fromPortfolio | The portfolio from which the transfer is initiated |
| toPortfolio   | The portfolio into which the transfer arrives      |

### Fee Class

| Field Name | Description                              |
|------------|------------------------------------------|
| quantity   | The fee amount                           |
| currency   | The fee currency                         |
| portfolio  | The portfolio the fee will be charged to |
| account    | The account the fee will be charged to   |

## Transaction methods and their descriptions:

### Transaction (Base Class)

| Method                  | Description                                       |
|-------------------------|---------------------------------------------------|
| `getUuid()`             | Returns the unique identifier for the transaction |
| `getUpdatedAt()`        | Returns the timestamp of the last update          |
| `getExecutionId()`      | Returns the execution identifier                  |
| `getDescription()`      | Returns the transaction description               |
| `getVenueExecutionId()` | Returns the venue-specific execution identifier   |
| `getDateTime()`         | Returns the transaction date and time             |
| `getSettled()`          | Returns the settlement status                     |
| `getSettledDateTime()`  | Returns the settlement date and time              |

### Trade (extends Transaction)

| Method                       | Description                                                              |
|------------------------------|--------------------------------------------------------------------------|
| `getQuantity()`              | Returns the quantity that was executed                                   |
| `getLeavesQuantity()`        | Returns the quantity that is still pending                               |
| `getPrice()`                 | Returns the unit price at which the trade was executed                   |
| `getCurrency()`              | Returns the transaction/quote currency                                   |
| `getIntOrderId()`            | Returns internal order identifier generated by the connector             |
| `getExtOrderId()`            | Returns external order identifier assigned by the trading venue/exchange |
| `getOrderId()`               | Returns internal OEMS order identifier                                   |
| `getParentOrderId()`         | Returns direct parent order identifier                                   |
| `getRootOrderId()`           | Returns the root order identifier in the order chain                     |
| `getUnderlyingExecutionId()` | Returns the execution ID of the parent/underlying trade                  |
| `getRootExecutionId()`       | Returns the execution ID of the original/root trade                      |
| `getClientRootOrderId()`     | Returns the original order ID from the client space                      |

### CashTrade (extends Trade)

| Method                  | Description                                                     |
|-------------------------|-----------------------------------------------------------------|
| `getBaseCurrency()`     | Returns the currency that represents the quantity of the trade  |
| `getTradeValue()`       | Returns the value of the trade in terms of quote currency       |
| `getLeg1EntryType()`    | Returns type of the ledger entry for base currency booking      |
| `getLeg2EntryType()`    | Returns type of the ledger entry for quote currency booking     |
| `getBookingEntries()`   | Retrieves the booking entries and release entries for the trade |
| `getRemainingEntries()` | Retrieves the remaining ledger entries for a trade              |

### AssetTrade (extends Trade)

| Method             | Description                                      |
|--------------------|--------------------------------------------------|
| `getSecurity()`    | Returns the Instrument traded                    |
| `getInstruments()` | Returns set of instruments involved in the trade |

### ClientCashTrade (extends CashTrade)

| Method                                            | Description                                                      |
|---------------------------------------------------|------------------------------------------------------------------|
| `getPortfolio()`                                  | Returns the (client) portfolio into which the trade is executed  |
| `getCounterPortfolio()`                           | Returns the (bank) portfolio against which the trade is executed |
| `getBaseCurrencyToPortfolioCurrencyRate()`        | Returns trade base currency to portfolio currency rate           |
| `getBaseCurrencyToCounterPortfolioCurrencyRate()` | Returns base currency to counter portfolio currency rate         |
| `doSnapshot()`                                    | Creates a snapshot of the current trade state                    |

### StreetCashTrade (extends CashTrade)

| Method                                     | Description                                                |
|--------------------------------------------|------------------------------------------------------------|
| `getPortfolio()`                           | Returns the portfolio into which the trade is executed     |
| `getAccount()`                             | Returns the venue account into which the trade is executed |
| `getBaseCurrencyToPortfolioCurrencyRate()` | Returns trade base currency to portfolio currency rate     |
| `getBaseCurrencyToAccountCurrencyRate()`   | Returns base currency to account currency rate             |
| `doSnapshot()`                             | Creates a snapshot of the current trade state              |

### ClientAssetTrade (extends AssetTrade)

| Method                  | Description                                                      |
|-------------------------|------------------------------------------------------------------|
| `getPortfolio()`        | Returns the (client) portfolio into which the trade is executed  |
| `getCounterPortfolio()` | Returns the (bank) portfolio against which the trade is executed |
| `doSnapshot()`          | Creates a snapshot of the current trade state                    |

### StreetAssetTrade (extends AssetTrade)

| Method           | Description                                                |
|------------------|------------------------------------------------------------|
| `getPortfolio()` | Returns the portfolio into which the trade is executed     |
| `getAccount()`   | Returns the venue account into which the trade is executed |
| `doSnapshot()`   | Creates a snapshot of the current trade state              |

### Transfer (extends Transaction)

| Method               | Description                                                     |
|----------------------|-----------------------------------------------------------------|
| `getQuantity()`      | Returns the quantity that is transferred                        |
| `getIntTransferId()` | Returns internal ID of the corresponding transfer order         |
| `getExtTransferId()` | Returns external/custody ID of the corresponding transfer order |

### CashTransfer (extends Transfer)

| Method             | Description                                                             |
|--------------------|-------------------------------------------------------------------------|
| `getCurrency()`    | Returns the currency being transferred                                  |
| `getInstruments()` | Returns set of instruments (currency in this case) involved in transfer |

### PortfolioCashTransfer (extends CashTransfer)

| Method                    | Description                                                |
|---------------------------|------------------------------------------------------------|
| `getFromPortfolio()`      | Returns the portfolio from which the transfer is initiated |
| `getToPortfolio()`        | Returns the portfolio into which the transfer arrives      |
| `getFeePortfolio()`       | Returns the portfolio where transfer fees will be charged  |
| `getFromPortfolioPrice()` | Returns the price in from-portfolio currency               |
| `getToPortfolioPrice()`   | Returns the price in to-portfolio currency                 |

### AccountCashTransfer (extends CashTransfer)

| Method                  | Description                                                      |
|-------------------------|------------------------------------------------------------------|
| `getFromAccount()`      | Returns the custody account from which the transfer is initiated |
| `getToAccount()`        | Returns the custody account into which the transfer arrives      |
| `getFeeAccount()`       | Returns the account where transfer fees will be charged          |
| `getFeePortfolio()`     | Returns the portfolio where transfer fees will be charged        |
| `getFromAccountPrice()` | Returns the price in from-account currency                       |
| `getToAccountPrice()`   | Returns the price in to-account currency                         |

### AssetTransfer (extends Transfer)

| Method               | Description                                                |
|----------------------|------------------------------------------------------------|
| `getInstruments()`   | Returns set containing the security being transferred      |
| `getFromPortfolio()` | Returns the portfolio from which the transfer is initiated |
| `getToPortfolio()`   | Returns the portfolio into which the transfer arrives      |

### Payment / Deposit / Withdrawal (extends Transaction)

| Method                | Description                                                          |
|-----------------------|----------------------------------------------------------------------|
| `getCurrency()`       | Returns the currency of the payment                                  |
| `getQuantity()`       | Returns the quantity that was paid                                   |
| `getPortfolio()`      | Returns the portfolio the payment was received or sent from/to       |
| `getAccount()`        | Returns the custody account the payment was received or sent from/to |
| `getFeeAccount()`     | Returns the account where payment fees will be charged               |
| `getFeePortfolio()`   | Returns the portfolio where payment fees will be charged             |
| `getPortfolioPrice()` | Returns the price in portfolio currency                              |
| `getAccountPrice()`   | Returns the price in account currency                                |

### Fee (extends Transaction)

| Method                | Description                                      |
|-----------------------|--------------------------------------------------|
| `getFeeAmount()`      | Returns the fee amount                           |
| `getFeeCurrency()`    | Returns the currency of the fee                  |
| `getPortfolio()`      | Returns the portfolio the fee will be charged to |
| `getAccount()`        | Returns the account the fee will be charged to   |
| `getPortfolioPrice()` | Returns the price in portfolio currency          |
| `getAccountPrice()`   | Returns the price in account currency            |
