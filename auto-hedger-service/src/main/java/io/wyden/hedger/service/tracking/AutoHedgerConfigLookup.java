package io.wyden.hedger.service.tracking;

import com.hazelcast.map.IMap;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.hedger.model.AutoHedgerConfig;
import io.wyden.hedger.model.AutoHedgerKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;

import java.util.Optional;

public abstract class AutoHedgerConfigLookup {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoHedgerConfigLookup.class);

    protected final Tracing otlTracing;
    protected final IMap<AutoHedgerKey, AutoHedgerConfig> autoHedgerConfigMap;

    protected AutoHedgerConfigLookup(IMap<AutoHedgerKey, AutoHedgerConfig> autoHedgerConfigMap, Tracing otlTracing) {
        this.autoHedgerConfigMap = autoHedgerConfigMap;
        this.otlTracing = otlTracing;
    }

    public int size() {
        return autoHedgerConfigMap.size();
    }

    @Nullable
    public AutoHedgerConfig find(AutoHedgerKey autoHedgerKey) {
        try (var ignored = otlTracing.createSpan("autohedgerconfiglookup.findByOrderId", SpanKind.CLIENT)) {
            return findInner(autoHedgerKey);
        }
    }

    private AutoHedgerConfig findInner(AutoHedgerKey autoHedgerKey) {
        AutoHedgerConfig autoHedgerConfig = Optional.ofNullable(autoHedgerKey)
            .map(autoHedgerConfigMap::get)
            .orElse(null);
        LOGGER.trace("Retrieved AutoHedgerConfig for autoHedgerKey={}\n{}", autoHedgerKey, autoHedgerConfig);
        return autoHedgerConfig;
    }
}
