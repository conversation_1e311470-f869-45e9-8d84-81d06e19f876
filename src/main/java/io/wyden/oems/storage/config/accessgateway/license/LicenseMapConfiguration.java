package io.wyden.oems.storage.config.accessgateway.license;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.accessgateway.domain.license.LicenseMapConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.EphemeralMapConfigBean;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.web.StringLicenseStateMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LicenseMapConfiguration {

    @Bean
    HazelcastMapConfigBean licenseMapConfig() {
        HazelcastMapConfig hazelcastMapConfig = new LicenseMapConfig();
        return new EphemeralMapConfigBean(hazelcastMapConfig);
    }

    @Bean
    WebAccess licenseMapWebAccess(HazelcastInstance hazelcast) {
        HazelcastMapConfig hazelcastMapConfig = new LicenseMapConfig();
        return new StringLicenseStateMapWebAccess(hazelcast, hazelcastMapConfig, null);
    }
}
