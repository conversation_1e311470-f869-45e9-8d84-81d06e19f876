package io.wyden.accessgateway.client.permission;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Nullable;

import static org.apache.commons.lang3.StringUtils.isBlank;

// TODO: Clash with protobuf permission
public enum Permission {
    WALLET_CREATE(Resources.WALLET, Scopes.CREATE, true, false),
    WALLET_READ(Resources.WALLET, Scopes.READ, true, false),
    WALLET_MANAGE(Resources.WALLET, Scopes.MANAGE, true, false),
    WALLET_TRADE(Resources.WALLET, Scopes.TRADE, true, false),
    WALLET_NOSTRO_CREATE(Resources.WALLET_NOSTRO, Scopes.CREATE, true, false),
    WALLET_NOSTRO_MANAGE(Resources.WALLET_NOSTRO, Scopes.MANAGE, true, false),
    WALLET_NOSTRO_READ(Resources.WALLET_NOSTRO, Scopes.READ, true, false),
    WALLET_NOSTRO_TRADE(Resources.WALLET_NOSTRO, Scopes.TRADE, true, false),
    WALLET_VOSTRO_CREATE(Resources.WALLET_VOSTRO, Scopes.CREATE, true, false),
    WALLET_VOSTRO_MANAGE(Resources.WALLET_VOSTRO, Scopes.MANAGE, true, false),
    WALLET_VOSTRO_READ(Resources.WALLET_VOSTRO, Scopes.READ, true, false),
    WALLET_VOSTRO_TRADE(Resources.WALLET_VOSTRO, Scopes.TRADE, true, false),

    CLIENT_INSTRUMENT_CREATE(Resources.CLIENT_INSTRUMENT, Scopes.CREATE, true, false),
    CLIENT_INSTRUMENT_READ(Resources.CLIENT_INSTRUMENT, Scopes.READ, true, false),
    CLIENT_INSTRUMENT_MANAGE(Resources.CLIENT_INSTRUMENT, Scopes.MANAGE, true, false),
    API_KEY_CREATE(Resources.API_KEY, Scopes.CREATE, true, false),
    API_KEY_MANAGE(Resources.API_KEY, Scopes.MANAGE, true, false),
    CLOB_INSTRUMENT_CREATE(Resources.CLOB_INSTRUMENT, Scopes.CREATE, true, false),
    CLOB_INSTRUMENT_MANAGE(Resources.CLOB_INSTRUMENT, Scopes.MANAGE, true, false),
    RISK_MANAGE(Resources.RISK, Scopes.MANAGE, true, false),
    RISK_READ(Resources.RISK, Scopes.READ, true, false),
    BROKER_CONFIG_READ(Resources.BROKER_CONFIG, Scopes.READ, true, false),
    BROKER_CONFIG_MANAGE(Resources.BROKER_CONFIG, Scopes.MANAGE, true, false),
    CURRENCY_MANAGE(Resources.CURRENCY, Scopes.MANAGE, true, false),

    PORTFOLIO_CREATE(Resources.PORTFOLIO, Scopes.CREATE, true, false),
    PORTFOLIO_MANAGE(Resources.PORTFOLIO, Scopes.MANAGE, true, true),
    PORTFOLIO_TRADE(Resources.PORTFOLIO, Scopes.TRADE, true, true),
    PORTFOLIO_READ(Resources.PORTFOLIO, Scopes.READ, true, true),

    PORTFOLIO_NOSTRO_CREATE(Resources.PORTFOLIO_NOSTRO, Scopes.CREATE, true, false),
    PORTFOLIO_NOSTRO_MANAGE(Resources.PORTFOLIO_NOSTRO, Scopes.MANAGE, true, false),
    PORTFOLIO_NOSTRO_TRADE(Resources.PORTFOLIO_NOSTRO, Scopes.TRADE, true, false),
    PORTFOLIO_NOSTRO_READ(Resources.PORTFOLIO_NOSTRO, Scopes.READ, true, false),
    PORTFOLIO_VOSTRO_CREATE(Resources.PORTFOLIO_VOSTRO, Scopes.CREATE, true, false),
    PORTFOLIO_VOSTRO_MANAGE(Resources.PORTFOLIO_VOSTRO, Scopes.MANAGE, true, false),
    PORTFOLIO_VOSTRO_TRADE(Resources.PORTFOLIO_VOSTRO, Scopes.TRADE, true, false),
    PORTFOLIO_VOSTRO_READ(Resources.PORTFOLIO_VOSTRO, Scopes.READ, true, false),

    SETTLEMENT_READ(Resources.SETTLEMENT, Scopes.READ, true, false),
    SETTLEMENT_MANAGE(Resources.SETTLEMENT, Scopes.MANAGE, true, false),

    VENUE_ACCOUNT_CREATE(Resources.VENUE_ACCOUNT, Scopes.CREATE, true, false),
    VENUE_ACCOUNT_MANAGE(Resources.VENUE_ACCOUNT, Scopes.MANAGE, true, true),
    VENUE_ACCOUNT_TRADE(Resources.VENUE_ACCOUNT, Scopes.TRADE, true, true),
    VENUE_ACCOUNT_READ(Resources.VENUE_ACCOUNT, Scopes.READ, true, true),

    VENUE_CREATE(Resources.VENUE, Scopes.CREATE, true, false),
    VENUE_MANAGE(Resources.VENUE, Scopes.MANAGE, true, false);

    public String getResource() {
        return resource;
    }

    public String getScope() {
        return scope;
    }

    private final String resource;
    private final String scope;

    private final boolean isStatic;
    private final boolean isDynamic;

    Permission(String resource, String scope, boolean isStatic, boolean isDynamic) {
        this.resource = resource;
        this.scope = scope;
        this.isStatic = isStatic;
        this.isDynamic = isDynamic;
    }

    public io.wyden.published.security.Permission toDynamicPermission(String resourceId) {
        return io.wyden.published.security.Permission.newBuilder()
            .setResource(this.resource)
            .setScope(this.scope)
            .setResourceId(resourceId).build();
    }

    public io.wyden.published.security.Permission toStaticPermission() {
        return io.wyden.published.security.Permission.newBuilder()
            .setResource(this.resource)
            .setScope(this.scope)
            .build();
    }

    public static Set<Permission> staticValues() {
        return Arrays.stream(values())
            .filter(permission -> permission.isStatic)
            .collect(Collectors.toSet());
    }

    public static Set<Permission> dynamicValues() {
        return Arrays.stream(values())
            .filter(permission -> permission.isDynamic)
            .collect(Collectors.toSet());
    }

    public PermissionDto toPermissionDto() {
        return toPermissionDto(null);
    }

    public PermissionDto toPermissionDto(@Nullable String resourceId) {
        return new PermissionDto(resource, scope, isBlank(resourceId) ? null : resourceId);
    }

    public boolean matches(io.wyden.published.security.Permission permission) {
        return permission.getResource().equals(resource) && permission.getScope().equals(scope);
    }

    public static Set<PermissionDto> getDynamicPermissionsForVenueAccount(String resourceId) {
        return Stream.of(VENUE_ACCOUNT_MANAGE, VENUE_ACCOUNT_TRADE, VENUE_ACCOUNT_READ)
            .map(v -> v.toPermissionDto(resourceId))
            .collect(Collectors.toSet());
    }

    public static Set<PermissionDto> getDynamicPermissionsForPortfolio(String resourceId) {
        return Stream.of(PORTFOLIO_MANAGE, PORTFOLIO_READ, PORTFOLIO_TRADE)
            .map(v -> v.toPermissionDto(resourceId))
            .collect(Collectors.toSet());
    }

    // TODO: Rename to Resource and make enum
    public static class Resources {
        public static final String PORTFOLIO = "portfolio";
        public static final String PORTFOLIO_NOSTRO = "portfolio.nostro";
        public static final String PORTFOLIO_VOSTRO = "portfolio.vostro";
        public static final String API_KEY = "api.key";
        public static final String CLIENT_INSTRUMENT = "client.instrument";
        public static final String CLOB_INSTRUMENT = "clob.instrument";
        public static final String WALLET = "wallet";
        public static final String WALLET_NOSTRO = "wallet.nostro";
        public static final String WALLET_VOSTRO = "wallet.vostro";
        public static final String VENUE_ACCOUNT = "venue.account";
        public static final String RISK = "risk";
        public static final String BROKER_CONFIG = "broker.config";
        public static final String CURRENCY = "currency";
        public static final String VENUE = "venue";
        public static final String SETTLEMENT = "settlement";
    }

    // TODO: Rename to Scope and make enum
    public static class Scopes {
        public static final String READ = "read";
        public static final String TRADE = "trade";
        public static final String MANAGE = "manage";
        public static final String CREATE = "create";
    }
}
