package io.wyden.apiserver.rest.trading.model;

import com.google.common.base.MoreObjects;
import io.wyden.apiserver.rest.trading.model.ExecutionReportResponse.OrderStatus;
import io.wyden.published.client.ClientResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class CancelRejectResponse implements OfPortfolioIdAndVenueAccountName {

    public enum CancelRejectResponseTo {
        UNSPECIFIED,
        ORDER_CANCEL_REQUEST,
        ORDER_CANCEL_REPLACE_REQUEST
    }

    private String clOrderId;
    private String origOrderId;
    private String origClOrderId;
    private String clientId;
    private String portfolioId;
    private String targetVenueAccount;
    private OrderStatus orderStatus;
    private String cancelRejectReason;
    private CancelRejectResponseTo cancelRejectResponseTo;
    private String result;

    public CancelRejectResponse() {
        // Empty
    }

    public CancelRejectResponse(ClientResponse cancelReject) {
        this.clOrderId = cancelReject.getClOrderId();
        this.origOrderId = cancelReject.getOrigOrderId();
        this.origClOrderId = cancelReject.getOrigClOrderId();
        this.clientId = cancelReject.getClientId();
        this.portfolioId = cancelReject.getPortfolioId();
        this.targetVenueAccount = cancelReject.getTarget();
        this.orderStatus = OrderStatus.valueOf(cancelReject.getOrderStatus().name());
        this.cancelRejectReason = cancelReject.getReason();
        this.cancelRejectResponseTo = CancelRejectResponseTo.valueOf(cancelReject.getCancelRejectResponseTo().name());
        this.result = cancelReject.getRequestResult().getValueDescriptor().getName();
    }

    public String getClOrderId() {
        return StringUtils.defaultString(clOrderId);
    }

    public void setClOrderId(String clOrderId) {
        this.clOrderId = clOrderId;
    }

    public String getOrigOrderId() {
        return StringUtils.defaultString(origOrderId);
    }

    public void setOrigOrderId(String origOrderId) {
        this.origOrderId = origOrderId;
    }

    public String getOrigClOrderId() {
        return StringUtils.defaultString(origClOrderId);
    }

    public void setOrigClOrderId(String origClOrderId) {
        this.origClOrderId = origClOrderId;
    }

    public String getClientId() {
        return StringUtils.defaultString(clientId);
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public OrderStatus getOrderStatus() {
        return ObjectUtils.firstNonNull(orderStatus, OrderStatus.ORDER_STATUS_UNSPECIFIED);
    }

    public void setOrderStatus(OrderStatus orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCancelRejectReason() {
        return StringUtils.defaultString(cancelRejectReason);
    }

    public void setCancelRejectReason(String cancelRejectReason) {
        this.cancelRejectReason = cancelRejectReason;
    }

    public CancelRejectResponseTo getCancelRejectResponseTo() {
        return ObjectUtils.firstNonNull(cancelRejectResponseTo, CancelRejectResponseTo.UNSPECIFIED);
    }

    public void setCancelRejectResponseTo(CancelRejectResponseTo cancelRejectResponseTo) {
        this.cancelRejectResponseTo = cancelRejectResponseTo;
    }

    @Override
    public String getPortfolioId() {
        return portfolioId;
    }

    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }

    public String getTargetVenueAccount() {
        return targetVenueAccount;
    }

    public void setTargetVenueAccount(String targetVenueAccount) {
        this.targetVenueAccount = targetVenueAccount;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("clOrderId", clOrderId)
            .add("origOrderId", origOrderId)
            .add("origClOrderId", origClOrderId)
            .add("portfolioId", portfolioId)
            .add("targetVenueAccount", targetVenueAccount)
            .add("orderStatus", orderStatus)
            .add("result", result)
            .toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CancelRejectResponse that = (CancelRejectResponse) o;
        return Objects.equals(clOrderId, that.clOrderId) && Objects.equals(origOrderId, that.origOrderId)
                && Objects.equals(origClOrderId, that.origClOrderId) && Objects.equals(clientId, that.clientId)
                && Objects.equals(portfolioId, that.portfolioId) && orderStatus == that.orderStatus
                && Objects.equals(targetVenueAccount, that.targetVenueAccount)
                && Objects.equals(cancelRejectReason, that.cancelRejectReason)
                && Objects.equals(result, that.result)
                && cancelRejectResponseTo == that.cancelRejectResponseTo;
    }

    @Override
    public int hashCode() {
        return Objects.hash(clOrderId, origOrderId, origClOrderId, clientId, portfolioId, targetVenueAccount, orderStatus, cancelRejectReason,
            result, cancelRejectResponseTo);
    }
}
