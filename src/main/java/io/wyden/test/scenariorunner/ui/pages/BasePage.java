package io.wyden.test.scenariorunner.ui.pages;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import io.qameta.allure.Step;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.microsoft.playwright.assertions.PlaywrightAssertions.assertThat;

@SuppressWarnings("unchecked")
public abstract class BasePage<T extends BasePage<T>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BasePage.class);

    protected Page page;
    protected String clientId;

    public BasePage(Page page, String clientId) {
        this.page = page;
        this.clientId = clientId;
    }

    @Step("Checking the visibility of the component {0}")
    public <S extends Enum<S> & UITarget> T checkComponentsVisibilityOf(Class<S> enumClass) {
        for (S element : enumClass.getEnumConstants()) {
            LOGGER.info("UI Actor {}: checking visibility of {} in {}", clientId, element, enumClass.getSimpleName());
            assertThat(getLocator(element)).isVisible();
        }
        return (T) this;
    }

    @Step("Going to {0}")
    public T navigateTo(UITarget uiTarget) {
        LOGGER.info("UI Actor {}: Navigate to {}", clientId, uiTarget);
        getLocator(uiTarget).click();
        return (T) this;
    }

    @Step("Clicking {0}")
    public T click(UITarget uiTarget) {
        LOGGER.info("UI Actor {}: Clicking {}", clientId, uiTarget);
        getLocator(uiTarget).click();
        return (T) this;
    }

    @Step("Close all menus")
    public T closeAllMenus() {
        LOGGER.info("UI Actor {}: Pressing ESC to close menus or dialogs", clientId);
        page.keyboard().press("Escape");
        return (T) this;
    }

    //TODO MOVE THIS METHOD TO A DIFFERENT CLASS
    protected Locator getLocator(UITarget uiTarget) {
        if (uiTarget.getParentSelector().equals(uiTarget.getTestId())) {
            return page.getByTestId(uiTarget.getParentSelector());
        }
        return page.getByTestId(uiTarget.getParentSelector()).getByTestId(uiTarget.getTestId());
    }

    //TODO MOVE THIS METHOD TO A DIFFERENT CLASS
    protected Locator getParametrizedLocator(UITarget uiTarget, String... parameters) {
        return page.getByTestId(uiTarget.getParentSelector()).getByTestId(uiTarget.getTestId().formatted((Object[]) parameters));
    }

}
