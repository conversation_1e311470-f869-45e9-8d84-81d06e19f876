import { useInstrumentStore } from '@wyden/features/app-header/useInstrumentStore';
import { useInstrumentSearch } from '@wyden/features/instrument-search/useInstrumentSearch';

export function useTradingInstrument() {
  const { instruments, instrumentsLoading } = useInstrumentSearch();
  const {
    instrument: storeInstrument,
    changeInstrument: changeStoreInstrument,
    previousInstrument,
  } = useInstrumentStore();

  return {
    instrument: storeInstrument || instruments[0],
    loading: instrumentsLoading,
    changeInstrument: changeStoreInstrument,
    previousInstrument,
  };
}
