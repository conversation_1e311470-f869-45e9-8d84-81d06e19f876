package io.wyden.rest.management.contract.transaction;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.TransactionRequest;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.rest.management.SpringTestBase;
import io.wyden.rest.management.common.Identifiers;
import io.wyden.rest.management.contract.client.TransactionBookingApi;
import io.wyden.rest.management.contract.model.ClientCashTrade;
import io.wyden.rest.management.contract.model.ClientCashTradeRequest;
import io.wyden.rest.management.contract.model.TransactionBase;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.slf4j.LoggerFactory.getLogger;

@Tag("contract-tests")
public class ClientCashTradeContractTest extends SpringTestBase {

    private static final Logger LOGGER = getLogger(ClientCashTradeContractTest.class);

    /**
     * Book completed Transaction and release reservations
     */
    @Test
    public void transactionsPostTest() throws JsonProcessingException {
        ClientCashTradeRequest transactionRequest = TransactionRequestContractFactory.createClientCashTradeRequest();

        String path = "/api/v1/transactions";
        TransactionBookingApi transactionApi = new TransactionBookingApi(configureApi(path, "POST", objectMapper.writeValueAsString(transactionRequest)));

        when(bookingEngineRabbitClient.requestTransaction(any()))
            .thenAnswer(invocation -> {
                TransactionRequest request = invocation.getArgument(0, TransactionRequest.class);
                io.wyden.published.booking.ClientCashTradeRequest clientCashTradeRequest = request.getClientCashTradeRequest();

                return TransactionSnapshot.newBuilder()
                    .setClientCashTrade(ClientCashTradeSnapshot.newBuilder()
                        // uuid is not passed through transaction request
                        .setUuid(Identifiers.randomIdentifier())
                        .setReservationRef(clientCashTradeRequest.getReservationRef())
                        .setDateTime(clientCashTradeRequest.getDateTime())
                        .setExecutionId(clientCashTradeRequest.getExecutionId())
                        .setVenueExecutionId(clientCashTradeRequest.getVenueExecutionId())
                        .setDescription(clientCashTradeRequest.getDescription())
                        .setQuantity(clientCashTradeRequest.getQuantity())
                        .setLeavesQuantity(clientCashTradeRequest.getLeavesQuantity())
                        .setPrice(clientCashTradeRequest.getPrice())
                        .setCurrency(clientCashTradeRequest.getCurrency())
                        // internal order id is not passed through transaction request
                        .setIntOrderId(Identifiers.randomIdentifier())
                        .setExtOrderId(clientCashTradeRequest.getExtOrderId())
                        .setOrderId(clientCashTradeRequest.getOrderId())
                        .setBaseCurrency(clientCashTradeRequest.getBaseCurrency())
                        .setPortfolio(clientCashTradeRequest.getPortfolioId())
                        .setCounterPortfolio(clientCashTradeRequest.getCounterPortfolioId())
                        .addAllTransactionFee(clientCashTradeRequest.getTransactionFeeList())
                        .setParentOrderId(clientCashTradeRequest.getParentOrderId())
                        .setRootOrderId(clientCashTradeRequest.getRootOrderId())
                        .setUnderlyingExecutionId(clientCashTradeRequest.getUnderlyingExecutionId())
                        .setRootExecutionId(clientCashTradeRequest.getRootExecutionId())
                        .setSettled(clientCashTradeRequest.getSettled())
                        .setIsLive(true)
                        .setClientRootOrderId(clientCashTradeRequest.getClientRootOrderId())
                        .setSettlementId(Identifiers.randomIdentifier())
                        .setSettledDateTime(clientCashTradeRequest.getSettledDateTime())
                        .setClientSettlementId(Identifiers.randomIdentifier())
                        .build())
                    .build();
            });

        TransactionBase response = transactionApi.bookTransactionAndReleaseReservation(transactionRequest);
        LOGGER.info("Response: {}", response);

        assertThat(response).isNotNull();
        validateTransaction((ClientCashTrade) response, transactionRequest);
        validateSettlement((ClientCashTrade) response);
    }

    public static void validateTransaction(ClientCashTrade transactionResponse, ClientCashTradeRequest transactionRequest) {
        assertThat(transactionResponse.getReservationRef()).isEqualTo(transactionRequest.getReservationRef());
        assertThat(transactionResponse.getDateTime()).isEqualTo(transactionRequest.getDateTime());
        assertThat(transactionResponse.getOrderId()).isEqualTo(transactionRequest.getOrderId());
        assertThat(transactionResponse.getParentOrderId()).isEqualTo(transactionRequest.getParentOrderId());
        assertThat(transactionResponse.getRootOrderId()).isEqualTo(transactionRequest.getRootOrderId());
        assertThat(transactionResponse.getClientRootOrderId()).isEqualTo(transactionRequest.getClientRootOrderId());
        assertThat(transactionResponse.getExtOrderId()).isEqualTo(transactionRequest.getExtOrderId());
        assertThat(transactionResponse.getExecutionId()).isEqualTo(transactionRequest.getExecutionId());
        assertThat(transactionResponse.getVenueExecutionId()).isEqualTo(transactionRequest.getVenueExecutionId());
        assertThat(transactionResponse.getUnderlyingExecutionId()).isEqualTo(transactionRequest.getUnderlyingExecutionId());
        assertThat(transactionResponse.getRootExecutionId()).isEqualTo(transactionRequest.getRootExecutionId());
        assertThat(transactionResponse.getTransactionType()).isEqualTo(transactionRequest.getTransactionType());
        assertThat(transactionResponse.getBaseCurrency()).isEqualTo(transactionRequest.getBaseCurrency());
        assertThat(transactionResponse.getCurrency()).isEqualTo(transactionRequest.getCurrency());
        assertThat(transactionResponse.getDescription()).isEqualTo(transactionRequest.getDescription());
        assertThat(transactionResponse.getQuantity()).isEqualTo(transactionRequest.getQuantity());
        assertThat(transactionResponse.getLeavesQuantity()).isEqualTo(transactionRequest.getLeavesQuantity());
        assertThat(transactionResponse.getPrice()).isEqualTo(transactionRequest.getPrice());
        assertThat(transactionResponse.getPortfolioId()).isEqualTo(transactionRequest.getPortfolioId());
        assertThat(transactionResponse.getCounterPortfolioId()).isEqualTo(transactionRequest.getCounterPortfolioId());
        assertThat(transactionResponse.getSettled()).isEqualTo(transactionRequest.getSettled());
        assertThat(transactionResponse.getSettlementDateTime()).isEqualTo(transactionRequest.getSettledDateTime());
        assertThat(transactionResponse.getId()).isNotBlank();

        assertThat(transactionResponse.getFees()).hasSize(1);
        assertThat(bd(transactionResponse.getFees().get(0).getAmount())).isEqualByComparingTo(bd(transactionRequest.getFees().get(0).getAmount()));
        assertThat(transactionResponse.getFees().get(0).getCurrency()).isEqualTo(transactionRequest.getFees().get(0).getCurrency());
        assertThat(transactionResponse.getFees().get(0).getFeeType().name()).isEqualTo(transactionRequest.getFees().get(0).getFeeType().name());
    }

    public static void validateSettlement(ClientCashTrade transactionResponse) {
        assertThat(transactionResponse.getClientSettlementId()).isNotBlank();
        assertThat(transactionResponse.getSettlementId()).isNotBlank();
    }
}
