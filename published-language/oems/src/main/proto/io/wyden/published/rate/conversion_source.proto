syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.rate;

message ConversionSource {
  string venue_account = 1;
  int32 priority = 2 [deprecated = true];
}

message ConversionSourceList {
  repeated ConversionSource conversion_source = 1;
}

message ConversionSourceCreateRequest {
  string venue_account = 1;
  int32 priority = 2;
}

message ConversionSourceUpdateRequest {
  string venue_account = 1;
  int32 priority = 2;
}