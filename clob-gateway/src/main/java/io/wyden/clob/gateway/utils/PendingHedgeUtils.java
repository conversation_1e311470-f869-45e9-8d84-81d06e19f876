package io.wyden.clob.gateway.utils;

import io.wyden.clob.gateway.model.PendingHedge;

import java.util.ArrayList;
import java.util.List;

public class PendingHedgeUtils {
    public static PendingHedge transformToRecursive(PendingHedge preferredHedge, List<PendingHedge> fallbackHedges) {
        List<PendingHedge> pendingHedges = new ArrayList<>();
        pendingHedges.add(preferredHedge);
        pendingHedges.addAll(fallbackHedges);

        PendingHedge result = null;

        // Traverse the list from end to start
        for (int i = pendingHedges.size() - 1; i >= 0; i--) {
            // Create a new PendingHedge with the current element's value and the previously created result as fallback
            PendingHedge.Builder resultBuilder = pendingHedges.get(i).toBuilder();
            if (result != null) {
                resultBuilder.setFallback(result);
            }
            result = resultBuilder.build();
        }

        // Return the head (first element) of the recursive structure
        return result;
    }
}
