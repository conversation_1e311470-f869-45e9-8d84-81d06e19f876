package io.wyden.clob.gateway.it;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.clob.gateway.infrastructure.rabbit.RabbitDestinations;
import io.wyden.clob.gateway.it.utils.AgencyTradingService;
import io.wyden.clob.gateway.it.utils.OrderCollider;
import io.wyden.clob.gateway.it.utils.QuotingOrdersObserver;
import io.wyden.clob.gateway.it.utils.ReferenceDataServer;
import io.wyden.clob.gateway.service.aeron.outbound.ExchangeCoreClient;
import io.wyden.clob.gateway.service.referencedata.InstrumentsTracker;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.apache.commons.lang3.RandomStringUtils;
import org.awaitility.Awaitility;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.images.PullPolicy;
import org.testcontainers.utility.DockerImageName;

import java.time.Duration;

@ContextConfiguration(
    initializers = ClobGatewayIntegrationTestWithoutMatchingEngineBase.Initializer.class
)
@SpringBootTest(properties = {
    "spring.main.allow-bean-definition-overriding=true"
})
@TestPropertySource(locations = "classpath:integration-test.properties")
@AutoConfigureWebTestClient
@ExtendWith(SpringExtension.class)
@ActiveProfiles("dev")
public class ClobGatewayIntegrationTestWithoutMatchingEngineBase {
    private static final Logger LOGGER_QOS = LoggerFactory.getLogger("quoting-order-service");
    private static final Logger LOGGER_STORAGE = LoggerFactory.getLogger("storage");

    public static final Network NETWORK = Network.newNetwork();
    public static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer(DockerImageName.parse("docker.wyden.io/mirror/rabbitmq:3.12-management").asCompatibleSubstituteFor("rabbitmq:3.12-management"))
        .withNetwork(NETWORK)
        .withNetworkAliases("rabbitmq")
        .withReuse(false);

    public static final PostgreSQLContainer<?> POSTGRES = new PostgreSQLContainer<>(DockerImageName.parse("docker.wyden.io/mirror/postgres:14.9").asCompatibleSubstituteFor("postgres:14.9"))
        .withReuse(false)
        .withNetwork(NETWORK)
        .withNetworkAliases("postgres")
        .withDatabaseName("storage")
        .withUsername("storage")
        .withPassword("password");

    public static final GenericContainer<?> STORAGE = new GenericContainer<>(DockerImageName.parse("docker.wyden.io/cloud/dev/storage:dev"))
        .withImagePullPolicy(PullPolicy.alwaysPull())
        .withNetwork(NETWORK)
        .withNetworkAliases("storage")
        .withEnv("SPRING_DATASOURCE_URL", "**********************************".formatted(POSTGRES.getDatabaseName()))
        .withEnv("SPRING_PROFILES_ACTIVE", "dev")
        .withLogConsumer(frame -> {
            LOGGER_STORAGE.info(frame.getUtf8String());
        })
        .withExposedPorts(5701);

    public static final GenericContainer<?> QUOTING_ORDER_SERVICE = new GenericContainer<>("docker.wyden.io/cloud/dev/quoting-order-service:dev")
        .withImagePullPolicy(PullPolicy.alwaysPull())
        .withLogConsumer(frame -> {
            LOGGER_QOS.info(frame.getUtf8String());
        })
        .withEnv("SPRING_PROFILES_ACTIVE", "dev")
        .withEnv("RABBITMQ_HOST", "rabbitmq")
        .withEnv("RABBITMQ_USERNAME", RABBIT_MQ.getAdminUsername())
        .withEnv("RABBITMQ_PASSWORD", RABBIT_MQ.getAdminPassword())
        .withEnv("HZ_ADDRESSLIST", "storage")
        .withNetwork(NETWORK)
        .dependsOn(STORAGE, RABBIT_MQ)
        .withReuse(false);

    static {
        RABBIT_MQ.start();
        POSTGRES.start();
        STORAGE.start();
        QUOTING_ORDER_SERVICE.start();

        Awaitility.setDefaultPollDelay(Duration.ZERO);
        Awaitility.setDefaultPollInterval(Duration.ofMillis(10));
        Awaitility.setDefaultTimeout(Duration.ofMinutes(5));
    }

    @Autowired
    RabbitIntegrator rabbitIntegrator;

    @Autowired
    RabbitDestinations destinations;

    @Autowired
    ExchangeCoreClient exchangeCoreClient;

    @Autowired
    InstrumentsTracker instrumentsTracker;

    AgencyTradingService agencyTradingService;
    QuotingOrdersObserver quotingOrdersObserver;
    OrderCollider orderCollider;

    @Autowired
    ReferenceDataServer referenceDataServer;

    @BeforeEach
    void setup() {
        // To prevent reusing rabbitmq queues on pipelines
        System.setProperty("HOSTNAME", "testhostname-" + RandomStringUtils.randomAlphanumeric(6));

        agencyTradingService = new AgencyTradingService(destinations, rabbitIntegrator);
        quotingOrdersObserver = new QuotingOrdersObserver(destinations, rabbitIntegrator);
        orderCollider = new OrderCollider(destinations, rabbitIntegrator);
    }

    @AfterEach
    void tearDown() {
        agencyTradingService.cleanup();
        quotingOrdersObserver.cleanup();
        orderCollider.cleanup();
    }

    @TestConfiguration
    static class TestHazelcastConfiguration {
        @Bean
        ApplicationListener<ApplicationReadyEvent> populateTestingData(HazelcastInstance hazelcast) {
            return event -> {
                TestingData.Instruments.populateInstrumentsMap(hazelcast);
                TestingData.Portfolios.populatePortfoliosMap(hazelcast);
                TestingData.QuotingConfigs.populateQuotingConfigMap(hazelcast);
                TestingData.VenueAccounts.populateVenueAccountsMap(hazelcast);
                TestingData.Rates.populateRatesMap(hazelcast);
                TestingData.MarketData.populateMarketDataMap(hazelcast);
            };
        }
    }

    // ReferenceDataServer is declared as a bean because it needs to attach ExchangeObserver before Spring emits ApplicationReadyEvent
    @TestConfiguration
    static class ReferenceDataServerConfiguration {
        @Bean
        ReferenceDataServer referenceDataServer(RabbitDestinations rabbitDestinations, RabbitIntegrator rabbitIntegrator) {
            return new ReferenceDataServer(rabbitDestinations, rabbitIntegrator);
        }
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {
            var values = TestPropertyValues.of(
                "hz.addressList=" + STORAGE.getHost() + ":" + STORAGE.getMappedPort(5701),
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword(),
                "clob.venue.name=" + TestingData.VenueAccounts.CLOB.venueAccount.getVenueName(),
                "clob.venue.account.id=" + TestingData.VenueAccounts.CLOB.venueAccount.getId(),
                "clob.venue.account.name=" + TestingData.VenueAccounts.CLOB.venueAccount.getVenueAccountName(),
                "clob.venue.account.owner=owner",
                "clob.venue.account.grants.trade=administrators,traders"
            );

            values.applyTo(applicationContext);
        }
    }
}
