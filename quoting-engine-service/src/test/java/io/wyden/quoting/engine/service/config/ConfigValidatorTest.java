package io.wyden.quoting.engine.service.config;

import io.wyden.published.brokerdesk.CalendarEntry;
import io.wyden.published.brokerdesk.ErrorType;
import io.wyden.published.brokerdesk.InstrumentQuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.brokerdesk.QuotingSource;
import io.wyden.published.brokerdesk.QuotingSourceAccountConfig;
import io.wyden.published.brokerdesk.ValidationError;
import io.wyden.published.brokerdesk.ValidationResult;
import io.wyden.published.brokerdesk.Validations;
import io.wyden.published.common.DayOfTheWeek;
import io.wyden.published.common.TimeInAWeek;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collection;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class InstrumentValidatorTest {

    @Mock
    private InstrumentsCacheFacade instrumentsCacheFacade;

    @Mock
    private PortfoliosCacheFacade portfoliosCacheFacade;

    @InjectMocks
    private ConfigValidator instrumentValidator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void validateQuotingSource_sourceInstrumentNotFound() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCUSD@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCUSD@FOREX@SOURCE")
            .build();
        Instrument clobInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("BTCUSD").build())
            .build();
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@SOURCE")).thenReturn(Optional.empty());

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(1, errors.size());
        ValidationError error = errors.iterator().next();
        assertEquals("quotingSource.sourceInstrumentId", error.getFieldName());
        assertEquals(ErrorType.INVALID, error.getErrorType());
        assertEquals("Source instrument not found", error.getDescription());
    }

    @Test
    void validateQuotingSource_sourceInstrumentBaseSymbolMatches() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCUSD@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCUSD@FOREX@SOURCE")
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("BTCUSD").build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("BTCUSD").build())
            .build();

        when(instrumentsCacheFacade.find("BTCUSD@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(0, errors.size());
    }

    @Test
    void validateQuotingSource_differentBaseCurrency() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCUSD@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("EURUSD@FOREX@SOURCE")
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("BTCUSD").build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("USD").build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("EURUSD").build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("EUR").build())
            .build();

        when(instrumentsCacheFacade.find("BTCUSD@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("EURUSD@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(1, errors.size());
        ValidationError error = errors.iterator().next();
        assertEquals("quotingSource.sourceInstrumentId", error.getFieldName());
        assertEquals(ErrorType.INVALID, error.getErrorType());
        assertEquals("Source instrument must have the same base currency as CLOB instrument", error.getDescription());
    }

    @Test
    void validateQuotingSource_conversionSourceInstrumentIdMissing() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCUSD@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCPLN@FOREX@SOURCE")
            .setConversionSourceInstrumentId("")
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("BTCUSD").build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder().setSymbol("BTCPLN").build())
            .build();

        when(instrumentsCacheFacade.find("BTCUSD@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCPLN@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(1, errors.size());
        ValidationError error = errors.iterator().next();
        assertEquals("quotingSource.conversionSourceInstrumentId", error.getFieldName());
        assertEquals(ErrorType.MISSING, error.getErrorType());
        assertEquals("Conversion source required when CLOB instrument and source instrument symbols do not match", error.getDescription());
    }

    @Test
    void validateQuotingSource_conversionSourceInstrumentNotFound() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCUSD@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCTRY@FOREX@SOURCE")
            .setConversionSourceInstrumentId("USDTRY@FOREX@CONVERSION")
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("USD")
                .setSymbol("BTCUSD").build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("TRY")
                .setSymbol("BTCTRY").build())
            .build();

        when(instrumentsCacheFacade.find("BTCUSD@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCTRY@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));
        when(instrumentsCacheFacade.find("USDTRY@FOREX@CONVERSION")).thenReturn(Optional.empty());

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(1, errors.size());
        ValidationError error = errors.iterator().next();
        assertEquals("quotingSource.conversionSourceInstrumentId", error.getFieldName());
        assertEquals(ErrorType.INVALID, error.getErrorType());
        assertEquals("Conversion source instrument not found", error.getDescription());
    }

    @Test
    void validateQuotingSource_validInverseFalseSetup() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCTRY@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCUSD@FOREX@SOURCE")
            .setConversionSourceInstrumentId("USDTRY@FOREX@CONVERSION")
            .setInverse(false)
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("TRY").build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("USD").build())
            .build();

        Instrument conversionSourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("USD").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("TRY").build())
            .build();

        when(instrumentsCacheFacade.find("BTCTRY@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));
        when(instrumentsCacheFacade.find("USDTRY@FOREX@CONVERSION")).thenReturn(Optional.of(conversionSourceInstrument));

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(0, errors.size());
    }

    @Test
    void validateQuotingSource_validInverseTrueSetup() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCEUR@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCUSD@FOREX@SOURCE")
            .setConversionSourceInstrumentId("EURUSD@FOREX@CONVERSION")
            .setInverse(true)
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("EUR").build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("USD").build())
            .build();

        Instrument conversionSourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("EUR").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("USD").build())
            .build();

        when(instrumentsCacheFacade.find("BTCEUR@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));
        when(instrumentsCacheFacade.find("EURUSD@FOREX@CONVERSION")).thenReturn(Optional.of(conversionSourceInstrument));

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(0, errors.size());
    }

    @Test
    void validateQuotingSource_invalidQuotingSources() {
        InstrumentQuotingConfig config = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCEUR@FOREX@CLOB")
            .build();
        QuotingSource source = QuotingSource.newBuilder()
            .setSourceInstrumentId("BTCUSD@FOREX@SOURCE")
            .setConversionSourceInstrumentId("GBPEUR@FOREX@CONVERSION")
            .setInverse(false)
            .build();

        Instrument clobInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("EUR")
                .setSymbol("BTCEUR")
                .build())
            .build();

        Instrument sourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("USD")
                .setSymbol("BTCUSD")
                .build())
            .build();

        Instrument conversionSourceInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("GBP").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("EUR")
                .setSymbol("GBPEUR")
                .build())
            .build();

        when(instrumentsCacheFacade.find("BTCEUR@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@SOURCE")).thenReturn(Optional.of(sourceInstrument));
        when(instrumentsCacheFacade.find("GBPEUR@FOREX@CONVERSION")).thenReturn(Optional.of(conversionSourceInstrument));

        Collection<ValidationError> errors = instrumentValidator.validateQuotingSource(config, source);

        assertEquals(1, errors.size());
    }

    @Test
    void validateBidMarkupCannotBeGreaterThan100Percent() {
        InstrumentQuotingConfig instrumentConfig = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCEUR@FOREX@CLOB")
            .build();
        mockInstrumentCache();
        mockPortfolioCacheFacade();

        QuotingConfig quotingConfig = QuotingConfig.newBuilder()
            .setBidMarkup("1.01")
            .addInstrumentQuotingConfigs(instrumentConfig)
            .build();

        ValidationResult validations = instrumentValidator.validate(quotingConfig, instrumentConfig);

        assertThat(validations.getErrorsList()).anySatisfy(error -> {
            assertThat(error.getFieldName()).isEqualTo("bidMarkup");
            assertThat(error.getDescription()).isEqualTo("Bid markup must be less than or equal to 100% but it is 101.00%");
        });
    }

    @Test
    void validateCalendarBidMarkupCannotBeGreaterThan100Percent() {
        InstrumentQuotingConfig instrumentConfig = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId("BTCEUR@FOREX@CLOB")
            .build();
        mockInstrumentCache();
        mockPortfolioCacheFacade();

        QuotingConfig quotingConfig = QuotingConfig.newBuilder()
            .setBidMarkup("0.9")
            .addInstrumentQuotingConfigs(instrumentConfig)
            .addSources(QuotingSourceAccountConfig.newBuilder()
                .addCalendarEntries(CalendarEntry.newBuilder()
                    .setFrom(TimeInAWeek.newBuilder().setDay(DayOfTheWeek.MONDAY).setTime("10:00").build())
                    .setTo(TimeInAWeek.newBuilder().setDay(DayOfTheWeek.MONDAY).setTime("11:00").build())
                    .setAdditionalMarkup("0.01")
                    .build())
                .addCalendarEntries(CalendarEntry.newBuilder()
                    .setFrom(TimeInAWeek.newBuilder().setDay(DayOfTheWeek.TUESDAY).setTime("10:00").build())
                    .setTo(TimeInAWeek.newBuilder().setDay(DayOfTheWeek.TUESDAY).setTime("11:00").build())
                    .setAdditionalMarkup("0.11")
                    .build())
                .build())
            .build();

        ValidationResult validations = instrumentValidator.validate(quotingConfig, instrumentConfig);

        assertThat(validations.getErrorsList()).anySatisfy(error -> {
            assertThat(error.getFieldName()).isEqualTo("source[%s].calendarEntry.additionalMarkup");
            assertThat(error.getDescription()).isEqualTo("Effective bid markup cannot be greater than 100% but after applying additional markup, it is 101.00%");
        });
    }

    private void mockPortfolioCacheFacade() {
        when(portfoliosCacheFacade.find(anyString())).thenReturn(Optional.of(Portfolio.newBuilder()
            .setMatchingEngineUid(123)
            .build()));
    }

    private void mockInstrumentCache() {
        Instrument clobInstrument = Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder().setBaseCurrency("BTC").build())
            .setBaseInstrument(BaseInstrument.newBuilder().setQuoteCurrency("EUR")
                .setSymbol("BTCEUR")
                .build())
            .build();
        when(instrumentsCacheFacade.find("BTCEUR@FOREX@CLOB")).thenReturn(Optional.of(clobInstrument));
    }
}
