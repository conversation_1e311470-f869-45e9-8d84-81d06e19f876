package io.wyden.agencytrading.service.fsm;

import io.opentelemetry.api.trace.Span;
import io.wyden.agencytrading.model.AgencyTradingOrderState;
import io.wyden.agencytrading.service.oems.outbound.OemsResponseFactory;
import io.wyden.agencytrading.service.tracking.ChildRequestFactory;
import io.wyden.agencytrading.service.tracking.FailureRequeueException;
import io.wyden.agencytrading.service.tracking.OrderCache;
import io.wyden.agencytrading.service.tracking.OrderIdentificationTracker;
import io.wyden.agencytrading.service.tracking.OrderValidator;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


// TODO: Duplicate messages

class OrderContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderContext.class);

    private final OrderService orderService;
    private OrderState orderState;

    OrderContext(OrderService orderService) {
        this.orderService = orderService;
    }

    void onOemsRequest(OemsRequest request) {
        OemsRequest.OemsRequestType requestType = request.getRequestType();
        Span.current().addEvent("Processing: " + requestType);
        LOGGER.info("Consuming OemsRequest {}: {}", requestType, request);
        switch (requestType) {
            case ORDER_SINGLE -> onNewOrder(request);
            case CANCEL -> onCancel(request);
            default -> throw new FailureRequeueException("Unknown OemsRequest requestType=" + request.getRequestTypeValue());
        }
    }

    private void onNewOrder(OemsRequest request) {
        try {
            getOrderValidator().validateOemsOrder(request);
            orderState = new OrderState(request);
            orderState.getOrderStatus().onNewOrder(this, request);
            updateAgencyTradingOrderState();
        } catch (OrderCache.DuplicateOrderState ex) {
            LOGGER.error("Exception: ", ex);
            Span.current().recordException(ex);
            handleDuplicateOrderState(request, ex);
            updateAgencyTradingOrderState();
        } catch (FailureRequeueException ex) {
            throw ex;
        } catch (Exception ex) {
            LOGGER.error("Exception: ", ex);
            Span.current().recordException(ex);
            handleOtherNewOrderException(request, ex);
            updateAgencyTradingOrderState();
        }
    }

    private void onCancel(OemsRequest request) {
        try {
            getOrderValidator().validateOemsCancel(request);
            String orderId = request.getOrderId();
            AgencyTradingOrderState agencyTradingOrderState = getOrderCache().find(orderId);
            if (agencyTradingOrderState == null) {
                throw new FailureRequeueException("AgencyTradingOrderState not found for orderId=" + orderId);
            }
            orderState = new OrderState(agencyTradingOrderState);
            orderState.getOrderStatus().onCancel(this, request);
            updateAgencyTradingOrderState();
        } catch (FailureRequeueException ex) {
            throw ex;
        } catch (Exception ex) {
            LOGGER.error("Exception: ", ex);
            Span.current().recordException(ex);
            handleOtherCancelException(request, ex);
            updateAgencyTradingOrderState();
        }
    }

    void onCancelReject(OemsResponse cancelReject) {
        getOrderValidator().validateCancelReject(cancelReject);
        AgencyTradingOrderState agencyTradingOrderState = getOrderIdentificationTracker().find(cancelReject)
            .orElse(null);
        if (agencyTradingOrderState == null) {
            throw new FailureRequeueException("OrderState not found for AgencyTradingOrderState parentOrderId=%s".formatted(cancelReject.getParentOrderId()));
        }
        orderState = new OrderState(agencyTradingOrderState);
        orderState.getOrderStatus().onCancelReject(this, cancelReject);
        updateAgencyTradingOrderState();
    }

    void onExecutionReport(OemsResponse executionReport) {
        getOrderValidator().validateExecutionReport(executionReport);
        AgencyTradingOrderState agencyTradingOrderState = getOrderIdentificationTracker().find(executionReport).orElse(null);
        if (agencyTradingOrderState == null) {
            String parentOrderId = executionReport.getParentOrderId();
            throw new FailureRequeueException("OrderState not found for parentOrderId=" + parentOrderId);
        }

        orderState = new OrderState(agencyTradingOrderState);
        Span.current().addEvent("Processing: " + executionReport.getExecType());
        switch (executionReport.getExecType()) {
            case EXEC_TYPE_UNSPECIFIED -> orderState.getOrderStatus().onUnspecified(this, executionReport);
            case PENDING_NEW -> orderState.getOrderStatus().onPendingNew(this, executionReport);
            case NEW -> orderState.getOrderStatus().onNew(this, executionReport);
            case REJECTED -> orderState.getOrderStatus().onRejected(this, executionReport);
            case PARTIAL_FILL, FILL -> orderState.getOrderStatus().onTrade(this, executionReport);
            case CANCELED -> orderState.getOrderStatus().onCancelled(this, executionReport);
            case EXPIRED -> orderState.getOrderStatus().onExpired(this, executionReport);
            case CALCULATED -> orderState.getOrderStatus().onCalculated(this, executionReport);
            case RESTATED -> orderState.getOrderStatus().onRestated(this, executionReport);
            default -> orderState.getOrderStatus().onUnrecognized(this, executionReport);
        }
        updateAgencyTradingOrderState();
    }

    OrderCache getOrderCache() {
        return orderService.getOrderCache();
    }

    OrderIdentificationTracker getOrderIdentificationTracker() {
        return orderService.getOrderIdentificationTracker();
    }

    OrderValidator getOrderValidator() {
        return orderService.getOrderValidator();
    }

    TradeExecutor getTradeExecutor() {
        return orderService.getTradeExecutor();
    }

    ChildRequestFactory getChildRequestFactory() {
        return orderService.getChildRequestFactory();
    }

    void emit(OemsRequest request) {
        orderService.getOemsRequestEmitter().emit(request);
    }

    void emit(OemsResponse oemsResponse) {
        orderService.getOemsResponseEmitter().emit(oemsResponse);
    }

    OrderState getOrderState() {
        return orderState;
    }

    void setOrderState(OrderState orderState) {
        this.orderState = orderState;
    }

    void addAgencyTradingOrderState() {
        AgencyTradingOrderState agencyTradingOrderState;
        if (orderState != null && orderState.isDirty()) {
            agencyTradingOrderState = orderState.toAgencyTradingOrderState();
            orderService.getOrderCache().add(agencyTradingOrderState);
            orderState.incSequenceNum();
            orderState.setDirty(false);
        } else {
            LOGGER.debug("OrderState clean - skipping add");
        }
    }

    void updateAgencyTradingOrderState() {
        AgencyTradingOrderState agencyTradingOrderState;
        if (orderState != null && orderState.isDirty()) {
            agencyTradingOrderState = orderState.toAgencyTradingOrderState();
            orderService.getOrderCache().update(agencyTradingOrderState);
            orderState.incSequenceNum();
            orderState.setDirty(false);
        } else {
            LOGGER.debug("OrderState clean - skipping update");
        }
    }

    private void handleDuplicateOrderState(OemsRequest request, OrderCache.DuplicateOrderState ex) {
        AgencyTradingOrderState agencyTradingOrderState = getOrderCache().find(request.getOrderId());
        if (agencyTradingOrderState != null) {
            OrderState state = new OrderState(agencyTradingOrderState);
            OemsResponse reject = OemsResponseFactory.createRejectAsDuplicate(state, ex.getMessage());
            emit(reject);
        } else {
            throw new FailureRequeueException("Inconsistent cache state. Re-queueing...");
        }
    }

    private void handleOtherNewOrderException(OemsRequest request, Exception ex) {
        OemsResponse reject = OemsResponseFactory.createRejectAsValidationError(request, ex.getMessage());
        emit(reject);
    }

    private void handleOtherCancelException(OemsRequest request, Exception ex) {
        OemsResponse reject = OemsResponseFactory.createCancelRejectGenericError(request, ex.getMessage());
        emit(reject);
    }

    public void onOrderNotDelivered(OemsRequest request) {
        AgencyTradingOrderState agencyTradingOrderState = getOrderIdentificationTracker().find(request).orElse(null);
        if (agencyTradingOrderState == null) {
            String parentOrderId = request.getParentOrderId();
            throw new FailureRequeueException("OrderState not found for parentOrderId=" + parentOrderId);
        }

        orderState = new OrderState(agencyTradingOrderState);
        Span.current().addEvent("Processing request not delivered");
        orderState.getOrderStatus().onOrderNotDelivered(this, request);
        updateAgencyTradingOrderState();
    }
}
