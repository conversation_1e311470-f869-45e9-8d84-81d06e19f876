package io.wyden.exchange.trading.orders;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;

import quickfix.FieldNotFound;
import quickfix.SessionID;
import quickfix.fix44.NewOrderSingle;

import static io.wyden.exchange.trading.ExecutionService.hasAskPrice;
import static io.wyden.exchange.trading.ExecutionService.hasBidPrice;
import static io.wyden.exchange.trading.orders.Order.OrderSide.BUY;
import static io.wyden.exchange.trading.orders.Order.OrderSide.SELL;

public class MarketOrder extends Order {
    public MarketOrder(NewOrderSingle message, SessionID sessionId) throws FieldNotFound {
        super(message, sessionId);
    }

    @Override
    public boolean isAffected(MarketDataEventDTO event) {
        return !finished() && (getSide() == BUY && hasAskPrice(event) || getSide() == SELL && hasBidPrice(event));
    }

    @Override
    public String toString() {
        return "MarketOrder{} " + super.toString();
    }
}
