package io.wyden.test.scenariorunner.trading.sor;

import io.qameta.allure.Epic;
import io.wyden.apiserver.rest.targetregistry.AggregatedTargetState;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientSide;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.published.venue.VenueOrderType;
import io.wyden.published.venue.VenueRequest;
import io.wyden.test.scenariorunner.assertion.diagnostic.AggregatedTargetStateCollectionAssert;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.refdata.InstrumentId;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountFactory;
import io.wyden.test.scenariorunner.data.trading.SimpleSmartOrderFactory;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.annotation.TestSetup;
import io.wyden.test.scenariorunner.extension.common.ConnectorMockSessions;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorConnectorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ConnectorMockSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.PortfolioExtension;
import io.wyden.test.scenariorunner.extension.eachtest.VenueAccountExtension;
import io.wyden.test.scenariorunner.extension.infra.EnabledOnlyOnEnv;
import io.wyden.test.scenariorunner.extension.infra.Env;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.Randomizer;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.extension.RegisterExtension;
import reactor.core.Disposable;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

import static io.wyden.published.client.ClientOrderType.LIMIT;
import static io.wyden.published.client.ClientOrderType.MARKET;
import static io.wyden.test.scenariorunner.assertion.diagnostic.AggregatedTargetStatePredicates.targetIs;
import static io.wyden.test.scenariorunner.data.infra.Epics.SMART_ORDER_ROUTER;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(SMART_ORDER_ROUTER)
@TestSetup
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.SMART_ORDER_ROUTER,
    Service.SMART_RECOMMENDATION_ENGINE,
    Service.MARKET_DATA_MANAGER,
    Service.RISK_ENGINE,
    Service.STORAGE
})
@EnabledOnlyOnEnv({Env.RUNNER, Env.QA})
public abstract class SORTradingTestBase {

    protected String bestCandidate = VenueAccountFactory.randomVenueAccountName();
    protected String secondBestCandidate = VenueAccountFactory.randomVenueAccountName();
    protected String[] tradeableAccounts = new String[]{bestCandidate, secondBestCandidate};
    protected static final double DEFAULT_PRICE_BOUND = 5000.0;
    protected static final Duration SOR_TIMEOUT = Duration.ofSeconds(10);
    protected static final Duration SOR_RETRY_SUSPENSION_TIMEOUT = Duration.ofSeconds(10);
    protected static final Duration SOR_CANDIDATE_TIMEOUT = Duration.ofSeconds(3);
    protected static final Duration SOR_CANDIDATE_SUSPENDED_TIME = Duration.ofSeconds(10);
    protected static final Duration MARKET_DATA_ARRIVAL_TIMEOUT = Duration.ofSeconds(5);
    protected static final String PREVENT_EXECUTION_ON_MULTIPLE_VENUES_REASON = "Prevent execution on multiple venues";
    protected static final double REQUESTED_QTY = 10;
    protected static final double LESS_QTY_THAN_MARKET_THRESHOLD =  REQUESTED_QTY * 0.99;
    protected static final double LESS_QTY_THAN_LIMIT_THRESHOLD =  REQUESTED_QTY * 0.59;

    @RegisterExtension
    @Order(0)
    protected ClientActorExtension clientActorExtension = new ClientActorExtension();
    @RegisterExtension
    @Order(1)
    PortfolioExtension portfolioExtension = PortfolioExtension.ofClientId(clientActorExtension.clientId());
    @RegisterExtension
    @Order(2)
    ConnectorMockSessionExtension connectorSessionExtension = new ConnectorMockSessionExtension(tradeableAccounts);
    @RegisterExtension
    @Order(3)
    VenueAccountExtension venueAccountExtension = new VenueAccountExtension(
        clientActorExtension.clientId(),
        Map.of(VenueAccountConstants.MOCK_VENUE, connectorSessionExtension.venueAccounts())
    );
    @RegisterExtension
    @Order(4)
    ClientActorConnectorExtension clientActorConnectorExtension = new ClientActorConnectorExtension(clientActorExtension.clientId(), true);
    @RegisterExtension
    @Order(5)
    ClientSessionExtension clientSessionExtension = new ClientSessionExtension(
        clientActorExtension.clientId(),
        connectorSessionExtension.venueAccountName(),
        portfolioExtension.portfolioId()
    );

    protected final SimpleSmartOrderFactory smartOrderFactory = new SimpleSmartOrderFactory(clientActorExtension.clientId(), portfolioExtension.portfolioId(), Arrays.asList(tradeableAccounts));
    protected String portfolio;
    protected final List<AggregatedTargetState> targetStates = new CopyOnWriteArrayList<>();
    private Disposable targetStatesSubscription;

    @BeforeEach
    protected void setup() {
        portfolio = portfolioExtension.portfolioId();
        targetStatesSubscription = clientActorExtension.configGqlActor().targetRegistry().targetStates().subscribe(targetStates::add);
        waitForAliveCapabilities(bestCandidate);
        waitForAliveCapabilities(secondBestCandidate);
    }

    protected void waitForAliveCapabilities(String accountName) {
        WaitUtils.waitUntil("any target state of account [%s] appear".formatted(accountName),
            () -> targetStates.stream()
                .anyMatch(targetIs(accountName)),
            Timeouts.POLL_INTERVAL,
            Timeouts.WAIT_FOR_CONDITION_L);

        WaitUtils.waitUntilAsserted(
            () -> AggregatedTargetStateCollectionAssert.assertThat(targetStates)
                .filteredOn(targetIs(accountName))
                .last()
                .capabilitiesAre(VenueAccountConstants.DEFAULT_ALIVE_CAPABILITIES, HealthStatus.ALIVE),
            Timeouts.POLL_INTERVAL,
            Timeouts.WAIT_FOR_CONDITION_M
        );
    }

    @AfterEach
    protected void cleanup(ClientActor actor) {
        actor.cancelAllOpenOrders();
        if (targetStatesSubscription != null) {
            targetStatesSubscription.dispose();
        }
        targetStates.clear();
    }

    protected void verifyChildOrder(VenueRequest child, ClientRequest parentOrder) {
        ClientOrderType expectedChildOrderType;
        switch (parentOrder.getOrderType()) {
            case STOP -> expectedChildOrderType = MARKET;
            case STOP_LIMIT -> expectedChildOrderType = LIMIT;
            default -> expectedChildOrderType = parentOrder.getOrderType();
        }
        assertThat(child.getOrderType().toString()).isEqualTo(expectedChildOrderType.toString());
        if (child.getOrderType().equals(VenueOrderType.LIMIT)) {
            assertThat(child.getPrice()).isEqualTo(parentOrder.getPrice());
        }
        assertThat(Double.parseDouble(child.getQuantity())).isEqualTo(Double.parseDouble(parentOrder.getQuantity()));
        assertThat(child.getTif().toString()).isEqualTo(parentOrder.getTif().toString());
        assertThat(child.getSide().toString()).isEqualTo(parentOrder.getSide().toString());
    }

    protected double prepareAcceptedSorOrder(ClientOrderType orderType, ConnectorMockSessions connSessions, ClientSession client) {
        // given
        ConnectorMockSession bestPriceCandidate = connSessions.getSession(bestCandidate);

        double bestPrice = Randomizer.randomDouble(DEFAULT_PRICE_BOUND);
        bestPriceCandidate.sendOrderBook(InstrumentId.DOGE_USD_FOREX_WYDENMOCK.getTicker().toString(), bestPrice);
        connSessions.getSession(secondBestCandidate).sendOrderBook(InstrumentId.DOGE_USD_FOREX_WYDENMOCK.getTicker().toString(), bestPrice + 1);

        // when
        client.sendOrder(smartOrderFactory.order(orderType, ClientSide.BUY, REQUESTED_QTY, String.valueOf(bestPrice), String.valueOf(bestPrice), "DOGEUSD"));
        bestPriceCandidate.acceptNewOrder(SOR_TIMEOUT);
        WaitUtils.justWait(Timeouts.JUST_WAIT);
        return bestPrice;
    }

}
