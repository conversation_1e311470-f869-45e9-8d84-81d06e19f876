package io.wyden.oems.ordercollider.collider.service.tracking;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.test.TestHazelcastInstanceFactory;
import io.wyden.cloud.utils.test.TelemetryMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.oems.ordercollider.model.VenueOrderState;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import io.wyden.published.venue.VenueOrderStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Objects;

import static io.wyden.published.venue.VenueOrderStatus.PARTIALLY_FILLED;
import static io.wyden.published.venue.VenueOrderStatus.PENDING_NEW;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.awaitility.Awaitility.await;

class OrderCacheTest {

    private static final String ORDER_ID = "abcd";
    private static final String TARGET_ACCOUNT = "BITMEX_ACC1";
    private static final String INSTRUMENT_ID = "BTC.USD";

    private static final String EXT_ID = "12345678ABCD";
    private static final String INT_ID = "ABCD12345678";

    private OrderCache orderCache;

    @BeforeEach
    void setUp() {
        HazelcastInstance hazelcastInstance = new TestHazelcastInstanceFactory().newHazelcastInstance();
        Telemetry telemetry = TelemetryMock.createMock();
        orderCache = new OrderCache(hazelcastInstance, telemetry, 1, false);
    }

    @Test
    void shouldSaveAndRetrieveNew() {
        VenueOrderState retrievedExpectedEmpty = orderCache.find(ORDER_ID);
        assertThat(retrievedExpectedEmpty).isNull();

        VenueOrderState orderState = initialOrderState();

        orderCache.add(orderState);

        VenueOrderState retrievedOS = orderCache.find(ORDER_ID);
        assertThat(retrievedOS).isEqualTo(orderState);
    }

    @Test
    void wontAddOrderWithTheSameATOrderId() {
        VenueOrderState orderState = initialOrderState();
        assertThatCode(() -> orderCache.add(orderState)).doesNotThrowAnyException();
        assertThatCode(() -> orderCache.add(orderState)).hasMessageContaining("venueOrderId");
    }

    @Test
    void wontAddOrderWithTheSameExtIdTargetAccount() {
        VenueOrderState.Builder builder1 = initialOrderState().toBuilder();

        VenueOrderState orderState1 = builder1.setRequest(builder1
            .getRequestBuilder()
            .setOrderId("1")
            .setVenueAccount("B")
            .build()
        )
            .setExtId("A")
            .build();

        VenueOrderState.Builder builder2 = initialOrderState().toBuilder();
        VenueOrderState orderState2 = builder2.setRequest(builder1
                .getRequestBuilder()
                .setOrderId("2")
            .setVenueAccount("B")
            .build()
        )
            .setExtId("A")
            .build();

        assertThatCode(() -> orderCache.add(orderState1)).doesNotThrowAnyException();
        orderCache.updateExtIdIndex("B", "A", "1");
        assertThatCode(() -> orderCache.add(orderState2)).hasMessageContaining("extId");
    }

    @Test
    void wontAddOrderWithTheSameIntIdTargetAccount() {
        VenueOrderState.Builder orderStateBuilder1 = initialOrderState().toBuilder();
        VenueOrderState orderState1 = orderStateBuilder1.setRequest(orderStateBuilder1
                        .getRequestBuilder()
                        .setOrderId("1")
            .setVenueAccount("B")
            .build()
        )
            .setIntId("A")
            .build();

        VenueOrderState.Builder orderStateBuilder2 = initialOrderState().toBuilder();
        VenueOrderState orderState2 = orderStateBuilder2.setRequest(orderStateBuilder2
            .getRequestBuilder()
            .setOrderId("2")
            .setVenueAccount("B")
            .build()
        )
            .setIntId("A")
            .build();

        assertThatCode(() -> orderCache.add(orderState1)).doesNotThrowAnyException();
        orderCache.updateIntIdIndex("B", "A", "1");
        assertThatCode(() -> orderCache.add(orderState2)).hasMessageContaining("intId");
    }

    @Test
    void shouldUpdateCurrentState() {
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
            .addCurrentStatus(PARTIALLY_FILLED)
            .setRemainingQuantity("5")
            .setFilledQuantity("5").build();
        orderCache.update(updated);

        VenueOrderState retrieved = orderCache.find(ORDER_ID);

        assertThat(retrieved).isEqualTo(updated);
    }

    @Test
    void shouldFindByExtIdAndTargetAccount() {
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
            .setExtId(EXT_ID)
            .addCurrentStatus(PARTIALLY_FILLED)
            .setRemainingQuantity("5")
            .setFilledQuantity("5").build();
        orderCache.update(updated);
        orderCache.updateExtIdIndex(TARGET_ACCOUNT, EXT_ID, ORDER_ID);

        VenueOrderState retrieved = orderCache.findOrderByExtId(EXT_ID, TARGET_ACCOUNT);

        assertThat(retrieved).isEqualTo(updated);
    }


    @Test
    void shouldNotFindByExtIdAndTargetAccount() {
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
            .setExtId(EXT_ID)
            .addCurrentStatus(PARTIALLY_FILLED)
            .setRemainingQuantity("5")
            .setFilledQuantity("5").build();
        orderCache.update(updated);

        VenueOrderState retrieved = orderCache.findOrderByExtId(EXT_ID, "Incorrect account");

        assertThat(retrieved).isNull();
    }

    @Test
    void shouldFindByIntIdAndTargetAccount() {
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
            .setIntId(INT_ID)
            .addCurrentStatus(PARTIALLY_FILLED)
            .setRemainingQuantity("5")
            .setFilledQuantity("5").build();
        orderCache.update(updated);
        orderCache.updateIntIdIndex(TARGET_ACCOUNT, INT_ID, ORDER_ID);

        VenueOrderState retrieved = orderCache.findOrderByIntId(INT_ID, TARGET_ACCOUNT);

        assertThat(retrieved).isEqualTo(updated);
    }


    @Test
    void shouldNotFindByIntIdAndTargetAccount() {
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
            .setIntId(INT_ID)
            .addCurrentStatus(PARTIALLY_FILLED)
            .setRemainingQuantity("5")
            .setFilledQuantity("5").build();
        orderCache.update(updated);

        VenueOrderState retrieved = orderCache.findOrderByIntId(INT_ID, "Incorrect account");

        assertThat(retrieved).isNull();
    }

    @Test
    void shouldRemoveClosedOrderFromCacheAfterUpdate() {
        // given
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
                .setClosed(true)
                .addCurrentStatus(VenueOrderStatus.REJECTED)
                .setErrorCode(StandardizedErrorMessage.REJECTED_BY_EXTERNAL_VENUE.getMessage())
                .setExtId(EXT_ID)
                .build();
        // when
        orderCache.update(updated);
        // then
        assertThatCode(() -> await()
                .atMost(Duration.ofSeconds(3))
                .until(() -> Objects.isNull(orderCache.find(updated.getRequest().getOrderId()))))
                .doesNotThrowAnyException();
    }

    @Test
    void shouldNotRemoveActiveOrderFromCacheAfterUpdate() {
        // given
        VenueOrderState orderState = initialOrderState();
        orderCache.add(orderState);

        VenueOrderState updated = orderState.toBuilder()
                .setExtId(EXT_ID)
                .addCurrentStatus(PARTIALLY_FILLED)
                .setRemainingQuantity("5")
                .setFilledQuantity("5").build();
        // when
        orderCache.update(updated);
        // then
        assertThatCode(() -> await()
                .atMost(Duration.ofSeconds(3))
                .until(() -> Objects.isNull(orderCache.find(updated.getRequest().getOrderId()))))
                .hasMessageContaining("Condition with io.wyden.oems.ordercollider.collider.service.tracking.OrderCacheTest was not fulfilled within 3 seconds.");
    }

    private VenueOrderState initialOrderState() {
        return VenueOrderState.newBuilder()
            .setRequest(OemsRequest.newBuilder()
                .setOrderId(ORDER_ID)
                .setVenueAccount(TARGET_ACCOUNT)
                .setSide(OemsSide.BUY)
                .setPrice("50000")
                .setOrderType(OemsOrderType.LIMIT)
                .setTif(OemsTIF.GTC)
                .setQuantity("10")
                .setInstrumentId(INSTRUMENT_ID)
                .build())
            .setFilledQuantity("0")
            .setRemainingQuantity("10")
            .setQuantity("10")
            .addCurrentStatus(PENDING_NEW)
            .build();
    }
}
