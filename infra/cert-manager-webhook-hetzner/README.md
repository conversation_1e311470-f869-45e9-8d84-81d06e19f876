# Add chart
```sh
helm repo add cert-manager-webhook-hetzner https://vadimkim.github.io/cert-manager-webhook-hetzner
helm repo update
```
# Deploy
```sh
helm diff upgrade -n cert-manager cert-manager-webhook-hetzner cert-manager-webhook-hetzner/cert-manager-webhook-hetzner --version 1.1.0 -f infra/cert-manager-webhook-hetzner/values-prod.yaml
```
```sh
helm upgrade -i -n cert-manager --create-namespace cert-manager-webhook-hetzner cert-manager-webhook-hetzner/cert-manager-webhook-hetzner --version 1.1.0 -f infra/cert-manager-webhook-hetzner/values-prod.yaml --atomic --timeout 5m
```

# Create Hetzner secret
```sh
kubectl -n cert-manager create secret generic hetzner-secret --save-config --dry-run=client -o yaml --from-literal=api-key="<HETZNER_DNS_API_TOKEN>" | kubectl apply -f -
```

# Deploy AWS
```sh
helm diff upgrade -n cert-manager cert-manager-webhook-hetzner cert-manager-webhook-hetzner/cert-manager-webhook-hetzner --version 1.3.1 -f infra/cert-manager-webhook-hetzner/values-prod.yaml
```
```sh
helm upgrade -i -n cert-manager --create-namespace cert-manager-webhook-hetzner cert-manager-webhook-hetzner/cert-manager-webhook-hetzner --version 1.3.1 -f infra/cert-manager-webhook-hetzner/values-prod.yaml --atomic --timeout 5m
```