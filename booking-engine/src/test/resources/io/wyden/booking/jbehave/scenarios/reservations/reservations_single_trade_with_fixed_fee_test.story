Meta: This story is an extension of "reservation_single_trade_test".
It additionally verifies, that fixed fee added on top of trade is correctly booked.

Scenario: deposit EUR 10'000

Given market data prices:
| instrument | price     |
| EUR        | 1 EUR     |
| EUR        | 1 USD     |
| ETH        | 1_000 EUR |
| ETH        | 1_000 USD |

When Client 1 makes a reservation DEPOSIT-1 for a deposit of 10_000 EUR into Bitfinex account
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | RESERVATION     | EUR        | 10_000 | 1     | -   | -             | -            |
| -           | Bitfinex  | RESERVATION     | EUR        | 10_000 | 1     | -   | -             | -            |

And position net state change to:
| portfolioId | instrument | qty | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | -   | 10_000     | -                      | -                         | -    | -           | -           | -             | -            |
| Client 1    | ETH        | -   | -          | -                      | -                         | -    | -           | -           | -             | -            |

When Client 1 deposit DEPOSIT-1 of 10_000 EUR into Bitfinex account is executed
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType     | instrument | qty     | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | DEPOSIT             | EUR        | 10_000  | 1     | -   | -             | 10_000       |
| Client 1    | -         | RESERVATION_RELEASE | EUR        | -10_000 | 1     | -   | 10_000        | 10_000       |
| -           | Bitfinex  | DEPOSIT             | EUR        | 10_000  | 1     | -   | -             | 10_000       |
| -           | Bitfinex  | RESERVATION_RELEASE | EUR        | -10_000 | 1     | -   | 10_000        | 10_000       |

And position net state change to:
| portfolioId | instrument | qty    | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | 10_000 | 10_000     | 10_000                 | 10_000                    | 10_000 | 10_000      | -           | -             | 1            |
| Client 1    | ETH        | -      | -          | -                      | -                         | -      | -           | -           | -             | -            |

Scenario: buy 5 ETH vs EUR @ 1'000 EUR with fixed fee 5 USD
Given market data prices:
| instrument | price     |
| ETH        | 1_000 EUR |

When Client 1 makes a reservation TRADE-A for a trade of 5 ETH/EUR on Bitfinex account with fixed fee 5 USD
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | RESERVATION     | ETH        | 5      | 1_000 | -   | -             | -            |
| Client 1    | -         | RESERVATION     | EUR        | -5_000 | 1     | -   | 10_000        | 10_000       |
| Client 1    | -         | RESERVATION     | USD        | -5     | 1     | -   | -             | -            |
| -           | Bitfinex  | RESERVATION     | ETH        | 5      | 1_000 | -   | -             | -            |
| -           | Bitfinex  | RESERVATION     | EUR        | -5_000 | 1     | -   | 10_000        | 10_000       |
| -           | Bitfinex  | RESERVATION     | USD        | -5     | 1     | -   | -             | -            |

And position net state change to:
| portfolioId | instrument | qty    | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | 10_000 | 5_000      | 5_000                  | 5_000                     | 10_000 | 10_000      | -           | -             | 1            |
| Client 1    | ETH        | -      | 5          | -                      | -                         | -      | -           | -           | -             | -            |

Given market data prices:
| instrument | price     |
| ETH        | 1_200 EUR |
| ETH        | 1_200 USD |

When Client 1 trade TRADE-A of 5 ETH/EUR with 5 USD fee on Bitfinex account is executed
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType               | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | CASH_TRADE_CREDIT             | ETH        | 5      | 1_200 | 5   | -             | 5            |
| Client 1    | -         | RESERVATION_RELEASE           | ETH        | -5     | 1_200 | -   | 5             | 5            |
| Client 1    | -         | CASH_TRADE_DEBIT              | EUR        | -6_000 | 1     | -   | 10_000        | 4_000        |
| Client 1    | -         | RESERVATION_RELEASE           | EUR        | 5_000  | 1     | -   | 4_000         | 4_000        |
| Client 1    | -         | TRADING_FEE                   | USD        | -5     | 1     | -   | -             | -5           |
| Client 1    | -         | RESERVATION_RELEASE_REMAINING | USD        | 5      | 1     | -   | -5            | -5           |
| -           | Bitfinex  | CASH_TRADE_CREDIT             | ETH        | 5      | 1_200 | 5   | -             | 5            |
| -           | Bitfinex  | RESERVATION_RELEASE           | ETH        | -5     | 1_200 | -   | 5             | 5            |
| -           | Bitfinex  | CASH_TRADE_DEBIT              | EUR        | -6_000 | 1     | -   | 10_000        | 4_000        |
| -           | Bitfinex  | RESERVATION_RELEASE           | EUR        | 5_000  | 1     | -   | 4_000         | 4_000        |
| -           | Bitfinex  | TRADING_FEE                   | USD        | -5     | 1     | -   | -             | -5           |
| -           | Bitfinex  | RESERVATION_RELEASE_REMAINING | USD        | 5      | 1     | -   | -5            | -5           |

And position net state change to:
| portfolioId | instrument | qty   | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost  | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | 4_000 | 4_000      | 4_000                  | 4_000                     | 4_000 | 4_000       | -           | -             | 1            |
| Client 1    | ETH        | 5     | 5          | 5                      | -                         | 6_005 | 6_000       | -           | -5            | 1_201        |
