package io.wyden.test.scenariorunner.integration.tool.connectormock;

import ch.algotrader.api.connector.marketdata.domain.AskDTO;
import ch.algotrader.api.connector.marketdata.domain.BidAskQuoteDTO;
import ch.algotrader.api.connector.marketdata.domain.BidDTO;
import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import ch.algotrader.api.connector.marketdata.domain.OrderBookDTO;
import ch.algotrader.api.connector.marketdata.domain.TradeDTO;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.wyden.published.venue.VenueResponse;
import io.wyden.test.scenariorunner.util.ProtobufUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.SignalType;

import java.net.URI;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.logging.Level;

import static io.wyden.test.scenariorunner.config.Timeouts.WAIT_FOR_CONDITION_L;

public class ConnectorMockCommands {

    private final WebClient webClient;

    public ConnectorMockCommands(WebClient webClient) {
        this.webClient = webClient;
    }

    public void activate(String accountName) {
        putBlocking("/account/%s".formatted(accountName));
    }

    public void rejectAll(String accountName, String reason) {
        postBlocking("/trading/%s/reject-all".formatted(accountName), reason);
    }

    public void keepShutOff(String accountName) {
        putBlocking("/account/blocked/%s".formatted(accountName));
    }

    public void cleanup(String accountName) {
        deleteBlocking("/account/%s".formatted(accountName));
    }

    public void acceptVenueOrder(String accountName, String intId) {
        postBlocking("/ack/new/%s".formatted(accountName), Map.of("intId", intId));
    }

    public void acceptVenueOrder(String accountName, String intId, String extId) {
        postBlocking("/ack/new/%s".formatted(accountName), Map.of("intId", intId, "extId", extId));
    }

    public void rejectVenueOrder(String accountName, String intId, String reason) {
        postBlocking("/ack/new/%s".formatted(accountName), Map.of("intId", intId, "reason", reason));
    }

    public void acceptVenueCancel(String accountName, String intId, String extId) {
        postBlocking("/ack/cancel/%s".formatted(accountName), Map.of("intId", intId, "extId", extId));
    }

    public void rejectVenueCancel(String accountName, String intId, String extId, String reason) {
        postBlocking("/ack/cancel/%s".formatted(accountName), Map.of("intId", intId, "extId", extId, "reason", reason));
    }

    public void pushExecutionReport(String accountName, VenueResponse executionReport) {
        postBlocking("/trading/%s/execution-report".formatted(accountName), executionReport);
    }

    public void configureMarketData(String venueAccount, MarketDataEventDTO marketData) {
        String url = routeMarketData(marketData) + "/" + venueAccount;
        postBlocking(url, marketData);
    }

    public void clearMarketData(String venueAccount) {
        deleteBlocking("/market-data/events/%s".formatted(venueAccount));
    }

    private String routeMarketData(MarketDataEventDTO event) {
        if (event instanceof BidDTO) {
            return "/market-data/bids";
        }

        if (event instanceof AskDTO) {
            return "/market-data/asks";
        }

        if (event instanceof BidAskQuoteDTO) {
            return "/market-data/bid-ask-quotes";
        }

        if (event instanceof TradeDTO) {
            return "/market-data/trades";
        }

        if (event instanceof OrderBookDTO) {
            return "/market-data/order-book";
        }

        throw new IllegalArgumentException("Cannot find routing for market data type: " + event.getClass().getSimpleName());
    }

    public void relayBidAsks(BidAskQuoteDTO bidAskQuote, String venueAccount, int fixedRateMillis, Double randomFactor) {
        relayMarketData(bidAskQuote, "/market-data/relay/bid-ask", venueAccount, fixedRateMillis, randomFactor);
    }

    public void relayOrderBooks(OrderBookDTO orderBook, String venueAccount, int fixedRateMillis, Double randomFactor) {
        relayMarketData(orderBook, "/market-data/relay/order-book", venueAccount, fixedRateMillis, randomFactor);
    }

    public void relayMarketData(MarketDataEventDTO mdEvent, String path, String venueAccount, int fixedRateMillis, Double randomFactor) {
        postBlocking(uriBuilder ->  {
                uriBuilder
                    .path(path)
                    .queryParam("venueAccount", venueAccount)
                    .queryParam("fixedRateMillis", fixedRateMillis);
                if (randomFactor != null) {
                    uriBuilder.queryParam("randomFactor", randomFactor);
                }
                return uriBuilder.build();
            },
            mdEvent);
    }

    public void stopRelayMarketData(String venueAccount) {
        String path = "/market-data/relay";
        deleteBlocking(uriBuilder -> uriBuilder
            .path(path)
            .queryParam("venueAccount", venueAccount)
            .build());
    }

    public void reportState(String venueAccount, ConnectorMockActor.DiagnosticEvent diagnosticEvent) {
        postBlocking("/diagnostic/reportState/%s".formatted(venueAccount), diagnosticEvent);
    }

    public void toggleReconciliation(String venueAccount, boolean isEnabled) {
        postBlocking("/reconciliation/enable/%s".formatted(venueAccount), Map.of("enabled", isEnabled));
    }

    public void configureReconciliationResponse(String venueAccount, Duration delay, List<VenueResponse> responses) {
        postBlocking("/reconciliation/response/%s".formatted(venueAccount), new ReconciliationResponseBody(delay, responses));
    }

    private void deleteBlocking(String path) {
        webClient.delete()
            .uri(path)
            .retrieve()
            .bodyToMono(Void.class)
            .log("Connector DELETE", Level.INFO, SignalType.ON_NEXT, SignalType.ON_ERROR)
            .block(WAIT_FOR_CONDITION_L);
    }

    private void postBlocking(String path, Object body) {
        webClient.post()
            .uri(path)
            .bodyValue(body)
            .retrieve()
            .bodyToMono(Void.class)
            .log("Connector POST", Level.INFO, SignalType.ON_NEXT, SignalType.ON_ERROR)
            .block(WAIT_FOR_CONDITION_L);
    }

    private void deleteBlocking(Function<UriBuilder, URI> uriFunction) {
        webClient.delete()
            .uri(uriFunction)
            .retrieve()
            .bodyToMono(Void.class)
            .log("Connector DELETE", Level.INFO, SignalType.ON_NEXT, SignalType.ON_ERROR)
            .block(WAIT_FOR_CONDITION_L);
    }

    private void postBlocking(Function<UriBuilder, URI> uriFunction, Object body) {
        webClient.post()
            .uri(uriFunction)
            .bodyValue(body)
            .retrieve()
            .bodyToMono(Void.class)
            .log("Connector POST", Level.INFO, SignalType.ON_NEXT, SignalType.ON_ERROR)
            .block(WAIT_FOR_CONDITION_L);
    }

    private void putBlocking(String path) {
        webClient.put()
            .uri(path)
            .retrieve()
            .bodyToMono(Void.class)
            .log("Connector PUT", Level.INFO, SignalType.ON_NEXT, SignalType.ON_ERROR)
            .block(WAIT_FOR_CONDITION_L);
    }

    @JsonSerialize
    public record ReconciliationResponseBody(Duration delay,
                                             @JsonSerialize(using = ProtobufUtils.VenueResponseListSerializer.class)
                                             @JsonDeserialize(using = ProtobufUtils.VenueResponseListDeserializer.class)
                                             List<VenueResponse> responses) {}

}
