package io.wyden.pricing.interfaces.web.debug;

import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import io.wyden.pricing.service.subscription.SourceSubscriptionHolder;
import io.wyden.pricing.service.subscription.StreamSource;
import io.wyden.pricing.service.subscription.SubscriptionService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/debug")
public class DebugRestController {

    public static final JsonFormat.Printer PRINTER = JsonFormat.printer()
        .includingDefaultValueFields()
        .preservingProtoFieldNames();

    private final SourceSubscriptionHolder sourceSubscriptionHolder;
    private final SubscriptionService subscriptionService;

    public DebugRestController(SourceSubscriptionHolder sourceSubscriptionHolder,
                               SubscriptionService subscriptionService) {
        this.sourceSubscriptionHolder = sourceSubscriptionHolder;
        this.subscriptionService = subscriptionService;
    }

    @GetMapping(value = "/subscriptions", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> getCurrentSubscriptionsRunning() {
        return subscriptionService.getCurrentSubscriptions().entrySet()
            .stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> toJson(e.getValue())));
    }

    @GetMapping(value = "/subscriptions/{streamId}/sources", produces = MediaType.APPLICATION_JSON_VALUE)
    public String getSourcesForStreamId(@PathVariable String streamId) {
        return sourceSubscriptionHolder.getSources(streamId).toString();
    }

    @GetMapping(value = "/bindings", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<String> getCurrentRabbitBindings() {
        return sourceSubscriptionHolder.getBindings().stream()
            .map(StreamSource::toString)
            .toList();
    }

    private String toJson(Message message) {
        try {
            String json = PRINTER.print(message);
            json = json.replace("\\n", " "); // Replace newlines with space or remove as needed
            json = json.replace("\\\"", "\""); // Unescape quotes
            return json;
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert to JSON", e);
        }
    }
}
