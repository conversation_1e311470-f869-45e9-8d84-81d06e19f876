package io.wyden.cloudutils.tools;

import java.util.Random;
import java.util.UUID;

public final class IdUtils {

    private IdUtils() {
        // Empty
    }

    public static String randomId() {
        return UUID.randomUUID().toString();
    }

    public static String randomSuffix(String base) {
        Random random = new Random();
        int suffix = random.nextInt(1, 100_000);
        return base + "-" + suffix;
    }
}
