package io.wyden.apiserver.rest.marketdata;

import io.wyden.apiserver.rest.marketdata.model.L1Event;
import io.wyden.apiserver.rest.marketdata.model.MarketDataEvent;
import io.wyden.apiserver.rest.marketdata.model.MarketDataParser;
import io.wyden.apiserver.rest.marketdata.model.MarketDataSubscriptionKey;
import io.wyden.apiserver.rest.marketdata.model.MdClientRequest;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import static io.wyden.apiserver.rest.marketdata.model.L1Event.emptyEvent;
import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;

@Component
public class TickService {

    private final SubscriptionServiceProvider subscriptionServiceProvider;
    private final LastMarketDataEventCache lastMarketDataEventCache;

    public TickService(SubscriptionServiceProvider subscriptionServiceProvider, LastMarketDataEventCache lastMarketDataEventCache) {
        this.subscriptionServiceProvider = subscriptionServiceProvider;
        this.lastMarketDataEventCache = lastMarketDataEventCache;
    }

    public Flux<L1Event> subscribeTick(MarketDataSubscriptionKey marketDataSubscriptionKey, MdClientRequest mdClientRequest) {
        MarketDataSubscriptionService subscriptionService = subscriptionServiceProvider.get(marketDataSubscriptionKey);

        return subscriptionService.subscribe(marketDataSubscriptionKey, mdClientRequest)
            .cast(L1Event.class)
            .scan(defaultIfNull(lastMarketDataEventCache.getLastTick(marketDataSubscriptionKey), emptyEvent()), MarketDataParser::buildL1Tick)
            .filter(TickService::correctIdentifier)
            .doOnNext(tick -> lastMarketDataEventCache.putLastTick(marketDataSubscriptionKey, tick));
    }

    private static boolean correctIdentifier(MarketDataEvent marketDataEvent) {
        return marketDataEvent.identifier() != null && !marketDataEvent.identifier().instrumentId().isEmpty();
    }
}
