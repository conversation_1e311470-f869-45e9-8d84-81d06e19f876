import { useTheme } from '@mui/material';
import { styled } from '@ui/styled';
import { Tab } from '@ui/Tab';
import { Tabs } from '@ui/Tabs';
import { color } from '@ui/theme/colors';
import { Header } from '@ui/Typography/Header';
import { Label } from '@ui/Typography/Label';
import { usePermissions } from '@wyden/hooks/usePermissions';
import { Resource, Scope } from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { useTranslation } from 'react-i18next';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { ENTITLEMENTS } from '../../constants';
import { useEntitlements } from '../entitlements/useEntitlements';
import { ROUTES } from '../Routes';

export function SettingsNavigation() {
  const theme = useTheme();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { checkIfPermitted } = usePermissions();
  const { checkIfEntitled } = useEntitlements();

  const path = location.pathname;

  const tabDefinitions = [
    { route: ROUTES.GENERAL_SETTINGS, label: t('tabs.generalSettings'), visible: true },
    { route: ROUTES.PORTFOLIO.DATA, label: t('tabs.portfolios'), visible: true },
    { route: ROUTES.VENUE_ACCOUNT.ACCOUNTS, label: t('tabs.accounts'), visible: true },
    { route: ROUTES.WALLET_ACCOUNT, label: t('tabs.walletAccounts'), visible: true },
    {
      route: ROUTES.INSTRUMENTS,
      label: t('tabs.instruments'),
      visible: true,
    },
    {
      route: ROUTES.QUOTING_ENGINES.ROOT,
      label: t('tabs.quotingEngines'),
      visible: checkIfEntitled(ENTITLEMENTS['Quoting Engine']),
    },
    {
      route: ROUTES.CURRENCIES,
      label: t('tabs.currencies'),
      visible: checkIfPermitted({ resource: Resource.Currency, scope: Scope.Manage }),
    },
    { route: ROUTES.HEALTH_STATUS, label: t('tabs.healthStatus'), visible: true },
    { route: ROUTES.PERMISSIONS.PORTFOLIOS, label: t('tabs.permissions'), visible: true },
    { route: ROUTES.CONVERSION_SOURCES, label: t('tabs.conversionSources'), visible: true },
  ];

  const index = tabDefinitions
    .filter((tab) => tab.visible)
    .findIndex((tab) => {
      const pathSegments = path.split('/').filter(Boolean);
      const basePath = `/${pathSegments.slice(0, 2).join('/')}`;
      return tab.route.startsWith(basePath);
    });

  const handleTabChange = (route: string) => () => {
    navigate(route);
  };

  return (
    <StyledContainer>
      <StyledWrapper>
        <StyledLabel variant="small">{t('header.dropdown.settings')}</StyledLabel>
        <StyledTabs
          value={index}
          orientation="vertical"
          TabIndicatorProps={{
            style: { backgroundColor: color[theme.palette.mode].textBrandTextBrandPrimary },
          }}
        >
          {tabDefinitions.map(
            (tab) =>
              tab.visible && (
                <StyledTab onClick={handleTabChange(tab.route)} label={tab.label} key={tab.route} />
              ),
          )}
        </StyledTabs>
      </StyledWrapper>
      <Outlet />
    </StyledContainer>
  );
}

const StyledContainer = styled('div')`
  display: flex;
  height: 100%;
  padding: 24px;
  border-radius: 4px;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfaceSecondary};
  border: solid 1px ${({ theme }) => color[theme.palette.mode].borderElementsBorderWeak};
`;

const StyledTabs = styled(Tabs)`
  height: calc(100% - 36px);
  border-right: 1px solid ${({ theme }) => color[theme.palette.mode].borderElementsBorderWeak};
`;

const StyledTab = styled(Tab)`
  min-width: 165px;
  align-items: flex-start !important;
  &:hover {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary} !important;
  }
  &.Mui-selected {
    color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary} !important;
  }
  &.MuiButtonBase {
    background: yellow !important;
  }
`;

const StyledWrapper = styled('div')`
  position: relative;
  margin-right: ${getSpacing(16)};
`;

const StyledLabel = styled(Label)`
  display: block;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak} !important;
  border-right: 1px solid ${({ theme }) => color[theme.palette.mode].borderElementsBorderWeak};
  font-size: 11px;
  font-weight: 500;
  user-select: none;
  line-height: 16px;
  font-style: normal;
  margin-right: 0;
  margin: ${getSpacing(2)} 0 0 ${getSpacing(4)};
  padding-bottom: 8px;
`;

export const StyledHeaderWrapper = styled('div')`
  //first child to the left and second to the right css
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: ${getSpacing(4)};
`;

//TODO mz ideally to be replaced with StyledHeaderWrapper
export const StyledSettingsHeader = styled(Header)`
  margin-bottom: ${getSpacing(6)};
`;
