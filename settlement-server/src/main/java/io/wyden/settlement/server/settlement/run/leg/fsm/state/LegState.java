package io.wyden.settlement.server.settlement.run.leg.fsm.state;

import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class LegState {
    private Logger LOGGER = LoggerFactory.getLogger(LegState.class);

    protected final SettlementRunEntity run;
    protected final Long legId;

    protected LegState(SettlementRunEntity run, Long legId) {
        this.run = run;
        this.legId = legId;
    }

    public LegState onInitiate(LegStateContext context) {
        throw new IllegalStateException(String.format("Leg is in state: %s cannot perform initiation", this.getClass().getSimpleName()));
    }

    public LegState onComplete(LegStateContext legStateContext) {
        throw new IllegalStateException(String.format("Leg is in state: %s cannot perform completion", this.getClass().getSimpleName()));
    }

}