package io.wyden.accessgateway.client.permission;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.accessgateway.domain.permission.PermissionGroupMapConfig;
import io.wyden.accessgateway.domain.permission.PermissionUserMapConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public final class PermissionChecker {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionChecker.class);

    private final IMap<String, String> groupPermissionMap;
    private final IMap<String, String> userPermissionMap;

    public PermissionChecker(HazelcastInstance hazelcastInstance) {
        this.groupPermissionMap = PermissionGroupMapConfig.getMap(hazelcastInstance);
        this.userPermissionMap = PermissionUserMapConfig.getMap(hazelcastInstance);
    }

    public boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, Permission permission, String resourceId) {
        return hasPermission(roles, groups, username, permission.getResource(), permission.getScope(), resourceId);
    }

    public boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, String resource, String scope, String resourceId) {
        return hasRolePermission(roles, scope)
            || hasGroupPermission(groups, resource, scope)
            || hasGroupPermission(groups, resource, scope, resourceId)
            || hasUserPermission(username, resource, scope)
            || hasUserPermission(username, resource, scope, resourceId);
    }

    public boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, Permission permission) {
        return hasPermission(roles, groups, username, permission.getResource(), permission.getScope());
    }

    public boolean hasPermission(Set<WydenRole> roles, Set<String> groups, String username, String resource, String scope) {
        return hasRolePermission(roles, scope)
            || hasGroupPermission(groups, resource, scope)
            || hasUserPermission(username, resource, scope);
    }

    public boolean hasUserPermission(String username, String resource, String scope) {
        String key = PermissionUserMapConfig.toKey(username, resource, scope, null);
        boolean result = userPermissionMap.containsKey(key);
        LOGGER.debug("Static permission {}:{} for user '{}' is {}", resource, scope, username, result);
        return result;
    }

    public boolean hasUserPermission(String username, String resource, String scope, String resourceId) {
        String key = PermissionUserMapConfig.toKey(username, resource, scope, resourceId);
        boolean result = userPermissionMap.containsKey(key);
        LOGGER.debug("Dynamic permission {}:{}:{} for user '{}' is {}", resource, scope, resourceId, username, result);
        return result;
    }

    public boolean hasGroupPermission(Set<String> groups, String resource, String scope) {
        return groups.stream()
            .anyMatch(group -> hasGroupPermission(group, resource, scope));
    }

    public boolean hasGroupPermission(String group, String resource, String scope) {
        String key = PermissionGroupMapConfig.toKey(group, resource, scope, null);
        boolean result = groupPermissionMap.containsKey(key);
        LOGGER.debug("Static permission {}:{} for group '{}' is {}", resource, scope, group, result);
        return result;
    }

    public boolean hasGroupPermission(Set<String> groups, String resource, String scope, String resourceId) {
        return groups.stream()
            .anyMatch(group -> hasGroupPermission(group, resource, scope, resourceId));
    }

    public boolean hasGroupPermission(String group, String resource, String scope, String resourceId) {
        String key = PermissionGroupMapConfig.toKey(group, resource, scope, resourceId);
        boolean result = groupPermissionMap.containsKey(key);
        LOGGER.debug("Dynamic permission {}:{}:{} for group '{}' is {}", resource, scope, resourceId, group, result);
        return result;
    }

    public boolean hasRolePermission(Set<WydenRole> roles, String scope) {
        boolean result = roles.stream()
            .anyMatch(r -> r.getScope().equals(scope));
        LOGGER.debug("Role permission {} for roles {} is {}", scope, roles, result);
        return result;
    }
}
