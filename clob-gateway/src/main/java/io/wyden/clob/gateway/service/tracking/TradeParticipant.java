package io.wyden.clob.gateway.service.tracking;

import io.wyden.clob.gateway.service.audit.MatchAuditContext;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Portfolio;

import static io.wyden.clob.gateway.utils.QuotingConfigUtils.findOneAccountOrDefault;

public abstract class TradeParticipant implements io.wyden.clob.gateway.service.audit.MatchAuditIdentity {
    public static class Client extends TradeParticipant {
        public final Portfolio portfolio;

        public Client(Portfolio portfolio) {
            this.portfolio = portfolio;
        }

        @Override
        public MatchAuditContext.Counterparty.Type getType() {
            return MatchAuditContext.Counterparty.Type.PORTFOLIO;
        }

        @Override
        public String getId() {
            return portfolio.getId();
        }

        @Override
        public String toString() {
            return "Client Portfolio %s".formatted(portfolio.getId());
        }
    }

    public static class QuotingEngine extends TradeParticipant {
        public final Portfolio portfolio;
        public final QuotingConfig quotingConfig;
        private final Instrument instrument;

        public QuotingEngine(Portfolio portfolio, QuotingConfig quotingConfig, Instrument instrument) {
            this.portfolio = portfolio;
            this.quotingConfig = quotingConfig;
            this.instrument = instrument;
        }

        @Override
        public MatchAuditContext.Counterparty.Type getType() {
            return MatchAuditContext.Counterparty.Type.VENUE;
        }

        @Override
        public String getId() {
            return findOneAccountOrDefault(quotingConfig, instrument, "");
        }

        @Override
        public String toString() {
            return "Quoting Portfolio %s".formatted(portfolio.getId());
        }
    }

    public static class Unresolved extends TradeParticipant {
        public final long uid;
        public Unresolved(long uid) {
            this.uid = uid;
        }

        @Override
        public MatchAuditContext.Counterparty.Type getType() {
            return MatchAuditContext.Counterparty.Type.UNRESOLVED;
        }

        @Override
        public String getId() {
            return Long.toString(uid);
        }
    }
}