package io.wyden.test.scenariorunner.accounting.restmanagement;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.rest.management.domain.OnboardingModel.AccountOnboardingRequest;
import io.wyden.rest.management.domain.SettlementModel.ClientSideMapping;
import io.wyden.rest.management.domain.SettlementModel.ConfigurationTarget;
import io.wyden.rest.management.domain.SettlementModel.ConfigurationTargetType;
import io.wyden.rest.management.domain.SettlementModel.SettlementConfiguration;
import io.wyden.rest.management.domain.SettlementModel.SettlementLeg;
import io.wyden.rest.management.domain.SettlementModel.SettlementRequest;
import io.wyden.rest.management.domain.SettlementModel.SettlementResponse;
import io.wyden.rest.management.domain.SettlementModel.SettlementStatus;
import io.wyden.rest.management.domain.SettlementModel.StreetSideMapping;
import io.wyden.rest.management.domain.TransactionModel.ClientCashTrade;
import io.wyden.rest.management.domain.TransactionModel.StreetCashTrade;
import io.wyden.rest.management.domain.TransactionModel.TransactionSearch;
import io.wyden.rest.management.domain.TransactionModel.TransactionType;
import io.wyden.test.scenariorunner.brokerdesk.agency.AgencyTradingTestBase;
import io.wyden.test.scenariorunner.data.accounting.RequestFactory;
import io.wyden.test.scenariorunner.data.infra.Epics;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.eachtest.RestManagementActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.extension.infra.OemsHealthObserverExtension;
import io.wyden.test.scenariorunner.integration.restmgmtclient.RestManagementActor;
import io.wyden.test.scenariorunner.integration.service.RateServiceClient;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.Randomizer;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.apache.commons.collections4.ListUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.function.Function;
import java.util.stream.Stream;

import static io.wyden.test.scenariorunner.data.infra.TestTags.BPCE;
import static io.wyden.test.scenariorunner.integration.restmgmtclient.searchinput.TransactionSearchInputs.byPortfolioIdAndTransactionType;
import static io.wyden.test.scenariorunner.util.WaitUtils.waitUntilElseContinue;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(Epics.REST_MANAGEMENT)
@Tag(BPCE)
@ExtendWith({
    OemsHealthObserverExtension.class,
    SecurityIntegratorExtension.class
})
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.REST_MANAGEMENT,
    Service.REFERENCE_DATA,
    Service.STORAGE
})
@Fix
public class SettlementTests extends AgencyTradingTestBase {

    private final BigDecimal TOTAL_FIAT = new BigDecimal(20000);
    private final BigDecimal TOTAL_CRYPTO = ONE;

    @RegisterExtension
    private final RestManagementActorExtension restManagementActor = new RestManagementActorExtension(clientActorExtension.clientId());

    private String quoteCurrency;
    private String baseCurrency;
    private String instrumentId;
    private String clientCustodyWallet;
    private String bankCustodyWallet;
    private String streetVenueAccount;

    private RestManagementActor restMgmtActor;

    static Stream<Arguments> orderIsSettled() {
        return Stream.of(
            Arguments.of("one", 1),
            Arguments.of("multiple", 4)
        );
    }

    @BeforeEach
    void setup(InstrumentResponseDto clientInstrument) {
        restMgmtActor = restManagementActor.actor();
        streetVenueAccount = streetConnectorSessionExtension.venueAccountName();
        quoteCurrency = clientInstrument.baseInstrument().quoteCurrency();
        baseCurrency = clientInstrument.forexSpotProperties().baseCurrency();
        instrumentId = clientInstrument.instrumentIdentifiers().instrumentId();

        RateServiceClient rateServiceClient = new RateServiceClient();
        rateServiceClient.updatePrice(baseCurrency, quoteCurrency, INITIAL_MARKET_PRICE);

        AccountOnboardingRequest sourceWalletOnboardingRequest = RequestFactory.createWalletOnboardingRequest();
        AccountOnboardingRequest targetWalletOnboardingRequest = RequestFactory.createWalletOnboardingRequest();

        restMgmtActor.accounts().onboardAccounts(List.of(sourceWalletOnboardingRequest, targetWalletOnboardingRequest));

        clientCustodyWallet = sourceWalletOnboardingRequest.id();
        bankCustodyWallet = targetWalletOnboardingRequest.id();
    }

    @ParameterizedTest(name = "{0}OrderIsPlaced_thenTransactionsAreSettled")
    @MethodSource
    public void orderIsSettled(String orderComplexity, Integer numberOfSplits, ClientSession clientSession, ConnectorMockSession mock) {
        getAcceptedAndFilledMarketBuyOrder(clientSession, mock, numberOfSplits);

        TransactionSearch streetTransactionSearch = byPortfolioIdAndTransactionType(bankPortfolio, TransactionType.STREET_CASH_TRADE);
        TransactionSearch clientTransactionSearch = byPortfolioIdAndTransactionType(clientPortfolio, TransactionType.CLIENT_CASH_TRADE);

        SettlementRequest settlementRequest = getSettlementRequest(
            streetTransactionSearch,
            clientTransactionSearch,
            null,
            numberOfSplits,
            TOTAL_FIAT
        );

        SettlementResponse settlementResponse = restMgmtActor.settlements().createSettlement(settlementRequest);

        assertThat(settlementResponse.settlementStatus()).isEqualTo(SettlementStatus.SUCCESS);

        verifyPortfolioBalanceUnsettledQuantitiesAre(ZERO, ZERO);

        verifyTransactionAre(true, streetTransactionSearch, clientTransactionSearch);
        //TODO Extend this verification after confirmation how this exactly should do work
        //TODO ENABLE THE CODE BELOW WHEN @Disabled("AC-5084") GETS RESOLVED
//        assertThat(restMgmtActor
//            .settlements()
//            .getSettlements()
//            .getAllNodes()
//            .stream()
//            .filter(e -> e.description().equals(settlementRequest.settlementId()))
//        ).hasSize(1);
    }

    @Test
    public void wrongSettlementLegs_thenTransactionIsNotSettled(ClientSession clientSession, ConnectorMockSession mock) {
        Integer numberOfSplits = 2;
        getAcceptedAndFilledMarketBuyOrder(clientSession, mock, numberOfSplits);

        TransactionSearch streetTransactionSearch = byPortfolioIdAndTransactionType(bankPortfolio, TransactionType.STREET_CASH_TRADE);
        TransactionSearch clientTransactionSearch = byPortfolioIdAndTransactionType(clientPortfolio, TransactionType.CLIENT_CASH_TRADE);

        SettlementRequest settlementRequest = getSettlementRequest(
            streetTransactionSearch,
            clientTransactionSearch,
            null,
            numberOfSplits,
            TOTAL_FIAT.add(new BigDecimal("0.01"))
        );

        SettlementResponse settlementResponse = restMgmtActor.settlements().createSettlement(settlementRequest);

        assertThat(settlementResponse.settlementStatus()).isEqualTo(SettlementStatus.FAILURE);

        verifyPortfolioBalanceUnsettledQuantitiesAre(TOTAL_FIAT.add(AGENCY_FIXED_FEE).negate(), TOTAL_CRYPTO);

        verifyTransactionAre(false, streetTransactionSearch, clientTransactionSearch);
        //TODO Extend this verification after confirmation how this exactly should do work
        //TODO ENABLE THE CODE BELOW WHEN @Disabled("AC-5084") GETS RESOLVED
//        assertThat(restMgmtActor
//            .settlements()
//            .getSettlements()
//            .getAllNodes()
//            .stream()
//            .filter(e -> e.description().equals(settlementRequest.settlementId()))
//        ).hasSize(0);
    }

    @Test
    public void forceSettlementIsTrue_thenTransactionsAreSettledAnyways(ClientSession clientSession, ConnectorMockSession mock) {
        Integer numberOfSplits = 2;
        getAcceptedAndFilledMarketBuyOrder(clientSession, mock, numberOfSplits);

        TransactionSearch streetTransactionSearch = byPortfolioIdAndTransactionType(bankPortfolio, TransactionType.STREET_CASH_TRADE);
        TransactionSearch clientTransactionSearch = byPortfolioIdAndTransactionType(clientPortfolio, TransactionType.CLIENT_CASH_TRADE);

        SettlementRequest settlementRequest = getSettlementRequestWithoutLegs(streetTransactionSearch, clientTransactionSearch, numberOfSplits);

        SettlementResponse settlementResponse = restMgmtActor.settlements().createForcedSettlement(settlementRequest);

        assertThat(settlementResponse.settlementStatus()).isEqualTo(SettlementStatus.SUCCESS_WITH_WARNING);

        verifyPortfolioBalanceUnsettledQuantitiesAre(ZERO, ZERO);

        verifyTransactionAre(true, streetTransactionSearch, clientTransactionSearch);
        //TODO Extend this verification after confirmation how this exactly should do work
        //TODO ENABLE THE CODE BELOW WHEN @Disabled("AC-5084") GETS RESOLVED
//        assertThat(restMgmtActor
//            .settlements()
//            .getSettlements()
//            .getAllNodes()
//            .stream()
//            .filter(e -> e.description().equals(settlementRequest.settlementId()))
//        ).hasSize(1);
    }

    @Test
    public void twoWalletsConfiguredForClientSide_thenTransactionsIsSettled(ClientSession clientSession, ConnectorMockSession mock) {
        AccountOnboardingRequest secondClientWalletOnboardingRequest = RequestFactory.createWalletOnboardingRequest();

        Integer numberOfSplits = 1;

        restMgmtActor.accounts().onboardAccounts(List.of(secondClientWalletOnboardingRequest));

        String secondClientCustodyWallet = secondClientWalletOnboardingRequest.id();

        getAcceptedAndFilledMarketBuyOrder(clientSession, mock, numberOfSplits);

        TransactionSearch streetTransactionSearch = byPortfolioIdAndTransactionType(bankPortfolio, TransactionType.STREET_CASH_TRADE);
        TransactionSearch clientTransactionSearch = byPortfolioIdAndTransactionType(clientPortfolio, TransactionType.CLIENT_CASH_TRADE);

        SettlementRequest settlementRequest = getSettlementRequest(
            streetTransactionSearch,
            clientTransactionSearch,
            secondClientCustodyWallet,
            numberOfSplits,
            TOTAL_FIAT
        );

        SettlementResponse settlementResponse = restMgmtActor.settlements().createSettlement(settlementRequest);

        assertThat(settlementResponse.settlementStatus()).isEqualTo(SettlementStatus.SUCCESS);

        verifyPortfolioBalanceUnsettledQuantitiesAre(ZERO, ZERO);

        verifyTransactionAre(true, streetTransactionSearch, clientTransactionSearch);
        //TODO Extend this verification after confirmation how this exactly should do work
        //TODO ENABLE THE CODE BELOW WHEN @Disabled("AC-5084") GETS RESOLVED
//        assertThat(restMgmtActor
//            .settlements()
//            .getSettlements()
//            .getAllNodes()
//            .stream()
//            .filter(e -> e.description().equals(settlementRequest.settlementId()))
//        ).hasSize(1);
    }

    @Step("Get an accepted and filled order")
    private ClientRequest getAcceptedAndFilledMarketBuyOrder(ClientSession clientSession, ConnectorMockSession mock, Integer orderSplits) {
        ClientRequest order = clientOrderFactory.defaultOrder(ClientOrderType.MARKET, ClientSide.BUY, 1, instrumentId, ClientTIF.GTC, clientPortfolio);

        clientSession.sendOrder(order);
        mock.acceptNewOrder();

        Double fillQuantity = 1.0 / orderSplits;

        while (orderSplits != 0) {
            mock.fillPart(fillQuantity, TOTAL_FIAT.doubleValue());
            orderSplits--;
        }
        return order;
    }

    @Step("Get a filled settlement request")
    private SettlementRequest getSettlementRequest(TransactionSearch streetTransactionSearch,
                                                   TransactionSearch clientTransactionSearch,
                                                   String secondClientCustodyWallet,
                                                   Integer expectedSize,
                                                   BigDecimal fiatAmount
    ) {
        if (secondClientCustodyWallet == null)
            secondClientCustodyWallet = clientCustodyWallet;

        ClientSideMapping clientSideMapping = new ClientSideMapping(
            bankCustodyWallet,
            List.of(new ConfigurationTarget(ConfigurationTargetType.FIAT, secondClientCustodyWallet), new ConfigurationTarget(ConfigurationTargetType.CRYPTO, clientCustodyWallet))
        );

        StreetSideMapping streetSideMapping = new StreetSideMapping(
            streetVenueAccount,
            List.of(new ConfigurationTarget(ConfigurationTargetType.FIAT, bankCustodyWallet), new ConfigurationTarget(ConfigurationTargetType.CRYPTO, bankCustodyWallet))
        );

        SettlementConfiguration settlementConfiguration = new SettlementConfiguration(List.of(streetSideMapping), List.of(clientSideMapping));

        SettlementLeg streetSettlementLegSend = new SettlementLeg(bankCustodyWallet, streetVenueAccount, quoteCurrency, fiatAmount);
        SettlementLeg streetSettlementLegReceive = new SettlementLeg(streetVenueAccount, bankCustodyWallet, baseCurrency, TOTAL_CRYPTO);
        SettlementLeg clientSettlementLegSend = new SettlementLeg(secondClientCustodyWallet, bankCustodyWallet, quoteCurrency, fiatAmount.add(AGENCY_FIXED_FEE));
        SettlementLeg clientSettlementLegReceive = new SettlementLeg(bankCustodyWallet, clientCustodyWallet, baseCurrency, TOTAL_CRYPTO);

        List<String> streetTrades = waitAndGetStreetTransactions(streetTransactionSearch, expectedSize);
        List<String> clientTrades = waitAndGetClientTransactions(clientTransactionSearch, expectedSize);

        return new SettlementRequest(
            Randomizer.uuid(),
            System.currentTimeMillis(),
            null,
            ListUtils.union(clientTrades, streetTrades),
            List.of(streetSettlementLegSend, streetSettlementLegReceive),
            List.of(clientSettlementLegSend, clientSettlementLegReceive),
            settlementConfiguration
        );
    }

    @Step("Get an empty settlement request")
    private SettlementRequest getSettlementRequestWithoutLegs(TransactionSearch streetTransactionSearch,
                                                              TransactionSearch clientTransactionSearch,
                                                              Integer expectedSize) {
        List<String> streetTrades = waitAndGetStreetTransactions(streetTransactionSearch, expectedSize);

        List<String> clientTrades = waitAndGetClientTransactions(clientTransactionSearch, expectedSize);

        ClientSideMapping clientSideMapping = new ClientSideMapping(
            bankCustodyWallet,
            List.of(new ConfigurationTarget(ConfigurationTargetType.FIAT, clientCustodyWallet), new ConfigurationTarget(ConfigurationTargetType.CRYPTO, clientCustodyWallet))
        );
        StreetSideMapping streetSideMapping = new StreetSideMapping(
            streetVenueAccount,
            List.of(new ConfigurationTarget(ConfigurationTargetType.FIAT, bankCustodyWallet), new ConfigurationTarget(ConfigurationTargetType.CRYPTO, bankCustodyWallet))
        );

        SettlementConfiguration settlementConfiguration = new SettlementConfiguration(List.of(streetSideMapping), List.of(clientSideMapping));

        return new SettlementRequest(
            Randomizer.uuid(),
            System.currentTimeMillis(),
            null,
            ListUtils.union(clientTrades, streetTrades),
            null,
            null,
            settlementConfiguration
        );
    }

    private List<String> waitAndGetTransactions(TransactionSearch search, Integer expectedSize, Function<TransactionSearch, List<String>> transactionFetcher) {
        List<String> utilList = new ArrayList<>();
        waitUntilElseContinue("Wait until transactions appear", () -> {
                utilList.clear();
                utilList.addAll(transactionFetcher.apply(search));
                return utilList.size() == expectedSize;
            }
        );
        assertThat(utilList).as("Transactions didn't appear in time").hasSize(expectedSize);
        return utilList;
    }

    private List<String> waitAndGetClientTransactions(TransactionSearch search, Integer expectedSize) {
        return waitAndGetTransactions(search, expectedSize, this::getClientTransactions);
    }

    private List<String> waitAndGetStreetTransactions(TransactionSearch search, Integer expectedSize) {
        return waitAndGetTransactions(search, expectedSize, this::getStreetTransactions);
    }

    private List<String> getClientTransactions(TransactionSearch clientTransactionSearch) {
        return restMgmtActor
            .transactions()
            .getClientCashTransactions(clientTransactionSearch)
            .getAllNodes()
            .stream()
            .map(ClientCashTrade::executionId)
            .toList();
    }

    private List<String> getStreetTransactions(TransactionSearch streetTransactionSearch) {
        return restMgmtActor
            .transactions()
            .getStreetCashTransactions(streetTransactionSearch)
            .getAllNodes()
            .stream()
            .map(StreetCashTrade::executionId)
            .toList();
    }

    @Step
    private void verifyTransactionAre(boolean isSettled, TransactionSearch streetTransactionSearch, TransactionSearch clientTransactionSearch) {
        List<StreetCashTrade> streetTransactions = restMgmtActor
            .transactions()
            .getStreetCashTransactions(streetTransactionSearch)
            .getAllNodes()
            .stream()
            .toList();

        List<ClientCashTrade> clientTransactions = restMgmtActor
            .transactions()
            .getClientCashTransactions(clientTransactionSearch)
            .getAllNodes()
            .stream()
            .toList();

        assertThat(streetTransactions).allSatisfy(transaction -> assertThat(transaction.settled()).isEqualTo(isSettled));
        assertThat(clientTransactions).allSatisfy(transaction -> assertThat(transaction.settled()).isEqualTo(isSettled));
    }

    @Step
    private void verifyPortfolioBalanceUnsettledQuantitiesAre(BigDecimal expectedFiatBalance, BigDecimal expectedCryptoBalance) {
        WaitUtils.waitUntilAsserted(() -> assertThat(restMgmtActor
            .portfolios()
            .getPortfolioBalances(clientPortfolio)
            .stream()
            .filter(b -> b.symbol().equals(quoteCurrency))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Cannot find portfolio=%s balance with currency=%s".formatted(clientPortfolio, quoteCurrency)))
            .unsettledQuantity()).isEqualTo(expectedFiatBalance));
        WaitUtils.waitUntilAsserted(() -> assertThat(restMgmtActor
            .portfolios()
            .getPortfolioBalances(clientPortfolio)
            .stream()
            .filter(b -> b.symbol().equals(baseCurrency))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Cannot find portfolio=%s balance with currency=%s".formatted(clientPortfolio, baseCurrency)))
            .unsettledQuantity()).isEqualTo(expectedCryptoBalance));
    }

}
