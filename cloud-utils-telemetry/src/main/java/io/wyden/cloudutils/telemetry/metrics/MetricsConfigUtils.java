package io.wyden.cloudutils.telemetry.metrics;

import com.google.common.base.CaseFormat;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import javax.annotation.Nullable;

import static java.lang.Math.max;

public final class MetricsConfigUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(MetricsConfigUtils.class);

    private MetricsConfigUtils() {
        // Empty
    }

    public static Timer createTimer(MeterRegistry meterRegistry, String name, String... tags) {
        try {
            return meterRegistry.timer(name, tags);
        } catch (Exception ex) {
            LOGGER.warn("Unable to get timer {}", name);
            return new EmptyTimer();
        }
    }

    public static void registerMeterFilter(MeterRegistry meterRegistry, @Nullable MeterFilter meterFilter) {
        try {
            if (meterFilter != null) {
                meterRegistry.config().meterFilter(meterFilter);
            }
        } catch (Exception e) {
            LOGGER.warn("Unable to register meter filter", e);
        }
    }

    public static MeterFilter createMeterFilter(String name, Duration minValue, Duration maxValue) {
        try {
            return new MeterFilter() {
                @Override
                public DistributionStatisticConfig configure(Meter.Id id, DistributionStatisticConfig config) {
                    try {
                        if (id.getName().startsWith(name)) {
                            LOGGER.info("Configuring meter Id: {} min={}, max={}", id.getName(), minValue, maxValue);
                            return DistributionStatisticConfig.builder()
                                .percentilesHistogram(true)
                                .minimumExpectedValue(max(100000, minValue.toNanos()) + 0.0)
                                .maximumExpectedValue(maxValue.toNanos() + 0.0)
                                .build()
                                .merge(config);
                        }
                        return config;
                    } catch (Exception ex) {
                        LOGGER.warn("Unable to configure meter filter {}", id, ex);
                        return config;
                    }
                }
            };
        } catch (Exception ex) {
            LOGGER.warn("Unable to configure meter filter", ex);
            return null;
        }
    }

    public static String toLowerCamel(String name) {
        return CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, name);
    }
}
