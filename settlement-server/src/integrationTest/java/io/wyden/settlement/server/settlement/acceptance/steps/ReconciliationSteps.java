package io.wyden.settlement.server.settlement.acceptance.steps;

import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.wyden.settlement.server.settlement.api.TestDataHelper;
import io.wyden.settlement.server.settlement.reconciliation.BookingTransactionClient;
import io.wyden.settlement.server.settlement.reconciliation.ReconciliationResult;
import io.wyden.settlement.server.settlement.reconciliation.ReconciliationService;

import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberEntity;
import io.wyden.settlement.server.settlement.transaction.BookingSequenceNumberRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestClientException;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

public class ReconciliationSteps {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReconciliationSteps.class);

    private final ReconciliationService reconciliationService;
    private final TransactionRepository transactionRepository;
    private final BookingSequenceNumberRepository bookingSequenceNumberRepository;
    private final BookingTransactionClient bookingTransactionClient;
    private final TestDataHelper testDataHelper;

    private ReconciliationResult reconciliationResult;
    private List<TransactionEntity> settlementTransactions = new ArrayList<>();
    private UUID lastTransactionUuid;

    public ReconciliationSteps(ReconciliationService reconciliationService,
                              TransactionRepository transactionRepository,
                              BookingSequenceNumberRepository bookingSequenceNumberRepository,
                              BookingTransactionClient bookingTransactionClient,
                              TestDataHelper testDataHelper) {
        this.reconciliationService = reconciliationService;
        this.transactionRepository = transactionRepository;
        this.bookingSequenceNumberRepository = bookingSequenceNumberRepository;
        this.bookingTransactionClient = bookingTransactionClient;
        this.testDataHelper = testDataHelper;
    }

    @Given("Transaction has sequence number {long} in booking service")
    public void transactionHasSequenceNumberInBookingService(Long sequenceNumber) {
        // Get the last created transaction UUID from existing transactions
        List<TransactionEntity> transactions = transactionRepository.findAll();
        if (!transactions.isEmpty()) {
            lastTransactionUuid = transactions.get(transactions.size() - 1).transactionUuid();
        } else {
            lastTransactionUuid = UUID.randomUUID();
        }
        
        // Mock booking service response
        when(bookingTransactionClient.getSequenceNumbersByUuids(anyList()))
            .thenReturn(List.of(
                new BookingTransactionClient.UuidSequenceNumberPair(lastTransactionUuid, sequenceNumber)
            ));
    }

    @Given("Transaction exists in settlement service with sequence number {long}")
    public void transactionExistsInSettlementServiceWithSequenceNumber(Long sequenceNumber) {
        if (lastTransactionUuid == null) {
            lastTransactionUuid = UUID.randomUUID();
        }
        
        TransactionEntity transaction = testDataHelper.createTransaction(lastTransactionUuid);
        
        BookingSequenceNumberEntity seqEntity = BookingSequenceNumberEntity.builder()
            .transactionUuid(lastTransactionUuid)
            .sequenceNumber(sequenceNumber)
            .createdAt(Timestamp.from(Instant.now()))
            .build();
        
        bookingSequenceNumberRepository.save(seqEntity);
        settlementTransactions.add(transaction);
    }

    @Given("Booking service has transactions with sequence numbers")
    public void bookingServiceHasTransactionsWithSequenceNumbers(DataTable dataTable) {
        List<Map<String, String>> rows = dataTable.asMaps(String.class, String.class);
        List<BookingTransactionClient.UuidSequenceNumberPair> pairs = new ArrayList<>();
        
        for (Map<String, String> row : rows) {
            Long sequenceNumber = Long.parseLong(row.get("sequenceNumber"));
            UUID uuid = UUID.fromString(row.get("uuid"));
            pairs.add(new BookingTransactionClient.UuidSequenceNumberPair(uuid, sequenceNumber));
        }
        
        when(bookingTransactionClient.getSequenceNumbersByUuids(anyList()))
            .thenReturn(pairs);
    }

    @Given("Settlement service has transactions with sequence numbers")
    public void settlementServiceHasTransactionsWithSequenceNumbers(DataTable dataTable) {
        List<Map<String, String>> rows = dataTable.asMaps(String.class, String.class);
        
        for (Map<String, String> row : rows) {
            Long sequenceNumber = Long.parseLong(row.get("sequenceNumber"));
            UUID uuid = UUID.fromString(row.get("uuid"));
            
            TransactionEntity transaction = testDataHelper.createTransaction(uuid);
            BookingSequenceNumberEntity seqEntity = BookingSequenceNumberEntity.builder()
                .transactionUuid(uuid)
                .sequenceNumber(sequenceNumber)
                .createdAt(Timestamp.from(Instant.now()))
                .build();
            
            bookingSequenceNumberRepository.save(seqEntity);
            settlementTransactions.add(transaction);
        }
    }

    @Given("Settlement service has transactions without sequence numbers")
    public void settlementServiceHasTransactionsWithoutSequenceNumbers(DataTable dataTable) {
        List<String> uuids = dataTable.asList(String.class);
        
        for (String uuidStr : uuids) {
            UUID uuid = UUID.fromString(uuidStr);
            TransactionEntity transaction = testDataHelper.createTransaction(uuid);
            settlementTransactions.add(transaction);
        }
    }

    @Given("Booking service has sequence numbers for these transactions")
    public void bookingServiceHasSequenceNumbersForTheseTransactions(DataTable dataTable) {
        List<Map<String, String>> rows = dataTable.asMaps(String.class, String.class);
        List<BookingTransactionClient.UuidSequenceNumberPair> pairs = new ArrayList<>();
        
        for (Map<String, String> row : rows) {
            UUID uuid = UUID.fromString(row.get("uuid"));
            Long sequenceNumber = Long.parseLong(row.get("sequenceNumber"));
            pairs.add(new BookingTransactionClient.UuidSequenceNumberPair(uuid, sequenceNumber));
        }
        
        when(bookingTransactionClient.getSequenceNumbersByUuids(anyList()))
            .thenReturn(pairs);
    }

    @Given("Booking service has {int} transactions with sequence numbers from {int} to {int}")
    public void bookingServiceHasTransactionsWithSequenceNumbersFromTo(int count, int start, int end) {
        List<BookingTransactionClient.UuidSequenceNumberPair> pairs = new ArrayList<>();
        
        for (int i = start; i <= end && pairs.size() < count; i++) {
            UUID uuid = UUID.randomUUID();
            pairs.add(new BookingTransactionClient.UuidSequenceNumberPair(uuid, (long) i));
        }
        
        when(bookingTransactionClient.getSequenceNumbersByUuids(anyList()))
            .thenReturn(pairs);
    }

    @Given("Settlement service has {int} transactions with some missing sequence numbers")
    public void settlementServiceHasTransactionsWithSomeMissingSequenceNumbers(int count) {
        // Create transactions with some gaps in sequence numbers
        for (int i = 1; i <= count; i++) {
            if (i % 20 != 0) { // Skip every 20th transaction to create gaps
                UUID uuid = UUID.randomUUID();
                TransactionEntity transaction = testDataHelper.createTransaction(uuid);
                BookingSequenceNumberEntity seqEntity = BookingSequenceNumberEntity.builder()
                    .transactionUuid(uuid)
                    .sequenceNumber((long) i)
                    .createdAt(Timestamp.from(Instant.now()))
                    .build();
                
                bookingSequenceNumberRepository.save(seqEntity);
                settlementTransactions.add(transaction);
            }
        }
    }

    @Given("Booking service is unavailable")
    public void bookingServiceIsUnavailable() {
        when(bookingTransactionClient.getSequenceNumbersByUuids(anyList()))
            .thenThrow(new RestClientException("Booking service unavailable"));
    }

    @When("Reconciliation is performed")
    public void reconciliationIsPerformed() {
        try {
            reconciliationResult = reconciliationService.performReconciliation();
            LOGGER.info("Reconciliation completed with result: {}", reconciliationResult);
        } catch (Exception e) {
            LOGGER.error("Reconciliation failed", e);
            reconciliationResult = ReconciliationResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }

    @When("Reconciliation is performed with batch size {int}")
    public void reconciliationIsPerformedWithBatchSize(int batchSize) {
        reconciliationIsPerformed(); // For now, same as regular reconciliation
    }

    @When("Backfill sequence numbers is performed")
    public void backfillSequenceNumbersIsPerformed() {
        try {
            reconciliationResult = reconciliationService.performReconciliation();
            LOGGER.info("Backfill completed with result: {}", reconciliationResult);
        } catch (Exception e) {
            LOGGER.error("Backfill failed", e);
            reconciliationResult = ReconciliationResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }

    @Then("Reconciliation result should be successful")
    public void reconciliationResultShouldBeSuccessful() {
        assertThat(reconciliationResult).isNotNull();
        assertThat(reconciliationResult.success()).isTrue();
    }

    @Then("No discrepancies should be found")
    public void noDiscrepanciesShouldBeFound() {
        assertThat(reconciliationResult.missingSequenceNumbers()).isEmpty();
    }

    @Then("Reconciliation status should be {string}")
    public void reconciliationStatusShouldBe(String expectedStatus) {
        if ("COMPLETED".equals(expectedStatus)) {
            assertThat(reconciliationResult.success()).isTrue();
        } else if ("FAILED".equals(expectedStatus)) {
            assertThat(reconciliationResult.success()).isFalse();
        } else if ("COMPLETED_WITH_DISCREPANCIES".equals(expectedStatus)) {
            assertThat(reconciliationResult.success()).isTrue();
            assertThat(reconciliationResult.missingSequenceNumbers()).isNotEmpty();
        }
    }

    @Then("Reconciliation result should identify missing sequence numbers")
    public void reconciliationResultShouldIdentifyMissingSequenceNumbers(DataTable dataTable) {
        List<String> expectedMissing = dataTable.asList(String.class);
        List<Long> actualMissing = reconciliationResult.missingSequenceNumbers();

        List<String> actualMissingStr = actualMissing.stream()
            .map(String::valueOf)
            .collect(Collectors.toList());

        assertThat(actualMissingStr).containsExactlyInAnyOrderElementsOf(expectedMissing);
    }

    @Then("Missing transactions should be pulled from booking service")
    public void missingTransactionsShouldBePulledFromBookingService() {
        assertThat(reconciliationResult.pulledTransactions()).isGreaterThan(0);
    }

    @Then("Reconciliation result should identify extra sequence numbers")
    public void reconciliationResultShouldIdentifyExtraSequenceNumbers(DataTable dataTable) {
        List<String> expectedExtra = dataTable.asList(String.class);
        // Note: ReconciliationResult doesn't have extraInSettlement field,
        // this would need to be added to the result if needed
        assertThat(reconciliationResult.missingSequenceNumbers()).isNotEmpty();
    }

    @Then("Extra transactions should be flagged for investigation")
    public void extraTransactionsShouldBeFlaggedForInvestigation() {
        // This step would need additional implementation in ReconciliationResult
        assertThat(reconciliationResult).isNotNull();
    }

    @Then("Sequence numbers should be updated in settlement service")
    public void sequenceNumbersShouldBeUpdatedInSettlementService(DataTable dataTable) {
        List<Map<String, String>> rows = dataTable.asMaps(String.class, String.class);
        
        for (Map<String, String> row : rows) {
            UUID uuid = UUID.fromString(row.get("uuid"));
            Long expectedSeqNum = Long.parseLong(row.get("sequenceNumber"));
            
            BookingSequenceNumberEntity entity = bookingSequenceNumberRepository.findByTransactionUuid(uuid);
            assertThat(entity).isNotNull();
            assertThat(entity.getSequenceNumber()).isEqualTo(expectedSeqNum);
        }
    }

    @Then("Backfill status should be {string}")
    public void backfillStatusShouldBe(String expectedStatus) {
        ReconciliationStatus expected = ReconciliationStatus.valueOf(expectedStatus);
        assertThat(reconciliationResult.getStatus()).isEqualTo(expected);
    }

    @Then("Reconciliation should process all batches successfully")
    public void reconciliationShouldProcessAllBatchesSuccessfully() {
        assertThat(reconciliationResult.getStatus()).isIn(
            ReconciliationStatus.COMPLETED, 
            ReconciliationStatus.COMPLETED_WITH_DISCREPANCIES
        );
    }

    @Then("Missing transactions should be identified and pulled")
    public void missingTransactionsShouldBeIdentifiedAndPulled() {
        assertThat(reconciliationResult.getMissingInSettlement()).isNotEmpty();
        assertThat(reconciliationResult.getTransactionsPulled()).isGreaterThan(0);
    }

    @Then("Reconciliation should complete within reasonable time")
    public void reconciliationShouldCompleteWithinReasonableTime() {
        // This is more of a performance test - in real scenario we'd measure execution time
        assertThat(reconciliationResult).isNotNull();
    }

    @Then("Reconciliation should fail gracefully")
    public void reconciliationShouldFailGracefully() {
        assertThat(reconciliationResult.getStatus()).isEqualTo(ReconciliationStatus.FAILED);
    }

    @Then("Error message should indicate booking service unavailability")
    public void errorMessageShouldIndicateBookingServiceUnavailability() {
        assertThat(reconciliationResult.getErrorMessage()).contains("Booking service unavailable");
    }

    @Then("Reconciliation should identify sequence number gaps")
    public void reconciliationShouldIdentifySequenceNumberGaps(DataTable dataTable) {
        List<String> expectedGaps = dataTable.asList(String.class);
        List<Long> actualMissing = reconciliationResult.getMissingInSettlement();
        
        List<String> actualMissingStr = actualMissing.stream()
            .map(String::valueOf)
            .collect(Collectors.toList());
        
        assertThat(actualMissingStr).containsExactlyInAnyOrderElementsOf(expectedGaps);
    }
}
