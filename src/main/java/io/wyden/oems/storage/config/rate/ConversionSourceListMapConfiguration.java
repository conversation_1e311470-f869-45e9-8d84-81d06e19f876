package io.wyden.oems.storage.config.rate;

import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.config.StoredMapConfigBean;
import io.wyden.oems.storage.web.StringProtobufMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.published.rate.ConversionSourceList;
import io.wyden.rate.domain.map.ConversionSourceListMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static io.wyden.oems.storage.config.MapStores.defaultEagerStoreConfig;

@Configuration
public class ConversionSourceListMapConfiguration {

    @Bean
    HazelcastMapConfigBean conversionSourcListeMapConfig(ConversionSourceListMapStore mapStore,
                                                         @Value("${hz.rate.conversionsource.config.writedelay}") int writeDelay,
                                                         @Value("${hz.rate.conversionsource.config.mapstore}") boolean mapStoreEnabled) {
        HazelcastMapConfig hazelcastMapConfig = new ConversionSourceListMapConfig();
        MapStoreConfig mapStoreConfig = defaultEagerStoreConfig(mapStore, writeDelay, mapStoreEnabled);
        return new StoredMapConfigBean(hazelcastMapConfig, mapStoreConfig);
    }

    @Bean
    WebAccess conversionSourceListMapWebAccess(HazelcastInstance hazelcast, ConversionSourceListMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new ConversionSourceListMapConfig();
        return new StringProtobufMapWebAccess<>(hazelcast, hazelcastMapConfig, ConversionSourceList.getDefaultInstance(), mapStore);
    }
}
