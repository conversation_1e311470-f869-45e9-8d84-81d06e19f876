import { Dialog } from '@ui/Dialog';
import { useTranslation } from 'react-i18next';
import { usePreTradeCheckStore } from '@wyden/features/risk-management/pre-trade-check/usePreTradeCheckStore';
import {
  PTCForm,
  BasicPTCFormSchema,
} from '@wyden/features/risk-management/pre-trade-check/schema-adapter/AddPTCForm';
import {
  PreTradeCheckChannels,
  PreTradeCheckLevel,
  PreTradeCheckPropertyType,
  usePreTradeCheckFormSchemaQuery,
  usePreTradeChecksQuery,
  useSavePreTradeCheckMutation,
} from '@wyden/services/graphql/generated/graphql';
import { Label } from '@ui/Typography/Label';
import {
  Notification,
  useNotification,
} from '@wyden/features/error-indicators/notification/useNotification';
import { v4 as uuidv4 } from 'uuid';
import { usePortfolioTags } from '@wyden/features/focus/usePortfolioTags';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { Alert } from '@ui/Alert';
import { usePtcSchemaToZodAdapter } from '@wyden/features/risk-management/pre-trade-check/schema-adapter/ptcSchemaToZodAdapter';
import { Paragraph } from '@ui/Typography/Paragraph';
import startCase from 'lodash/startCase';

export const PRE_TRADE_CHECK_MODAL_TEST_ID = 'pre-trade-check-modal';

export const AddPTCDialogContent = () => {
  const { t } = useTranslation();
  const { addPTCDialogOpen, toggleAddPTCDialog, selectedCheckType } = usePreTradeCheckStore();
  const { data } = usePreTradeCheckFormSchemaQuery();
  const { refetch } = usePreTradeChecksQuery();

  const [savePreTradeCheck, { error, loading, reset }] = useSavePreTradeCheckMutation();
  const { addMessage } = useNotification();
  const { tags } = usePortfolioTags('network-only');
  const tagsWithID = tags.map((tag) => ({
    ...tag,
    id: `${tag.key}: ${tag.value}`,
  }));
  const checkTypeOptions = data?.preTradeCheckFormSchema.map((value) => ({
    id: value.type,
    label: startCase(value.type),
  }));
  const ptcSchema =
    data?.preTradeCheckFormSchema.find((value) => value.type === selectedCheckType)
      ?.configuration || [];
  const { generatedConfig, generatedProps } = usePtcSchemaToZodAdapter(
    ptcSchema,
    selectedCheckType,
  );
  const handleClose = () => {
    toggleAddPTCDialog();
    reset();
  };

  return (
    <Dialog
      data-testid={PRE_TRADE_CHECK_MODAL_TEST_ID}
      open={addPTCDialogOpen}
      onClose={handleClose}
    >
      <PTCForm
        defaultValues={{
          channels: [PreTradeCheckChannels.Api, PreTradeCheckChannels.Ui],
        }}
        formProps={{
          loading,
          onClose: handleClose,
        }}
        schema={BasicPTCFormSchema.extend(generatedConfig)}
        props={{
          ...generatedProps,
          id: {
            label: t('common.id'),
            disabled: true,
          },
          checkType: {
            label: t('riskManagement.addPTCForm.checkType'),
            options: checkTypeOptions || [],
          },
          checkLevel: {
            label: t('riskManagement.addPTCForm.checkLevel'),
            disableRipple: true,
            align: 'equal',
            options: [
              {
                id: PreTradeCheckLevel.Warn,
                label: 'Warning',
              },
              {
                id: PreTradeCheckLevel.Block,
                label: 'Block',
              },
            ],
          },
          portfolioTags: {
            multiple: true,
            label: t('riskManagement.addPTCForm.portfolioTags'),
            options: tagsWithID.map((tag) => tag.id),
            freeSolo: false,
          },
        }}
        onSubmit={(values) => {
          const { checkType, checkLevel, portfolio, portfolioTags, channels, ...rest } = values;
          const request = {
            type: checkType,
            level: checkLevel,
            portfolios: portfolio ? portfolio.map((p) => p.id) : [],
            portfolioTags: tagsWithID
              .filter((tag) => portfolioTags?.includes(tag.id))
              .map((tag) => ({
                key: tag.key,
                value: tag.value,
              })),
            channels: channels || [],
            id: uuidv4(),
            configuration: Object.entries(rest)
              .filter(([, value]) => value)
              .map(([key, value]) => ({
                name: key,
                values: Array.isArray(value)
                  ? value.map((v) => (typeof v === 'string' ? v : String(v)))
                  : [String(value)],
                type:
                  data?.preTradeCheckFormSchema
                    .find((value) => value.type === checkType)
                    ?.configuration?.find((value) => value.name === key)?.type ||
                  PreTradeCheckPropertyType.String,
              })),
          };
          savePreTradeCheck({
            variables: {
              request,
            },
          })
            .then(() => {
              toggleAddPTCDialog();
              addMessage(Notification.SUCCESS, t('riskManagement.addPTCForm.preMessage'));
              return refetch();
            })
            .catch((err) => {
              const isInternalError =
                err.graphQLErrors[0]?.extensions?.classification === 'INTERNAL_ERROR';
              if (isInternalError) {
                toggleAddPTCDialog();
                addMessage(Notification.ERROR, t('riskManagement.addPTCForm.preMessage'));
              }
            });
        }}
      >
        {/* eslint-disable-next-line @typescript-eslint/no-unused-vars */}
        {({ id, checkType, checkLevel, portfolio, portfolioTags, channels, ...rest }) => {
          return (
            <>
              {checkType}
              {checkLevel}
              {portfolio}
              {portfolioTags}
              <Paragraph variant={'xsmall'}>
                {t('riskManagement.addPTCForm.defaultUnselectedOptionsBehavior')}
              </Paragraph>
              {channels}
              {selectedCheckType && Object.values(rest).length > 0 && (
                <ConfigurationLabel variant="small">
                  {t('riskManagement.addPTCForm.configuration')}
                </ConfigurationLabel>
              )}
              {Object.values(rest)}
              {error && <Alert severity="error">{error?.message}</Alert>}
            </>
          );
        }}
      </PTCForm>
    </Dialog>
  );
};

const ConfigurationLabel = styled(Label)`
  font-size: 11px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;
