package io.wyden.booking.snapshotter.domain.transaction.payment;

import io.wyden.booking.snapshotter.domain.transaction.SettlementType;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.booking.snapshotter.domain.transaction.TransactionFee;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * A payment represents an inbound from or outbound transfer to an external system outside Wyden oversight,
 * e.g., a deposit from a Metamask wallet onto an exchange account, or withdrawal from a custody account
 * to an external blockchain address.
 */
@Entity
public abstract class Payment extends Transaction {

    /**
     * the currency of the payment
     */
    protected String currency;

    /**
     * the quantity that was paid
     */
    protected BigDecimal quantity;

    /**
     * the portfolio the payment was received or sent
     */
    protected String portfolioId;

    /**
     * the custody account the payment was received or sent
     */
    protected String accountId;

    /**
     * the account (from/to) payment fees will be charged to
     */
    protected String feeAccountId;

    /**
     * the portfolio payment fees will be charged to
     */
    protected String feePortfolioId;

    public Payment(String uuid,
                   String reservationRef,
                   ZonedDateTime dateTime,
                   String executionId,
                   String venueExecutionId,
                   Collection<TransactionFee> fees,
                   String description,
                   boolean isLive,
                   String currency,
                   BigDecimal quantity,
                   String portfolioId,
                   String accountId,
                   String feeAccountId,
                   String feePortfolioId) {
        super(uuid, reservationRef, dateTime, executionId, venueExecutionId, fees, description, isLive, SettlementType.INSTANT_SETTLEMENT);
        this.currency = currency;
        this.quantity = quantity;
        this.portfolioId = portfolioId;
        this.accountId = accountId;
        this.feeAccountId = feeAccountId;
        this.feePortfolioId = feePortfolioId;
    }

    protected Payment() {
        // JPA
    }

    @Transient
    @Override
    protected Set<String> getInstruments() {
        return Set.of(currency);
    }

    @Transient
    @Override
    public Set<String> getAffectedPortfolioIds() {
        return Stream.of(portfolioId, feePortfolioId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    }

    @Override
    @Transient
    public Set<String> getAffectedAccountIds() {
        return Stream.of(accountId, feeAccountId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    }

    public String getCurrency() {
        return currency;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getAccountId() {
        return accountId;
    }

    public String getFeeAccountId() {
        return feeAccountId;
    }

    public String getFeePortfolioId() {
        return feePortfolioId;
    }
}
