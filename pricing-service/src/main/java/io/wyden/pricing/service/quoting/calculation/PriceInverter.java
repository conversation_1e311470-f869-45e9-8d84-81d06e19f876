package io.wyden.pricing.service.quoting.calculation;

import io.wyden.published.marketdata.BidAskQuote;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class PriceInverter {

    private static final int DEFAULT_SCALE = 18;

    public static BidAskQuote invertPrice(final BidAskQuote quote) {
        String askPrice = quote.getAskPrice();
        String bidPrice = quote.getBidPrice();

        String invertedAskPrice = invert(askPrice);
        String invertedBidPrice = invert(bidPrice);

        return quote.toBuilder()
            .setAskPrice(invertedAskPrice)
            .setBidPrice(invertedBidPrice)
            .build();
    }

    public static String invert(String price) {
        BigDecimal asBigDecimal = new BigDecimal(price);
        return BigDecimal.ONE.divide(asBigDecimal, DEFAULT_SCALE, RoundingMode.HALF_UP)
            .stripTrailingZeros()
            .toPlainString();
    }
}
