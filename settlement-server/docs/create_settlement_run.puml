@startuml
participant UI
participant Booking
participant SettlementService

Booking -> SettlementService: BookingCompletedEvent
SettlementService -> SettlementService: Store transactions data (streetCashTrade type only) in transactions table

UI -> SettlementService: Create SettlementRun1
SettlementService -> SettlementService: 1. Create settlement run \n2. Create settlement legs for all asset/account \n3. Set <b>settlement run id</b> for all unsettled transactions

UI -> SettlementService: Create SettlementRun2

SettlementService -> UI: All Transactions are assigned to settlements
@enduml
