package io.wyden.orderhistory.service;

import com.google.common.collect.Lists;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.orderhistory.model.CancelReplaceRequestEntity;
import io.wyden.orderhistory.model.CollectionPredicateInput;
import io.wyden.orderhistory.model.DatePredicateInput;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.orderhistory.model.SimplePredicateInput;
import io.wyden.orderhistory.model.SortingField;
import io.wyden.orderhistory.model.SortingOrder;
import io.wyden.orderhistory.repository.OrderStateRepository;
import io.wyden.orderhistory.service.utils.CancelReplaceRequestMapper;
import io.wyden.orderhistory.service.utils.DbUtils;
import io.wyden.orderhistory.service.utils.OrderStateMapper;
import io.wyden.published.client.ClientCancelRejectResponseTo;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static io.wyden.cloudutils.tools.DateUtils.instantToEpochMicros;
import static io.wyden.orderhistory.service.utils.CancelReplaceRequestMapper.requestToEntity;
import static io.wyden.orderhistory.service.utils.OrderStateMapper.entityToProto;
import static io.wyden.published.client.ClientCancelRejectResponseTo.ORDER_CANCEL_REPLACE_REQUEST;
import static io.wyden.published.client.ClientCancelRejectResponseTo.ORDER_CANCEL_REQUEST;

@Deprecated
@Service
public class OrderHistoryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderHistoryService.class);

    private final OrderStateRepository orderStateRepository;
    private final OrderStateProcessor orderStateProcessor;

    public OrderHistoryService(@Qualifier("meteredPostgresOrderStateRepository") OrderStateRepository orderStateRepository,
                               OrderStateProcessor orderStateProcessor) {
        this.orderStateRepository = orderStateRepository;
        this.orderStateProcessor = orderStateProcessor;
    }

    public void processClientNewOrderRequest(ClientRequest clientRequest) {
        LOGGER.debug("Processing ORDER_SINGLE ClientRequest: {}", clientRequest);

        Optional<OrderStateEntity> optionalOrderState = orderStateRepository.findOrderStateSnapshotByOrderId(clientRequest.getOrderId());
        optionalOrderState.ifPresentOrElse(state -> LOGGER.info("OrderId already present in Order history, skipping ClientRequest: {}", clientRequest),
            () -> orderStateProcessor.processNewState(clientRequest));
    }

    /*
    Stores the clientRequest CANCEL_REPLACE in a db as a temporary. After receiving a response of the type CLIENT_EXEC_TYPE_REPLACED
    for an original order request can be got from a db and stored and emitted as a OrderState
    * */
    public void processClientCancelReplaceRequest(ClientRequest clientRequest) {
        LOGGER.debug("Processing CANCEL_REPLACE ClientRequest: {}", clientRequest);
        CancelReplaceRequestEntity cancelReplaceRequest = requestToEntity(clientRequest);
        orderStateRepository.saveCancelReplaceRequest(cancelReplaceRequest);
    }

    public void processClientExecutionReport(ClientResponse clientResponse) {
        if (clientResponse.getExecType() == ClientExecType.CLIENT_EXEC_TYPE_REPLACED) {
            processClientResponseExecTypeReplaced(clientResponse);
        } else {
            processClientResponseExecutionReport(clientResponse);
        }
    }

    public void processClientCancelReject(ClientResponse clientResponse) {
        ClientCancelRejectResponseTo cancelRejectResponseTo = clientResponse.getCancelRejectResponseTo();
        if (cancelRejectResponseTo.equals(ORDER_CANCEL_REQUEST) || cancelRejectResponseTo.equals(ORDER_CANCEL_REPLACE_REQUEST)) {
            processClientResponseCancelReject(clientResponse);
        } else {
            LOGGER.warn("CancelRejectResponseTo: {} unsupported. CancelReject won't be processed", cancelRejectResponseTo);
        }
    }

    public void processOemsRequest(OemsRequest oemsRequest) {
        LOGGER.debug("Processing OemsRequest: {}", oemsRequest);

        Optional<OrderStateEntity> optionalOrderState = orderStateRepository.findOrderStateSnapshotByOrderId(oemsRequest.getOrderId());
        optionalOrderState.ifPresentOrElse(state -> orderStateProcessor.processExistingState(state, oemsRequest),
            () -> orderStateProcessor.processNewState(oemsRequest));
    }

    public void processOemsResponse(OemsResponse oemsResponse) {
        LOGGER.debug("Processing OemsResponse: {}", oemsResponse);

        Optional<OrderStateEntity> optionalOrderState = orderStateRepository.findOrderStateSnapshotByOrderId(oemsResponse.getOrderId());
        optionalOrderState.ifPresentOrElse(state -> orderStateProcessor.processExistingState(state, oemsResponse),
            () -> orderStateProcessor.processNewState(oemsResponse));
    }

    public List<OrderState> getOrderStateSnapshots(OrderHistorySearchInput orderHistorySearchInput) {
        LOGGER.debug("Getting OrderStateSnapshots for input: {}", orderHistorySearchInput);
        OrderHistorySearchInput newSearchInput = mapMultipleSimpleInputsToCollectionsIfExists(orderHistorySearchInput);
        return orderStateRepository.findOrderStateSnapshotsBySearchInput(newSearchInput).stream()
            .map(OrderStateMapper::entityToProto)
            .toList();
    }

    public CursorConnection getOrderStateSnapshotsPaged(OrderHistorySearchInput orderHistorySearchInput) {
        LOGGER.debug("Getting paged OrderStateSnapshots for input: {}", orderHistorySearchInput);
        OrderHistorySearchInput newSearchInput = mapMultipleSimpleInputsToCollectionsIfExists(orderHistorySearchInput);
        List<OrderStateEntity> orderStateSnapshots = orderStateRepository.findOrderStateSnapshotsBySearchInput(newSearchInput);

        // count all orders matching predicate
        Integer totalCount = orderStateRepository.countOrderStateSnapshotsBySearchInput(newSearchInput.withoutCursor());

        // count all orders matching predicate + after cursor
        Integer count = orderStateRepository.countOrderStateSnapshotsBySearchInput(newSearchInput);

        LOGGER.debug("Found {} orderStateSnapshots", orderStateSnapshots.size());
        return getWrapped(orderStateSnapshots, count, totalCount.longValue());
    }

    public List<OrderState> getOrderStates(OrderHistorySearchInput orderHistorySearchInput) {
        LOGGER.debug("Getting OrderStates for input: {}", orderHistorySearchInput);
        return orderStateRepository.findOrderStatesBySearchInput(orderHistorySearchInput).stream()
            .map(OrderStateMapper::entityToProto)
            .toList();
    }

    public CursorConnection getOrderStatesPaged(OrderHistorySearchInput orderHistorySearchInput) {
        LOGGER.debug("Getting paged OrderStates for input: {}", orderHistorySearchInput);
        List<OrderStateEntity> orderStates = orderStateRepository.findOrderStatesBySearchInput(orderHistorySearchInput);

        // count all orders matching predicate
        Integer totalCount = orderStateRepository.countOrderStatesBySearchInput(orderHistorySearchInput.withoutCursor());

        // count all orders matching predicate + after cursor
        Integer count = orderStateRepository.countOrderStatesBySearchInput(orderHistorySearchInput);

        LOGGER.debug("Found {} orderStates", orderStates.size());
        return getWrapped(orderStates, count, totalCount.longValue());
    }

    private void processClientResponseExecTypeReplaced(ClientResponse clientResponse) {
        LOGGER.debug("Processing ExecutionReport of type ExecTypeReplaced: {}", clientResponse);
        String origOrderId = clientResponse.getOrigOrderId();
        String orderId = clientResponse.getOrderId();
        if (StringUtils.isNotEmpty(origOrderId) && StringUtils.isNotEmpty(orderId)) {
            processExecTypeReplacedOrigOrder(origOrderId);
            processExecTypeReplacedReplacingOrder(orderId, origOrderId, clientResponse);
        } else if (StringUtils.isNotEmpty(origOrderId)) {
            processExecTypeReplacedOrigOrder(origOrderId);
        } else {
            LOGGER.warn("Unknown OrigOrderId ({}) and NewOrderId ({}), ClientResponse won't be applied: {}", origOrderId, orderId, clientResponse);
        }
    }

    private void processClientResponseExecutionReport(ClientResponse clientResponse) {
        LOGGER.debug("Processing ClientExecutionReport of exec type ({}):\r {}", clientResponse.getExecType().name(), clientResponse);

        Optional<OrderStateEntity> orderState = orderStateRepository.findOrderStateSnapshotByOrderId(clientResponse.getOrderId());
        orderState.ifPresentOrElse(state -> orderStateProcessor.processExistingState(state, clientResponse),
            () -> logOrderStateNotFoundForClientResponse(clientResponse, clientResponse.getOrderId()));
    }

    private void processExecTypeReplacedOrigOrder(String origOrderId) {
        LOGGER.debug("Processing Original Order with origOrderId: {}", origOrderId);

        Optional<OrderStateEntity> origOrderStateEntity = orderStateRepository.findOrderStateSnapshotByOrderId(origOrderId);
        origOrderStateEntity.ifPresentOrElse(orderStateProcessor::updateStateToCanceled,
            () -> LOGGER.warn("Cannot find OrderStateEntity for origOrderId: {}. OrderState won't be updated", origOrderId));
    }

    private void processExecTypeReplacedReplacingOrder(String newOrderId, String origOrderId, ClientResponse clientResponse) {
        LOGGER.debug("Processing New Order with orderId: {}", newOrderId);

        Optional<CancelReplaceRequestEntity> cancelReplaceRequest = orderStateRepository.findCancelReplaceRequestByOrderId(newOrderId, origOrderId);
        cancelReplaceRequest.ifPresentOrElse(cancelReplaceRequestEntity -> {
            processClientCancelReplaceRequest(cancelReplaceRequestEntity);
            processClientResponseExecutionReport(clientResponse);
            orderStateRepository.deleteCancelReplaceRequest(cancelReplaceRequestEntity);
        }, () -> LOGGER.warn("Unknown OrigOrderId ({}) or OrderId ({}), ClientRequest won't be applied", origOrderId, newOrderId));
    }

    private void processClientResponseCancelReject(ClientResponse clientResponse) {
        LOGGER.debug("Processing CancelReject: {}", clientResponse);
        String newOrderId = clientResponse.getOrderId();
        String origOrderId = clientResponse.getOrigOrderId();
        if (StringUtils.isNotEmpty(newOrderId) && StringUtils.isNotEmpty(origOrderId)) {
            findAndDeleteCachedCancelReplaceRequest(newOrderId, origOrderId);
        } else if (StringUtils.isNotEmpty(origOrderId)) {
            LOGGER.info("NewOrderId is empty - CancelReject for CancelRequest.");
        } else {
            LOGGER.warn("NewOrderId and OrigOrderId is empty, CachedCancelReplaceRequest won't be deleted");
        }

        applyCancelRejectedToOrderState(clientResponse, origOrderId);
    }

    private void processClientCancelReplaceRequest(CancelReplaceRequestEntity cancelReplaceRequestEntity) {
        ClientRequest clientRequest = CancelReplaceRequestMapper.entityToClientRequest(cancelReplaceRequestEntity);
        LOGGER.debug("Processing CancelReplaceRequest: {}", clientRequest);

        Optional<OrderStateEntity> previousState = orderStateRepository.findOrderStateSnapshotByOrderId(clientRequest.getOrderId());
        previousState.ifPresentOrElse(previous -> LOGGER.warn("OrderId already present in Order history, skipping CancelReplaceRequest: {}", clientRequest),
            () -> orderStateProcessor.processCancelReplaceNewState(clientRequest));
    }

    private void findAndDeleteCachedCancelReplaceRequest(String newOrderId, String origOrderId) {
        Optional<CancelReplaceRequestEntity> cancelReplaceRequestEntity = orderStateRepository.findCancelReplaceRequestByOrderId(newOrderId, origOrderId);
        cancelReplaceRequestEntity.ifPresentOrElse(orderStateRepository::deleteCancelReplaceRequest,
            () -> LOGGER.warn("Cannot find CancelReplaceRequestEntity for newOrderId: {} and origOrderId: {}. CancelReplaceRequest won't be deleted", newOrderId, origOrderId));
    }

    private void applyCancelRejectedToOrderState(ClientResponse clientResponse, String orderId) {
        Optional<OrderStateEntity> orderState = orderStateRepository.findOrderStateSnapshotByOrderId(orderId);
        orderState.ifPresentOrElse(state -> orderStateProcessor.updateStateToRejected(state, clientResponse),
            () -> logOrderStateNotFoundForClientResponse(clientResponse, orderId));
    }

    Optional<OrderStateEntity> getOrderStateSnapshotByOrderId(String orderId) {
        return orderStateRepository.findOrderStateSnapshotByOrderId(orderId);
    }

    void deleteAll() {
        orderStateRepository.deleteAllOrderStateSnapshots();
        orderStateRepository.deleteAllOrderStates();
    }

    private static @NotNull OrderHistorySearchInput mapMultipleSimpleInputsToCollectionsIfExists(OrderHistorySearchInput orderHistorySearchInput) {
        // map orderHistorySearchInput to new objects Conditions. Map singleSearchInput with same fields to collection
        Collection<SimplePredicateInput> simplePredicateInputs = Lists.newArrayList(orderHistorySearchInput.simplePredicates());
        Collection<CollectionPredicateInput> collectionPredicateInputs = orderHistorySearchInput.collectionPredicates();
        Collection<DatePredicateInput> datePredicateInputs = Lists.newArrayList(orderHistorySearchInput.datePredicateInputs());
        Integer first = orderHistorySearchInput.first();
        String after = orderHistorySearchInput.after();
        SortingOrder sortingOrder = orderHistorySearchInput.sortingOrder();

        // Map SimplePredicateInputs with more than 1 value to CollectionPredicateInput
        List<CollectionPredicateInput> collectionPredicateInputsFromSingle = simplePredicateInputs.stream()
            .collect(Collectors.groupingBy(SimplePredicateInput::fieldName))
            .entrySet().stream()
            .filter(stringListEntry -> stringListEntry.getValue().size() > 1)
            .map(stringListEntry -> new CollectionPredicateInput(
                CollectionPredicateInput.PredicateType.IN,
                CollectionPredicateInput.Field.valueOf(stringListEntry.getKey()),
                stringListEntry.getValue().stream().map(SimplePredicateInput::value).toList()))
            .toList();

        // Concat collectionPredicateInputsFromSingle and collectionPredicateInputs from orderHistorySearchInput and remove duplicates
        List<CollectionPredicateInput> joinedCollectionInput = Stream.concat(collectionPredicateInputs.stream(), collectionPredicateInputsFromSingle.stream())
            .collect(Collectors.groupingBy(CollectionPredicateInput::fieldName))
            .entrySet().stream()
            .map(stringListEntry -> new CollectionPredicateInput(
                CollectionPredicateInput.PredicateType.IN,
                CollectionPredicateInput.Field.valueOf(stringListEntry.getKey()),
                stringListEntry.getValue().stream().flatMap(predicateInput -> predicateInput.value().stream()).distinct().toList()
            ))
            .toList();

        // Remove simple search inputs mapped to collection search inputs
        simplePredicateInputs
            .removeIf(simplePredicateInput -> joinedCollectionInput.stream().map(CollectionPredicateInput::fieldName).toList().contains(simplePredicateInput.fieldName()));

        // unify dates from epoch or ISO_DATE_TIME/ISO_INSTANT to ISO_INSTANT
        List<DatePredicateInput> datePredicateInputsUnified = datePredicateInputs.stream()
            .map(DbUtils::unifyDatesInDatePredicateInput)
            .toList();

        return new OrderHistorySearchInput(Lists.newArrayList(simplePredicateInputs), Lists.newArrayList(joinedCollectionInput), Lists.newArrayList(datePredicateInputsUnified), first, after, sortingOrder, SortingField.UPDATED_AT);
    }

    private static @NotNull CursorConnection getWrapped(List<OrderStateEntity> orderStates, Integer count, Long totalSize) {
        return PaginationWrapper.wrapToProto(orderStates,
            orderStateEntity -> instantToEpochMicros(orderStateEntity.updatedAt().toInstant()),
            orderStateEntity -> CursorNode.newBuilder().setOrderState(entityToProto(orderStateEntity)).build(),
            count,
            totalSize);
    }

    private static void logOrderStateNotFoundForClientResponse(ClientResponse clientResponse, String orderId) {
        LOGGER.warn("Unknown OrderId ({}), ClientResponse ({}) won't be applied", orderId, clientResponse.hasMetadata() ? clientResponse.getMetadata().getResponseId() : StringUtils.EMPTY);
    }
}
