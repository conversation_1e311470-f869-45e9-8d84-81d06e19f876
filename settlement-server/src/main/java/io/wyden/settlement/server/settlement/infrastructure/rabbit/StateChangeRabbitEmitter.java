package io.wyden.settlement.server.settlement.infrastructure.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.published.settlement.LegStateChange;
import io.wyden.published.settlement.RunStateChange;
import io.wyden.published.settlement.SettlementStateChange;
import io.wyden.published.settlement.SettlementStatus;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class StateChangeRabbitEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(StateChangeRabbitEmitter.class);

    private final RabbitExchange<SettlementStateChange> stateChangeRabbitExchange;

    public StateChangeRabbitEmitter(RabbitExchange<SettlementStateChange> stateChangeRabbitExchange) {
        this.stateChangeRabbitExchange = stateChangeRabbitExchange;
    }

    public void emitRunStateChange(long runId, SettlementStatus status) {
        SettlementStateChange message = SettlementStateChange.newBuilder()
            .setRunStateChange(RunStateChange.newBuilder()
                .setRunId("" + runId)
                .setStatus(mapStatus(status))
                .build())
            .build();
        LOGGER.info("Emitting run state change {}", message);
        stateChangeRabbitExchange.tryPublish(message, "");
    }

    public void emitLegStateChange(long legId, long runId, String venueAccountId, SettlementStatus status) {
        SettlementStateChange message = SettlementStateChange.newBuilder()
            .setLegStateChange(LegStateChange.newBuilder()
                .setLegId(String.valueOf(legId))
                .setRunId(String.valueOf(runId))
                .setStatus(mapStatus(status))
                .setAccount(venueAccountId)
                .build())
            .build();

        LOGGER.info("Emitting leg state change {}", message);
        stateChangeRabbitExchange.tryPublish(message, "");
    }

    private SettlementStatus mapStatus(SettlementStatus status) {
        return switch (status) {
            case UNSPECIFIED, UNRECOGNIZED -> null;
            case PENDING -> SettlementStatus.PENDING;
            case IN_PROGRESS -> SettlementStatus.IN_PROGRESS;
            case COMPLETED -> SettlementStatus.COMPLETED;
            case CANCELED -> SettlementStatus.CANCELED;
        };
    }
}
