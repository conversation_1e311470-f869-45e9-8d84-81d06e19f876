package io.wyden.booking.pnl.domain.service;

import io.wyden.booking.pnl.application.PositionService;
import io.wyden.booking.pnl.application.PositionSnapshotEventEmitter;
import io.wyden.booking.pnl.domain.model.ledgerentry.LedgerEntry;
import io.wyden.booking.pnl.domain.model.ledgerentry.LedgerEntryService;
import io.wyden.booking.pnl.domain.model.position.Position;
import io.wyden.booking.pnl.domain.model.position.PositionReference;
import io.wyden.booking.pnl.domain.model.position.Track;
import io.wyden.booking.pnl.domain.model.state.SnapshotService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static io.wyden.cloudutils.tools.BigDecimalUtils.isNonZero;

@Service
public class PnlService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PnlService.class);

    private final LedgerEntryService ledgerEntryService;
    private final PositionService positionService;
    private final PositionSnapshotEventEmitter positionSnapshotEventEmitter;
    private final SnapshotService snapshotService;

    public PnlService(LedgerEntryService ledgerEntryService,
                      PositionService positionService,
                      SnapshotService snapshotService,
                      PositionSnapshotEventEmitter positionSnapshotEventEmitter) {
        this.ledgerEntryService = ledgerEntryService;
        this.positionService = positionService;
        this.snapshotService = snapshotService;
        this.positionSnapshotEventEmitter = positionSnapshotEventEmitter;
    }

    @Transactional
    public void consume(List<LedgerEntry> input, long sequenceNumber) {
        // Map all ledger entries from the message
        List<LedgerEntry> ledgerEntries = input.stream()
            .filter(ledgerEntry -> isNonZero(ledgerEntry.getQuantity()))
            .filter(ledgerEntry -> ledgerEntry.getType().isNonReservationOrRelease())
            .toList();

        // Group ledger entries by their position reference (reference and instrument)
        Map<PositionReference, List<LedgerEntry>> entriesByPositionRef = ledgerEntries.stream()
            .collect(Collectors.groupingBy(
                entry -> new PositionReference(entry.getReference(), entry.getInstrument())
            ));

        LOGGER.info("Grouped {} ledger entries into {} unique position references",
            ledgerEntries.size(), entriesByPositionRef.size());

        Collection<Position> affectedPositions = new ArrayList<>();

        // For each group, only capture the latest Position view
        entriesByPositionRef.forEach((positionRef, entries) -> {
            // This assumes that entries are processed in chronological order
            LOGGER.info("Processing ledger entries (x{}) for position reference {}", entries.size(), positionRef);
            Position affectedPosition = entries.stream()
                .map(ledgerEntry -> accept(ledgerEntry, sequenceNumber))
                .reduce((first, second) -> second)
                .orElseThrow(() -> new IllegalArgumentException("No position found for reference: " + positionRef));

            affectedPositions.add(affectedPosition);
        });

        LOGGER.info("Emitting affected positions: {}", affectedPositions);
        affectedPositions.forEach(positionSnapshotEventEmitter::emit);

        snapshotService.updateLastDurablyProcessedCommand(sequenceNumber);
        LOGGER.info("Finished processing {} ledger entries for sequence number {}", ledgerEntries.size(), sequenceNumber);
    }

    private Position accept(LedgerEntry ledgerEntry, long sequenceNumber) {
        LOGGER.info("Processing ledger entry: {}", ledgerEntry);

        LedgerEntry saved = ledgerEntryService.save(ledgerEntry);
        LOGGER.info("Saved ledger entry: {}", saved);

        Position position = positionService.getOrCreate(saved);
        Track before = position.track();
        LOGGER.info("Position before update: {}", position);

        List<LedgerEntry> additionalLedgerEntries = position.applyLedgerEntry(ledgerEntry);
        LOGGER.info("Updated Position:       {}", position);
        Track after = position.track();
        before.diff(after)
            .forEach(s -> LOGGER.debug("Changed:                {}", s));

        ledgerEntry.setSequenceNumber(sequenceNumber);
        position.setSequenceNumber(sequenceNumber);
        positionService.save(position);

        return position;
    }
}
