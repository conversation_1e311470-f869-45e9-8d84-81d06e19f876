create table processed_message
(
    id           bigserial primary key,
    created_at   timestamp not null,
    updated_at   timestamp not null,
    version      bigint    not null,
    message_id   text      not null,
    processed_by text      not null,
    status       text      not null
);

create unique index processed_message_unique_message_id_target
    on processed_message (message_id, processed_by, status);
