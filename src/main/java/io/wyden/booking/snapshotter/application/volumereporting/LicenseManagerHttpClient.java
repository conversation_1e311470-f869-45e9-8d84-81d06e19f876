package io.wyden.booking.snapshotter.application.volumereporting;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.wyden.booking.snapshotter.domain.volumereporting.LicenseCacheFacade;
import io.wyden.booking.snapshotter.domain.volumereporting.VolumeReportModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.security.spec.KeySpec;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Collection;
import java.util.Optional;

import static java.util.Optional.empty;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class LicenseManagerHttpClient {

    private static final Logger LOGGER = getLogger(LicenseManagerHttpClient.class);

    private final ObjectMapper objectMapper;
    private final LicenseCacheFacade licenseCacheFacade;
    private final String licenseManagerHost;

    public LicenseManagerHttpClient(ObjectMapper objectMapper,
                                    LicenseCacheFacade licenseCacheFacade,
                                    @Value("${license.manager.host}") String licenseManagerHost) {
        this.objectMapper = objectMapper;
        this.licenseCacheFacade = licenseCacheFacade;
        this.licenseManagerHost = licenseManagerHost;
    }

    public Optional<Collection<VolumeReportModel.VolumeReport>> createVolumeReport(Collection<VolumeReportModel.VolumeReport> volumeReport) {
        if (CollectionUtils.isEmpty(volumeReport)) {
            LOGGER.info("Sending volume report is not required, because volumes are empty");
            return Optional.empty();
        }

        Optional<String> validLicense = licenseCacheFacade.getValidLicense();
        if (validLicense.isEmpty()) {
            LOGGER.info("Sending volume report is not possible without valid licenseId");
            return Optional.empty();
        }

        String licenseId = validLicense.get();

        try {
            ObjectNode root = objectMapper.createObjectNode();
            ArrayNode array = objectMapper.valueToTree(volumeReport);
            root.put("licenseId", licenseId);
            root.set("volumes", array);
            String json = objectMapper.writeValueAsString(root);

            LOGGER.info("Trading volumes: " + json);

            String encrypted = encrypt(json, licenseId);

            if (StringUtils.isBlank(encrypted)) {
                LOGGER.error("Encrypted volume report is empty and it will not be uploaded for {}", licenseId);
                return Optional.empty();
            }

            String urlTemplate = licenseManagerHost + "/volume?licenseId=" + licenseId;
            LOGGER.info("Calling license-manager using url: POST {}", urlTemplate);

            URI uri = new URI(urlTemplate);

            HttpRequest request = HttpRequest.newBuilder()
                .POST(HttpRequest.BodyPublishers.ofString(encrypted))
                .header("Content-Type", "text/html")
                .uri(uri)
                .build();

            HttpClient.newBuilder()
                .followRedirects(HttpClient.Redirect.ALWAYS)
                .build()
                .send(request, HttpResponse.BodyHandlers.ofString());

            return Optional.of(volumeReport);

        } catch (Exception ex) {
            LOGGER.error("Failed to upload volume report to license manager for {}", licenseId, ex);
            return Optional.empty();
        }
    }

    public Optional<LocalDate> getLastDayReported() {
        Optional<String> validLicense = licenseCacheFacade.getValidLicense();
        if (validLicense.isEmpty()) {
            LOGGER.info("Checking last reported day is not possible without valid licenseId");
            return Optional.empty();
        }

        String licenseId = validLicense.get();

        String urlTemplate = licenseManagerHost + "/volume/last?licenseId=" + licenseId;

        LOGGER.info("Calling license-manager using url: GET {}", urlTemplate);

        try {
            URI uri = new URI(urlTemplate);
            HttpRequest request = HttpRequest.newBuilder()
                .GET()
                .uri(uri)
                .build();

            HttpResponse<String> response = HttpClient.newBuilder()
                .followRedirects(HttpClient.Redirect.ALWAYS)
                .build()
                .send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                LOGGER.error("Failed to retrieve last date reported for {}", licenseId);
                return empty();
            }

            String decrypted = decrypt(response.body(), licenseId);
            LOGGER.info("Last date reported for {} is {}", licenseId, decrypted);

            return Optional.ofNullable(decrypted)
                .map(s -> LocalDate.parse(s, DateTimeFormatter.ISO_LOCAL_DATE));

        } catch (Exception e) {
            LOGGER.error("Failed to retrieve last report date from license manager for: {}", licenseId, e);
            return Optional.empty();
        }
    }

    private String encrypt(String json, String licenseId) {
        try {
            String key = "tQdCfEjv_5HfUk4V" + licenseId;
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            KeySpec spec = new PBEKeySpec(key.toCharArray(), licenseId.getBytes(StandardCharsets.UTF_8), 65536, 256);
            SecretKey tmp = factory.generateSecret(spec);
            SecretKeySpec secretKey = new SecretKeySpec(tmp.getEncoded(), "AES");

            Cipher ecipher = Cipher.getInstance("AES");
            ecipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] enc = ecipher.doFinal(json.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(enc);
        } catch (Exception e) {
            LOGGER.error("Encryption failed for {}", licenseId, e);
            return null;
        }
    }

    private String decrypt(String encryptedRequest, String licenseId) {
        try {
            String key = "$89JaeP+-yf3h+uf" + licenseId;
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            KeySpec spec = new PBEKeySpec(key.toCharArray(), licenseId.getBytes(StandardCharsets.UTF_8), 65536, 256);
            SecretKey tmp = factory.generateSecret(spec);
            SecretKeySpec secretKey = new SecretKeySpec(tmp.getEncoded(), "AES");

            Cipher dcipher = Cipher.getInstance("AES");
            dcipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedRequest.getBytes(StandardCharsets.UTF_8));
            byte[] decrypted = dcipher.doFinal(encryptedBytes);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            LOGGER.error("Decryption failed for {}", licenseId, e);
            return null;
        }
    }
}
