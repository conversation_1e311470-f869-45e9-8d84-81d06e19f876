include:
  - project: infrastructure/components
    ref: main
    file: components.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.9.8-eclipse-temurin-11

stages:
  - version
  - test
  - build
  - deploy
  - tag

ME_version-verify:
  variables:
    VERSION_FILE: "build.gradle"
    LIBRARY: "matching-engine"
  extends: .lib-verification
  stage: version

ME_build:
  stage: build
  script:
    - echo "Build stage"
    - ./gradlew clean build
  artifacts:
    paths:
      - build/libs/
    expire_in: 1 week
  except:
    - tags

ME_deploy_snapshot:
  stage: deploy
  variables:
    BUILD_VERSION: "-SNAPSHOT"
  script:
    - echo "Running task 'publishMavenJavaPublicationToNexus-snapshotsRepository'"
    - ./gradlew publishMavenJavaPublicationToNexus-snapshotsRepository
  only:
    - main
  needs:
    - job: ME_build
      artifacts: true

ME_tag:
  variables:
    VERSION_FILE: "build.gradle"
    LIBRARY: "matching-engine"
  extends: .tag_library
  stage: tag
  only:
    - main
  except:
    - schedules
  needs:
    - job: ME_deploy_snapshot

ME_unit_test:
  stage: test
  interruptible: true
  script:
    - ./gradlew test