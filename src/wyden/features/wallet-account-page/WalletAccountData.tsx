import AddIcon from '@mui/icons-material/Add';
import { styled } from '@ui/styled';
import { ErrorBoundary } from '@wyden/features/error-indicators/error-boundary/ErrorBoundary';
import { GetRowIdParams } from 'ag-grid-community';
import { useTranslation } from 'react-i18next';
import { Button } from '../../../ui/Button';
import { Search } from '../../../ui/Search';
import { Resource, Scope, WalletAccountResponse } from '../../services/graphql/generated/graphql';
import { FullWorkspaceComponentContainer } from '../FullWorkspaceComponentContainer';
import { InfiniteScrollGridWrapper } from '../grid/InfiniteScrollGridWrapper';
import { WydenGrid } from '../grid/WydenGrid';
import { StyledSettingsHeader } from '../settings-navigation/SettingsNavigation';
import { walletAccountDataColumnDefinitions } from './columnDefinitions';
import { useWalletAccountDataGrid } from './useWalletAccountDataGrid';
import { useWalletAccountDataObserver } from './useWalletAccountDataObserver';
import { useWalletAccountStore } from './useWalletAccountStore';
import { usePermissions } from '../../hooks/usePermissions';
import { CreateWalletAccountDialog } from './create-wallet-account/CreateWalletAccountDialog';

const getRowId = (params: GetRowIdParams<WalletAccountResponse>) => params.data.id;

export function WalletAccountData() {
  const { t } = useTranslation();
  const { checkIfPermitted } = usePermissions();
  const { toggleCreateWalletAccountDialog, isDataLoading } = useWalletAccountStore();
  const {
    initialLoading,
    error,
    reloading,
    showNoRows,
    refresh,
    onGridReady,
    handleSearch,
    searchVal,
  } = useWalletAccountDataGrid();
  const { onGridReady: onObserverReady } = useWalletAccountDataObserver(refresh);
  const canCreateWallet =
    checkIfPermitted({
      resource: Resource.Wallet,
      scope: Scope.Create,
    }) ||
    checkIfPermitted({
      resource: Resource.WalletNostro,
      scope: Scope.Create,
    }) ||
    checkIfPermitted({
      resource: Resource.WalletVostro,
      scope: Scope.Create,
    });

  return (
    <ErrorBoundary errorInfo={t('walletAccountData.errorBoundaryTitle')}>
      <FullWorkspaceComponentContainer>
        <StyledSettingsHeader variant="h2">{t('tabs.walletAccounts')}</StyledSettingsHeader>
        <StyledMainSection>
          <StyledLineWithSearchAndAddButton>
            <StyledSearchWrapper>
              <Search
                fullWidth
                value={searchVal}
                onChange={handleSearch}
                autoFocus={false}
                placeholder={t('walletAccountData.findWalletAccount')}
              />
            </StyledSearchWrapper>
            <Button
              disabled={!canCreateWallet}
              disabled-tooltip-message={t('walletAccountData.noPermissionToCreateNewWalletAccount')}
              onClick={toggleCreateWalletAccountDialog}
            >
              <AddIcon />
              {t('walletAccountData.createNewWalletAccount')}
            </Button>
          </StyledLineWithSearchAndAddButton>
          <InfiniteScrollGridWrapper
            error={error}
            initialLoading={initialLoading}
            reloading={reloading}
            showNoRows={showNoRows}
            refresh={refresh}
            noRowsInfo={t('walletAccountData.noWalletAccount')}
          >
            <WydenGrid
              key="wallets"
              data-testid="wallets-grid"
              getRowId={getRowId}
              rowModelType="infinite"
              onGridReady={(params) => {
                onGridReady(params);
                onObserverReady(params);
              }}
              columnDefs={walletAccountDataColumnDefinitions}
              isDataLoading={isDataLoading || (!initialLoading && reloading)}
            />
          </InfiniteScrollGridWrapper>
        </StyledMainSection>
      </FullWorkspaceComponentContainer>

      <CreateWalletAccountDialog />
    </ErrorBoundary>
  );
}

const StyledSearchWrapper = styled('div')`
  min-width: 400px;
  display: flex;
`;

const StyledLineWithSearchAndAddButton = styled('div')`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StyledMainSection = styled('div')`
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
`;
