package io.wyden.booking.application.reservation;

import io.wyden.booking.application.state.BookingStateInput;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.ledgerentry.SimpleReference;
import io.wyden.booking.domain.position.Position;
import io.wyden.booking.domain.reservation.Reservation;

import java.util.List;
import java.util.Map;

import static java.util.Objects.requireNonNull;

public class ReservationReleaseStateInput extends BookingStateInput {

    private final Reservation reservation;

    public ReservationReleaseStateInput(Map<SimpleReference, Position> positions,
                                        Reservation reservation,
                                        Map<Reservation, List<LedgerEntry>> activeReservationEntries) {
        super(positions, reservation, activeReservationEntries);
        this.reservation = requireNonNull(reservation);
    }

    public Reservation getReservation() {
        return reservation;
    }
}
