package io.wyden.booking.domain.reservation.trade;

import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.instrument.Instrument;
import io.wyden.booking.domain.reservation.ReservationFee;
import io.wyden.published.oems.OemsOrderType;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.AttributeOverride;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Set;

import static io.wyden.cloudutils.tools.BigDecimalUtils.isPositive;
import static io.wyden.cloudutils.tools.BigDecimalUtils.max;
import static io.wyden.cloudutils.tools.BigDecimalUtils.min;

@Entity
@Access(AccessType.FIELD)
public abstract class CashTradeReservation extends TradeReservation {

    /**
     * the currency that represents the quantity of the trade, for a BTC/USD trade, BTC is the baseCurrency
     */
    @Embedded
    @AttributeOverride(name = "currency", column = @Column(name = "base_currency"))
    protected Currency baseCurrency;

    public CashTradeReservation(String uuid,
                                String reservationRef,
                                ZonedDateTime dateTime,
                                Collection<ReservationFee> fees,
                                BigDecimal quantity,
                                BigDecimal price,
                                BigDecimal stopPrice,
                                Currency currency,
                                Currency baseCurrency) {
        super(uuid, reservationRef, dateTime, fees, quantity, price, stopPrice, currency);
        this.baseCurrency = baseCurrency;
    }

    protected CashTradeReservation() {
        // JPA
    }

    @Override
    public Set<Instrument> getInstruments() {
        return Set.of(getBaseCurrency(), getCurrency());
    }

    /**
     * The value of the trade, in terms of quote currency.
     * For example: Client 1 buys 10 BTC/USD @ 20'000 USD, so the trade value = 200'000 USD
     */
    protected BigDecimal getTradeValue() {
        return getExecutionPrice().multiply(quantity);
    }

    public BigDecimal getExecutionPrice() {
        if (stopPrice != null) {
            if (isPositive(quantity)) {
                return min(price, stopPrice);
            }
            return max(price, stopPrice);
        }

        return price;
    }

    public BigDecimal getExecutionPrice(BigDecimal referencePrice) {
        if (stopPrice != null) {
            if (isPositive(quantity)) {
                return min(referencePrice, stopPrice);
            }
            return max(referencePrice, stopPrice);
        }

        return referencePrice;
    }

    /**
     * Rules/configuration for the buying power checks/reservations:
     *     Market order buy: buy 0.5 BTCEUR, current top of book ask price: 30’000
     *     Wyden reserves a % more than current ask price to cater for market volatility and quantity-confirm prices.
     *     E.g., reserve 0.5*30’000*1.02 (volatility) = 15’300 EUR
     *
     *     Limit order buy: buy 0.5 BTCEUR, current ask price: 30’000, limit price: 25’000
     *     Wyden reserves min(quantity * limit price, market order reservation value)
     *     E.g., reserve min(0.5*25’000, 0.5*30’000) = min(12’500, 15’000) = 12’500 EUR
     *
     *     Stop order buy: buy 0.5 BTCEUR, current ask price: 30’000, stop price: 34’000
     *     Wyden reserves max(quantity * stop price * volatility factor, market order reservation value)
     *     E.g., reserve max(0.5*34’000*1.02 , 0.5*30’000* 1.02) = max(17’340, 15’300) = 17’340 EUR
     *
     *     Stop-limit order buy: buy 0.5 BTCEUR, current ask price: 30’000, stop price: 34’000, limit price 34’100
     *     Wyden reserves min(limit price * quantity, max(stop price * quantity * volatility factor, market order reservation value))
     *     E.g., reserve min(34’100*0.5, max(0.5*34’000*1.02 , 0.5*30’000* 1.02)) = 17’340 EUR
     *
     *     Sell orders: Sell 0.5 BTCEUR (no matter the order type)
     *     We reserve 0.5 BTC
     */
    protected BigDecimal getQuoteCurrencyToReserve(OemsOrderType orderType, BigDecimal marketPrice, BigDecimal volatilityBufferPct) {
        BigDecimal orderMarketValue = quantity.multiply(marketPrice);
        BigDecimal orderNotionalValue = quantity.multiply(price);

        BigDecimal marketVolatilityMultiplier = BigDecimal.ONE.add(volatilityBufferPct);

        return switch (orderType) {
            case MARKET -> orderMarketValue.multiply(marketVolatilityMultiplier);
            case LIMIT -> {
                BigDecimal estimatedExecutionPrice;
                if (isPositive(quantity)) {
                    estimatedExecutionPrice = orderNotionalValue.min(orderMarketValue);
                } else {
                    estimatedExecutionPrice = orderNotionalValue.max(orderMarketValue);
                }
                yield estimatedExecutionPrice;
            }
            case STOP -> {
                BigDecimal estimatedExecutionPrice;
                if (isPositive(quantity)) {
                    estimatedExecutionPrice = max(quantity.multiply(stopPrice), orderMarketValue);
                } else {
                    estimatedExecutionPrice = min(quantity.multiply(stopPrice), orderMarketValue);
                }
                yield estimatedExecutionPrice.multiply(marketVolatilityMultiplier);
            }
            case STOP_LIMIT -> {
                BigDecimal estimatedExecutionPrice;
                if (isPositive(quantity)) {
                    estimatedExecutionPrice = min(
                        orderNotionalValue,
                        max(
                            quantity.multiply(stopPrice).multiply(marketVolatilityMultiplier),
                            orderMarketValue.multiply(marketVolatilityMultiplier)
                        )
                    );
                } else {
                    estimatedExecutionPrice = max(
                        orderNotionalValue,
                        min(
                            quantity.multiply(stopPrice).multiply(marketVolatilityMultiplier),
                            orderMarketValue.multiply(marketVolatilityMultiplier)
                        )
                    );
                }
                yield estimatedExecutionPrice;
            }
            case UNRECOGNIZED, ORDER_TYPE_UNSPECIFIED -> throw new IllegalArgumentException("Order type %s not supported".formatted(orderType));
        };
    }

    public Currency getBaseCurrency() {
        return baseCurrency;
    }
}
