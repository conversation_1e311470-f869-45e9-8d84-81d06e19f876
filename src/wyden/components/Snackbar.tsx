import { forwardRef, useCallback } from 'react';
import { useSnackbar, SnackbarContent, CustomContentProps, SnackbarKey } from 'notistack';

import { styled } from '@ui/styled';
import { IconButton } from '@ui/IconButton';
import Card from '@mui/material/Card';
import { color } from '@ui/theme/colors';
import { Typography } from '@mui/material';
import CardActions from '@mui/material/CardActions';
import { ReactComponent as CloseSnackbarIcon } from '@wyden/assets/close-snackbar.svg';
import { getSpacing } from '@wyden/utils/styles';
import { ReactComponent as SnackbarInfoIcon } from '@wyden/assets/snackbar-info.svg';
import { ReactComponent as SnackbarCheckIcon } from '@wyden/assets/snackbar-check.svg';
import { ReactComponent as SnackbarErrorIcon } from '@wyden/assets/snackbar-error.svg';
import { ReactComponent as SnackbarWarningIcon } from '@wyden/assets/snackbar-warning.svg';

interface CustomSnackbarProps extends CustomContentProps {
  id: SnackbarKey;
  variant: 'default' | 'error' | 'success' | 'warning' | 'info';
}

export const CustomSnackbar = forwardRef<HTMLDivElement, CustomSnackbarProps>(
  ({ id, variant, ...props }, ref) => {
    const { closeSnackbar } = useSnackbar();

    const handleDismiss = useCallback(() => {
      closeSnackbar(id);
    }, [id, closeSnackbar]);

    return (
      <SnackbarContent ref={ref}>
        <StyledCard>
          <CardActions>
            {props.iconVariant[variant]}
            <StyledTypography>{props.message}</StyledTypography>
            <div>
              <IconButton size="small" onClick={handleDismiss}>
                <CloseSnackbarIcon />
              </IconButton>
            </div>
          </CardActions>
        </StyledCard>
      </SnackbarContent>
    );
  },
);

export const snackbarComponents = {
  info: CustomSnackbar,
  error: CustomSnackbar,
  success: CustomSnackbar,
  warning: CustomSnackbar,
};

export const snackbarIconVariants = {
  info: <SnackbarInfoIcon />,
  error: <SnackbarErrorIcon />,
  success: <SnackbarCheckIcon />,
  warning: <SnackbarWarningIcon />,
};

CustomSnackbar.displayName = 'CustomSnackbar';

const StyledCard = styled(Card)`
  border-top: 4px solid ${({ theme }) => color[theme.palette.mode].fillsElementsFillInactive};
  background-color: ${({ theme }) => color[theme.palette.mode].fillsElementsFillInvert};
  font-size: 14px;
  font-weight: 600;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  padding: ${getSpacing(2)};
  border-radius: ${getSpacing(1)};
  margin-bottom: 6px;
  margin-left: 71px;
`;

const StyledTypography = styled(Typography)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
  font-size: 14px;
  font-weight: 600;
`;
