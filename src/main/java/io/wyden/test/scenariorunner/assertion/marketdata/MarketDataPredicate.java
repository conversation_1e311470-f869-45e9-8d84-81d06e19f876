package io.wyden.test.scenariorunner.assertion.marketdata;

import ch.algotrader.api.connector.marketdata.domain.AskDTO;
import ch.algotrader.api.connector.marketdata.domain.BidAskQuoteDTO;
import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import ch.algotrader.api.connector.marketdata.domain.TradeDTO;

import io.wyden.test.scenariorunner.integration.fixclient.model.BidAskDTO;
import io.wyden.test.scenariorunner.integration.fixclient.model.BidDTO;
import io.wyden.test.scenariorunner.model.marketdata.L1Event;
import io.wyden.test.scenariorunner.model.marketdata.MarketDataIdentifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.function.Predicate;

import static java.time.ZoneOffset.UTC;

public class MarketDataPredicate {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataPredicate.class);

    public static Predicate<BidDTO> equalsBid(MarketDataEventDTO injectedMarketDataEvent) {
        return actual -> {
            if (!(injectedMarketDataEvent instanceof ch.algotrader.api.connector.marketdata.domain.BidDTO expected)) {
                return false;
            }

            LOGGER.info("Actual price: {}, expected: {}", actual.price(), expected.getPrice());
            LOGGER.info("Actual size: {}, expected: {}", actual.size(), expected.getSize());

            return equalsIdentifier(actual.identifier(), expected)
                && actual.price() == expected.getPrice()
                && actual.size() == expected.getSize();
        };
    }

    public static Predicate<io.wyden.test.scenariorunner.integration.fixclient.model.AskDTO> equalsAsk(MarketDataEventDTO injectedMarketDataEvent) {
        return actual -> {
            if (!(injectedMarketDataEvent instanceof AskDTO expected)) {
                return false;
            }

            LOGGER.info("Actual price: {}, expected: {}", actual.price(), expected.getPrice());
            LOGGER.info("Actual size: {}, expected: {}", actual.size(), expected.getSize());

            return equalsIdentifier(actual.identifier(), expected)
                && actual.price() == expected.getPrice()
                && actual.size() == expected.getSize();
        };
    }

    public static Predicate<BidAskDTO> equalsBidAskQuote(MarketDataEventDTO injectedMarketDataEvent) {
        return actual -> {
            if (!(injectedMarketDataEvent instanceof BidAskQuoteDTO expected)) {
                return false;
            }

            return equalsIdentifier(actual.identifier(), expected)
                && actual.bidPrice() == expected.getBidPrice()
                && actual.bidSize() == expected.getBidSize()
                && actual.askPrice() == expected.getAskPrice()
                && actual.askSize() == expected.getAskSize();
        };
    }

    public static Predicate<io.wyden.test.scenariorunner.integration.fixclient.model.TradeDTO> equalsTrade(MarketDataEventDTO injectedMarketDataEvent) {
        return actual -> {
            if (!(injectedMarketDataEvent instanceof TradeDTO expected)) {
                return false;
            }

            LOGGER.info("Actual last price: {}, expected: {}", actual.lastPrice(), expected.getLastPrice());
            LOGGER.info("Actual last size: {}, expected: {}", actual.lastSize(), expected.getLastSize());

            return equalsIdentifier(actual.identifier(), expected)
                && actual.lastPrice() == expected.getLastPrice()
                && actual.lastSize() == expected.getLastSize()
                && actual.side().toString().equals(expected.getSide().toString());
        };
    }

    public static Predicate<L1Event> l1EventEqualsTrade(MarketDataEventDTO injectedMarketDataEvent) {
        return actual -> {
            if (!(injectedMarketDataEvent instanceof TradeDTO expected)) {
                return false;
            }

            LOGGER.info("Actual last price: {}, expected: {}", actual.lastPrice(), expected.getLastPrice());
            LOGGER.info("Actual last size: {}, expected: {}", actual.lastSize(), expected.getLastSize());

            return equalsIdentifier(actual.identifier(), expected)
                && actual.lastPrice() == expected.getLastPrice()
                && actual.lastSize() == expected.getLastSize()
                && actual.side().toString().equals(expected.getSide().toString());
        };
    }

    public static Predicate<L1Event> l1EventEqualsAsk(MarketDataEventDTO injectedMarketDataEvent) {
        return actual -> {
            if (!(injectedMarketDataEvent instanceof AskDTO expected)) {
                return false;
            }

            LOGGER.info("Actual price: {}, expected: {}", actual.askPrice(), expected.getPrice());
            LOGGER.info("Actual size: {}, expected: {}", actual.askSize(), expected.getSize());

            return equalsIdentifier(actual.identifier(), expected)
                && actual.askPrice() == expected.getPrice()
                && actual.askSize() == expected.getSize();
        };
    }

    private static boolean equalsIdentifier(MarketDataIdentifier actual, MarketDataEventDTO expected) {
        ZonedDateTime actualDateTime = actual.dateTime().truncatedTo(ChronoUnit.MILLIS);
        ZonedDateTime expectedDateTime = expected.getDateTime().truncatedTo(ChronoUnit.MILLIS);
        LOGGER.info("Actual instrumentId: {}, expected: {}", actual.instrumentId(), expected.getTickerId());
        LOGGER.info("Actual date: {}, expected: {}", actualDateTime.withZoneSameInstant(UTC), expectedDateTime);
        return actual.instrumentId().contains(expected.getTickerId())
            // we need to handle the difference between ZoneId.of("UTC") and ZoneOffset.UTC
            // example: 2022-08-10T08:49:53.180Z vs 2022-08-10T08:49:53.180Z[UTC]
            && actualDateTime.withZoneSameInstant(UTC).equals(expectedDateTime);
    }

}
