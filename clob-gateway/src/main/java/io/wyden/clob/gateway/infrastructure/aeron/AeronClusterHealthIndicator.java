package io.wyden.clob.gateway.infrastructure.aeron;

import io.aeron.cluster.client.AeronCluster;
import io.aeron.status.ChannelEndpointStatus;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class AeronClusterHealthIndicator extends AbstractHealthIndicator {
    private final AeronCluster aeronCluster;

    private int lastSeenSessionId = 0;
    private Boolean lastSeenHealthy = null;

    public AeronClusterHealthIndicator(AeronCluster aeronCluster) {
        this.aeronCluster = aeronCluster;
    }

    @Override
    protected void doHealthCheck(Health.Builder builder) {
        boolean ingressConnected = aeronCluster.ingressPublication().isConnected();
        boolean egressConnected = aeronCluster.egressSubscription().isConnected();

        int sessionId = aeronCluster.ingressPublication().sessionId();
        boolean sessionIdChangeDetected = lastSeenSessionId != 0 && sessionId != lastSeenSessionId;
        this.lastSeenSessionId = sessionId;

        Map<String, String> details = new HashMap<>() {{
            put("ingressPublicationChannel", aeronCluster.ingressPublication().channel());
            put("ingressPublicationStatus", ChannelEndpointStatus.status(aeronCluster.ingressPublication().channelStatus()));
            put("ingressPublicationRegistrationId", "" + aeronCluster.ingressPublication().registrationId());
            put("ingressPublicationSessionId", "" + lastSeenSessionId);
            put("ingressPublicationStreamId", "" + aeronCluster.ingressPublication().streamId());
            put("egressSubscriptionChannel", aeronCluster.egressSubscription().channel());
            put("egressSubscriptionStatus", ChannelEndpointStatus.status(aeronCluster.egressSubscription().channelStatus()));
            put("egressSubscriptionRegistrationId", "" + aeronCluster.egressSubscription().registrationId());
            put("egressSubscriptionStreamId", "" + aeronCluster.egressSubscription().streamId());
            put("clusterLeaderId", "" + aeronCluster.leaderMemberId());
            put("clusterSessionId", "" + aeronCluster.clusterSessionId());
            put("sessionIdChangeDetected", "" + sessionIdChangeDetected);
        }};

        if (ingressConnected && egressConnected && !sessionIdChangeDetected) {
            builder.up().withDetails(details);
            lastSeenHealthy = Boolean.TRUE;
        } else {
            builder.down().withDetails(details);
            lastSeenHealthy = Boolean.FALSE;
        }
    }

    public boolean memoizedIsHealthy() {
        return Objects.requireNonNullElse(lastSeenHealthy, false);
    }
}
