package io.wyden.rate.service;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.rate.RateSubscription;
import io.wyden.published.rate.RateSubscriptionHealthStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Objects;
import javax.annotation.Nullable;

@Service
public class RateSubscriptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RateSubscriptionService.class);

    private final RateSubscriptionRepository rateSubscriptionRepository;

    public RateSubscriptionService(RateSubscriptionRepository rateSubscriptionRepository) {
        this.rateSubscriptionRepository = rateSubscriptionRepository;
    }

    public void findAndSetRateSubscriptionHealthDown(String baseCurrency, String quoteCurrency) {
        RateSubscription rateSubscription = findRateSubscription(baseCurrency, quoteCurrency);
        if (rateSubscription == null) {
            return;
        }
        setRateSubscriptionHealthDown(rateSubscription);
    }

    public void setRateSubscriptionHealthDown(RateSubscription rateSubscription) {
        rateSubscription = rateSubscription.toBuilder()
            .setHealth(RateSubscriptionHealthStatus.DOWN)
            .build();
        rateSubscriptionRepository.save(rateSubscription);
    }

    public void findAndSetRateSubscriptionHealthUpAndTimestamp(String baseCurrency, String quoteCurrency) {
        RateSubscription rateSubscription = findRateSubscription(baseCurrency, quoteCurrency);
        if (rateSubscription == null) {
            return;
        }
        setRateSubscriptionHealthUpAndTimestamp(rateSubscription);
    }

    public RateSubscription findOrSaveInitialSubscription(String baseCurrency, String quoteCurrency) {
        RateSubscription rateSubscription = rateSubscriptionRepository.find(baseCurrency, quoteCurrency);
        if (Objects.isNull(rateSubscription)) {
            rateSubscription = saveInitialRateSubscription(baseCurrency, quoteCurrency);
        }
        return rateSubscription;
    }

    private @Nullable RateSubscription findRateSubscription(String baseCurrency, String quoteCurrency) {
        RateSubscription rateSubscription = rateSubscriptionRepository.find(baseCurrency, quoteCurrency);
        if (Objects.isNull(rateSubscription)) {
            LOGGER.warn("Could not find rate subscription for baseCurrency: {}, quoteCurrency: {}", baseCurrency, quoteCurrency);
            return null;
        }
        return rateSubscription;
    }

    private void setRateSubscriptionHealthUpAndTimestamp(RateSubscription rateSubscription) {
        rateSubscription = rateSubscription.toBuilder()
            .setHealth(RateSubscriptionHealthStatus.UP)
            .setLastEventTimestamp(DateUtils.toIsoUtcTime(Instant.now()))
            .build();
        rateSubscriptionRepository.save(rateSubscription);
    }

    private RateSubscription saveInitialRateSubscription(String baseCurrency, String quoteCurrency) {
        RateSubscription rateSubscription = RateSubscription.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setHealth(RateSubscriptionHealthStatus.PENDING)
            .build();
        rateSubscriptionRepository.save(rateSubscription);
        return rateSubscription;
    }
}
