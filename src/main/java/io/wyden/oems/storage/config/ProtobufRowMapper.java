package io.wyden.oems.storage.config;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class ProtobufRowMapper<V extends Message> implements RowMapper<V> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProtobufRowMapper.class);

    private static final String PROTOBUF_COLUMN = "payload";

    private final Parser<V> parser;

    public ProtobufRowMapper(Parser<V> parser) {
        this.parser = parser;
    }

    @Override
    public V mapRow(ResultSet rs, int rowNum) throws SQLException {
        try {
            return parser.parseFrom(rs.getBytes(PROTOBUF_COLUMN));
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("Invalid protobuf", e);
            throw new SQLException("Invalid protobuf", e);
        }
    }
}
