import { AlertTitle } from '@mui/material';
import LinearProgress from '@mui/material/LinearProgress';
import { Alert } from '@ui/Alert';
import { Button } from '@ui/Button';
import { Dialog } from '@ui/Dialog';
import { DialogActions } from '@ui/DialogActions';
import { DialogContent } from '@ui/DialogContent';
import { DialogTitle } from '@ui/DialogTitle';
import { styled } from '@ui/styled';
import { TextField } from '@ui/TextField';
import { useApiKeyStore } from '@wyden/features/general-settings/useApiKeyStore';
import {
  useApiKeysQuery,
  useCreateApiKeyMutation,
} from '@wyden/services/graphql/generated/graphql';
import { getSpacing } from '@wyden/utils/styles';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

export const GENERATE_NEW_KEY_BUTTON_TEST_ID = 'GENERATE_NEW_KEY_BUTTON_TEST_ID';

export const ApiKeyDialog = () => {
  const { t } = useTranslation();
  const { isApiKeyDialogOpen, toggleApiKeyDialog, openApiKeyResultDialog } = useApiKeyStore();
  const { refetch } = useApiKeysQuery();
  const [apiKeyAlias, setApiKeyAlias] = useState('');
  const [createApiKey, { error, loading }] = useCreateApiKeyMutation({
    onCompleted: () => {
      refetch();
    },
  });

  const resetApiKeyName = () => {
    setApiKeyAlias('');
  };

  const handleClick = async () => {
    await createApiKey({
      variables: {
        name: apiKeyAlias,
      },
    })
      .then((data) => {
        toggleApiKeyDialog();
        openApiKeyResultDialog(data.data?.createApiKey ?? undefined);
      })
      // already handled by the error variable
      .catch(() => undefined);

    resetApiKeyName();
  };

  return (
    <Dialog open={isApiKeyDialogOpen} onClose={toggleApiKeyDialog}>
      <DialogTitle onClose={toggleApiKeyDialog}>
        {error ? t('common.error') : t('config.generateApiKey')}
      </DialogTitle>
      <DialogContent>
        <FullWidthContainer>
          {loading ? (
            <FullWidthLoader />
          ) : (
            <>
              <FieldContainer>
                <FullWidthTextField
                  label={t('config.alias')}
                  skipOptionalLabel
                  value={apiKeyAlias}
                  onChange={(e) => setApiKeyAlias(e.target.value)}
                />
              </FieldContainer>
            </>
          )}
        </FullWidthContainer>
        {error && (
          <Alert severity="error">
            <AlertTitle>{t('common.error')}</AlertTitle>
            {t('config.error')}
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          disabled={loading}
          onClick={handleClick}
          data-testid={GENERATE_NEW_KEY_BUTTON_TEST_ID}
        >
          {t('common.generate')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const FullWidthContainer = styled('div')`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
`;

const FieldContainer = styled('div')`
  width: 100%;
  display: flex;
  gap: ${getSpacing(3)};
  align-items: center;
  padding-bottom: ${getSpacing(3)};
`;

const FullWidthTextField = styled(TextField)`
  flex: 1;
`;

const FullWidthLoader = styled(LinearProgress)`
  flex: 1;
  margin-top: ${getSpacing(2)};
  margin-bottom: ${getSpacing(2)};
`;
