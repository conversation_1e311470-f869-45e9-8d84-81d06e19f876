import { graphql, rest } from 'msw';
import { worker } from '../../../../../mocks/browser';
import { TradingKitWithWatchlistPO } from './TradingKitPO.cy';
import { symbolSearchResultMock } from '../../order-form/__tests__/order-entry.mock';
import { useSymbolStore } from '../../instrument-search/useSymbolStore';

describe('Trading Kit with Watchlist', () => {
  const PO = new TradingKitWithWatchlistPO();

  beforeEach(() => {
    useSymbolStore.getState().reset();
    worker.use(
      rest.get('https://s3.tradingview.com/tv.js', () => {
        return;
      }),
      graphql.query('UserData', (req, res, ctx) => {
        const userData = {
          timestamp: Date.now(),
          widgets: [
            {
              id: 'test-widget',
              type: 'WATCHLIST',
            },
          ],
        };
        return res(
          ctx.data({
            userData: { data: JSON.stringify(userData) },
          }),
        );
      }),
      graphql.query('SymbolSearch', (req, res, ctx) => {
        return res(ctx.data(symbolSearchResultMock));
      }),
    );
  });

  it('renders watchlist with portfolios and changes instrument', () => {
    worker.use(
      graphql.query('PortfolioSearch', (req, res, ctx) => {
        return res(
          ctx.data({
            portfolioSearch: {
              edges: [
                {
                  node: {
                    id: 'BANK_Portfolio',
                    name: 'BANK_Portfolio',
                    createdAt: '2024-04-01T05:03:45.379242Z',
                    scopes: ['MANAGE', 'TRADE', 'READ'],
                    dynamicScopes: [],
                    portfolioCurrency: 'USD',
                    portfolioType: 'VOSTRO',
                    tags: [],
                    archivedAt: null,
                    __typename: 'PortfolioResponse',
                  },
                  cursor: '2024-04-01T05:03:45.379242Z',
                  __typename: 'PortfolioEdge',
                },
              ],
            },
          }),
        );
      }),
    );

    PO.render();
    PO.expectTextToBeDisplay('BMEXUSDT').should('have.length', '0');
    PO.addInstrumentWithPortfolioToWatchlist('BANK_Portfolio');
    PO.expectTextToBeDisplay('BMEXUSDT').should('have.length', '2');

    PO.changeInstrumentFromWatchlist();
  });
});
