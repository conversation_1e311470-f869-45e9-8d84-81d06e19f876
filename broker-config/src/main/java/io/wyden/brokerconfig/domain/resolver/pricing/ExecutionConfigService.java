package io.wyden.brokerconfig.domain.resolver.pricing;

import com.google.protobuf.Message;
import io.wyden.brokerconfig.domain.referencedata.InstrumentsService;
import io.wyden.brokerconfig.domain.referencedata.VenueAccountService;
import io.wyden.brokerconfig.domain.resolver.ConfigResolver;
import io.wyden.published.brokerdesk.AgencyTargetType;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.ExecutionMode;
import io.wyden.published.brokerdesk.SorTarget;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.VenueAccount;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ExecutionConfigService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ExecutionConfigService.class);

    private final VenueAccountService venueAccountService;
    private final InstrumentsService instrumentsService;
    private final List<ConfigResolver<ExecutionConfig.Builder, ExecutionConfig>> executionConfigResolvers;

    public ExecutionConfigService(VenueAccountService venueAccountService,
                                  InstrumentsService instrumentsService,
                                  List<ConfigResolver<ExecutionConfig.Builder, ExecutionConfig>> executionConfigResolvers) {
        this.venueAccountService = venueAccountService;
        this.instrumentsService = instrumentsService;
        this.executionConfigResolvers = executionConfigResolvers;
    }

    public ExecutionConfig resolveExecutionConfig(String baseCurrency, String quoteCurrency, String symbol, String instrumentType, ExecutionConfig instrumentConfig, ExecutionConfig portfolioConfig, ExecutionConfig groupInstrumentConfig, ExecutionConfig groupConfig) {
        ExecutionConfig.Builder builder = ExecutionConfig.newBuilder();

        executionConfigResolvers
            .forEach(resolver -> resolver.resolve(builder, instrumentConfig, portfolioConfig, groupInstrumentConfig, groupConfig));

        if (builder.getExecutionMode() == ExecutionMode.SIMPLE) {
            resolveAgencyTradingInstrumentId(builder, baseCurrency, quoteCurrency);
        } else if (builder.getExecutionMode() == ExecutionMode.SOR) {
            resolveSorTarget(builder, symbol, instrumentType);
            builder.setAgencyTargetType(AgencyTargetType.EXTERNAL_VENUE);
        }

        return builder.build();
    }

    private void resolveAgencyTradingInstrumentId(ExecutionConfig.Builder builder, String baseCurrency, String quoteCurrency) {
        if (StringUtils.isBlank(builder.getAgencyTradingAccount()) || StringUtils.isNotBlank(builder.getAgencyTargetInstrumentId())) {
            return;
        }

        VenueAccount venueAccount = venueAccountService.getVenueAccount(builder.getAgencyTradingAccount());
        if (venueAccount == null) {
            LOGGER.warn("Cannot find venue account for provided target account: %s".formatted(builder.getAgencyTradingAccount()));
            return;
        }

        AccountType accountType = venueAccount.getAccountType();
        if (accountType == AccountType.ACCOUNT_TYPE_CLOB) {
            builder.setAgencyTargetType(AgencyTargetType.CLOB);
        } else {
            builder.setAgencyTargetType(AgencyTargetType.EXTERNAL_VENUE);
        }

        Optional<String> streetInstrumentId = instrumentsService.findInstrument(baseCurrency, quoteCurrency, venueAccount.getVenueName());
        streetInstrumentId.ifPresentOrElse(builder::setAgencyTargetInstrumentId,
            () -> LOGGER.warn("Cannot find matching instrument that could be used as an execution source for base currency: %s, quote currency: %s, target account: %s"
                .formatted(baseCurrency, quoteCurrency, builder.getAgencyTradingAccount())));
    }

    private void resolveSorTarget(ExecutionConfig.Builder builder, String symbol, String instrumentType) {
        if (!isEmpty(builder.getSorTarget())) {
            return;
        }

        try {
            SorTarget sorTarget = SorTarget.newBuilder()
                .setSymbol(symbol)
                .setAssetClass(AssetClass.valueOf(instrumentType))
                .build();
            builder.setSorTarget(sorTarget);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Cannot build SOR target for symbol: %s, instrumentType: %s, exception: %s".formatted(symbol, instrumentType, e));
        }
    }

    public static <T extends Message> boolean isEmpty(T message) {
        return message.equals(message.getDefaultInstanceForType());
    }
}
