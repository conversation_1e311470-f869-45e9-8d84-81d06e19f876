package io.wyden.test.scenariorunner.brokerdesk.agency;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus;
import io.wyden.apiserver.rest.orderhistory.model.OrderStateResponse;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientTIF;
import io.wyden.test.scenariorunner.accounting.base.AccountingAgencyTradingTestBase;
import io.wyden.test.scenariorunner.assertion.trading.OrderStateSoftAssert;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.CANCELED;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.FILLED;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.NEW;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.PARTIALLY_FILLED;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.PENDING_CANCEL;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.PENDING_NEW;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.REJECTED;
import static io.wyden.cloud.utils.test.TestUtils.bd;
import static io.wyden.test.scenariorunner.assertion.trading.OrderStatePredicate.clOrderIdAndOrderStatusAndCategoryAre;
import static io.wyden.test.scenariorunner.assertion.trading.OrderStatePredicate.clOrderIdAndOrderStatusAre;
import static io.wyden.test.scenariorunner.data.ErrorMsg.INSUFFICIENT_FUNDS_REJECT_MSG;
import static io.wyden.test.scenariorunner.data.infra.Epics.BROKER_DESK_AGENCY;
import static io.wyden.test.scenariorunner.data.infra.TestTags.SMOKE;

@Epic(BROKER_DESK_AGENCY)
@GraphQL
public class AgencyCurrentOrdersTest extends AccountingAgencyTradingTestBase {

    private final BigDecimal firstFillQty = BigDecimal.valueOf(10000);
    private final BigDecimal lastFillQty = BigDecimal.valueOf(2);
    private final BigDecimal fullFillQty = firstFillQty.add(lastFillQty);

    private GraphQLActor actor;

    @BeforeEach
    void setupActor() {
        actor = (GraphQLActor) clientActorExtension.actor();
    }

    // since there are no portfolio READ permissions client receives both order states bank and client
    // so we need to carefully filter out only original client order states
    @Tag(SMOKE)
    @Test
    void newAndRejectedAndCancelledAndExecutedInSeveralFillsClientSideOrderStates_shouldBeReturnedByCurrentOrdersSubscription(
            InstrumentResponseDto clientInstrument, ConnectorMockSession conn, ClientSession client) {

        String instrumentId = clientInstrument.instrumentIdentifiers().instrumentId();
        // one rejected
        ClientRequest orderRejected = sendOrderToBeRejected(client, instrumentId, conn);

        verifyRejectedOrderStates(orderRejected);

        //one canceled
        ClientRequest orderCancelled = sendOrderToBeCancelled(client, instrumentId, conn);

        verifyCanceledOrderStates(orderCancelled);

        // one filled
        ClientRequest orderNew = sendOrderToBeFilledFullyInSeveralFills(client, instrumentId, conn);

        verifyFilledOrderStates(orderNew);

        // GTD
        ClientRequest orderGTDNew = sendGtdOrderToBeNew(client, instrumentId, conn);

        verifyGtdNewOrderStates(orderGTDNew);
    }

    @Step
    private ClientRequest sendOrderToBeRejected(ClientSession client, String instrumentId, ConnectorMockSession conn) {
        client.sendDefaultLimitOrder(2000, instrumentId);
        ClientRequest order = client.getOrder();
        conn.rejectNewOrder(INSUFFICIENT_FUNDS_REJECT_MSG);
        return order;
    }

    @Step
    private void verifyRejectedOrderStates(ClientRequest orderRejected) {
        OrderStateResponse orderStatePendingNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAre(orderRejected.getClOrderId(), PENDING_NEW));
        OrderStateSoftAssert.assertThat(orderStatePendingNew)
            .isOriginToClientRequest(orderRejected)
            .orderStatusIs(PENDING_NEW)
            .remainingQtyIs(bd(orderRejected.getQuantity()))
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStateRejected = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderRejected.getClOrderId(), REJECTED, SharedModel.OrderCategory.AGENCY_ORDER));
        OrderStateSoftAssert.assertThat(orderStateRejected)
            .isOriginToClientRequest(orderRejected)
            .orderStatusIs(REJECTED)
            .reasonIs(INSUFFICIENT_FUNDS_REJECT_MSG)
            .remainingQtyIs(BigDecimal.ZERO)
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();
    }

    @Step
    private ClientRequest sendOrderToBeCancelled(ClientSession client, String instrumentId, ConnectorMockSession conn) {
        client.sendDefaultLimitOrder(10001.0, instrumentId);
        ClientRequest order = client.getOrder();
        conn.acceptNewOrder();
        WaitUtils.justWait(Timeouts.JUST_WAIT);
        client.sendCancel();
        conn.acceptCancel(0);
        return order;
    }

    @Step
    private void verifyCanceledOrderStates(ClientRequest orderCancelled) {
        OrderStateResponse orderStatePendingNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderCancelled.getClOrderId(), PENDING_NEW, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStatePendingNew)
            .isOriginToClientRequest(orderCancelled)
            .orderStatusIs(PENDING_NEW)
            .remainingQtyIs(bd(orderCancelled.getQuantity()))
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStateNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderCancelled.getClOrderId(), NEW, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStateNew)
            .isOriginToClientRequest(orderCancelled)
            .orderStatusIs(NEW)
            .remainingQtyIs(bd(orderCancelled.getQuantity()))
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStatePendingCancel = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderCancelled.getClOrderId(), PENDING_CANCEL, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStatePendingCancel)
            .isOriginToClientRequest(orderCancelled)
            .orderStatusIs(PENDING_CANCEL)
            .remainingQtyIs(bd(orderCancelled.getQuantity()))
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStateCancelled = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderCancelled.getClOrderId(), CANCELED, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStateCancelled)
            .isOriginToClientRequest(orderCancelled)
            .orderStatusIs(CANCELED)
            .remainingQtyIs(BigDecimal.ZERO)
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();
    }

    @Step
    private ClientRequest sendOrderToBeFilledFullyInSeveralFills(ClientSession client, String instrumentId, ConnectorMockSession conn) {
        double price = 6;
        client.sendDefaultLimitOrder(fullFillQty.doubleValue(), instrumentId, String.valueOf(price));
        ClientRequest order = client.getOrder();
        conn.acceptNewOrder();
        conn.fillPart("exec-1", firstFillQty.doubleValue(), price);
        conn.fillFull("exec-2", fullFillQty.doubleValue(), lastFillQty.doubleValue(), price, price);
        return order;
    }

    @Step
    private void verifyFilledOrderStates(ClientRequest orderNew) {
        OrderStateResponse orderStatePendingNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderNew.getClOrderId(), PENDING_NEW, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStatePendingNew)
            .isOriginToClientRequest(orderNew)
            .orderStatusIs(PENDING_NEW)
            .remainingQtyIs(fullFillQty)
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStateNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderNew.getClOrderId(), NEW, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStateNew)
            .isOriginToClientRequest(orderNew)
            .orderStatusIs(NEW)
            .remainingQtyIs(fullFillQty)
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStatePartiallyFilled = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderNew.getClOrderId(), PARTIALLY_FILLED, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStatePartiallyFilled)
            .isOriginToClientRequest(orderNew)
            .filledQtyIs(firstFillQty)
            .remainingQtyIs(lastFillQty)
            .orderStatusIs(PARTIALLY_FILLED)
            .assertAll();

        OrderStateResponse orderStateFilledFully = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderNew.getClOrderId(), FILLED, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStateFilledFully)
            .isOriginToClientRequest(orderNew)
            .filledQtyIs(fullFillQty)
            .remainingQtyIs(BigDecimal.ZERO)
            .orderStatusIs(OrderStatus.FILLED)
            .assertAll();
    }

    @Step
    private ClientRequest sendGtdOrderToBeNew(ClientSession client, String instrumentId, ConnectorMockSession conn) {
        ClientRequest gtdOrder = clientOrderFactory
            .defaultLimitOrder(instrumentId, 10003.0, "5", ClientTIF.GTD);
        client.sendOrder(gtdOrder);
        conn.acceptNewOrder();
        return gtdOrder;
    }

    @Step
    private void verifyGtdNewOrderStates(ClientRequest orderGTDNew) {
        OrderStateResponse orderStatePendingNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderGTDNew.getClOrderId(), PENDING_NEW, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStatePendingNew)
            .isOriginToClientRequest(orderGTDNew)
            .orderStatusIs(PENDING_NEW)
            .expirationDateTimeIsNotBlank()
            .expirationDateTimeIsInTheFuture()
            .remainingQtyIs(bd(orderGTDNew.getQuantity()))
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();

        OrderStateResponse orderStateNew = actor.orderState()
            .await(clOrderIdAndOrderStatusAndCategoryAre(orderGTDNew.getClOrderId(), NEW, SharedModel.OrderCategory.AGENCY_ORDER));

        OrderStateSoftAssert.assertThat(orderStateNew)
            .isOriginToClientRequest(orderGTDNew)
            .orderStatusIs(NEW)
            .expirationDateTimeIsNotBlank()
            .expirationDateTimeIsInTheFuture()
            .remainingQtyIs(bd(orderGTDNew.getQuantity()))
            .filledQtyIs(BigDecimal.ZERO)
            .assertAll();
    }
}
