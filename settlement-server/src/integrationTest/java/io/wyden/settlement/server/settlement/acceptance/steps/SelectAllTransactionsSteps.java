package io.wyden.settlement.server.settlement.acceptance.steps;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.SettlementRunRepository;
import io.wyden.settlement.client.transaction.SelectAllTransactionInput;
import io.wyden.settlement.server.settlement.transaction.TransactionEntity;
import io.wyden.settlement.server.settlement.transaction.TransactionRepository;
import io.wyden.settlement.server.settlement.transaction.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class SelectAllTransactionsSteps {
    private Logger LOGGER = LoggerFactory.getLogger(SelectAllTransactionsSteps.class);

    private final SettlementRunRepository runRepository;
    private final TransactionRepository transactionRepository;
    private final TransactionService transactionService;

    public SelectAllTransactionsSteps(SettlementRunRepository runRepository, TransactionRepository transactionRepository, TransactionService transactionService) {
        this.runRepository = runRepository;
        this.transactionRepository = transactionRepository;
        this.transactionService = transactionService;
    }

    @When("Select all with select: {word} for accountId: {string}")
    public void select_all_with_for_account_id_simulator(String select, String accountId) {

        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        transactionService.selectAllTransactions(new SelectAllTransactionInput(run.id().toString(),
            new SelectAllTransactionInput.SelectAllTransactionFiltersInput(
                select.equalsIgnoreCase("true"),
                null,
                List.of(accountId),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )));
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), runRepository.findById(run.id()));
    }

    @When("Select all with select: {word}")
    public void select_all_with_for_account_id_simulator(String select) {

        SettlementRunEntity run = SharedTestState.getSettlementRuns(SharedTestState.getCurrentStartDate());
        transactionService.selectAllTransactions(new SelectAllTransactionInput(run.id().toString(),
            new SelectAllTransactionInput.SelectAllTransactionFiltersInput(
                select.equalsIgnoreCase("true"),
                null,
                List.of(),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null
            )));
        SharedTestState.addSettlementRun(SharedTestState.getCurrentStartDate(), runRepository.findById(run.id()));
    }

    @Then("All transactions are unsettled")
    public void allTransactionsAreUnsettled() {
        List<TransactionEntity> transactions = transactionRepository.findAll();
        for (TransactionEntity transaction : transactions) {
            assertThat(transaction.settlementRunId()).isNull();
        }
    }
}