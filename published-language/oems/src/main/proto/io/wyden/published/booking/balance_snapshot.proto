syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.booking;

import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/booking/fee.proto";

message BalanceSnapshot {
  io.wyden.published.common.Metadata metadata = 1;

  string quantity = 2;
  string pending_quantity = 3;
  string available_for_trading_quantity = 4;
  string available_for_withdrawal_quantity = 5;
  string settled_quantity = 6;
  string unsettled_quantity = 7;

  string symbol = 8; // currency symbol BTC (CashPosition) or asset symbol BTCEUR (AssetPosition)
  string currency = 9; // BTC (CashPosition) or EUR (AssetPosition)
  string portfolio_id = 10; // portfolioId, BANK_Portfolio
  string account_id = 11; // accountId, bitmex-testnet1

  string last_applied_ledger_entry_id = 12;

  repeated Fee fees = 13;
}
