image: maven:3.8.4-eclipse-temurin-17

stages:
  - validate
  - deploy

validate:
  stage: validate
  script:
    - echo "Validation stage"

deploy:
  stage: deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $CI_PIPELINE_SOURCE == "push"
      when: on_success
    - if: $CI_COMMIT_BRANCH =~ /-maintenance/ && $CI_PIPELINE_SOURCE == "push"
      when: on_success
  script:
    - echo "Running task 'publishMavenJavaPublicationToNexus-snapshotsRepository'"
    - ./gradlew publishMavenJavaPublicationToNexus-snapshotsRepository
