Feature: Simple Test
  As a developer
  I want to verify that the testing infrastructure works
  So that I can be confident in my test results

  Background:
    Given last processed sequence number is 0

  Scenario: Simple test scenario
    Given portfolio test-portfolio exists and is configured with USD currency
    And account test-venue-account exists and is configured with USD currency

    Given current market price for BTC is 50000 USD
    And current market price for ETH is 1200 USD

    Given a BookingCompleted event for test-order-1 with sequence number 1
    And the event contains a ledger entry with following details:
      | transactionId   | test-uuid-1       |
      | reservationRef  | test-order-1      |
      | ledgerEntryType | CASH_TRADE_CREDIT |
      | qty             | 1.0               |
      | price           | 50000             |
      | instrument      | BTC               |
      | portfolioId     | test-portfolio    |
      | fee1            | 10 USD            |
      | fee2            | 0.0001 ETH        |

    And the event contains a ledger entry with following details:
      | transactionId   | test-uuid-1      |
      | reservationRef  | test-order-1     |
      | ledgerEntryType | CASH_TRADE_DEBIT |
      | qty             | -50000           |
      | price           | 1.0              |
      | instrument      | USD              |
      | portfolioId     | test-portfolio   |

    And the event contains a ledger entry with following details:
      | transactionId   | test-uuid-1        |
      | reservationRef  | test-order-1       |
      | ledgerEntryType | CASH_TRADE_DEBIT   |
      | qty             | -1.0               |
      | price           | 50000              |
      | instrument      | BTC                |
      | accountId       | test-venue-account |
      | fee1            | 10 USD             |
      | fee2            | 0.0001 ETH         |

    And the event contains a ledger entry with following details:
      | transactionId   | test-uuid-1        |
      | reservationRef  | test-order-1       |
      | ledgerEntryType | CASH_TRADE_CREDIT  |
      | qty             | 50000              |
      | price           | 1.0                |
      | instrument      | USD                |
      | accountId       | test-venue-account |


    When event with sequence number 1 is processed

    Then the ledger entries for test-order-1 should be stored in the database
    And positions should be updated with following details:
      | referenceId    | test-portfolio     | test-portfolio | test-venue-account | test-venue-account |
      | instrument     | BTC                | USD            | BTC                | USD                |
      | qty            | 1.0                | -50000         | -1.0               | 50000              |