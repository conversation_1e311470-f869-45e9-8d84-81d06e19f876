package io.wyden.executionengine.service.brokerdesk;

import io.wyden.executionengine.domain.ClientSideInstrument;
import io.wyden.executionengine.service.config.ExposureTarget;
import io.wyden.executionengine.domain.model.Execution;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

import static io.wyden.cloud.utils.test.TestUtils.bd;
import static io.wyden.executionengine.service.TestSample.CLIENT_SIDE_INSTRUMENT;
import static io.wyden.executionengine.service.brokerdesk.BookSide.BUY;
import static io.wyden.executionengine.service.brokerdesk.BookSide.SELL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class TradeExecutorTest {

    private static final ClientSideInstrument CLIENT_SIDE_SEC = CLIENT_SIDE_INSTRUMENT;

    private final ExposureTarget exposureTarget = new ExposureTarget(bd(0), bd(-10), bd(10));
    private final TradingEngineState engineState = new TradingEngineState(exposureTarget);
    private TradeExecutor tradeExecutor;
    private InternalClientSideBookService clientSideBookService;

    @BeforeEach
    void setUp() {
        engineState.setCurrentMarketPrice(bd(16000));
        engineState.setMaxTimeSinceLastMdEvent(Duration.parse("PT10S"));
        engineState.refreshLastMarketDataEventTimestamp();
        clientSideBookService = mock(InternalClientSideBookService.class);
        tradeExecutor = new TradeExecutor(CLIENT_SIDE_SEC, clientSideBookService);
    }

    @Test
    void shouldNotExecuteIfExposureUnknown() {
        // given
        engineState.setBaseCurrencyExposure(null);

        // when
        List<Execution> executionList = tradeExecutor.execute(engineState, exposureTarget);

        // then
        assertThat(executionList).isEmpty();

    }

    @ParameterizedTest(name = "shouldNotExecuteMoreDemandThanThreshold({0}, {1}, {2}, {3})")
    @CsvSource({
        "20,0,10,0",
        "0,20,0,10",
        "20,20,20,20",
        "200,20,30,20", // we would stop at exposition +10
        "20,200,20,30", // we would stop at exposition -10
    })
    void shouldNotExecuteMoreDemandThanThreshold(int buyDemand, int sellDemand, int expectedBuyExecQty, int expectedSellExecQty) {
        // given
        engineState.setBaseCurrencyExposure(bd(0));
        when(clientSideBookService.getExecutable(any(), eq(BUY))).thenReturn(bd(buyDemand));
        when(clientSideBookService.getExecutable(any(), eq(SELL))).thenReturn(bd(sellDemand));
        Execution dummyBuyExecution = dummyExecution(expectedBuyExecQty, engineState.getCurrentMarketPrice());
        Execution dummySellExecution = dummyExecution(expectedSellExecQty, engineState.getCurrentMarketPrice());
        List<Execution> expectedExecutions = new ArrayList<>();
        if (expectedBuyExecQty != 0) {
            when(clientSideBookService.execute(bd(expectedBuyExecQty), engineState.getCurrentMarketPrice(), BUY)).thenReturn(
                List.of(dummyBuyExecution)
            );
            expectedExecutions.add(dummyBuyExecution);
        }
        if (expectedSellExecQty != 0) {
            when(clientSideBookService.execute(bd(expectedSellExecQty), engineState.getCurrentMarketPrice(), SELL)).thenReturn(
                List.of(dummySellExecution)
            );
            expectedExecutions.add(dummySellExecution);
        }

        // when
        List<Execution> executionList = tradeExecutor.execute(engineState, exposureTarget);

        // then
        verify(clientSideBookService).execute(bd(expectedBuyExecQty), engineState.getCurrentMarketPrice(), BUY);
        verify(clientSideBookService).execute(bd(expectedSellExecQty), engineState.getCurrentMarketPrice(), SELL);

        assertThat(executionList).containsExactlyElementsOf(expectedExecutions);
    }

    @Test
    void afterErrorShouldReturnWhatManagedToExecute() {
        // given
        engineState.setBaseCurrencyExposure(bd(0));
        when(clientSideBookService.getExecutable(any(), eq(BUY))).thenReturn(bd(10));
        when(clientSideBookService.getExecutable(any(), eq(SELL))).thenReturn(bd(10));
        Execution dummyBuyExecution = dummyExecution(10, engineState.getCurrentMarketPrice());
        when(clientSideBookService.execute(bd(10), engineState.getCurrentMarketPrice(), BUY)).thenReturn(
            List.of(dummyBuyExecution)
        );
        when(clientSideBookService.execute(any(), any(), eq(SELL))).thenThrow(new RuntimeException("FAILED TO EXEC SELLS!"));

        // when
        List<Execution> executionList = tradeExecutor.execute(engineState, exposureTarget);

        // then
        verify(clientSideBookService).execute(bd(10), engineState.getCurrentMarketPrice(), BUY);

        assertThat(executionList).containsExactly(dummyBuyExecution);
    }

    @NotNull
    private static Execution dummyExecution(int qty, BigDecimal price) {
        return new Execution(null, bd(qty), BigDecimal.ZERO, price, price);
    }
}
