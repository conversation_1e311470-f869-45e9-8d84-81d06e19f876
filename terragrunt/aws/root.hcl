locals {
  merged = try(merge(
    yamldecode(file(find_in_parent_folders("account.yaml"))),
    yamldecode(file(find_in_parent_folders("region.yaml"))),
    yamldecode(file(find_in_parent_folders("env.yaml")))
  ))

  # Extract the variables we need for easy access
  account_name = local.merged.account_name
  account_id   = local.merged.aws_account_id
  aws_region   = local.merged.aws_region
  default_tags = local.merged.default_tags
  environment  = local.merged.default_tags.environment

  # Modules version
  terraform-aws-vpc               = "v5.16.0"
  terraform-aws-kms               = "v3.1.0"
  terraform-aws-sg                = "v5.2.0"
  terraform-aws-eks               = "v20.31.6"
  terraform-aws-iam               = "v5.52.2"
  terraform-aws-s3-bucket         = "v4.4.0"
  terraform-aws-budgets           = "v1.1.1"
  ecr-pull-through                = "v0.1.0"

  #Provider versions lock
  aws_provider_version = ">= 5.75.0"
}

terraform_version_constraint  = ">= 1.5.7"
terragrunt_version_constraint = ">= 0.68.17"

iam_role = "arn:aws:iam::${local.account_id}:role/OrganizationAccountAccessRole"

# Generate an AWS provider block
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "aws" {
  region  = "${local.aws_region}"

  # Only these AWS Account IDs may be operated on by this template
  allowed_account_ids = ["${local.account_id}"]
}
EOF
}

# Configure Terragrunt to automatically store tfstate files in an S3 bucket
remote_state {
  backend = "s3"
  config = {
    encrypt        = true
    bucket         = "terraform-state-acc-${local.merged.aws_account_id}"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform-locks"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

# ---------------------------------------------------------------------------------------------------------------------
# GLOBAL PARAMETERS
# These variables apply to all configurations in this subfolder. These are automatically merged into the child
# `terragrunt.hcl` config via the include block.
# ---------------------------------------------------------------------------------------------------------------------

# Configure root level variables that all resources can inherit. This is especially helpful with multi-account configs
# where terraform_remote_state data sources are placed directly into the modules.
# inputs = merge(
#   local.account_vars.locals,
#   local.region_vars.locals,
#   local.environment_vars.locals,
# )
