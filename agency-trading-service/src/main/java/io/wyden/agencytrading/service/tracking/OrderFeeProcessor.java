package io.wyden.agencytrading.service.tracking;

import io.wyden.agencytrading.utils.BigDecimalUtils;
import io.wyden.agencytrading.utils.ProtobufUtils;
import io.wyden.published.brokerdesk.CurrencyType;
import io.wyden.published.brokerdesk.TernaryBool;
import io.wyden.published.oems.FeeBasis;
import io.wyden.published.oems.FeeData;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.rate.Rate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static io.wyden.agencytrading.utils.BigDecimalUtils.bd;
import static io.wyden.agencytrading.utils.BigDecimalUtils.isCreatable;

@Service
public class OrderFeeProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderFeeProcessor.class);

    private final RateCache rateCache;

    public OrderFeeProcessor(RateCache rateCache) {
        this.rateCache = rateCache;
    }

    public List<FeeData> calculateFees(BigDecimal totalPercFee, BigDecimal percFeeInPercFeeCurrency, BigDecimal totalFeeCharged, String percFeeCurrency,
                                       TernaryBool chargeExchangeFeeTernary, List<FeeData> exchangeFees,
                                       BigDecimal minFeeInPercFeeCurrency,
                                       FixedFeeConfig fixedFeeConfig) {
        boolean chargeExchangeFee = chargeExchangeFeeTernary == TernaryBool.TRUE;
        List<FeeData> fees = new ArrayList<>();
        if (shouldChargeFixedFee(fixedFeeConfig)) {
            FeeData fixedFee = buildFixedFee(fixedFeeConfig);
            fees.add(fixedFee);
            LOGGER.debug("Fixed fee charged: {}", ProtobufUtils.shortDebugString(fixedFee));
        }

        if (shouldChargeExchangeFee(chargeExchangeFee, exchangeFees)) {
            fees.addAll(exchangeFees);
            LOGGER.debug("Exchange fees charged: {}", exchangeFees.stream().map(ProtobufUtils::shortDebugString).collect(Collectors.joining(",", "[", "]")));
        }

        if (shouldChargeMinFee(totalFeeCharged, minFeeInPercFeeCurrency, percFeeInPercFeeCurrency)) {
            FeeData minFee = buildMinFee(minFeeInPercFeeCurrency.toPlainString(), percFeeCurrency);
            fees.add(minFee);
            LOGGER.debug("Min fee charged: {}", ProtobufUtils.shortDebugString(minFee));
        } else if (shouldChargePercFee(totalPercFee, totalFeeCharged, percFeeInPercFeeCurrency)) {
            String percFeeToCharge = totalPercFee.subtract(totalFeeCharged).min(percFeeInPercFeeCurrency).toPlainString();
            FeeData percFee = buildPercFee(percFeeToCharge, percFeeCurrency);
            fees.add(percFee);
            LOGGER.debug("Perc fee charged: {}", ProtobufUtils.shortDebugString(percFee));
        }

        return fees;
    }

    public static String resolvePercFeeCurrency(OemsRequest oemsRequest, CurrencyType currencyType, String specificCurrency) {
        if (currencyType == CurrencyType.QUOTE_CURRENCY) {
            return oemsRequest.getQuoteCurrency();
        } else if (currencyType == CurrencyType.BASE_CURRENCY) {
            return oemsRequest.getBaseCurrency();
        } else {
            return specificCurrency;
        }
    }

    public BigDecimal calculateMinFeeInPercFeeCurrency(MinFeeConfig minFeeConfig, String percFeeCurrency) {
        try {
            if (!isCreatable(minFeeConfig.amount()) || StringUtils.isBlank(minFeeConfig.currency())) {
                return BigDecimal.ZERO;
            }

            Optional<Rate> optionalRate = rateCache.find(minFeeConfig.currency(), percFeeCurrency);
            Rate rate = optionalRate.orElseThrow(() -> new IllegalArgumentException("Rate not available for currency pair: %s %s".formatted(percFeeCurrency, minFeeConfig.currency())));

            LOGGER.debug("Rate - calculateMinFeeInFeeCurrency: {}", ProtobufUtils.shortDebugString(rate));
            return bd(minFeeConfig.amount()).multiply(bd(rate.getValue()));
        } catch (Exception e) {
            LOGGER.warn("Error when calculating minFee in fee currency: %s".formatted(e.getMessage()));
            return BigDecimal.ZERO;
        }
    }

    public BigDecimal calculatePercFeeInPercFeeCurrency(PercFeeConfig percFeeConfig, String quoteCurrency, BigDecimal executedQuantity, String price) {
        try {
            if (percFeeConfig.currencyType() == CurrencyType.CURRENCY_TYPE_UNSPECIFIED || !isCreatable(percFeeConfig.amount())) {
                return BigDecimal.ZERO;
            }

            BigDecimal percentageFeeAmount = bd(percFeeConfig.amount());
            CurrencyType currencyType = percFeeConfig.currencyType();
            BigDecimal feeInFeeCurrency;
            if (currencyType == CurrencyType.QUOTE_CURRENCY) {
                feeInFeeCurrency = executedQuantity.multiply(bd(price)).multiply(percentageFeeAmount);
            } else if (currencyType == CurrencyType.BASE_CURRENCY) {
                feeInFeeCurrency = executedQuantity.multiply(percentageFeeAmount);
            } else if (currencyType == CurrencyType.SPECIFIC_CURRENCY) {
                Optional<Rate> optionalRate = rateCache.find(quoteCurrency, percFeeConfig.currency());
                Rate rate = optionalRate.orElseThrow(() -> new IllegalArgumentException("Rate not available for currency pair: %s %s".formatted(quoteCurrency, percFeeConfig.currency())));
                LOGGER.debug("Rate - calculatePercFeeInPercFeeCurrency: {}", ProtobufUtils.shortDebugString(rate));
                feeInFeeCurrency = executedQuantity
                    .multiply(bd(price))
                    .multiply(percentageFeeAmount)
                    .multiply(bd(rate.getValue()));
            } else {
                throw new IllegalArgumentException("Unspecified currencyType: %s".formatted(currencyType));
            }
            return feeInFeeCurrency;
        } catch (Exception e) {
            LOGGER.warn("Error when calculating percFee in fee currency: %s".formatted(e.getMessage()));
            return BigDecimal.ZERO;
        }
    }

    private boolean shouldChargeFixedFee(FixedFeeConfig fixedFeeConfig) {
        return !fixedFeeConfig.charged() && BigDecimalUtils.isCreatable(fixedFeeConfig.amount()) && bd(fixedFeeConfig.amount()).compareTo(BigDecimal.ZERO) > 0;
    }

    private boolean shouldChargeMinFee(BigDecimal totalFeeCharged, BigDecimal minFee, BigDecimal percFee) {
        return minFee.compareTo(BigDecimal.ZERO) > 0 &&
            totalFeeCharged.add(percFee).compareTo(minFee) < 0;
    }

    private boolean shouldChargePercFee(BigDecimal totalPercFee, BigDecimal totalFeeCharged, BigDecimal percFeeInCurrency) {
        return percFeeInCurrency.compareTo(BigDecimal.ZERO) > 0 && totalPercFee.compareTo(totalFeeCharged) > 0;
    }

    private boolean shouldChargeExchangeFee(boolean chargeExchangeFee, List<FeeData> exchangeFees) {
        return chargeExchangeFee && CollectionUtils.isNotEmpty(exchangeFees);
    }

    private FeeData buildPercFee(String amount, String currency) {
        return FeeData.newBuilder()
            .setType(FeeType.TRANSACTION_FEE)
            .setBasis(FeeBasis.ABSOLUTE)
            .setAmount(amount)
            .setCurrency(currency)
            .build();
    }

    private FeeData buildMinFee(String amount, String currency) {
        return FeeData.newBuilder()
            .setType(FeeType.TRANSACTION_FEE)
            .setBasis(FeeBasis.ABSOLUTE)
            .setAmount(amount)
            .setCurrency(currency)
            .build();
    }

    private FeeData buildFixedFee(FixedFeeConfig fixedFeeConfig) {
        return FeeData.newBuilder()
            .setType(FeeType.FIXED_FEE)
            .setBasis(FeeBasis.ABSOLUTE)
            .setAmount(fixedFeeConfig.amount())
            .setCurrency(fixedFeeConfig.currency())
            .build();
    }
}
