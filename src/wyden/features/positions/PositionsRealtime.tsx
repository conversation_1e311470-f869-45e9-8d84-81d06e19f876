import { styled } from '@ui/styled';
import { FullScreenButton } from '@wyden/components/FullScreenButton';
import { TutorialButton } from '@wyden/features/tutorial/TutorialButton';
import { positionsRealtimeSteps } from '@wyden/features/tutorial/tutorialData';
import { Widget, WidgetBody } from '@wyden/features/widgets-renderer/widget/Widget';
import { WidgetHeader } from '@wyden/features/widgets-renderer/widget/WidgetHeader';
import { useAnchor } from '@wyden/hooks/useAnchor';
import { PositionResponseSchema } from '@wyden/services/graphql/generated/graphql';
import { useEffect, useState } from 'react';
import { z } from 'zod';
import { selectFocus, useFocusStore } from '../focus/useFocusStore';
import { ColumnManagerProvider } from '../grid/ColumnManager';
import { ColumnSettings } from '../grid/ColumnSettings';
import { WIDGETS_NAMES } from '../widgets-renderer/widget-names';
import { useHeaderItemsCountUpdate } from '../widgets-renderer/widget/hooks/useHeaderItemsCountUpdate';
import { ChangeLivePositionsFilterModeButton } from './ChangeLivePositionsFilterModeButton';
import { PositionsGrid } from './PositionsGrid';
import {
  actionsKey,
  defaultPositionsVisibleFields,
  positionsRealtimeDefinitions,
} from './positionsColumnDefinitions';
import { PositionsProvider } from './useLazyPositions';
import { selectPositions, useLivePositionsStore } from './useLivePositionsStore';
import { usePositionsSubscription } from './usePositionsSubscription';
import { useSystemCurrency } from '@wyden/hooks/useSystemCurrency';
import { usePositionFilters, usePositionSelectors } from './usePositionsFilters';

export function PositionsRealtime() {
  const CONTAINER_NAME = 'positions-realtime-widget';
  usePositionsSubscription();
  const { selectCurrenciesFilters } = usePositionSelectors();
  const currencyFilters = usePositionFilters(selectCurrenciesFilters);
  const { clearPositions } = useLivePositionsStore((state) => ({
    clearPositions: state.clearPositions,
  }));
  const columnSettingsModalProps = useAnchor('column-settings');
  const { portfolio, account } = useFocusStore(selectFocus);
  const [positionsFilterModeSetting, setPositionsFilterModeSetting] = useState(false);
  const { systemCurrency } = useSystemCurrency();

  const livePositions = useLivePositionsStore((state) =>
    selectPositions(
      portfolio ? [portfolio?.id] : [],
      account ? [account.venueAccountId] : [],
      positionsFilterModeSetting,
    )(state),
  );

  useEffect(() => {
    if (currencyFilters !== undefined) {
      clearPositions();
    }
  }, [currencyFilters, clearPositions]);

  useHeaderItemsCountUpdate(livePositions.length);

  return (
    <ColumnManagerProvider
      type={WIDGETS_NAMES.POSITIONS_REALTIME}
      defaultVisibleFields={defaultPositionsVisibleFields}
      validationSchema={PositionResponseSchema().extend({
        [actionsKey]: z.string(),
      })}
      columnDefinitions={positionsRealtimeDefinitions(systemCurrency)}
    >
      <Widget actionsVisible={columnSettingsModalProps.open} dataTestId="positions-widget">
        <PositionsProvider>
          <WidgetHeader>
            <WidgetHeaderSection>
              <ChangeLivePositionsFilterModeButton
                positionsFilterModeSetting={positionsFilterModeSetting}
                setPositionsFilterModeSetting={setPositionsFilterModeSetting}
              />
              <ColumnSettings {...columnSettingsModalProps} />
              <TutorialButton steps={positionsRealtimeSteps} />
              <FullScreenButton />
            </WidgetHeaderSection>
          </WidgetHeader>
          <StyledWidgetBody className={CONTAINER_NAME}>
            <PositionsGrid
              positionsSource={{ source: 'REALTIME', positions: livePositions }}
              onGridReady={() => null}
            />
          </StyledWidgetBody>
        </PositionsProvider>
      </Widget>
    </ColumnManagerProvider>
  );
}

const StyledWidgetBody = styled(WidgetBody)`
  flex-direction: column;
`;
const WidgetHeaderSection = styled('div')`
  display: flex;
  align-items: center;
`;
