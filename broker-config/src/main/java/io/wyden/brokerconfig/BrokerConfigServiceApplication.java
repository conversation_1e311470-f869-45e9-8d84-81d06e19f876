package io.wyden.brokerconfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;

@SpringBootApplication
@Import(value = {io.wyden.cloud.utils.spring.fluentd.FluentdAppender.class})
public class BrokerConfigServiceApplication {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrokerConfigServiceApplication.class);

    public static void main(String[] args) {
        printMemorySettings();
        SpringApplication.run(BrokerConfigServiceApplication.class, args);
    }

    private static void printMemorySettings() {
        int mb = 1024 * 1024;
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        long xmx = memoryMXBean.getHeapMemoryUsage().getMax() / mb;
        long xms = memoryMXBean.getHeapMemoryUsage().getInit() / mb;
        LOGGER.info("Initial Memory : {}mb", xms);
        LOGGER.info("Max Memory: {} mb", xmx);
    }
}
