package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.apiserver.rest.referencedata.portfolio.model.CreatePortfolioDto;
import io.wyden.rest.management.domain.PortfolioModel;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.extension.ExtensionUtils;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.tool.TestGarbageRemover;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.gqlActorKey;
import static io.wyden.test.scenariorunner.extension.common.ExtensionActorUtils.throwInvalidExtensionConfigExceptionIfNull;

/**
 * This extension creates portfolio by provided user clientId beforeAll tests<br>
 *
 * <b>NOTE:</b><br>
 * All e2e prefixed portfolios will be removed by {@link TestGarbageRemover}
 * before tests will run.<br>
 * Check cleanTestGarbage task in build.gradle file
 */
public class PortfolioExtension implements BeforeAllCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(PortfolioExtension.class);

    private final String clientId;
    private final List<CreatePortfolioDto> portfolios;

    private PortfolioModel.PortfolioType portfolioType;

    public PortfolioExtension(String clientId, String... portfolios) {
        this.clientId = clientId;
        this.portfolios = Arrays.stream(portfolios).map(PortfolioFactory::randomCreatePortfolio).toList();
        LOGGER.debug("Initialized with actor={}, portfolios={}", clientId, portfolios);
    }

    public PortfolioExtension(String clientId, CreatePortfolioDto... portfolios) {
        this.clientId = clientId;
        this.portfolios = Arrays.asList(portfolios);
        LOGGER.debug("Initialized with actor={}, portfolios={}", clientId, portfolios);
    }

    @Override
    public void beforeAll(ExtensionContext context) {
        GraphQLActor actor = ExtensionUtils.getGlobalStore(context).get(gqlActorKey(clientId), GraphQLActor.class);

        throwInvalidExtensionConfigExceptionIfNull(actor);

        portfolios.forEach(
            portfolio -> {
                actor.portfolio().createAndWait(portfolio);
                LOGGER.debug("Portfolio created with id and name [{}]", portfolio.id());
            }
        );
    }
}
