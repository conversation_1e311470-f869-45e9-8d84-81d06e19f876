package io.wyden.accessgateway.referencedata;

import com.rabbitmq.client.AMQP;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.accessgateway.client.permission.Permission;
import io.wyden.accessgateway.permissions.service.HazelcastPermissionFacade;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.tools.ProtobufUtils;
import io.wyden.published.referencedata.Grant;
import io.wyden.published.referencedata.account.AccountOnboardingAccessGrantedEvent;
import io.wyden.published.referencedata.account.AccountOnboardingCreatedEvent;
import io.wyden.published.referencedata.account.AccountOnboardingItem;
import io.wyden.published.referencedata.account.AccountOnboardingStatus;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.failureNeedsRequeue;

@Component
public class AccountOnboardingEventConsumer implements MessageConsumer<AccountOnboardingCreatedEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountOnboardingEventConsumer.class);

    private final HazelcastPermissionFacade hazelcastPermissionFacade;
    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<AccountOnboardingCreatedEvent> accountChangeEventExchange;
    private final AccountOnboardingEventEmitter accountOnboardingEventEmitter;

    public AccountOnboardingEventConsumer(RabbitIntegrator rabbitIntegrator,
                                          RabbitExchange<AccountOnboardingCreatedEvent> accountChangeEventExchange,
                                          HazelcastPermissionFacade hazelcastPermissionFacade,
                                          @Value("${rabbitmq.reference-data-access-gateway-queue.account-onboarding-event}") final String queueName,
                                          @Value("${spring.application.name}") String consumerName,
                                          AccountOnboardingEventEmitter accountOnboardingEventEmitter) {
        this.accountChangeEventExchange = accountChangeEventExchange;
        this.rabbitIntegrator = rabbitIntegrator;
        this.hazelcastPermissionFacade = hazelcastPermissionFacade;
        this.queueName = queueName;
        this.consumerName = consumerName;
        this.accountOnboardingEventEmitter = accountOnboardingEventEmitter;
    }

    @PostConstruct
    void init() {
        declareQueue();
    }

    private void declareQueue() {
        RabbitQueue<AccountOnboardingCreatedEvent> queue = new RabbitQueueBuilder<AccountOnboardingCreatedEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(accountChangeEventExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", accountChangeEventExchange, queue);
        queue.attachConsumer(AccountOnboardingCreatedEvent.parser(), this);
    }

    @Override
    public ConsumptionResult consume(AccountOnboardingCreatedEvent accountChangeEvent, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming AccountOnboardingCreatedEvent message, {}", ProtobufUtils.shortDebugString(accountChangeEvent));

            List<AccountOnboardingItem> onboardingItems = accountChangeEvent.getItemList().stream()
                .map(event -> {
                    try {
                        if (event.getStatus() == AccountOnboardingStatus.ACCOUNT_ONBOARDING_STATUS_OK) {
                            String venueAccountId = event.getAccount().getId();

                            LOGGER.info("Creating new permissions for venueAccount with id, {}", venueAccountId);
                            if (!event.getRequest().getGrantsList().isEmpty()) {
                                addAdditionalPermissions(event.getRequest().getGrantsList(), venueAccountId);
                            }
                        }
                    } catch (Exception e) {
                        LOGGER.error("Failed to onboard account using onboarding event: {}. Reason: {}", event, e.getMessage(), e);
                        return event.toBuilder()
                            .setStatus(AccountOnboardingStatus.ACCOUNT_ONBOARDING_STATUS_FAILED)
                            .setReason("Failed to grant access rights to account. Reason: " + e.getMessage())
                            .build();
                    }

                    return event;
                })
                .toList();

            AccountOnboardingAccessGrantedEvent onboardingAccessGrantedEvent = AccountOnboardingAccessGrantedEvent.newBuilder()
                .setMetadata(accountChangeEvent.getMetadata())
                .addAllItem(onboardingItems)
                .build();

            accountOnboardingEventEmitter.emit(onboardingAccessGrantedEvent);

        } catch (Exception e) {
            LOGGER.error("Failed to consume incoming: %s, re-queueing...".formatted(accountChangeEvent), e);
            return failureNeedsRequeue();
        }

        return ConsumptionResult.consumed();
    }

    private void addAdditionalPermissions(List<Grant> grants, String venueAccountId) {
        for (Grant grant : grants) {
            for (String groupName : grant.getGroupsList()) {
                PermissionDto groupPermission = new PermissionDto(Permission.Resources.VENUE_ACCOUNT, grant.getScope(), venueAccountId);
                Set<PermissionDto> permissionsToCreate = Set.of(groupPermission);

                LOGGER.info("Creating additional permissions for group: {}, permissions: {}", groupName, groupPermission);
                hazelcastPermissionFacade.addGroupPermissions(groupName, permissionsToCreate);
            }
        }
    }
}
