package io.wyden.apiserver.rest.referencedata.portfolio.model;

import io.wyden.apiserver.rest.security.model.Scope;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

public record PortfolioResponseDto(String id,
                                   String name,
                                   String createdAt,
                                   String portfolioCurrency,
                                   PortfolioTypeDto portfolioType,
                                   List<TagDto> tags,
                                   Instant archivedAt,
                                   List<Scope> scopes,
                                   List<Scope> dynamicScopes) implements Serializable {
}
