import { WIDGETS_NAMES } from '@wyden/features/widgets-renderer/widget-names';

export const verticalEmptyWorkspace = {
  id: 'nest-basicRow',
  type: 'LayoutCell',
  name: '<PERSON><PERSON>',
  direction: 'DIRECTION_VERTICAL',
  children: [
    {
      id: 'nest-mainRow',
      type: 'LayoutCell',
      name: '<PERSON><PERSON>',
      direction: 'DIRECTION_VERTICAL',
      children: [
        {
          id: 'nest-main-workspace',
          type: 'LayoutCell',
          name: 'MAINWORKSPACE',
          direction: 'DIRECTION_HORIZONTAL',
          children: [
            {
              id: 'nest-center-workspace',
              type: 'LayoutCell',
              name: 'CENTERWORKSPACE',
              direction: 'DIRECTION_VERTICAL',
              children: [
                {
                  id: 'nest-dashboard-workspace',
                  type: 'LayoutCell',
                  name: 'BOT<PERSON><PERSON><PERSON>',
                  children: [
                    {
                      id: 'nest-east',
                      type: 'LayoutCell',
                      name: '<PERSON>EF<PERSON><PERSON>UM<PERSON>',
                      direction: 'DIRECTION_VERTICAL',
                      acceptsFlavours: ['DraggableComponent'],
                      children: [
                        {
                          id: 'empty-workspace-1',
                          type: 'LayoutComponent',
                          name: WIDGETS_NAMES.EMPTY_WORKSPACE,
                          displayName: 'Empty Workspace',
                          flavours: ['DraggableComponent'],
                          acceptsFlavours: ['DraggableComponent'],
                        },
                      ],
                    },
                    {
                      id: 'nest-west',
                      type: 'LayoutCell',
                      name: 'RIGHTCOLUMN',
                      direction: 'DIRECTION_VERTICAL',
                      acceptsFlavours: ['DraggableComponent'],
                      children: [
                        {
                          id: 'empty-workspace-2',
                          type: 'LayoutComponent',
                          name: WIDGETS_NAMES.EMPTY_WORKSPACE,
                          displayName: 'Empty Workspace',
                          flavours: ['DraggableComponent'],
                          acceptsFlavours: ['DraggableComponent'],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};
