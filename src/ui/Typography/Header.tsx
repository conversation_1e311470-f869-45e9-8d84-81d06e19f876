import { styled } from '@ui/styled';
import React from 'react';

type Props = {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: React.ReactNode;
  className?: string;
};

export function Header({ variant = 'h1', children, className }: Props) {
  const ProperComponent = {
    h1: styledH1,
    h2: styledH2,
    h3: styledH3,
    h4: styledH4,
    h5: styledH5,
    h6: styledH6,
  }[variant];

  return <ProperComponent className={className}>{children}</ProperComponent>;
}

const styledH1 = styled('h1')`
  font-size: 2rem;
  margin: 0;
  padding: 0;
  line-height: 2.5rem;
  font-weight: 600;
`;

const styledH2 = styled('h2')`
  font-size: 1.75rem;
  line-height: 2.25rem;
  margin: 0;
  padding: 0;
  font-weight: 600;
`;

const styledH3 = styled('h3')`
  font-size: 1.5rem;
  line-height: 2rem;
  margin: 0;
  padding: 0;
  font-weight: 600;
`;

const styledH4 = styled('h4')`
  font-size: 1.25rem;
  line-height: 1.625rem;
  margin: 0;
  padding: 0;
  font-weight: 600;
`;

const styledH5 = styled('h5')`
  font-size: 1rem;
  line-height: 1.5rem;
  margin: 0;
  padding: 0;
  font-weight: 600;
`;

const styledH6 = styled('h6')`
  font-size: 0.875rem;
  line-height: 1.375rem;
  margin: 0;
  padding: 0;
  font-weight: 500;
`;
