package io.wyden.aeron.client.pojo;

import io.wyden.aeron.client.enumeration.EventType;

import exchange.core2.core.common.OrderAction;

public class TradeEvent extends ExecutionEvent {

    private final long volume;
    private final long tradeId;
    private final long matchId;
    private final OrderAction action;
    private final boolean orderCompleted;


    public TradeEvent(int symbol, long timestamp, long uid, long orderId, long price, long volume, long tradeId, long matchId, OrderAction action, boolean orderCompleted, long orderUuidMostSigBits, long orderUuidLeastSigBits) {
        super(EventType.TRADE_EVENT, symbol, timestamp, uid, orderId, price, orderUuidMostSigBits, orderUuidLeastSigBits);
        this.volume = volume;
        this.tradeId = tradeId;
        this.matchId = matchId;
        this.action = action;
        this.orderCompleted = orderCompleted;
    }

    public long getVolume() {
        return volume;
    }

    public long getTradeId() {
        return tradeId;
    }

    public long getMatchId() {
        return matchId;
    }

    public OrderAction getAction() {
        return action;
    }

    public boolean isOrderCompleted() {
        return orderCompleted;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("[TradeEvent]symbol=")
            .append(getSymbol())
            .append("|timestamp=")
            .append(getTimestamp())
            .append("|uid=")
            .append(getUid())
            .append("|orderId=")
            .append(getOrderId())
            .append("|price=")
            .append(getPrice())
            .append("|volume=")
            .append(volume)
            .append("|tradeId=")
            .append(tradeId)
            .append("|matchId=")
            .append(matchId)
            .append("|action=")
            .append(action)
            .append("|orderCompleted=")
            .append(orderCompleted);
        return builder.toString();
    }

}
