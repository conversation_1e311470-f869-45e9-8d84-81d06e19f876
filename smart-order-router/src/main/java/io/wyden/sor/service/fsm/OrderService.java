package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.smartrecommendationengine.*;
import io.wyden.sor.service.tracking.OrderIdentificationTracker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

// TODO: Duplicate messages

@Component
public class OrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderService.class);
    private final OrderIdentificationTracker orderIdentificationTracker;

    private final SmartOrderRoutingService smartOrderRoutingService;

    public OrderService(OrderIdentificationTracker orderIdentificationTracker,
                        SmartOrderRoutingService smartOrderRoutingService) {
        this.orderIdentificationTracker = orderIdentificationTracker;
        this.smartOrderRoutingService = smartOrderRoutingService;
    }

    public void onOemsRequest(OemsRequest smartOrder) {
        OrderContext context = new OrderContext(this);
        context.onOemsRequest(smartOrder);
    }

    public void onOemsParentResponse(OemsResponse smartOrderExecutionReport) {
        OrderContext context = new OrderContext(this);
        context.onSmartOrderExecutionReport(smartOrderExecutionReport);
    }

    public void onOemsChildResponse(OemsResponse childExecutionReport) {
        OrderContext context = new OrderContext(this);
        context.onChildExecutionReport(childExecutionReport);
    }

    public void onCancelReject(OemsResponse childExecutionReport) {
        OrderContext context = new OrderContext(this);
        context.onChildCancelReject(childExecutionReport);
    }

    public void onOrderExpiredEvent(SOROrderExpiredEvent orderExpiredEvent) {
        OrderContext context = new OrderContext(this);
        context.onExpired(orderExpiredEvent);
    }

    public void onCandidateSuspensionTimeoutEvent(SORCandidateSuspensionTimeoutEvent sorCandidateSuspensionTimeoutEvent) {
        OrderContext context = new OrderContext(this);
        context.onCandidateSuspensionTimeout(sorCandidateSuspensionTimeoutEvent);
    }

    public void onSubmissionCheckEvent(SORSubmissionCheckEvent submissionCheckEvent) {
        OrderContext context = new OrderContext(this);
        context.onSubmissionCheck(submissionCheckEvent);
    }

    public void onExecutionCheckEvent(SORLimitOrderExecutionCheckEvent executionCheckEvent) {
        OrderContext context = new OrderContext(this);
        context.onExecutionCheck(executionCheckEvent);
    }

    public void onCancellationCheckEvent(SORCancellationCheckEvent cancellationCheckEvent) {
        OrderContext context = new OrderContext(this);
        context.onCancellationCheck(cancellationCheckEvent);
    }

    public void onChildOrderNotDelivered(OemsRequest childOrder) {
        LOGGER.warn("OemsRequest not delivered: {}", childOrder);
    }

    SmartOrderRoutingService getSmartOrderRoutingService() {
        return smartOrderRoutingService;
    }

    OrderIdentificationTracker getOrderIdentificationTracker() {
        return orderIdentificationTracker;
    }

    public void onSmartOrderRetry(SOROrderRetryEvent smartOrderRetryEvent) {
        OrderContext context = new OrderContext(this);
        context.onSmartOrderRetry(smartOrderRetryEvent);
    }
}
