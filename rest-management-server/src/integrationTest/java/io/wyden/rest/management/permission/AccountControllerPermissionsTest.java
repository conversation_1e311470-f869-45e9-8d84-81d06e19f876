package io.wyden.rest.management.permission;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.rest.management.DisableApiSignatureValidation;
import io.wyden.rest.management.WithMockCustomUser;
import io.wyden.rest.management.account.AccountController;
import io.wyden.rest.management.account.AccountPermissionValidator;
import io.wyden.rest.management.account.AccountService;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.infrastructure.web.WebSecurityConfig;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.security.permission.AccessGatewayClient;
import io.wyden.rest.management.security.permission.AccessGatewayFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static java.util.Objects.isNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = AccountController.class)
@Import({WebSecurityConfig.class, DisableApiSignatureValidation.class, AccountPermissionValidator.class})
@MockBean(classes = { PermissionChecker.class, HazelcastInstance.class, LicenseService.class } )
@AutoConfigureMockMvc
public class AccountControllerPermissionsTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    BookingEngineService bookingEngineService;
    @MockBean
    AccountService accountService;
    @MockBean
    AccessGatewayFacade accessGatewayFacade;
    @MockBean
    VenueAccountRepository venueAccountRepository;
    @MockBean
    PortfolioRepository portfolioRepository;

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AccountPermissionValidator accountPermissionValidator;

    @BeforeEach
    void mockResponses() {
        when(accountService.exists(eq("accountId"))).thenReturn(true);
        PaginationModel.CursorConnection pageMock = new PaginationModel.CursorConnection<>(List.of(), new PaginationModel.PageInfo(true, "", 0, 0L));
        when(bookingEngineService.findPositionsByAccountId(eq("accountId"), anyString()))
            .thenReturn(pageMock);
        when(bookingEngineService.findBalancesByAccountId(eq("accountId"), anyString()))
            .thenReturn(pageMock);
        when(bookingEngineService.findBalancesByPortfolioId(eq("portfolioId"), anyString()))
            .thenReturn(pageMock);
        when(bookingEngineService.findPositionsByPortfolioId(eq("portfolioId"), anyString()))
            .thenReturn(pageMock);
    }

    @WithMockCustomUser(authorities = {
        "accountId:venue.account:read",
        "venue.account:create",
    })
    @ParameterizedTest
    @MethodSource("accountIdPermissions_endpointsAndPayloads")
    void accountReadPermissions(String endpoint, Object payload) throws Exception {
        // Given
        // When
        when(accessGatewayFacade.hasPermission(any(), any(), any())).thenReturn(true);
        when(venueAccountRepository.find(any())).thenReturn(Optional.of(VenueAccount.newBuilder().setId("accountId").build()));
        mockMvc.perform(isNull(payload)
                ? MockMvcRequestBuilders.get(endpoint)
                : MockMvcRequestBuilders.post(endpoint)
                .content(objectMapper.writeValueAsString(payload))
                .contentType(MediaType.APPLICATION_JSON))
            //then
            .andExpect(status().isOk());
    }

    @WithMockCustomUser
    @ParameterizedTest
    @MethodSource("accountIdPermissions_endpointsAndPayloads")
    void negativeTest(String endpoint, Object payload) throws Exception {
        when(accessGatewayFacade.hasPermission(any(), any(), any())).thenReturn(false);
        when(venueAccountRepository.find(any())).thenReturn(Optional.of(VenueAccount.newBuilder().setId("accountId").build()));

        mockMvc.perform(isNull(payload)
                ? MockMvcRequestBuilders.get(endpoint)
                : MockMvcRequestBuilders.post(endpoint)
                .content(objectMapper.writeValueAsString(payload))
                .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(status().isUnauthorized());
    }

    static Stream<Arguments> accountIdPermissions_endpointsAndPayloads() {
        return Stream.of(
            Arguments.of("/api/v1/accounts/accountId/balances", null),
            Arguments.of("/api/v1/accounts/accountId/positions", null)
        );
    }
}
