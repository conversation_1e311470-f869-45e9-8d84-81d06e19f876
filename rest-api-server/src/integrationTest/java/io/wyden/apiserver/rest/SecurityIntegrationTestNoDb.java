
package io.wyden.apiserver.rest;

import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioValidator;
import io.wyden.apiserver.rest.userdata.UserDataService;
import io.wyden.apiserver.rest.walletaccount.WalletAccountValidator;
import org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializer;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.JdbcTemplate;

@MockBean(classes = {
    JdbcTemplate.class,
    UserDataService.class,
    WalletAccountValidator.class,
    PortfolioValidator.class,
    FlywayMigrationInitializer.class,
    LicenseService.class
})
public abstract class SecurityIntegrationTestNoDb extends SecurityIntegrationTest {
}
