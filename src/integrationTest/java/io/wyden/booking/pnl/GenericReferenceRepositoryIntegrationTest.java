package io.wyden.booking.pnl;

import io.wyden.booking.pnl.domain.model.reference.GenericReferenceRepository;
import io.wyden.booking.pnl.application.GenericReferenceService;
import io.wyden.booking.pnl.domain.model.instrument.InstrumentRepository;
import io.wyden.booking.pnl.domain.model.instrument.Currency;
import io.wyden.booking.pnl.domain.model.reference.AccountReference;
import io.wyden.booking.pnl.domain.model.reference.GenericReference;
import io.wyden.booking.pnl.domain.model.reference.PortfolioReference;
import org.hibernate.exception.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Collection;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

public class GenericReferenceRepositoryIntegrationTest extends TestContainersIntegrationBase {

    @Autowired
    private GenericReferenceRepository genericReferenceRepository;

    @Autowired
    private GenericReferenceService genericReferenceService;

    @Autowired
    private InstrumentRepository instrumentRepository;

    private Currency btcCurrency;
    private Currency ethCurrency;

    @BeforeEach
    void setUp() throws Exception {
        // Clear tables before each test
        DB.clearTable("ledger_entry");
        DB.clearTable("generic_reference");
        DB.clearTable("instrument");

        // Create and save currencies
        btcCurrency = new Currency("BTC");
        instrumentRepository.save(btcCurrency);

        ethCurrency = new Currency("ETH");
        instrumentRepository.save(ethCurrency);
    }

    @Test
    void shouldSaveAndRetrievePortfolioReference() throws Exception {
        // Given
        PortfolioReference portfolioReference = GenericReference.createPortfolioReference(
            "Test Portfolio", "portfolio1", btcCurrency);

        // When
        GenericReference savedReference = genericReferenceRepository.save(portfolioReference);

        // Then
        assertThat(savedReference).isNotNull();
        assertThat(savedReference.getId()).isNotNull();
        assertThat(savedReference.getName()).isEqualTo("Test Portfolio");
        assertThat(savedReference.getReferenceId()).isEqualTo("portfolio1");
        assertThat(savedReference.getCurrency()).isEqualTo(btcCurrency);

        // Verify database state
        DB.logTableContent("generic_reference");
        int count = DB.count("generic_reference");
        assertThat(count).isEqualTo(1);
    }

    @Test
    void shouldSaveAndRetrieveAccountReference() throws Exception {
        // Given
        AccountReference accountReference = GenericReference.createAccountReference(
            "Test Account", "account1", ethCurrency);

        // When
        GenericReference savedReference = genericReferenceRepository.save(accountReference);

        // Then
        assertThat(savedReference).isNotNull();
        assertThat(savedReference.getId()).isNotNull();
        assertThat(savedReference.getName()).isEqualTo("Test Account");
        assertThat(savedReference.getReferenceId()).isEqualTo("account1");
        assertThat(savedReference.getCurrency()).isEqualTo(ethCurrency);

        // Verify database state
        DB.logTableContent("generic_reference");
        int count = DB.count("generic_reference");
        assertThat(count).isEqualTo(1);
    }

    @Test
    void shouldFindReferenceByReferenceId() throws Exception {
        // Given
        PortfolioReference portfolioReference = GenericReference.createPortfolioReference(
            "Test Portfolio", "portfolio1", btcCurrency);
        genericReferenceRepository.save(portfolioReference);

        AccountReference accountReference = GenericReference.createAccountReference(
            "Test Account", "account1", ethCurrency);
        genericReferenceRepository.save(accountReference);

        // When
        Collection<GenericReference> foundPortfolioReferences = genericReferenceRepository.findByReferenceId("portfolio1");
        Collection<GenericReference> foundAccountReferences = genericReferenceRepository.findByReferenceId("account1");
        Collection<GenericReference> nonExistentReferences = genericReferenceRepository.findByReferenceId("nonexistent");

        // Then
        assertThat(foundPortfolioReferences).hasSize(1);
        assertThat(foundPortfolioReferences.iterator().next().getName()).isEqualTo("Test Portfolio");

        assertThat(foundAccountReferences).hasSize(1);
        assertThat(foundAccountReferences.iterator().next().getName()).isEqualTo("Test Account");

        assertThat(nonExistentReferences).isEmpty();
    }

    @Test
    void shouldEnforceUniqueConstraintOnReferenceIdAndType() throws Exception {
        // Given
        PortfolioReference portfolioReference1 = GenericReference.createPortfolioReference(
            "Test Portfolio 1", "portfolio1", btcCurrency);
        genericReferenceRepository.save(portfolioReference1);

        // When/Then - Same reference_id and type (PORTFOLIO) should violate the constraint
        PortfolioReference portfolioReference2 = GenericReference.createPortfolioReference(
            "Test Portfolio 2", "portfolio1", ethCurrency);

        assertThatThrownBy(() -> genericReferenceRepository.save(portfolioReference2))
            .isInstanceOf(DataIntegrityViolationException.class)
            .hasCauseInstanceOf(ConstraintViolationException.class);

        // But different reference_id with same type should be allowed
        PortfolioReference portfolioReference3 = GenericReference.createPortfolioReference(
            "Test Portfolio 3", "portfolio2", btcCurrency);
        genericReferenceRepository.save(portfolioReference3);

        // And same reference_id with different type should be allowed
        AccountReference accountReference = GenericReference.createAccountReference(
            "Test Account", "portfolio1", ethCurrency);
        genericReferenceRepository.save(accountReference);

        // Verify database state
        DB.logTableContent("generic_reference");
        int count = DB.count("generic_reference");
        assertThat(count).isEqualTo(3);
    }

    @Test
    void shouldUseGenericReferenceServiceToSaveReference() throws Exception {
        // Given
        PortfolioReference portfolioReference = GenericReference.createPortfolioReference(
            "Test Portfolio", "portfolio1", btcCurrency);

        // When
        GenericReference savedReference = genericReferenceService.save(portfolioReference);

        // Then
        assertThat(savedReference).isNotNull();
        assertThat(savedReference.getId()).isNotNull();
        assertThat(savedReference.getName()).isEqualTo("Test Portfolio");
        assertThat(savedReference.getReferenceId()).isEqualTo("portfolio1");
        assertThat(savedReference.getCurrency().getSymbol()).isEqualTo(btcCurrency.getSymbol());

        // Verify database state
        DB.logTableContent("generic_reference");
        int count = DB.count("generic_reference");
        assertThat(count).isEqualTo(1);
    }

    @Test
    void shouldUseGenericReferenceServiceToFindOrCreateReference() throws Exception {
        // Given
        PortfolioReference portfolioReference = GenericReference.createPortfolioReference(
            "Test Portfolio", "portfolio1", btcCurrency);

        // When - First call should create
        GenericReference savedReference = genericReferenceService.getOrCreate(portfolioReference);

        // Then
        assertThat(savedReference).isNotNull();
        assertThat(savedReference.getId()).isNotNull();
        assertThat(savedReference.getName()).isEqualTo("Test Portfolio");
        assertThat(savedReference.getReferenceId()).isEqualTo("portfolio1");

        // When - Second call with same reference_id should find existing
        PortfolioReference duplicateReference = GenericReference.createPortfolioReference(
            "Different Name", "portfolio1", ethCurrency);
        GenericReference foundReference = genericReferenceService.getOrCreate(duplicateReference);

        // Then - Should return the existing reference, not create a new one
        assertThat(foundReference.getId()).isEqualTo(savedReference.getId());
        assertThat(foundReference.getName()).isEqualTo("Test Portfolio"); // Original name
        assertThat(foundReference.getCurrency().getSymbol()).isEqualTo(btcCurrency.getSymbol()); // Original currency

        // Verify database state - still only one record
        DB.logTableContent("generic_reference");
        int count = DB.count("generic_reference");
        assertThat(count).isEqualTo(1);
    }
}
