Meta: Implements "Wyden Cloud - Accounting.xlsx", tab "PnL - CashTrade"

Scenario: deposit
Given system currency is set to USD

Given portfolio Client 1 is configured to use EUR as currency
Given portfolio Bank is configured to use BTC as currency

Given market data prices:
| instrument | price       |
| BTC        | 40_000 EUR  |
| BTC        | 50_000 USD  |
| BTC        | 200_000 PLN |
| EUR        | 5 PLN       |
| USD        | 4 PLN       |
| EUR        | 1.25 USD    |

When Client 1 deposits 1000 PLN into Bitfinex account
When Bank deposits 1000 USD into Bitfinex account
Then position net state change to:
| portfolioId | instrument | qty   | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | PLN        | 1_000 | 200  | 200         | -           | -             | 0.2          |
| Client 1    | USD        | -     | -    | -           | -           | -             | -            |
| Bank        | PLN        | -     | -    | -           | -           | -             | -            |
| Bank        | USD        | 1_000 | 0.02 | 0.02        | -           | -             | 0.00002      |

When Client 1 trades -500 PLN/USD with 0 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | PLN        | 500 | 100    | 100         | -           | -             | 0.2          |
| Client 1    | USD        | 125 | 100    | 100         | -           | -             | 0.8          |
| Bank        | PLN        | 500 | 0.0025 | 0.0025      | -           | -             | 0.000005     |
| Bank        | USD        | 875 | 0.0175 | 0.0175      | -           | -             | 0.00002      |

Given market data prices:
| instrument | price    |
| EUR        | 4.5 PLN  |
| USD        | 3.6 PLN  |
| EUR        | 1.25 USD |

!-- When Client 1 trades 500 PLN/USD with 0 USD transaction fee
When Client 1 trades -125 USD/PLN with 0 USD transaction fee
Then position net state change to:
| portfolioId | instrument | qty | cost | marketValue  | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | PLN        | 950 | 200  | 211.******** | -           | 11.********   | 0.********   |
| Client 1    | USD        | -   | -    | -            | -           | -             | -            |
