locals {
  environment = include.root.locals.environment
  location = include.root.locals.location
}

dependency "key-vault" {
  config_path = "../key-vault"
  mock_outputs = {
    key_vault_id = "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/ResourceGroup/providers/Microsoft.KeyVault/vaults/myVault"
  }
}

dependencies {
  paths = ["../key-vault"]
}

terraform {
  source = "${get_parent_terragrunt_dir()}/terragrunt/modules/key-vault-key"
}

include "root" {
  path = find_in_parent_folders()
  expose = true
}

inputs = {
  name = "unseal-poc"
  key_vault_id = dependency.key-vault.outputs.key_vault_id

  tags = {
    Environment = "${include.root.locals.merged.environment}"
  }
}