package io.wyden.quotingorderservice.service.fsm;

import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.quotingorderservice.model.QuotingOrderState;
import io.wyden.quotingorderservice.service.oems.outbound.OemsResponseFactory;
import io.wyden.quotingorderservice.service.tracking.FailureRequeueException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;
import java.util.stream.Collectors;

import static io.wyden.published.oems.OemsOrderStatus.STATUS_CANCELED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_EXPIRED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_FILLED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PARTIALLY_FILLED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_CANCEL_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_NEW_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED_VALUE;

/**
 * OrderStatus descendants represent order state in state machine. Each order may exist in more than one order state,
 * the value with the highest precedence is used to handle events. State transitions are described below:
 *
 *              +----------+--------------------> Rejected
 *              | +--------+---------+-------------v
 * Start -> PendingNew -> New -> PartialFill   -> Filled
 *               |         |          v ^
 *               +---------+---> PendingCancel -> Cancelled
 */
@SuppressWarnings("squid:S1172") // Remove unused method parameters
abstract class OrderStatus implements Comparable<OrderStatus> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatus.class);
    private static final String DUPLICATE_ORDER_ID = "Duplicate orderId";

    enum Precedence {
        START(0),
        PENDING_NEW(2),
        NEW(2),
        REJECTED(2),
        PARTIAL_FILL(3),
        CANCELLED(4),
        EXPIRED(4),
        FILLED(7),
        PENDING_CANCEL(11),
        UNRECOGNIZED(100);

        private final int value;

        Precedence(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    abstract Precedence getPrecedence();

    void onNewOrder(OrderContext context, OemsRequest request) {
        OrderState orderState = context.getOrderState();
        OemsResponse reject = OemsResponseFactory.createRejectAsDuplicate(orderState, DUPLICATE_ORDER_ID);
        context.emit(reject);
    }

    void onCancel(OrderContext context, OemsRequest request) {
        String inResponseToRequestId = request.getMetadata().getRequestId();
        String inResponseToRequesterId = request.getMetadata().getRequesterId();
        OrderState orderState = context.getOrderState();
        context.emit(OemsResponseFactory.createCancelRejectWrongState(inResponseToRequestId, inResponseToRequesterId, orderState));
    }

    void onCancelReject(OrderContext context, OemsResponse venueCancelReject) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Ignoring CancelReject '{}' for force cancelled order. Current OrderState: {}", venueCancelReject.getMetadata().getRequestId(), context.getOrderState());
            return;
        }

        LOGGER.warn("Received CancelReject '{}' inconsistent with OrderState. Current OrderState: {}", venueCancelReject.getMetadata().getRequestId(), context.getOrderState());
    }

    void onUnspecified(OrderContext context, OemsResponse report) {
        LOGGER.debug("Received correlation message");
    }

    void onPendingNew(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.PENDING_NEW inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onNew(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.NEW inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onRejected(OrderContext context, OemsResponse report) {
        LOGGER.warn("Received ExecType.REJECTED inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onTrade(OrderContext context, OemsResponse report) {
        LOGGER.info("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onCancelled(OrderContext context, OemsResponse report) {
        LOGGER.info("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onExpired(OrderContext context, OemsResponse report) {
        LOGGER.info("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onCalculated(OrderContext context, OemsResponse report) {
        LOGGER.info("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onRestated(OrderContext context, OemsResponse report) {
        LOGGER.info("Received ExecType.{} inconsistent with OrderState. Current OrderState: {}", report.getExecType(), context.getOrderState());
    }

    void onUnrecognized(OrderContext context, OemsResponse report) {
        throw new FailureRequeueException("Unrecognized ExecType.%d in received ExecutionReport.".formatted(report.getExecTypeValue()));
    }

    void onOrderNotDelivered(OrderContext context, OemsRequest venueOrder) {
        LOGGER.info("Received order delivery error in wrong state. Current state: {}", context.getOrderState());
    }

    void onCancelNotDelivered(OrderContext context, OemsRequest venueCancel) {
        LOGGER.info("Received cancel delivery error in wrong state. Current state: {}", context.getOrderState());
    }

    boolean isTerminal() {
        return false;
    }

    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
    }

    static Set<OrderStatus> toOrderStates(QuotingOrderState quotingOrderState) {
        return quotingOrderState.getCurrentStatusValueList().stream()
            .map(OrderStatus::toOrderStatus)
            .collect(Collectors.toSet());
    }

    static OrderStatus toOrderStatus(int venueOrderStatus) {
        return switch (venueOrderStatus) {
            case STATUS_PENDING_NEW_VALUE -> StatusPendingNew.create();
            case STATUS_NEW_VALUE -> StatusNew.create();
            case STATUS_PARTIALLY_FILLED_VALUE -> StatusPartialFill.create();
            case STATUS_FILLED_VALUE -> StatusFilled.create();
            case STATUS_PENDING_CANCEL_VALUE -> StatusPendingCancel.create();
            case STATUS_CANCELED_VALUE -> StatusCancelled.create();
            case STATUS_REJECTED_VALUE -> StatusRejected.create();
            case STATUS_EXPIRED_VALUE -> StatusExpired.create();
            default -> StatusUnrecognized.create(venueOrderStatus);
        };
    }

    @Override
    public boolean equals(Object o) {
        return o != null && getClass() == o.getClass();
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "%s.%d".formatted(this.getClass().getSimpleName(), this.getPrecedence().getValue());
    }

    @Override
    public int compareTo(OrderStatus o) {
        return this.getPrecedence().getValue() - o.getPrecedence().getValue();
    }
}
