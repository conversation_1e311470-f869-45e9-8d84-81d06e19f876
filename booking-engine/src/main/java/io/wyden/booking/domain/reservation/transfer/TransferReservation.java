package io.wyden.booking.domain.reservation.transfer;

import io.wyden.booking.domain.reservation.Reservation;
import io.wyden.booking.domain.reservation.ReservationFee;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;

/**
 * A Transfer describes the process of moving a Cash or an Asset from one portfolio/account within Wyden’s
 * oversight to another portfolio/account within Wyden’s oversight.
 */
@Entity
public abstract class TransferReservation extends Reservation {

    /**
     * the quantity that is transferred
     */
    protected BigDecimal quantity;

    /**
     * internal ID of the corresponding transfer order
     */
    @Transient // not needed, use extTransactionId instead?
    protected String intTransferId;

    /**
     * external / custody ID of the corresponding transfer order
     */
    @Transient // not needed, use extTransactionId instead?
    protected String extTransferId;

    public TransferReservation(String uuid,
                               String reservationRef,
                               ZonedDateTime dateTime,
                               BigDecimal quantity,
                               String intTransferId,
                               String extTransferId,
                               Collection<ReservationFee> fees) {
        super(uuid, reservationRef, dateTime, fees);
        this.quantity = quantity;
        this.intTransferId = intTransferId;
        this.extTransferId = extTransferId;
    }

    protected TransferReservation() {
        // JPA
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public String getIntTransferId() {
        return intTransferId;
    }

    public String getExtTransferId() {
        return extTransferId;
    }
}
