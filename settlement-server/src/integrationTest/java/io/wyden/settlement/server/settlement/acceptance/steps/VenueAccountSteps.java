package io.wyden.settlement.server.settlement.acceptance.steps;

import com.hazelcast.map.IMap;
import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.settlement.server.settlement.acceptance.SharedTestState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class VenueAccountSteps {
    private Logger LOGGER = LoggerFactory.getLogger(VenueAccountSteps.class);
    private final IMap<String, VenueAccount> venueAccountsMap;

    public VenueAccountSteps(IMap<String, VenueAccount> venueAccountsMap) {
        this.venueAccountsMap = venueAccountsMap;
    }

    @Given("Venue Accounts")
    public void preconfiguredVenueAccount(DataTable dataTable) {
        List<Map<String, String>> variables = dataTable.asMaps();
        variables.forEach(row -> {
            String accountId = row.get("accountId");
            String accountName = row.getOrDefault("accountName", "");
            String venueName = row.get("venueName");
            VenueAccount venueAccount = VenueAccount.newBuilder()
                .setId(accountId)
                .setVenueAccountName(accountName)
                .setVenueName(venueName)
                .build();

            venueAccountsMap.put(accountId, venueAccount);

            SharedTestState.addVenueAccount(accountId, venueAccount);
        });
    }
}