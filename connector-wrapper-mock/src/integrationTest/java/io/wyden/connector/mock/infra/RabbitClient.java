package io.wyden.connector.mock.infra;

import com.google.protobuf.Message;
import com.rabbitmq.client.Return;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.GetInstrumentsRequest;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.StreetSideInstrumentListSnapshot;
import io.wyden.published.venue.VenueEnvironmentType;
import io.wyden.published.venue.VenueExecType;
import io.wyden.published.venue.VenueFill;
import io.wyden.published.venue.VenueInstrumentType;
import io.wyden.published.venue.VenueOrderType;
import io.wyden.published.venue.VenueReconciliationRequest;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueRequestType;
import io.wyden.published.venue.VenueResponse;
import io.wyden.published.venue.VenueResponseType;
import io.wyden.published.venue.VenueSide;
import io.wyden.published.venue.VenueTIF;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

public class RabbitClient {

    public static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(5);
    public static final VenueEnvironmentType DEFAULT_VENUE_ENVIRONMENT_TYPE = VenueEnvironmentType.NOT_LIVE;

    static final String TRADING_QUEUE_NAME = "connector-wrapper-queue.trading.%s.ALL";

    private final RabbitIntegrator rabbitIntegrator;

    private ExchangeObserver<Message> venueResponseExchangeObserver;
    private ExchangeObserver<StreetSideInstrumentListSnapshot> referenceDataInstrumentsResponseExchangeObserver;

    private final AtomicReference<Return> returnMessage = new AtomicReference<>();

    public RabbitClient(RabbitIntegrator rabbitIntegrator) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitIntegrator.getDeclarationAndPublishChannel().addReturnListener(this.returnMessage::set);
    }

    public static String tradingQueueName(String venueAccount) {
        return TRADING_QUEUE_NAME.formatted(venueAccount);
    }

    public void publish(VenueRequest venueOrder) {
        String venueAccount = venueOrder.getVenueAccount();
        RabbitExchange<Message> venueOrderExchange = OemsExchange.Trading.declareIngressExchange(rabbitIntegrator);
        Map<String, String> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), VenueRequest.class.getSimpleName(),
            OemsHeader.VENUE_ACCOUNT.getHeaderName(), venueAccount
        );
        venueOrderExchange.publishWithHeaders(venueOrder, headers);
    }

    public void publish(VenueReconciliationRequest request) {
        String venueAccount = request.getVenueAccount();
        RabbitExchange<VenueReconciliationRequest> exchange = OemsExchange.Venue.declareReconciliationRequestExchange(rabbitIntegrator);
        exchange.publish(request, venueAccount);
    }

    public void attachVenueAccountObserver(String venueAccount) {
        RabbitExchange<Message> tradingExchange = OemsExchange.Trading.declareRouterExchange(rabbitIntegrator);
        RabbitExchange<StreetSideInstrumentListSnapshot> referenceDataInstrumentsResponseExchange = OemsExchange.ReferenceData.declareReferenceDataInstrumentsResponse(rabbitIntegrator);

        Map<String, Object> erHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), VenueResponse.class.getSimpleName(),
            OemsHeader.VENUE_ACCOUNT.getHeaderName(), venueAccount
        );
        venueResponseExchangeObserver = ExchangeObserver.newBuilder(rabbitIntegrator, tradingExchange, TradingMessageParser.parser(), "venue-execution-report")
            .withHeaders(erHeaders)
            .build();
        venueResponseExchangeObserver.attach();

        referenceDataInstrumentsResponseExchangeObserver = ExchangeObserver.newBuilder(rabbitIntegrator, referenceDataInstrumentsResponseExchange, (d, t) -> StreetSideInstrumentListSnapshot.parseFrom(d), "get-instruments-response")
            .build();
        referenceDataInstrumentsResponseExchangeObserver.attach();
    }

    public void detachVenueAccountObserver() {
        venueResponseExchangeObserver.detach();
        venueResponseExchangeObserver = null;
        referenceDataInstrumentsResponseExchangeObserver.detach();
        referenceDataInstrumentsResponseExchangeObserver = null;
    }

    public VenueResponse awaitExecutionReport() {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(Objects::nonNull);
    }

    public VenueResponse awaitReconciliationResponse(String extId) {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(m -> {
           if (m instanceof VenueResponse response) {
               return response.getExtId().equals(extId);
           } else {
               return false;
           }
        });
    }

    public List<VenueResponse> getReconciliationResponses() {
        List<Message> messages = venueResponseExchangeObserver.getMessages();
        List<VenueResponse> result = new ArrayList<>();
        for (Message message : messages) {
            if (message instanceof VenueResponse response) {
                if (response.getResponseType() == VenueResponseType.VENUE_RECONCILIATION_RESULT) {
                    result.add(response);
                }
            }
        }
        return result;
    }

    public VenueResponse awaitExecutionReportPendingNew(Duration duration) {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(duration, m -> {
            if (m instanceof VenueResponse response) {
                return response.getExecType().equals(VenueExecType.VENUE_EXEC_TYPE_PENDING_NEW);
            } else {
                return false;
            }
        });
    }

    public VenueResponse awaitExecutionReportRejected(Duration duration) {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(duration, m -> {
            if (m instanceof VenueResponse response) {
                return response.getExecType().equals(VenueExecType.VENUE_EXEC_TYPE_REJECTED);
            } else {
                return false;
            }
        });
    }

    public VenueResponse awaitExecutionReportNew(Duration duration) {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(duration, m -> {
            if (m instanceof VenueResponse response) {
                return response.getExecType().equals(VenueExecType.VENUE_EXEC_TYPE_NEW);
            } else {
                return false;
            }
        });
    }

    public VenueResponse awaitCancelReject() {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(Objects::nonNull);
    }

    public VenueResponse awaitCancelReject(Duration duration) {
        return (VenueResponse) venueResponseExchangeObserver.awaitMessage(duration, Objects::nonNull);
    }

    public void ensureNoVenueResponse(Duration duration) {
        venueResponseExchangeObserver.ensureNoMoreMessages(duration);
    }

    public StreetSideInstrumentListSnapshot awaitInstrumentsResponse() {
        return referenceDataInstrumentsResponseExchangeObserver.awaitMessage(Objects::nonNull);
    }

    public static VenueRequest.Builder defaultVenueOrder(String venueAccount) {
        return VenueRequest.newBuilder()
            .setRequestType(VenueRequestType.VENUE_ORDER)
            .setInstrumentType(VenueInstrumentType.FOREX)
            .setOrderId(UUID.randomUUID().toString())
            .setVenueAccount(venueAccount)
            .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder().setAdapterTicker("BTCUSD").build())
            .setOrderType(VenueOrderType.MARKET)
            .setSide(VenueSide.BUY)
            .setTif(VenueTIF.GTC)
            .setQuantity("1");
    }

    public static VenueRequest.Builder defaultVenueCancel(VenueRequest venueOrder) {
        return VenueRequest.newBuilder()
            .setRequestType(VenueRequestType.VENUE_CANCEL)
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(venueOrder.getOrderId())
            .setIntId(venueOrder.getOrderId())
            .setVenueAccount(venueOrder.getVenueAccount())
            .setInstrumentType(venueOrder.getInstrumentType())
            .setInstrumentIdentifiers(venueOrder.getInstrumentIdentifiers())
            .setOrderType(venueOrder.getOrderType())
            .setSide(venueOrder.getSide())
            .setQuantity(venueOrder.getQuantity())
            .setTif(venueOrder.getTif())
            .setExpireTime(venueOrder.getExpireTime());
    }

    public static VenueResponse.Builder defaultExecutionReport(String venueAccount) {
        return VenueResponse.newBuilder()
            .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
            .setExecutionId(UUID.randomUUID().toString())
            .setVenueExecutionId(UUID.randomUUID().toString())
            .setOrderId(UUID.randomUUID().toString())
            .setExtId(UUID.randomUUID().toString())
            .setVenueAccount(venueAccount)
            .setExecType(VenueExecType.VENUE_EXEC_TYPE_FILL)
            .setEnvironmentType(DEFAULT_VENUE_ENVIRONMENT_TYPE);
    }

    public static VenueResponse.Builder defaultReconciliationResult(String venueAccount) {
        return VenueResponse.newBuilder()
            .setResponseType(VenueResponseType.VENUE_RECONCILIATION_RESULT)
            .setExtId(UUID.randomUUID().toString())
            .setIntId(UUID.randomUUID().toString())
            .setVenueAccount(venueAccount)
            .setExecType(VenueExecType.VENUE_EXEC_TYPE_FILL)
            .setLeavesQty("0")
            .setCumQty("10000")
            .setAvgPrice("1000")
            .setVenueTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setFee("99.9")
            .setFeeCurrency("USD");
    }

    public static VenueFill.Builder defaultVenueFill(String venueAccount) {
        return VenueFill.newBuilder()
            .setVenueExecutionId(UUID.randomUUID().toString())
            .setIntId(UUID.randomUUID().toString())
            .setExtId(UUID.randomUUID().toString())
            .setSide(VenueSide.BUY)
            .setLastQty("10000")
            .setLastPrice("1000")
            .setFee("99.9")
            .setFeeCurrency("USD")
            .setVenueTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));
    }

    public static VenueResponse.Builder defaultCancelReject(String venueAccount) {
        return VenueResponse.newBuilder()
            .setResponseType(VenueResponseType.VENUE_CANCEL_REJECT)
            .setOrderId(UUID.randomUUID().toString())
            .setVenueAccount(venueAccount);
    }

    public static GetInstrumentsRequest.Builder defaultGetInstrumentsRequest(String venueAccount) {
        return GetInstrumentsRequest.newBuilder()
            .setVenue(venueAccount);
    }

    public static VenueReconciliationRequest.Builder defaultVenueReconciliationRequest(String venueAccount) {
        VenueRequest venueOrder1 = RabbitClient.defaultVenueOrder(venueAccount)
            .setIntId(UUID.randomUUID().toString())
            .setExtId(UUID.randomUUID().toString())
            .build();
        VenueRequest venueOrder2 = RabbitClient.defaultVenueOrder(venueAccount)
            .setIntId(UUID.randomUUID().toString())
            .setExtId(UUID.randomUUID().toString())
            .build();
        return VenueReconciliationRequest.newBuilder()
            .setVenueAccount(venueAccount)
            .addAllVenueOrders(List.of(venueOrder1, venueOrder2));
    }

    public Return getReturnMessage() {
        return returnMessage.getAndSet(null);
    }
}
