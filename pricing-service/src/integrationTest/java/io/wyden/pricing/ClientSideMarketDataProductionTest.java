package io.wyden.pricing;

import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.pricing.service.MarketDataEventType;
import io.wyden.published.marketdata.BidAskQuote;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.published.marketdata.MarketDataRequest;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import org.assertj.core.groups.Tuple;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.annotation.DirtiesContext;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

import static io.wyden.pricing.TestData.BITMEX_ACCOUNT;
import static io.wyden.pricing.TestData.BTCUSD_BANK;
import static io.wyden.pricing.TestData.BTCUSD_BITMEX;
import static io.wyden.pricing.TestData.PRICING_ENGINE_ROUTING_KEY;
import static io.wyden.pricing.TestData.pricingRequestWithBitmexAsSource;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class ClientSideMarketDataProductionTest extends IntegrationTestBase {

    private static final String STREET_BID_PRICE = "10";
    private static final String STREET_BID_SIZE = "1";
    private static final String STREET_ASK_PRICE = "11";
    private static final String STREET_ASK_SIZE = "2";

    private static final String MARKUP_0_01 = "0.01";
    private static final String MARKUP_0_1 = "0.1";

    // given markup = 0.01:
    private static final String EXPECTED_CLIENT_BID_PRICE = "9.90";
    private static final String EXPECTED_CLIENT_BID_SIZE = "1";
    private static final String EXPECTED_CLIENT_ASK_PRICE = "11.11";
    private static final String EXPECTED_CLIENT_ASK_SIZE = "2";
    // given markup = 0.1:
    private static final String EXPECTED_CLIENT_BID_PRICE_2 = "9.0";
    private static final String EXPECTED_CLIENT_BID_SIZE_2 = "1";
    private static final String EXPECTED_CLIENT_ASK_PRICE_2 = "12.1";
    private static final String EXPECTED_CLIENT_ASK_SIZE_2 = "2";

    // given markup = 0.01:
    private static final Map<String, OrderBookLevel> EXPECTED_CLIENT_BID_BOOK = Map.of("9.900", OrderBookLevel.newBuilder()
        .setPrice("9.900")
        .setCount("1")
        .setAmount("1.0")
        .build());
    private static final Map<String, OrderBookLevel> EXPECTED_CLIENT_ASK_BOOK = Map.of("11.110", OrderBookLevel.newBuilder()
        .setPrice("11.110")
        .setCount("1")
        .setAmount("2.0")
        .build());

    // given markup = 0.1:
    private static final Map<String, OrderBookLevel> EXPECTED_CLIENT_BID_BOOK_2 = Map.of("9.00", OrderBookLevel.newBuilder()
        .setPrice("9.00")
        .setCount("1")
        .setAmount("1.0")
        .build());
    private static final Map<String, OrderBookLevel> EXPECTED_CLIENT_ASK_BOOK_2 = Map.of("12.10", OrderBookLevel.newBuilder()
        .setPrice("12.10")
        .setCount("1")
        .setAmount("2.0")
        .build());
    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
    private final List<Future<?>> toDisposeAfterTest = new ArrayList<>();
    private ExchangeObserver<MarketDataEvent> marketDataEventExchangeObserver;

    @BeforeEach
    void setUp() {
        marketDataEventExchangeObserver = ExchangeObserver.newBuilder(rabbitIntegrator, mdEventRabbitExchange, (m, p) -> MarketDataEvent.parseFrom(m), "it-mde")
            .withHeaders(Map.of(OemsHeader.INSTRUMENT_ID.getHeaderName(), BTCUSD_BANK))
            .build()
            .attach();
    }

    @AfterEach
    void tearDown() {
        toDisposeAfterTest.forEach(f -> f.cancel(true));
        toDisposeAfterTest.clear();
    }

    @Test
    void level1WithMarkupAugmentation() {
        // given
        MarketDataRequest request = pricingRequestWithBitmexAsSource(MARKUP_0_01, 1);
        Predicate<MarketDataEvent> instrumentRequestedByClient = mde -> mde.getIdentifier().getInstrumentId().equals(request.getInstrumentKey().getInstrumentId());

        requestRabbitExchange.publish(request, PRICING_ENGINE_ROUTING_KEY);

        // when
        streetIsProducingL1();

        // then
        await()
            .until(() -> marketDataEventExchangeObserver
            .getMessages()
            .stream()
            .anyMatch(instrumentRequestedByClient)
        );

        MarketDataEvent marketDataEventProduced = marketDataEventExchangeObserver.getMessages().stream().filter(instrumentRequestedByClient).findFirst().get();
        assertThat(marketDataEventProduced.getBidAskQuote()).isNotNull();
        assertThat(marketDataEventProduced.getBidAskQuote().getBidPrice()).isEqualTo(EXPECTED_CLIENT_BID_PRICE);
        assertThat(marketDataEventProduced.getBidAskQuote().getBidSize()).isEqualTo(EXPECTED_CLIENT_BID_SIZE);
        assertThat(marketDataEventProduced.getBidAskQuote().getAskPrice()).isEqualTo(EXPECTED_CLIENT_ASK_PRICE);
        assertThat(marketDataEventProduced.getBidAskQuote().getAskSize()).isEqualTo(EXPECTED_CLIENT_ASK_SIZE);
    }

    @Test
    void level1WithMarkupAugmentationDifferentConfigs() {
        // given
        MarketDataRequest request1 = pricingRequestWithBitmexAsSource(MARKUP_0_01, 1);
        MarketDataRequest request2 = pricingRequestWithBitmexAsSource(MARKUP_0_1, 1);

        requestRabbitExchange.publish(request1, PRICING_ENGINE_ROUTING_KEY);
        requestRabbitExchange.publish(request2, PRICING_ENGINE_ROUTING_KEY);

        // when
        streetIsProducingL1();

        // then
        await("Events of two distinct streams should arrive")
            .until(() -> marketDataEventExchangeObserver
                .getMessages()
                .stream()
                .map(e -> e.getIdentifier().getStreamId())
                .distinct()
                .count() == 2
            );

        List<MarketDataEvent> marketDataEventsProduced = marketDataEventExchangeObserver.getMessages();

        assertThat(marketDataEventsProduced).extracting(
                mde -> mde.getBidAskQuote().getBidPrice(),
                mde -> mde.getBidAskQuote().getBidSize(),
                mde -> mde.getBidAskQuote().getAskPrice(),
                mde -> mde.getBidAskQuote().getAskSize())
            .contains(Tuple.tuple(EXPECTED_CLIENT_BID_PRICE, EXPECTED_CLIENT_BID_SIZE, EXPECTED_CLIENT_ASK_PRICE, EXPECTED_CLIENT_ASK_SIZE));

        assertThat(marketDataEventsProduced).extracting(
                mde -> mde.getBidAskQuote().getBidPrice(),
                mde -> mde.getBidAskQuote().getBidSize(),
                mde -> mde.getBidAskQuote().getAskPrice(),
                mde -> mde.getBidAskQuote().getAskSize())
            .contains(Tuple.tuple(EXPECTED_CLIENT_BID_PRICE_2, EXPECTED_CLIENT_BID_SIZE_2, EXPECTED_CLIENT_ASK_PRICE_2, EXPECTED_CLIENT_ASK_SIZE_2));
    }

    @Test
    void level2WithMarkupAugmentation() {
        // given
        MarketDataRequest request = pricingRequestWithBitmexAsSource(MARKUP_0_01, 2);
        Predicate<MarketDataEvent> instrumentRequestedByClient = mde -> mde.getIdentifier().getInstrumentId().equals(request.getInstrumentKey().getInstrumentId());

        requestRabbitExchange.publish(request, PRICING_ENGINE_ROUTING_KEY);

        // when
        streetIsProducingL2();

        // then
        await()
            .until(() -> marketDataEventExchangeObserver
                .getMessages()
                .stream()
                .anyMatch(instrumentRequestedByClient)
            );

        MarketDataEvent marketDataEventProduced = marketDataEventExchangeObserver.getMessages().stream().filter(instrumentRequestedByClient).findFirst().get();
        assertThat(marketDataEventProduced.getOrderBook()).isNotNull();
        assertThat(marketDataEventProduced.getOrderBook().getBidsMap()).isEqualTo(EXPECTED_CLIENT_BID_BOOK);
        assertThat(marketDataEventProduced.getOrderBook().getAsksMap()).isEqualTo(EXPECTED_CLIENT_ASK_BOOK);
    }

    @Test
    void level2WithMarkupAugmentationDifferentConfigs() {
        // given
        MarketDataRequest request1 = pricingRequestWithBitmexAsSource(MARKUP_0_01, 2);
        MarketDataRequest request2 = pricingRequestWithBitmexAsSource(MARKUP_0_1, 2);
        Predicate<MarketDataEvent> instrumentRequestedByClient = mde -> mde.getIdentifier().getInstrumentId().equals(request1.getInstrumentKey().getInstrumentId());

        requestRabbitExchange.publish(request1, PRICING_ENGINE_ROUTING_KEY);
        requestRabbitExchange.publish(request2, PRICING_ENGINE_ROUTING_KEY);

        // when
        streetIsProducingL2();

        // then
        await()
            .until(() -> marketDataEventExchangeObserver
                .getMessages()
                .stream()
                .anyMatch(instrumentRequestedByClient)
            );

        List<MarketDataEvent> marketDataEventsProduced = marketDataEventExchangeObserver.getMessages();

        assertThat(marketDataEventsProduced).extracting(
                mde -> mde.getOrderBook().getBidsMap(),
                mde -> mde.getOrderBook().getAsksMap())
            .contains(Tuple.tuple(EXPECTED_CLIENT_BID_BOOK, EXPECTED_CLIENT_ASK_BOOK));

        assertThat(marketDataEventsProduced).extracting(
                mde -> mde.getOrderBook().getBidsMap(),
                mde -> mde.getOrderBook().getAsksMap())
            .contains(Tuple.tuple(EXPECTED_CLIENT_BID_BOOK_2, EXPECTED_CLIENT_ASK_BOOK_2));
    }

    @Test
    void emptyLevel1IsPassedAsIs() {
        // given
        MarketDataRequest request = pricingRequestWithBitmexAsSource(MARKUP_0_01, 1);
        Predicate<MarketDataEvent> instrumentRequestedByClient = mde -> mde.getIdentifier().getInstrumentId().equals(request.getInstrumentKey().getInstrumentId());

        requestRabbitExchange.publish(request, PRICING_ENGINE_ROUTING_KEY);

        // when
        String zero = BigDecimal.ZERO.toPlainString();
        streetIsProducingL1(MarketDataEvent.newBuilder()
            .setIdentifier(MarketDataIdentifier.newBuilder()
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setInstrumentId(BTCUSD_BITMEX)
                .setVenueAccount(BITMEX_ACCOUNT)
                .build())
            .setBidAskQuote(BidAskQuote.newBuilder()
                .setBidPrice(zero)
                .setBidSize(zero)
                .setAskPrice(zero)
                .setAskSize(zero)
                .build())
            .build()
        );

        // then
        await()
            .until(() -> marketDataEventExchangeObserver
                .getMessages()
                .stream()
                .anyMatch(instrumentRequestedByClient)
            );

        MarketDataEvent marketDataEventProduced = marketDataEventExchangeObserver.getMessages().stream().filter(instrumentRequestedByClient).findFirst().get();
        assertThat(marketDataEventProduced.getBidAskQuote()).isNotNull();
        assertThat(new BigDecimal(marketDataEventProduced.getBidAskQuote().getBidPrice())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(marketDataEventProduced.getBidAskQuote().getBidSize())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(marketDataEventProduced.getBidAskQuote().getAskPrice())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(marketDataEventProduced.getBidAskQuote().getAskSize())).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @Test
    void emptyLevel2IsPassedAsIs_asBothL1AndL2() {
        // given
        MarketDataRequest request = pricingRequestWithBitmexAsSource(MARKUP_0_01, 0);
        Predicate<MarketDataEvent> instrumentRequestedByClient = mde -> mde.getIdentifier().getInstrumentId().equals(request.getInstrumentKey().getInstrumentId());

        requestRabbitExchange.publish(request, PRICING_ENGINE_ROUTING_KEY);

        // when
        String zero = BigDecimal.ZERO.toPlainString();
        streetIsProducingL2(MarketDataEvent.newBuilder()
            .setIdentifier(MarketDataIdentifier.newBuilder()
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setInstrumentId(BTCUSD_BITMEX)
                .setVenueAccount(BITMEX_ACCOUNT)
                .build())
            .setOrderBook(OrderBook.newBuilder()
                .setTopAsk(OrderBookLevel.newBuilder()
                    .setPrice(zero)
                    .setAmount(zero)
                    .setCount(zero)
                    .build())
                .setTopBid(OrderBookLevel.newBuilder()
                    .setPrice(zero)
                    .setAmount(zero)
                    .setCount(zero)
                    .build())
                .build())
            .build()
        );

        // then await L2 received
        await()
            .until(() -> marketDataEventExchangeObserver
                .getMessages()
                .stream()
                .filter(MarketDataEvent::hasOrderBook)
                .anyMatch(instrumentRequestedByClient)
            );

        MarketDataEvent orderBookProduced = marketDataEventExchangeObserver.getMessages().stream()
            .filter(instrumentRequestedByClient)
            .filter(MarketDataEvent::hasOrderBook)
            .findFirst().get();

        assertThat(new BigDecimal(orderBookProduced.getOrderBook().getTopAsk().getPrice())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(orderBookProduced.getOrderBook().getTopAsk().getAmount())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(orderBookProduced.getOrderBook().getTopAsk().getCount())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(orderBookProduced.getOrderBook().getTopBid().getPrice())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(orderBookProduced.getOrderBook().getTopBid().getAmount())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(orderBookProduced.getOrderBook().getTopBid().getCount())).isEqualByComparingTo(BigDecimal.ZERO);

        // then await L1 received
        await()
            .until(() -> marketDataEventExchangeObserver
                .getMessages()
                .stream()
                .filter(MarketDataEvent::hasBidAskQuote)
                .anyMatch(instrumentRequestedByClient)
            );

        MarketDataEvent tickProduced = marketDataEventExchangeObserver.getMessages().stream()
            .filter(instrumentRequestedByClient)
            .filter(MarketDataEvent::hasBidAskQuote)
            .findFirst().get();

        assertThat(new BigDecimal(tickProduced.getBidAskQuote().getAskPrice())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(tickProduced.getBidAskQuote().getAskSize())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(tickProduced.getBidAskQuote().getBidPrice())).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(new BigDecimal(tickProduced.getBidAskQuote().getBidSize())).isEqualByComparingTo(BigDecimal.ZERO);
    }

    @NotNull
    private static MarketDataEvent referenceBidAsk() {
        return MarketDataEvent.newBuilder()
            .setIdentifier(MarketDataIdentifier.newBuilder()
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setVenueAccount(TestData.BITMEX_ACCOUNT)
                .setInstrumentId(TestData.BTCUSD_BITMEX)
                .build())
            .setBidAskQuote(BidAskQuote.newBuilder()
                .setBidPrice(STREET_BID_PRICE)
                .setBidSize(STREET_BID_SIZE)
                .setAskPrice(STREET_ASK_PRICE)
                .setAskSize(STREET_ASK_SIZE)
                .build())
            .build();
    }

    @NotNull
    private static MarketDataEvent referenceBook() {
        return MarketDataEvent.newBuilder()
            .setIdentifier(MarketDataIdentifier.newBuilder()
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .setVenueAccount(TestData.BITMEX_ACCOUNT)
                .setInstrumentId(TestData.BTCUSD_BITMEX)
                .build())
            .setOrderBook(OrderBook.newBuilder()
                .putAllBids(Map.of(STREET_BID_PRICE, OrderBookLevel.newBuilder().setPrice(STREET_BID_PRICE).setCount("1").setAmount(STREET_BID_SIZE).build()))
                .putAllAsks(Map.of(STREET_ASK_PRICE, OrderBookLevel.newBuilder().setPrice(STREET_ASK_PRICE).setCount("1").setAmount(STREET_ASK_SIZE).build()))
                .build())
            .build();
    }

    private void streetIsProducingL1() {
        streetIsProducingL1(referenceBidAsk());
    }

    private void streetIsProducingL1(MarketDataEvent marketDataEvent) {
        ScheduledFuture<?> scheduledFuture = scheduledExecutorService.scheduleAtFixedRate(() -> {
            String level = MarketDataEventType.L1.name();
            mdEventRabbitExchange.publishWithHeaders(
                marketDataEvent,
                Map.of(
                    OemsHeader.INSTRUMENT_ID.getHeaderName(), marketDataEvent.getIdentifier().getInstrumentId(),
                    OemsHeader.VENUE_ACCOUNT.getHeaderName(), marketDataEvent.getIdentifier().getVenueAccount(),
                    OemsHeader.MD_LEVEL.getHeaderName(), level
                ));
        }, 0, 100, TimeUnit.MILLISECONDS);

        toDisposeAfterTest.add(scheduledFuture);
    }

    private void streetIsProducingL2() {
        streetIsProducingL2(referenceBook());
    }
    
    private void streetIsProducingL2(MarketDataEvent marketDataEvent) {
        ScheduledFuture<?> scheduledFuture = scheduledExecutorService.scheduleAtFixedRate(() -> {
            String level = MarketDataEventType.L2.name();
            mdEventRabbitExchange.publishWithHeaders(
                marketDataEvent,
                Map.of(
                    OemsHeader.INSTRUMENT_ID.getHeaderName(), marketDataEvent.getIdentifier().getInstrumentId(),
                    OemsHeader.VENUE_ACCOUNT.getHeaderName(), marketDataEvent.getIdentifier().getVenueAccount(),
                    OemsHeader.MD_LEVEL.getHeaderName(), level
                ));
        }, 0, 100, TimeUnit.MILLISECONDS);

        toDisposeAfterTest.add(scheduledFuture);
    }
}
