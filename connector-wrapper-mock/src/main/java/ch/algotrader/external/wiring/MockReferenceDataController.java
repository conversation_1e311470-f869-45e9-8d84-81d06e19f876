package ch.algotrader.external.wiring;

import ch.algotrader.api.connector.referencedata.ReferenceDataController;
import ch.algotrader.api.connector.referencedata.TradingConstraints;
import ch.algotrader.api.domain.exchange.ExchangeDTO;
import ch.algotrader.api.domain.security.ForexDTO;
import ch.algotrader.api.domain.security.SecurityDTO;
import ch.algotrader.api.domain.security.SecurityIdentity;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.GroupedFlux;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class MockReferenceDataController implements ReferenceDataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MockReferenceDataController.class);

    private static final String WYDEN_MOCK_EXCHANGE = "WydenMock";

    private static final Map<ReferenceDataKey, SecurityDTO> SECURITIES = new HashMap<>();
    private final Map<String, ExchangeDTO> EXCHANGES = new HashMap<>();

    public MockReferenceDataController(MockContext context) {
        context.getProvider().getExchanges().forEach(e -> EXCHANGES.put(e.getName(), e));
    }

    @Override
    public Flux<? extends GroupedFlux<ExchangeDTO, ? extends SecurityDTO>> collectSecuritiesByExchange() {
        return Flux.fromStream(SECURITIES.keySet().stream())
            .groupBy(rdk -> EXCHANGES.get(rdk.exchange()), SECURITIES::get, Integer.MAX_VALUE);
    }

    private static ForexDTO createSecurity(String venue, String adapterTicker, String symbol, String baseCurrency, String quoteCurrency) {
        return createSecurity(venue, adapterTicker, symbol, baseCurrency, quoteCurrency, "0", "9999999", "1", "9999999", 8, 8, "0.01", "0.00000001");
    }

    private static ForexDTO createSecurity(String venue, String adapterTicker, String symbol, String baseCurrency, String quoteCurrency, String minQty, String maxQty,
                                           String minPrice, String maxPrice, int priceScale, int qtyScale, String priceIncr, String qtyIncr) {
        String description = adapterTicker + "@" + venue;

        return ForexDTO.builder()
            .setSettlementCurrency(baseCurrency)
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setDescription(description)
            .setInverseContract(true)
            .setSecurityIdentity(new SecurityIdentity(Map.of("symbol", symbol, "adapterTicker", adapterTicker)))
            .setTradingConstraints(TradingConstraints.newBuilder()
                .setMinQty(new BigDecimal(minQty))
                .setMaxQty(new BigDecimal(maxQty))
                .setMaxPrice(new BigDecimal(maxPrice))
                .setMinPrice(new BigDecimal(minPrice))
                .setPriceScale(priceScale)
                .setQtyScale(qtyScale)
                .setPriceIncr(new BigDecimal(priceIncr))
                .setQtyIncr(new BigDecimal(qtyIncr))
                .setIsTradeable(true)
                .build())
            .create();
    }

    static {
        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "DOGEUSD@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "DOGEUSD", "DOGEUSD@FOREX@WydenMock", "DOGE", "USD", "0", "9999999", "0.00000001", "9999999", 8, 8, "0.00000001", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "ETHUSD@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "ETHUSD", "ETHUSD@FOREX@WydenMock", "ETH", "USD", "0", "9999999", "1", "9999999", 8, 8, "0.01", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "BTCUSD@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "BTCUSD", "BTCUSD@FOREX@WydenMock", "BTC", "USD", "0", "9999999", "1", "9999999", 8, 8, "0.1", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "BTCUSDT@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "BTCUSDT", "BTCUSDT@FOREX@WydenMock", "BTC", "USDT", "0", "9999999", "1", "9999999", 8, 8, "0.1", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "ADAUSD@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "ADAUSD", "ADAUSD@FOREX@WydenMock", "ADA", "USD", "1", "9999999", "1", "9999999", 8, 8, "0.00000001", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "BASEQUOTE@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "BASEQUOTE", "BASEQUOTE@FOREX@WydenMock", "BASE", "QUOTE", "0", "9999999", "1", "9999999", 8, 8, "0.00000001", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "BASEUSD@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "BASEUSD", "BASEUSD@FOREX@WydenMock", "BASE", "USD", "0", "9999999", "1", "9999999", 8, 8, "0.00000001", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "BMEXUSDT@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "BMEXUSDT", "ETHUSD@FOREX@WydenMock", "BMEX", "USDT", "0", "9999999", "1", "9999999", 8, 8, "0.01", "0.00000001"));

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "DOGEQUOTE@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "DOGEQUOTE", "DOGEQUOTE@FOREX@WydenMock", "DOGE", "QUOTE", "1000", "150000", "0.00000001", "9999999", 1, 1, "0.1", "0.1"));

        generateClobSecurities(250,"USD");

        generateClobSecurities(250,"EUR");

        generateTriangulationClobSecurities(500);

        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "EURTRY@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "EURTRY", "EURTRY@FOREX@WydenMock", "EUR", "TRY"));
        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "USDTRY@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "USDTRY", "USDTRY@FOREX@WydenMock", "USD", "TRY"));
        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "EURPLN@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "EURPLN", "EURPLN@FOREX@WydenMock", "EUR", "PLN"));
        SECURITIES.put(
            new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, "USDPLN@FOREX@WydenMock"),
            createSecurity(WYDEN_MOCK_EXCHANGE, "USDPLN", "USDPLN@FOREX@WydenMock", "EUR", "PLN"));
    }

    private static void generateTriangulationClobSecurities(int amount) {
        for (int i = 0; i < amount; i++) {
            // primary security
            String primaryBaseCurrency = "CLOB" + i;
            String primaryQuoteCurrency = "FXPRIM" + i;
            String primaryAdapterTicker = primaryBaseCurrency + primaryQuoteCurrency;
            String primarySymbol = String.join("@", primaryAdapterTicker, "FOREX", WYDEN_MOCK_EXCHANGE);
            SECURITIES.put(
                new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, primarySymbol),
                createSecurity(WYDEN_MOCK_EXCHANGE, primaryAdapterTicker, primarySymbol, primaryBaseCurrency, primaryQuoteCurrency));

            // conversion security
            String convQuoteCurrency = "FXCONV" + i;
            String convAdapterTicker = primaryQuoteCurrency + convQuoteCurrency;
            String convSymbol = String.join("@", convAdapterTicker, "FOREX", WYDEN_MOCK_EXCHANGE);
            SECURITIES.put(
                new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, convSymbol),
                createSecurity(WYDEN_MOCK_EXCHANGE, convAdapterTicker, convSymbol, primaryQuoteCurrency, convQuoteCurrency, "0", "9999999", "1", "9999999", 2, 2, "0.01", "0.01"));
        }
    }

    private static void generateClobSecurities(int amount, String quoteCurrency) {
        for (int i = 0; i < amount; i++) {
            String baseCurrency = "CLOB" + i;
            String adapterTicker = baseCurrency + quoteCurrency;
            String symbol = String.join("@", adapterTicker, "FOREX", WYDEN_MOCK_EXCHANGE);
            SECURITIES.put(
                new ReferenceDataKey(WYDEN_MOCK_EXCHANGE, symbol),
                createSecurity(WYDEN_MOCK_EXCHANGE, adapterTicker, symbol, baseCurrency, quoteCurrency));
        }
    }

    public record ReferenceDataKey(String exchange, String symbol) {}
}
