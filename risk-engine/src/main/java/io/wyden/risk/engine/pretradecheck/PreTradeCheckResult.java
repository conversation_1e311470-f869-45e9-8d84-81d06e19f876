package io.wyden.risk.engine.pretradecheck;

import javax.annotation.Nullable;

public class PreTradeCheckResult {
    public enum Status {
        APPROVED,
        APPROVED_WITH_WARNING,
        REJECTED,
    }

    private final Status status;
    @Nullable
    private final String reason;

    private PreTradeCheckResult(Status status, @Nullable String reason) {
        this.status = status;
        this.reason = reason;
    }

    public static PreTradeCheckResult approved() {
        return new PreTradeCheckResult(Status.APPROVED, null);
    }

    public static PreTradeCheckResult approvedWithWarning(String warning) {
        return new PreTradeCheckResult(Status.APPROVED_WITH_WARNING, warning);
    }

    public static PreTradeCheckResult rejected(String reason) {
        return new PreTradeCheckResult(Status.REJECTED, reason);
    }

    public static PreTradeCheckResult failure(PreTradeCheckLevel level, String reason) {
        return new PreTradeCheckResult(level.getFailedStatus(), reason);
    }

    public boolean isApproved() {
        return status == Status.APPROVED;
    }

    public boolean isApprovedWithWarning() {
        return status == Status.APPROVED_WITH_WARNING;
    }

    public boolean isRejected() {
        return status == Status.REJECTED;
    }

    public Status getStatus() {
        return status;
    }

    @Nullable
    public String getReason() {
        return reason;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(status.toString());
        if (reason != null) {
            sb.append("(").append(reason).append(")");
        }
        return sb.toString();
    }
}
