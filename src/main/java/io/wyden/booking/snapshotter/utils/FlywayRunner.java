package io.wyden.booking.snapshotter.utils;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Properties;

@Component
public class FlywayRunner {

    public static void main(String[] args) throws IOException {
        URL resource = ClassLoader.getSystemClassLoader().getResource("./application.properties");
        String appConfigPath = resource.getPath();

//        String rootPath = Thread.currentThread().getContextClassLoader().getResource("").getPath();
//        String appConfigPath = rootPath + "application.properties";

        Properties appProps = new Properties();
        appProps.load(new FileInputStream(appConfigPath));

        FluentConfiguration configuration = Flyway.configure().configuration(toFlywayProperties(appProps));
        Flyway flyway = configuration.load();

        flyway.clean();
        flyway.migrate();
    }

    public static Properties toFlywayProperties(Properties springProperties) {
        Properties properties = new Properties();

        properties.setProperty("flyway.url", springProperties.getProperty("spring.datasource.url"));
        properties.setProperty("flyway.user", springProperties.getProperty("spring.datasource.username"));
        properties.setProperty("flyway.password", springProperties.getProperty("spring.datasource.password"));
        properties.setProperty("flyway.locations", springProperties.getProperty("spring.flyway.locations"));
        properties.setProperty("flyway.cleanDisabled", "false");

        return properties;
    }
}
