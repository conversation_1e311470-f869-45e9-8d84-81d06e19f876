import { graphql } from 'msw';
import { worker } from '../../../../../mocks/browser';
import { Scope } from '../../../services/graphql/generated/graphql';
import { AccountPermissionsPO } from './AccountPermissions.PO';

describe('AccountPermissions', () => {
  before(() => {
    worker.use(
      graphql.query('VenueAccounts', (req, res, ctx) => {
        return res(
          ctx.data({
            venueAccounts: [
              {
                venue: 'BitMEX',
                venueAccounts: [
                  {
                    venueAccountName: 'BitMEX testnet1',
                    venueAccountId: 'BitMEX-testnet1',
                    scopes: [Scope.Manage, Scope.Trade, Scope.Read],
                    dynamicScopes: [Scope.Manage, Scope.Trade, Scope.Read],
                  },
                  {
                    venueAccountName: 'BitMEX testnet2',
                    venueAccountId: 'BitMEX-testnet2',
                    scopes: [Scope.Trade, Scope.Read],
                    dynamicScopes: [Scope.Trade, Scope.Read],
                  },
                ],
                __typename: 'VenueAccountsPerVenue',
              },
              {
                venue: 'Kraken',
                venueAccounts: [
                  {
                    venueAccountName: 'Kraken 1',
                    venueAccountId: 'Kraken-1',
                    scopes: [Scope.Read],
                    dynamicScopes: [Scope.Read],
                  },
                  {
                    venueAccountName: 'Kraken 2',
                    venueAccountId: 'Kraken-2',
                    scopes: [],
                    dynamicScopes: [],
                  },
                ],
                __typename: 'VenueAccountsPerVenue',
              },
            ],
          }),
        );
      }),
    );
  });
  it('shows correctly venue accounts with scopes', () => {
    const PO = new AccountPermissionsPO();

    PO.render();
    PO.gridHeaders().should('have.length', '5');

    PO.row(0).account().should('contain.text', 'BitMEX testnet1');
    PO.row(0).id().should('contain.text', 'BitMEX-testnet1');
    PO.row(0).venue().should('contain.text', 'BitMEX');
    PO.row(0).scopes().should('contain.text', 'Manage, Trade, Read');

    PO.row(1).account().should('contain.text', 'BitMEX testnet2');
    PO.row(1).id().should('contain.text', 'BitMEX-testnet2');
    PO.row(1).venue().should('contain.text', 'BitMEX');
    PO.row(1).scopes().should('contain.text', 'Trade, Read');

    PO.row(2).account().should('contain.text', 'Kraken 1');
    PO.row(2).id().should('contain.text', 'Kraken-1');
    PO.row(2).venue().should('contain.text', 'Kraken');
    PO.row(2).scopes().should('contain.text', 'Read');

    PO.row(3).account().should('contain.text', 'Kraken 2');
    PO.row(3).id().should('contain.text', 'Kraken-2');
    PO.row(3).venue().should('contain.text', 'Kraken');
    PO.row(3).scopes().should('contain.text', '');
  });

  it('Can manage managable venue account', () => {
    const PO = new AccountPermissionsPO();

    worker.use(
      graphql.query('PermissionsForResource', (req, res, ctx) => {
        return res(
          ctx.data({
            usersPermissionsForResource: [
              { username: 'trader', scopes: [Scope.Trade, Scope.Read] },
            ],
            groupsPermissionsForResource: [{ groupName: 'administrator', scopes: [Scope.Manage] }],
          }),
        );
      }),
      graphql.query('GroupNames', (req, res, ctx) => {
        return res(
          ctx.data({
            groupNames: ['trader', 'administrator'],
          }),
        );
      }),
      graphql.query('UserNames', (req, res, ctx) => {
        return res(
          ctx.data({
            userNames: ['another user', 'yet another user'],
          }),
        );
      }),
      graphql.mutation('AddUserPermissions', (req, res, ctx) => {
        return res(
          ctx.data({
            addUserPermissions: {},
          }),
        );
      }),
      graphql.mutation('AddGroupPermissions', (req, res, ctx) => {
        return res(
          ctx.data({
            addGroupPermissions: {},
          }),
        );
      }),
    );

    PO.render();
    PO.gridHeaders().should('have.length', '5');
    PO.row(0).scopes().should('contain.text', 'Manage, Trade, Read');
    PO.row(0).clickManageOrPreview();

    // Sharing with user
    PO.expectTextToBeVisible('BitMEX-testnet1: permission details');
    PO.permissionsManagementPO.clickShareWithUsers();
    PO.permissionsManagementPO.selectUser('another user');
    PO.permissionsManagementPO.checkRole('Manage');
    PO.permissionsManagementPO.clickSave();
    PO.permissionsManagementPO.expectSharingFormClosed();

    // Sharing with group
    PO.permissionsManagementPO.clickShareWithGroups();
    PO.permissionsManagementPO.selectGroup('trader');
    PO.permissionsManagementPO.checkRole('Trade');
    PO.permissionsManagementPO.checkRole('Read');
    PO.permissionsManagementPO.clickSave();
    PO.permissionsManagementPO.expectSharingFormClosed();
  });
});
