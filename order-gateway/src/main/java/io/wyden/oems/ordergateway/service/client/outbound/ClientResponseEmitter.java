package io.wyden.oems.ordergateway.service.client.outbound;

import com.google.protobuf.Message;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.oems.ordergateway.infrastructure.rabbit.RabbitDestinations;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.state.ClientOrderContext;
import io.wyden.oems.ordergateway.service.tracking.AlreadyPresentOrderIdException;
import io.wyden.oems.ordergateway.service.tracking.InstrumentNotFoundException;
import io.wyden.oems.ordergateway.service.tracking.RequestValidationException;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.client.Result;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecRestatementReason;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsResponse;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.oems.ordergateway.infrastructure.telemetry.Meters.tradingResponseOutgoingLatencyTimer;
import static io.wyden.oems.ordergateway.service.tracking.ClientOemsMapper.asClientSide;
import static io.wyden.published.client.ClientCancelRejectResponseTo.ORDER_CANCEL_REQUEST;

@Component
public class ClientResponseEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientResponseEmitter.class);

    private final ExecutionReportFactory executionReportFactory;
    private final RabbitExchange<Message> tradingIngressExchange;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public ClientResponseEmitter(RabbitDestinations rabbitDestinations,
                                 ExecutionReportFactory executionReportFactory,
                                 Telemetry telemetry) {
        this.executionReportFactory = executionReportFactory;
        this.tradingIngressExchange = rabbitDestinations.getTradingIngressExchange();
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    private void emit(ClientResponse clientResponse) {
        Map<String, String> baggage = Map.of(
            TracingConv.RESPONSE_ID, clientResponse.getMetadata().getResponseId(),
            TracingConv.REQUEST_ID, clientResponse.getMetadata().getRequestId(),
            TracingConv.ORDER_ID, clientResponse.getOrderId(),
            TracingConv.ORIG_ORDER_ID, clientResponse.getOrigOrderId(),
            TracingConv.CL_ORDER_ID, clientResponse.getClOrderId(),
            TracingConv.ORIG_CL_ORDER_ID, clientResponse.getOrigClOrderId(),
            TracingConv.CLIENT_ID, clientResponse.getClientId(),
            TracingConv.EXECUTION_ID, clientResponse.getExecutionId(),
            TracingConv.VENUE_ACCOUNT, clientResponse.getVenueAccount(),
            TracingConv.INSTRUMENT_ID, clientResponse.getInstrumentId()
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("clientresponse.emit", SpanKind.PRODUCER)) {
                recordLatencyIn(latencyTimer(clientResponse)).of(() -> emitInner(clientResponse));
            }
        }
    }

    private void emitInner(ClientResponse clientResponse) {
        Map<String, String> routingHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientResponse.class.getSimpleName(),
            OemsHeader.OEMS_RESPONSE_TYPE.getHeaderName(), clientResponse.getResponseType().name(),
            OemsHeader.CLIENT_ID.getHeaderName(), clientResponse.getClientId(),
            OemsHeader.VENUE_ACCOUNT.getHeaderName(), clientResponse.getVenueAccount(),
            OemsHeader.INSTRUMENT_ID.getHeaderName(), clientResponse.getInstrumentId()
        );

        Map<String, String> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(routingHeaders), RabbitHeadersPropagator.setter())
            .getHeaders();

        LOGGER.info("Emitting {} to {} with headers: {}\n{}",
            clientResponse.getClass().getSimpleName(), tradingIngressExchange.getName(), headers, clientResponse);

        updateMetrics(clientResponse);

        tradingIngressExchange.publishWithHeaders(clientResponse, headers);
    }

    public void emitExecutionReportAfterStateChange(ClientOrderContext ctx, @Nullable OemsResponse execution, @Nullable ClientRequest clientRequest) {
        ClientResponse.Builder clientResponse = executionReportFactory.reportStatusHasChanged(ctx, execution);

        ClientOrderState persistentState = ctx.getPersistentState();

        if (!persistentState.getPreReplaceOrderId().isBlank() && execution != null && execution.getExecType() == OemsExecType.NEW) {
            clientResponse.setExecType(ClientExecType.CLIENT_EXEC_TYPE_REPLACED).build();
            clientResponse.setOrigOrderId(persistentState.getPreReplaceOrderId());
        }

        if (clientRequest != null) {
            // state changed based on Client request to cancel or cancel-replace
            clientResponse.setClOrderId(clientRequest.getClOrderId());
            clientResponse.setOrigClOrderId(clientRequest.getOrigClOrderId());
            clientResponse.setInstrumentId(StringUtils.isEmpty(clientRequest.getInstrumentId()) ? persistentState.getInstrumentId() : clientRequest.getInstrumentId() );

            if (clientRequest.getRequestType() == ClientRequestType.CANCEL_REPLACE
                && persistentState.getCurrentStatus() == ClientOrderStatus.FILLED) {

                clientResponse.setExecType(ClientExecType.CLIENT_EXEC_TYPE_REPLACED);
                clientResponse.setOrigOrderId(persistentState.getClientRequest().getOrigOrderId());

            } else if (clientResponse.getExecType() == ClientExecType.CLIENT_EXEC_TYPE_CANCELED) {

                clientResponse.setClOrderId(persistentState.getPendingCancelClOrderId());
                clientResponse.setOrigClOrderId((persistentState.getOrder().getClOrderId()));
                clientResponse.setExecRestatementReason(asClientSide(execution != null ? execution.getExecRestatementReason() : OemsExecRestatementReason.OTHER));
            }
        }

        emit(clientResponse.build());
    }

    public void emitExecutionReportCalculated(ClientOrderContext ctx, OemsResponse oemsResponse) {
        ClientResponse clientResponse = executionReportFactory.createOrderStatusExecutionReportBuilder(ctx.getPersistentState())
            .setExecType(ClientExecType.CLIENT_EXEC_TYPE_CALCULATED)
            .setExecutionId(oemsResponse.getExecutionId())
            .addAllFeeData(asClientSide(oemsResponse.getFeeDataList()))
            .setVenueExecutionId(oemsResponse.getVenueExecutionId())
            .setCurrency(oemsResponse.getCurrency())
            .setMatchId(oemsResponse.getMatchId())
            .build();

        emit(clientResponse);
    }

    public void emitExecutionReportRestated(ClientOrderContext ctx, OemsResponse oemsResponse) {
        ClientResponse clientResponse = executionReportFactory.createOrderStatusExecutionReportBuilder(ctx.getPersistentState())
            .setExecType(ClientExecType.CLIENT_EXEC_TYPE_RESTATED)
            .setExecRestatementReason(asClientSide(oemsResponse.getExecRestatementReason()))
            .setCalculatedReducedQty(oemsResponse.getCalculatedReducedQty())
            .setReason(oemsResponse.getReason())
            .build();

        emit(clientResponse);
    }

    public void requestNotMatchedWithActiveOrder(ClientRequest clientRequest) {
        ClientResponse clientResponse = switch (clientRequest.getRequestType()) {
            case CANCEL, CANCEL_REPLACE -> CancelRejectFactory.rejectNoLiveOrderWithGivenIds(clientRequest);
            default -> throw new IllegalArgumentException("Client request type not supported: " + clientRequest);
        };

        emit(clientResponse);
    }

    public void emitOrderCreationRejection(ClientRequest clientNewOrderRequest, @Nullable Throwable cause) {
        String reason;
        if (cause instanceof AlreadyPresentOrderIdException || cause instanceof InstrumentNotFoundException || cause instanceof RequestValidationException) {
            reason = cause.getMessage();
        } else {
            reason = "Service Unavailable";
        }

        ClientResponse reject;
        if (cause instanceof AlreadyPresentOrderIdException && duplicatedEvent(clientNewOrderRequest, ((AlreadyPresentOrderIdException) cause).getOriginalOrder())) {
            // duplicate delivery - ignore
            return;
        } else if (cause instanceof AlreadyPresentOrderIdException && sameClient(clientNewOrderRequest, ((AlreadyPresentOrderIdException) cause).getOriginalOrder())) {
            // client reuses orderId of already present Order - reject request and describe current order state
            reject = executionReportFactory.createOrderRejectBecauseOrderAlreadyPresent(clientNewOrderRequest, ((AlreadyPresentOrderIdException) cause).getOriginalOrder(), reason);
        } else {
            // requested Order creation failed - reject request
            reject = executionReportFactory.createOrderRejectedReportAsRegistrationError(clientNewOrderRequest, reason);
        }

        emit(reject);
    }

    private boolean duplicatedEvent(ClientRequest clientNewOrderRequest, ClientOrderState originalOrder) {
        return clientNewOrderRequest.getMetadata().getRequestId().equals(originalOrder.getOrder().getMetadata().getRequestId());
    }

    private boolean sameClient(ClientRequest clientNewOrderRequest, ClientOrderState originalOrder) {
        return clientNewOrderRequest.getClientId().equals(originalOrder.getOrder().getClientId());
    }

    public void respondNonCancellable(String pendingCancelClOrderId, String replacingOrderId, String requesterId, ClientOrderState orderState, OemsResponse underlyingOemsResponse) {
        ClientRequest clientRequest = orderState.getOrder();
        String clientId = clientRequest.getClientId();
        String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        Metadata metadata = Metadata.newBuilder()
            .setResponseId(UUID.randomUUID().toString())
            .setInResponseToRequestId(pendingCancelClOrderId)
            .setInResponseToRequesterId(requesterId)
            .setCreatedAt(now)
            .build();

        ClientResponse.Builder cancelReject1 = ClientResponse.newBuilder()
            .setMetadata(metadata)
            .setRootExecution(ClientResponse.ExecutionReference.newBuilder()
                .setExecutionId(underlyingOemsResponse.getRootExecution().getExecutionId())
                .setOrderId(underlyingOemsResponse.getRootExecution().getOrderId())
                .setVenueAccount(underlyingOemsResponse.getRootExecution().getVenueAccount())
                .build())
            .setResponseType(ClientResponseType.CANCEL_REJECT)
            .setOrigOrderId(clientRequest.getOrderId())
            .setClOrderId(pendingCancelClOrderId)
            .setClientId(clientId)
            .setOrigClOrderId(clientRequest.getClOrderId())
            .setCancelRejectResponseTo(ORDER_CANCEL_REQUEST)
            .setRequestResult(Result.OEMS_CANCEL_ERROR_WRONG_STATE)
            .setTimestamp(now)
            .setOrderStatus(orderState.getCurrentStatus())
            .setOrderCategory(asClientSide(underlyingOemsResponse.getOrderCategory()))
            .setTif(clientRequest.getTif())
            .setCurrency(clientRequest.getCurrency())
            .setExpireTime(clientRequest.getExpireTime())
            .setRequest(clientRequest);

        if (StringUtils.isNotEmpty(replacingOrderId)) {
            cancelReject1.setOrderId(replacingOrderId);
        }

        ClientResponse cancelReject = cancelReject1.build();

        emit(cancelReject);
    }

    public void respondCancelRejectedConnectorNotAvailable(ClientRequest clientRequest, ClientOrderContext ctx) {
        ClientResponse clientResponse = CancelRejectFactory.connectorNotAvailableCancelFailed(clientRequest, ctx.getPersistentState());
        emit(clientResponse);
    }

    public void respondCancelReject(ClientRequest clientRequest, ClientOrderState orderState, String reason) {
        ClientResponse cancelReject = CancelRejectFactory.cancelReject(clientRequest, orderState, reason);
        emit(cancelReject);
    }

    public void respondCancelReject(ClientRequest clientRequest, String reason) {
        ClientResponse cancelReject = CancelRejectFactory.cancelReject(clientRequest, reason);
        emit(cancelReject);
    }

    private void updateMetrics(ClientResponse clientResponse) {
        try {
            String instrumentId = clientResponse.getInstrumentId();
            String messageType = clientResponse.getResponseType().name();
            String execType = clientResponse.getExecType().name();
            this.meterRegistry.counter("wyden.trading.response.outgoing.count",
                "instrumentId", instrumentId,
                "messageType", messageType,
                "orderType", "",
                "execType", execType).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private Timer latencyTimer(ClientResponse response) {
        try {
            return tradingResponseOutgoingLatencyTimer(meterRegistry, response.getResponseType());
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }

    public void produceAccessDeniedReject(ClientRequest clientRequest) {
        ClientResponse reject = executionReportFactory.createAccessDeniedReport(clientRequest).build();
        emit(reject);
    }

    public void produceNoConnectorCancelReject(String pendingCancelClOrderId, String pendingCancelReplaceClOrderId,
                                               String requesterId, ClientOrderState orderState) {
        ClientResponse cancelReject = CancelRejectFactory.connectorNotAvailableCancelFailed(
                pendingCancelClOrderId,
                pendingCancelReplaceClOrderId,
                requesterId,
                orderState);

        emit(cancelReject);
    }

    public void produceOrderStatusReportOnDemand(ClientRequest request, ClientOrderState orderState) {
        ClientResponse orderStatusExecutionReport = executionReportFactory.createOrderStatusExecutionReport(request, orderState);
        emit(orderStatusExecutionReport);
    }

    public void produceOrderStatusReportReject(ClientRequest request, String orderId, String clientOrderId, String reason) {
        ClientResponse reject = executionReportFactory.createRejectForOrderStatusRequest(
            request,
            orderId,
            clientOrderId,
            reason
        );
        emit(reject);
    }
}
