package io.wyden.booking.snapshotter.domain.state.cache;

import io.wyden.booking.snapshotter.domain.ledgerentry.SimpleReference;
import io.wyden.booking.snapshotter.domain.position.Position;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class PositionCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionCache.class);

    private final Map<SimpleReference, CachedEntry<Position>> cachedPositions;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final Duration ttl;

    public PositionCache(Telemetry telemetry,
                         @Value("${cache.position.eviction.ttl.duration}") Duration duration) {
        this.ttl = duration == null ? Duration.ZERO : duration;
        LOGGER.info("Effective TTL: {}", ttl);

        this.cachedPositions = new ConcurrentHashMap<>();
        telemetry.getMeterRegistry().gaugeMapSize(
            "wyden.booking-snapshotter.cache.position",
            List.of(),
            cachedPositions);

        if (ttl.compareTo(Duration.ZERO) > 0) {
            startEvictionTask();
        }
    }

    private void startEvictionTask() {
        scheduler.scheduleAtFixedRate(this::evictExpiredEntries, ttl.toSeconds(), ttl.toSeconds(), TimeUnit.SECONDS);
    }

    private void evictExpiredEntries() {
        long now = System.currentTimeMillis();
        cachedPositions.entrySet().removeIf(entry -> entry.getValue().isExpired(now, ttl));
    }

    public Optional<Position> findPositionBySymbolAndPortfolioId(String symbol, String portfolioId) {
        return cachedPositions.entrySet().stream()
            .filter(e ->
                e.getKey().symbol().equals(symbol)
                    && e.getKey().matchesPortfolioId(portfolioId))
            .findFirst()
            .map(e -> e.getValue().value()
                // Always return a copy - else state changes will be propagated directly to the cached values.
                // We want state propagation to happen only explicitly in store() method
                // Why - some command processors will apply their changes only conditionally, if the result state fulfills some conditions (e.g., buying power check)
                .snapshot()
            );
    }

    public Optional<Position> findPositionBySymbolAndAccountId(String symbol, String accountId) {
        return cachedPositions.entrySet().stream()
            .filter(e -> e.getKey().symbol().equals(symbol) && e.getKey().matchesAccountId(accountId))
            .findFirst()
            .map(e -> e.getValue().value()
                // Always return a copy - else state changes will be propagated directly to the cached values.
                // We want state propagation to happen only explicitly in store() method
                // Why - some command processors will want to apply changes conditionally, if the result state fulfills some conditions (e.g. buying power check)
                .snapshot()
            );
    }

    public void store(Position position) {
        SimpleReference simpleReference = new SimpleReference(position.getInstrument(), position.getPortfolioId(), position.getAccountId());
        cachedPositions.put(simpleReference, CachedEntry.of(position));
    }

    public void evictAll() {
        cachedPositions.clear();
    }
}
