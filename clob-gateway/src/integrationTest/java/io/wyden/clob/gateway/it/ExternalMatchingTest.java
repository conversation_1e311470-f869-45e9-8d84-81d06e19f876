package io.wyden.clob.gateway.it;

import io.wyden.clob.gateway.it.utils.ClientContext;
import io.wyden.clob.gateway.it.utils.MatchingEngine;
import io.wyden.published.oems.OemsExecRestatementReason;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.oems.OemsTIF;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.util.List;

import static io.wyden.clob.gateway.it.TestingData.Instruments.BTCUSD_BIT2ME;
import static io.wyden.clob.gateway.it.TestingData.Instruments.BTCUSD_BITSTAMP;
import static io.wyden.clob.gateway.it.TestingData.Instruments.BTCUSD_CLOB;
import static io.wyden.clob.gateway.it.TestingData.Instruments.BTCUSD_KRAKEN;
import static io.wyden.clob.gateway.it.TestingData.Instruments.DOGEUSD_BITSTAMP;
import static io.wyden.clob.gateway.it.TestingData.Instruments.DOGEUSD_CLOB;
import static io.wyden.clob.gateway.it.TestingData.Instruments.XRPUSD_BITSTAMP;
import static io.wyden.clob.gateway.it.TestingData.Instruments.XRPUSD_CLOB;
import static io.wyden.clob.gateway.it.TestingData.Portfolios.MAKER1;
import static io.wyden.clob.gateway.it.TestingData.Portfolios.QUOTING_BIT2ME;
import static io.wyden.clob.gateway.it.TestingData.Portfolios.QUOTING_BITSTAMP;
import static io.wyden.clob.gateway.it.TestingData.Portfolios.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS;
import static io.wyden.clob.gateway.it.TestingData.Portfolios.TAKER1;
import static io.wyden.clob.gateway.it.TestingData.VenueAccounts.BIT2ME;
import static io.wyden.clob.gateway.it.TestingData.VenueAccounts.BITSTAMP;
import static io.wyden.clob.gateway.it.TestingData.VenueAccounts.CLOB;
import static io.wyden.clob.gateway.it.utils.HedgingRequestExpectations.expectHedgingRequest;
import static io.wyden.clob.gateway.it.utils.OemsResponseAssertions.assertHedgingAccount;
import static io.wyden.clob.gateway.it.utils.OemsResponseExpectations.restated;
import static io.wyden.clob.gateway.it.utils.OemsResponseExpectations.statusCanceled;
import static io.wyden.clob.gateway.it.utils.OemsResponseExpectations.statusFilled;
import static io.wyden.clob.gateway.it.utils.OemsResponseExpectations.statusNew;
import static io.wyden.clob.gateway.it.utils.OemsResponseExpectations.statusPartiallyFilled;
import static io.wyden.clob.gateway.it.utils.OemsResponseExpectations.statusRejected;
import static io.wyden.clob.gateway.it.utils.QuotingOrderRequestExpectations.expectQuotingRequest;
import static io.wyden.published.oems.OemsLiquidityIndicator.LIQUIDITY_INDICATOR_UNSPECIFIED;
import static io.wyden.published.oems.OemsLiquidityIndicator.MAKER;
import static io.wyden.published.oems.OemsLiquidityIndicator.TAKER;
import static org.assertj.core.api.Assertions.assertThat;

public class ExternalMatchingTest extends ClobGatewayIntegrationTestBase {

    @Nested
    class HedgingFallbackSuccess {
        @Test
        void clientTaker_singleFill() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportNew(hedgingFallbackRequest);
            orderCollider.emitExecutionReportFilled(hedgingFallbackRequest, "1", "50050");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BIT2ME, "50000", "50000", "50000")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BIT2ME, "50000", "50000", null)
            );
        }

        @Test
        void clientTaker_singleFill_qtyAdjustedToZero() {
            // given
            // Bitstamp Qty precision: 0.01
            // CLOB Qty precision: 0.0001
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(), List.of(new MatchingEngine.BulkOrderLeg("0.1111", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(DOGEUSD_CLOB, OemsSide.BUY, 0.0001, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest),
                statusCanceled(takerRequest, "0.0001", "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            );
        }
    }

    @Nested
    class HedgingSuccess {
        @Test
        void clientTaker_singleFill() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP,
                "55000"); // limit price adjusted with hedging safety margin 0.1

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "1", "50050");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "50000", "50000", "50000")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "50000", "50000", null)
            );
        }

        @Test
        void clientTaker_singleFill_hedging_margin_override() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(DOGEUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP,
                "60000"); // limit price adjusted with instrument-level hedging safety margin 0.2

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "1", "50050");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "50000", "50000", "50000")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "50000", "50000", null)
            );
        }

        @Test
        void clientTaker_singleFill_hedgingOrderFilledInMultipleFills() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportPartiallyFilled(hedgingRequest, "0.3", "0.3", "50100", "0.7");
            orderCollider.emitExecutionReportPartiallyFilled(hedgingRequest, "0.6", "0.3", "50100", "0.4");
            orderCollider.emitExecutionReportFilled(hedgingRequest, "0.4", "44975", "50050");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "50000", "50000", "50000")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "50000", "50000", null)
            );
        }

        @Test
        void clientTaker_marketIocPartiallyCancelled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrder(BTCUSD_CLOB, OemsSide.BUY, 2, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "1", "50050");

            agencyTradingService.expectResponse(
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "1", "50000", "50000", "1", "50000")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "50000", "50000", null)
            );

            agencyTradingService.expectResponse(
                statusCanceled(takerRequest, "1", "50000", "IOC", null)
            );
        }

        @Test
        void clientMaker_singleFill() {
            // given
            ClientContext maker = new ClientContext(MAKER1);

            // when
            OemsRequest makerRequest = agencyTradingService.emitsOemsRequest(maker.limitOrder(BTCUSD_CLOB, OemsSide.SELL, 3, OemsTIF.GTC, 60000));

            // then
            agencyTradingService.expectResponses(
                statusNew(makerRequest)
            );

            // when
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(new MatchingEngine.BulkOrderLeg("3", "70000")), List.of());

            // then
            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, makerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "60000", "3");
            OemsRequest hedgingRequest = orderCollider.awaitNewChildOrderRequest(quotingRequest.getOrderId(), BTCUSD_BITSTAMP);

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "3", "61000");

            agencyTradingService.expectResponse(
                statusFilled(makerRequest, MAKER, BITSTAMP, "60000", "60000", "180000")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "60000", "60000", null)
            );
        }

        @Test
        void mixedInternalWithExternal() {
            // given
            ClientContext maker = new ClientContext(MAKER1);
            ClientContext taker = new ClientContext(TAKER1);

            // when
            OemsRequest makerRequest = agencyTradingService.emitsOemsRequest(maker.limitOrder(BTCUSD_CLOB, OemsSide.SELL, 1, OemsTIF.GTC, 50000));
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000.1")));
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 2, OemsTIF.GTC, 50000.1));

            // then
            agencyTradingService.expectResponses(
                statusNew(makerRequest),
                statusNew(takerRequest)
            );

            agencyTradingService.expectResponses(
                statusFilled(makerRequest, MAKER, TestingData.VenueAccounts.CLOB, "50000", "50000", "50000"),
                statusPartiallyFilled(takerRequest, TAKER, TestingData.VenueAccounts.CLOB, "1", "50000", "50000", "1", "50000")
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000.1", "1");
            OemsRequest hedgingRequest = orderCollider.awaitNewChildOrderRequest(quotingRequest.getOrderId(), BTCUSD_BITSTAMP);

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "1", "50001");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "50000.1", "50000.05", "50000.1")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "50000.1", "50000.1", null)
            );
        }

        @Test
        void clientTaker_singleFill_qtyAdjusted() {
            // given
            // Bitstamp Qty precision: 0.01
            // CLOB Qty precision: 0.0001
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(), List.of(new MatchingEngine.BulkOrderLeg("0.1111", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(DOGEUSD_CLOB, OemsSide.BUY, 0.1111, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "50000", "0.1111");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP, 0.11, "55000");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "0.11", "50050");

            agencyTradingService.expectResponses(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "0.11", "0", "0.11", "0", "55.0000", "Unsolicited reduce, hedging venue qty constraints"),
                statusFilled(takerRequest, TAKER, BITSTAMP, "0.11", "50000", "50000", "5500")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "0.11","50000", "50000", null)
            );
        }
    }

    @Nested
    class HedgingFailures {
        @Test
        void clientTaker_fullQtyHedge_orderRejected_clientOrderCanceled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest);

            agencyTradingService.expectResponse(
                statusCanceled(takerRequest, "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.OTHER);
            });

            quotingOrdersObserver.expectResponses(
                statusRejected(quotingRequest, "Rejected by venue")
            );
        }

        @Test
        void clientTaker_marketIOC_partialQtyAvailable_orderRejected_clientOrderCanceled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrder(BTCUSD_CLOB, OemsSide.BUY, 2, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest);

            agencyTradingService.expectResponses(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "1", "0", "1", "0", null, "Unsolicited reduce, cannot hedge"),
                statusCanceled(takerRequest, "1", "0", "0", "IOC", null)
            );

            quotingOrdersObserver.expectResponses(
                statusRejected(quotingRequest, "Rejected by venue")
            );
        }

        @Test
        void clientTaker_twoMakers_oneRejected_oneFilled_clientOrderReduced_thenFilled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(
                new MatchingEngine.BulkOrderLeg("1", "3400"),
                new MatchingEngine.BulkOrderLeg("3", "3300")
            ), List.of());

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrder(DOGEUSD_CLOB, OemsSide.SELL, 4, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "3400", "1");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP, 1, "3060");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportRejected(hedgingRequest);
            }

            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "3300", "3");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP, 3, "2970");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportFilled(hedgingRequest, "3", "3300");
            }

            agencyTradingService.expectResponses(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "3", "0", "3", "0", "3400", "Unsolicited reduce, cannot hedge"),
                statusFilled(takerRequest, TAKER, BITSTAMP, "3", "3300", "3300", "9900")
            );
        }

        @Test
        void clientTaker_twoMakers_oneFilled_oneRejected_clientOrderPartiallyFilled_thenUnsolicitedCancel() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(
                new MatchingEngine.BulkOrderLeg("1", "3400"),
                new MatchingEngine.BulkOrderLeg("3", "3300")
            ), List.of());

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrder(DOGEUSD_CLOB, OemsSide.SELL, 4, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "3300", "3");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP, 3, "2970");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportFilled(hedgingRequest, "3", "3300");
            }

            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "3400", "1");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP, 1, "3060");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportRejected(hedgingRequest);
            }

            agencyTradingService.expectResponses(
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "3", "3300", "3300", "1", "9900"),
                statusCanceled(takerRequest, "3", "3300", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            );
        }

        @Test
        void clientTaker_fullQtyHedge_orderCanceled_clientOrderCanceled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportCancelled(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportCancelled(hedgingFallbackRequest);

            agencyTradingService.expectResponse(
                statusCanceled(takerRequest, "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.OTHER);
            });

            quotingOrdersObserver.expectResponses(
                statusCanceled(quotingRequest, "0", "0", "", null)
            );
        }

        @Test
        void clientTaker_partialQtyHedge_orderRejected_clientOrderReduced() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 2, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest);

            agencyTradingService.expectResponse(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "1", "0", "1", "0", "50000", "Unsolicited reduce, cannot hedge")
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);
            });

            quotingOrdersObserver.expectResponses(
                statusRejected(quotingRequest, "Rejected by venue")
            );
        }

        @Test
        void clientTaker_partialQtyHedge_orderCanceled_clientOrderReduced() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 2, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportCancelled(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportCancelled(hedgingFallbackRequest);

            agencyTradingService.expectResponse(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "1", "0", "1", "0", "50000", "Unsolicited reduce, cannot hedge")
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);
            });

            quotingOrdersObserver.expectResponses(
                statusCanceled(quotingRequest, "0", "0", "", null)
            );
        }

        @ParameterizedTest
        @EnumSource(value = OemsTIF.class, names = {"GTC", "GTD"})
        void clientTaker_resting_partialQtyHedge_partialFills_orderCanceled_clientOrderReduced_thenFilled(OemsTIF tif) {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(
                new MatchingEngine.BulkOrderLeg("1", "50000"), // filled
                new MatchingEngine.BulkOrderLeg("2", "51500"), // filled
                new MatchingEngine.BulkOrderLeg("3", "55000"), // canceled
                new MatchingEngine.BulkOrderLeg("4", "58000")) // filled
            );

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 10, tif, 60000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );


            // 1st hedging order filled, client order partially filled
            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, 1, "55000");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportFilled(hedgingRequest, "1", "50000");
            }

            agencyTradingService.expectResponse(
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "1", "50000", "50000", "9", "50000")
            );

            // 2nd hedging order filled, client order partially filled
            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "51500", "2");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, 2, "56650");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportFilled(hedgingRequest, "2", "51000");
            }

            agencyTradingService.expectResponse(
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "3", "51500", "51000", "7", "103000")
            );

            // 3rd hedging order canceled, client order restated
            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "55000", "3");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, 3, "60500");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportCancelled(hedgingRequest);
                OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "60500");
                orderCollider.emitExecutionReportCancelled(hedgingFallbackRequest);
            }


            agencyTradingService.expectResponse(
                restated(takerRequest, OemsOrderStatus.STATUS_PARTIALLY_FILLED, "7", "3", "4", "51000", "165000", "Unsolicited reduce, cannot hedge")
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);
            });

            // 4th hedging order filled, client order filled
            {
                OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "58000", "4");
                OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, 4, "63800");
                orderCollider.emitExecutionReportNew(hedgingRequest);
                orderCollider.emitExecutionReportFilled(hedgingRequest, "4", "60000");
            }

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "7", "58000", "55000", "232000")
            );
        }

        @Test
        void clientTaker_ioc_partialQtyHedge_partialFills_hedgingOrderCanceled_clientOrderPartiallyFilled_thenCanceled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(
                new MatchingEngine.BulkOrderLeg("1", "50000"))
            );

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 10, OemsTIF.IOC, 60000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            // 1st hedging order filled, client order partially filled
            OemsRequest quotingRequest1 = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest1 = expectHedgingRequest(orderCollider, quotingRequest1.getOrderId(), BTCUSD_BITSTAMP, 1, "55000");
            orderCollider.emitExecutionReportNew(hedgingRequest1);
            orderCollider.emitExecutionReportFilled(hedgingRequest1, "1", "50000");

            agencyTradingService.expectResponses(
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "1", "50000", "50000", "9", "50000"),
                statusCanceled(takerRequest, "1", "50000", "IOC", null)
            );
        }

        @Test
        void clientTaker_ioc_partialQtyHedge_partialFills_hedgingOrderCanceled_clientOrderPartiallyFilled_thenCanceled_mixedWithInternal() {
            // given
            ClientContext maker = new ClientContext(MAKER1);
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(
                new MatchingEngine.BulkOrderLeg("1", "50000"))
            );
            OemsRequest makerRequest = agencyTradingService.emitsOemsRequest(maker.limitOrder(BTCUSD_CLOB, OemsSide.SELL, 1, OemsTIF.GTC, 50000));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 10, OemsTIF.IOC, 60000));

            // then
            agencyTradingService.expectResponses(
                statusNew(makerRequest),
                statusNew(takerRequest)
            );

            // 1st hedging order filled, client order partially filled
            OemsRequest quotingRequest1 = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest1 = expectHedgingRequest(orderCollider, quotingRequest1.getOrderId(), BTCUSD_BITSTAMP, 1, "55000");
            orderCollider.emitExecutionReportNew(hedgingRequest1);
            orderCollider.emitExecutionReportFilled(hedgingRequest1, "1", "50000");

            agencyTradingService.expectResponses(
                statusFilled(makerRequest, MAKER, CLOB, "50000", "50000", "50000"),
                statusPartiallyFilled(takerRequest, TAKER, CLOB, "1", "50000", "50000", "9", "50000"),
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "2", "50000", "50000", "8", "50000"),
                statusCanceled(takerRequest, "2", "50000", "IOC", null)
            );
        }

        @Test
        void clientTaker_partialQtyHedge_partialFills_orderCanceled_clientOrderCanceled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(
                new MatchingEngine.BulkOrderLeg("1", "50000"), // filled
                new MatchingEngine.BulkOrderLeg("2", "51000"), // canceled
                new MatchingEngine.BulkOrderLeg("3", "60000")) // canceled
            );

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 6, OemsTIF.GTC, 60000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            // 1st hedging order filled, client order partially filled
            OemsRequest quotingRequest1 = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest1 = expectHedgingRequest(orderCollider, quotingRequest1.getOrderId(), BTCUSD_BITSTAMP, 1, "55000");
            orderCollider.emitExecutionReportNew(hedgingRequest1);
            orderCollider.emitExecutionReportFilled(hedgingRequest1, "1", "50000");

            agencyTradingService.expectResponse(
                statusPartiallyFilled(takerRequest, TAKER, BITSTAMP, "1", "50000", "50000", "5", "50000")
            );

            // 2nd hedging order canceled, client order restated
            OemsRequest quotingRequest2 = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "51000", "2");
            OemsRequest hedgingRequest2 = expectHedgingRequest(orderCollider, quotingRequest2.getOrderId(), BTCUSD_BITSTAMP, 2, "56100");
            orderCollider.emitExecutionReportNew(hedgingRequest2);
            orderCollider.emitExecutionReportCancelled(hedgingRequest2);

            OemsRequest hedgingFallbackRequest2 = expectHedgingRequest(orderCollider, quotingRequest2.getOrderId(), BTCUSD_BIT2ME, 2, "56100");
            orderCollider.emitExecutionReportCancelled(hedgingFallbackRequest2);

            agencyTradingService.expectResponse(
                restated(takerRequest, OemsOrderStatus.STATUS_PARTIALLY_FILLED, "4", "1", "3", "50000", "102000", "Unsolicited reduce, cannot hedge")
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);
            });

            // 3rd hedging order canceled, client order canceled
            OemsRequest quotingRequest3 = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "60000", "3");
            OemsRequest hedgingRequest3 = expectHedgingRequest(orderCollider, quotingRequest3.getOrderId(), BTCUSD_BITSTAMP, 3, "66000");
            orderCollider.emitExecutionReportNew(hedgingRequest3);
            orderCollider.emitExecutionReportCancelled(hedgingRequest3);

            OemsRequest hedgingFallbackRequest3 = expectHedgingRequest(orderCollider, quotingRequest3.getOrderId(), BTCUSD_BIT2ME, 3, "66000");
            orderCollider.emitExecutionReportCancelled(hedgingFallbackRequest3);

            agencyTradingService.expectResponse(
                statusCanceled(takerRequest, "4", "1", "50000", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
            });
        }

        @Test
        void clientTaker_multiHedgingAttempts_thenGiveUp_buy() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BIT2ME, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.BUY, 1, OemsTIF.GTC, 50000));


            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BIT2ME, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "50500");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest1 = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "50500");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest1);

            OemsRequest hedgingFallbackRequest2 = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_KRAKEN, "50500");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest2);

            agencyTradingService.expectResponses(
                statusCanceled(takerRequest, "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            );
        }

        @Test
        void clientTaker_multiHedgingAttempts_thenGiveUp_sell() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BIT2ME, List.of(new MatchingEngine.BulkOrderLeg("1", "50000")), List.of());

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(BTCUSD_CLOB, OemsSide.SELL, 1, OemsTIF.GTC, 50000));


            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BIT2ME, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "49500");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest1 = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_KRAKEN, "49500");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest1);

            OemsRequest hedgingFallbackRequest2 = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "49500");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest2);

            agencyTradingService.expectResponses(
                statusCanceled(takerRequest, "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            );
        }

        @Test
        void clientTaker_singleFill_qtyAdjustedToZero() {
            // given
            // Bitstamp Qty precision: 0.01
            // CLOB Qty precision: 0.0001
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(), List.of(new MatchingEngine.BulkOrderLeg("0.1111", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrder(DOGEUSD_CLOB, OemsSide.BUY, 0.0001, OemsTIF.GTC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest),
                statusCanceled(takerRequest, "0.0001", "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            );
        }
    }

    @Nested
    class CashOrders {
        @Test
        void cashOrder_clientTaker_singleFill_limit() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrderQuoteCcy(BTCUSD_CLOB, OemsSide.BUY, 50_000, 100_000, "USD", OemsTIF.IOC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "1", "50050");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "50000", "50000", "1")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "50000", "50000", null)
            );
        }

        @Test
        void cashOrder_clientTaker_qtyTooSmallToMatch() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrderQuoteCcy(BTCUSD_CLOB, OemsSide.BUY, 0.0001, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusCanceled(takerRequest, "0.0001", "0", "0", "Unsolicited cancel, cannot place in CLOB", OemsExecRestatementReason.OTHER)
            );
        }

        @Test
        void cashOrder_partialQtyHedge_orderRejected_clientOrderCanceled() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrderQuoteCcy(BTCUSD_CLOB, OemsSide.BUY, 10, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "0.0002");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest);

            agencyTradingService.expectResponse(
                statusCanceled(takerRequest, "0", "0", "Unsolicited cancel, cannot hedge", OemsExecRestatementReason.OTHER)
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.OTHER);
            });

            quotingOrdersObserver.expectResponses(
                statusRejected(quotingRequest, "Rejected by venue")
            );
        }

        @ParameterizedTest
        @EnumSource(value = OemsTIF.class, names = {"GTC", "GTD"})
        void cashOrder_resting_partialQtyHedge_clientOrderRestated_hedgingOrderRejected_clientOrderReduced(OemsTIF tif) {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrderQuoteCcy(BTCUSD_CLOB, OemsSide.BUY, 60000, tif, 60000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest),
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "59996", "0", "59996", "0", "0", "Unsolicited reduce, CLOB qty increment")
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "1");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");
            orderCollider.emitExecutionReportRejected(hedgingRequest);

            OemsRequest hedgingFallbackRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BIT2ME, "55000");
            orderCollider.emitExecutionReportRejected(hedgingFallbackRequest);

            agencyTradingService.expectResponse(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "9996", "0", "9996", "0", "1", "Unsolicited reduce, cannot hedge")
            ).satisfies(response -> {
                assertHedgingAccount(response, BIT2ME);
                assertThat(response.getExecRestatementReason()).isEqualTo(OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);
            });

            quotingOrdersObserver.expectResponses(
                statusRejected(quotingRequest, "Rejected by venue")
            );
        }

        @ParameterizedTest
        @EnumSource(value = OemsTIF.class, names = {"GTC", "GTD"})
        void cashOrder_resting_partialQtyHedge_clientOrderRestated_hedgingOrderFilled_clientOrderFilled(OemsTIF tif) {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "70000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrderQuoteCcy(BTCUSD_CLOB, OemsSide.BUY, 100, tif, 70000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest),
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "98", "0", "98", "0", "0", "Unsolicited reduce, CLOB qty increment")
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "70000", "0.0014");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "77000");
            orderCollider.emitExecutionReportFilled(hedgingRequest, "0.0014", "70000");

            agencyTradingService.expectResponse(
                statusFilled(takerRequest, TAKER, BITSTAMP, "98", "70000", "70000", "0.0014")
            );

            quotingOrdersObserver.expectResponses(
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "0.0014","70000", "70000", null)
            );
        }

        @Test
        void cashOrder_impossibleToMatchExactly_clientOrderRestated_thenFilled() {
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(BTCUSD_CLOB, QUOTING_BITSTAMP, List.of(), List.of(new MatchingEngine.BulkOrderLeg("1", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.marketOrderQuoteCcy(BTCUSD_CLOB, OemsSide.BUY, 7, OemsTIF.IOC));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, BTCUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP, "50000", "0.0001");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), BTCUSD_BITSTAMP, "55000");

            orderCollider.emitExecutionReportFilled(hedgingRequest, hedgingRequest.getQuantity(), "50000");

            agencyTradingService.expectResponses(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "5", "0", "5", "0", "0", "Unsolicited reduce, CLOB qty increment"),
                statusFilled(takerRequest, TAKER, BITSTAMP, "5", "50000", "50000", "0.0001")
            );
        }

        @Test
        void cashOrder_clientTaker_singleFill_qtyAdjusted() {
            // given
            // Bitstamp Qty precision: 0.01
            // CLOB Qty precision: 0.0001
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(DOGEUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(), List.of(new MatchingEngine.BulkOrderLeg("0.1111", "50000")));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrderQuoteCcy(DOGEUSD_CLOB, OemsSide.BUY, 5555, OemsTIF.IOC, 50000));

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest)
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, DOGEUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "50000", "0.1111");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), DOGEUSD_BITSTAMP, 0.11, "55000");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportFilled(hedgingRequest, "0.1100", "50050");

            agencyTradingService.expectResponses(
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "5500", "0", "5500", "0", "0.0011", "Unsolicited reduce, hedging venue qty constraints"),
                statusFilled(takerRequest, TAKER, BITSTAMP, "5500", "50000", "50000", "0.11")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "0.11","50000", "50000", null)
            );
        }

        @Test
        void cashOrder_clientTaker_fourFills_qtyAdjusted() {
            // given
            ClientContext taker = new ClientContext(TAKER1);
            matchingEngine.insertBulkOrders(XRPUSD_CLOB, QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, List.of(), List.of(
                new MatchingEngine.BulkOrderLeg("3745.********", "2.67")
            ));

            // when
            OemsRequest takerRequest = agencyTradingService.emitsOemsRequest(taker.limitOrderQuoteCcy(XRPUSD_CLOB, OemsSide.BUY, 10000.0, OemsTIF.GTD, 2.67)); // qty = 3745.********

            // then
            agencyTradingService.expectResponses(
                statusNew(takerRequest),
                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "9999.**********", "0", "9999.**********", "0", "0.00000000988547767374", "Unsolicited reduce, CLOB qty increment")
//                restated(takerRequest, OemsOrderStatus.STATUS_NEW, "9999.**********002", "0", "9999.**********002", "0", "0.00000000988540306636", "Unsolicited reduce, CLOB qty increment")
            );

            OemsRequest quotingRequest = expectQuotingRequest(quotingOrdersObserver, takerRequest, XRPUSD_CLOB, TestingData.QuotingConfigs.QUOTING_BITSTAMP_NO_ALTERNATIVE_ACCOUNTS, "2.67", "3745.********");
            OemsRequest hedgingRequest = expectHedgingRequest(orderCollider, quotingRequest.getOrderId(), XRPUSD_BITSTAMP, 3745.********, "2.67267");

            orderCollider.emitExecutionReportNew(hedgingRequest);
            orderCollider.emitExecutionReportPartiallyFilled(hedgingRequest, "1215.********", "1215.********", "2.66956", "2529.********");
            orderCollider.emitExecutionReportPartiallyFilled(hedgingRequest, "1401.********", "185.8497702", "2.66995", "2343.********");
            orderCollider.emitExecutionReportPartiallyFilled(hedgingRequest, "2009.********", "607.5", "2.67", "1736.********");
            orderCollider.emitExecutionReportFilled(hedgingRequest, "1736.********", "2.67001", "2.66986");

            agencyTradingService.expectResponses(
                statusFilled(takerRequest, TAKER, BITSTAMP, "9999.**********", "2.67", "2.67", "3745.********")
            );

            quotingOrdersObserver.expectResponses(
                statusNew(quotingRequest),
                statusFilled(quotingRequest, LIQUIDITY_INDICATOR_UNSPECIFIED, BITSTAMP, "3745.********","2.67", "2.67", null)
            );
        }
    }
}
