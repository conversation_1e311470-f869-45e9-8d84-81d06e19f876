### Requirements
Install cert-manager with <PERSON><PERSON><PERSON> integration and create certificate secret:
```sh
kubectl apply -f ingress-nginx/certificate.yaml
```

### Add the repo
```sh
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
```

### Install the chart
```sh
helm diff upgrade ingress-nginx ingress-nginx/ingress-nginx --namespace ingress-nginx --version 4.12.1 -f infra/ingress-nginx/values-prod.yaml
```

```sh
helm upgrade -i ingress-nginx ingress-nginx/ingress-nginx --create-namespace --namespace ingress-nginx --version 4.12.1 --atomic --timeout 5m -f infra/ingress-nginx/values-prod.yaml
```

### Install the chart AWS
```sh
helm diff upgrade ingress-nginx ingress-nginx/ingress-nginx --namespace ingress-nginx --version 4.12.1 -f infra/ingress-nginx/values-aws-prod.yaml
```

```sh
helm upgrade -i ingress-nginx ingress-nginx/ingress-nginx --create-namespace --namespace ingress-nginx --version 4.12.1 --atomic --timeout 5m -f infra/ingress-nginx/values-aws-prod.yaml
```
