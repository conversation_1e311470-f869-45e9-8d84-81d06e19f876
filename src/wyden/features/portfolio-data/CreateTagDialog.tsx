import { Dialog } from '@ui/Dialog';
import { usePortfolioStore } from './usePortfolioStore';
import { z } from 'zod';
import { createTsForm, createUniqueFieldSchema } from '@ts-react/form';
import { ReactNode } from 'react';
import { DialogTitle } from '@ui/DialogTitle';
import { DialogContent } from '@ui/DialogContent';
import { DialogActions } from '@ui/DialogActions';
import { Button } from '@ui/Button';
import { styled } from '@ui/styled';
import { useTranslation } from 'react-i18next';
import { TagCategoryInput } from './create-portfolio/TagsInput';
import { TextField } from '../form/TextField';
import { PORTFOLIO_GROUP_TAG_NAME } from '@wyden/constants';

const TagCategoryDef = createUniqueFieldSchema(
  z
    .string()
    .refine((value) => value.replace('create-new-custom-tag:', '') !== PORTFOLIO_GROUP_TAG_NAME, {
      message: 'portfolioData.addPortfolioForm.portfolioGroupReserved',
    }),
  'tagCategory',
);

export const CreateTagSchema = z.object({
  key: TagCategoryDef,
  value: z.string(),
});

type Props = {
  setPortfolioFormTags: (tags: { key: string; value: string }[]) => void;
  portfolioFormTags: { key: string; value: string }[];
};

export const CreateTagDialog = (props: Props) => {
  const { createNewTagProps, toggleCreateTagDialog, notSavedTags, addNotSavedTag } =
    usePortfolioStore();
  const { t } = useTranslation();

  const onSubmit = (values: z.infer<typeof CreateTagSchema>) => {
    const keyWithoutCustomTag = values.key.replace('create-new-custom-tag:', '');
    const keyTaken = props.portfolioFormTags.some((tag) => tag.key === keyWithoutCustomTag);

    if (keyTaken) {
      return;
    }

    const addedTagExistsInPortfolio = props.portfolioFormTags.some(
      (tag) => tag.key === values.key && tag.value === values.value,
    );

    const tagToSave = {
      key: keyWithoutCustomTag,
      value: values.value,
    };

    if (!addedTagExistsInPortfolio) {
      props.setPortfolioFormTags([...props.portfolioFormTags, tagToSave]);
    }

    const addedTagExistsInNotSavedTags = notSavedTags.some(
      (tag) => tag.key === values.key && tag.value === values.value,
    );

    if (!addedTagExistsInNotSavedTags) {
      addNotSavedTag(tagToSave);
    }

    toggleCreateTagDialog();
  };

  const defaultVals = {
    key: createNewTagProps.key || '',
    value: createNewTagProps.value || '',
  };

  return (
    <Dialog open={true} onClose={() => toggleCreateTagDialog()}>
      <CreateTagForm
        defaultValues={defaultVals}
        schema={CreateTagSchema}
        onSubmit={onSubmit}
        props={{
          value: {
            label: t('portfolioData.addPortfolioForm.tagValue'),
            required: true,
          },
          key: {
            usedCategories: props.portfolioFormTags.map((tag) => tag.key),
          },
        }}
      />
    </Dialog>
  );
};

const CreateTagFormComponent = (props: { children: ReactNode; onSubmit: () => void }) => {
  const { toggleCreateTagDialog } = usePortfolioStore();
  const { t } = useTranslation();

  return (
    <form onSubmit={props.onSubmit}>
      <DialogTitle onClose={() => toggleCreateTagDialog()}>
        {t('portfolioData.addPortfolioForm.createNewTag')}
      </DialogTitle>
      <DialogContent>
        <Container>{props.children}</Container>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => toggleCreateTagDialog()} size="lg">
          {t('common.dismiss')}
        </Button>
        <Button variant="primary" type="submit" size="lg">
          {t('portfolioData.addPortfolioForm.createTag')}
        </Button>
      </DialogActions>
    </form>
  );
};

const Container = styled('div')`
  display: flex;
  flex-direction: column;
  margin-top: ${({ theme }) => theme.spacing(2)};
  gap: ${({ theme }) => theme.spacing(6)};
`;

const mapping = [
  [z.string(), TextField],
  [TagCategoryDef, TagCategoryInput],
] as const;

export const CreateTagForm = createTsForm(mapping, {
  FormComponent: CreateTagFormComponent,
});
