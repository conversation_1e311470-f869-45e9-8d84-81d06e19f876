# Process run: 2025.02.17, Release 18
# Code Freeze date: 2025.03.04
# Planned release date: 2025.03.10

| Team         | Dev Capacity | QA Capacity | Requested Tech Debt |
|--------------|--------------|-------------|---------------------|
| UI           | 30.0 MD      | -           | -                   |
| Blue         | 16.8 MD      | 3.5 MD      | -                   |
| Red          | 14.5 MD      | 7.0 MD      | -                   |
| Yellow       | 14.0 MD      | 7.0 MD      | -                   |
| **Total BE** | **75.3 MD**  | -           | -                   |
| **Total FE** | **30.0 MD**  | -           | -                   | 
| **Total QA** | -            | **17.5 MD** | -                   | 

## Decision Records

**Bartosz Wój<PERSON>k**
* Process moved by one day so we don't release on Fridays
* Bartosz to raise a MR to the process accounting for above

**Release Master**
<PERSON><PERSON>

**Sprint Goals**

* Settlement Engine - New microservice, UI Dashboard & Schema
* Garanti - Calendar Quoting (minimal scope for weekend triangulation)
* Static portfolio permissions - Nostro vs. Vostro
* Remove Rabbit MQ dynamic bindings
* Add CoinAPI as source in the conversion rate service

## Retrospective

* Releasing on Mondays is a good change
* More Time for Testing and Stabilization
* Big Changes are coming right before Stabilization Phase starts
* Bartosz/Dzianis to pick it up and discuss changes in the process
* Better plan for capacity breaches (like events/workshops/etc)
