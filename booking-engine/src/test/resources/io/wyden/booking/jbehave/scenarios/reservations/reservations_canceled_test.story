Narrative:
Verify what happens when order is cancelled by the user

Scenario: cancelled reservations are released fully

Given market data prices:
| instrument | price      |
| BTC        | 60_000 USD |

When Client 1 makes a reservation TRADE-A for a trade of 0.001 BTC/USD on Bitfinex account with fixed fee 5 USD

Then position net state change to:
| portfolioId | instrument | qty | pendingQty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | -   | -65        | -    | -           | -           | -             | -            |
| Client 1    | BTC        | -   | 0.001      | -    | -           | -           | -             | -            |

When TRADE-A reservation is canceled by the user

Then position net state change to:
| portfolioId | instrument | qty | pendingQty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | USD        | -   | -          | -    | -           | -           | -             | -            |
| Client 1    | BTC        | -   | -          | -    | -           | -           | -             | -            |
