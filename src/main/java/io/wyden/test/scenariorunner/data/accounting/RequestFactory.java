package io.wyden.test.scenariorunner.data.accounting;

import io.wyden.apiserver.rest.referencedata.portfolio.model.CreatePortfolioDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.TagDto;
import io.wyden.rest.management.domain.OnboardingModel;
import io.wyden.rest.management.domain.OnboardingModel.AccountOnboardingRequest;
import io.wyden.rest.management.domain.OnboardingModel.Grant;
import io.wyden.rest.management.domain.OnboardingModel.PortfolioOnboardingRequest;
import io.wyden.rest.management.domain.PortfolioModel.PortfolioType;
import io.wyden.rest.management.domain.PortfolioModel.Tag;
import io.wyden.rest.management.domain.ReservationModel.AccountCashTransferReservationRequest;
import io.wyden.rest.management.domain.ReservationModel.ClientCashTradeReservationRequest;
import io.wyden.rest.management.domain.ReservationModel.DepositReservationRequest;
import io.wyden.rest.management.domain.ReservationModel.PortfolioCashTransferReservationRequest;
import io.wyden.rest.management.domain.ReservationModel.StreetCashTradeReservationRequest;
import io.wyden.rest.management.domain.ReservationModel.WithdrawalReservationRequest;
import io.wyden.rest.management.domain.TransactionModel.AccountCashTransferRequest;
import io.wyden.rest.management.domain.TransactionModel.ClientCashTradeRequest;
import io.wyden.rest.management.domain.TransactionModel.Deposit;
import io.wyden.rest.management.domain.TransactionModel.PortfolioCashTransferRequest;
import io.wyden.rest.management.domain.TransactionModel.StreetCashTradeRequest;
import io.wyden.rest.management.domain.TransactionModel.TransactionFee;
import io.wyden.rest.management.domain.TransactionModel.TransactionType;
import io.wyden.rest.management.domain.TransactionModel.Withdrawal;
import io.wyden.test.scenariorunner.data.refdata.Currency;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.util.Randomizer;
import org.keycloak.common.util.Time;
import org.testcontainers.shaded.com.google.common.collect.ImmutableCollection;
import org.testcontainers.shaded.com.google.common.collect.ImmutableList;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import static io.wyden.rest.management.domain.TransactionModel.TransactionType.CLIENT_CASH_TRADE;
import static io.wyden.test.scenariorunner.data.refdata.Currency.USD;

public class RequestFactory {

    private static final BigDecimal DEFAULT_PRICE = BigDecimal.TEN;
    private static final BigDecimal DEFAULT_QUANTITY = BigDecimal.ONE;
    private static final Currency DEFAULT_CURRENCY = USD;

    public static AccountOnboardingRequest createWalletOnboardingRequest() {
        String name = AccountingFactory.getRandomWalletName();
        return new AccountOnboardingRequest(
            name,
            name,
            OnboardingModel.WalletType.VOSTRO,
            List.of()
        );
    }

    public static AccountOnboardingRequest createWalletOnboardingRequest(OnboardingModel.WalletType type) {
        String name = AccountingFactory.getRandomWalletName();
        return new AccountOnboardingRequest(
            name,
            name,
            type,
            List.of()
        );
    }

    public static AccountOnboardingRequest createWalletOnboardingRequest(String name, OnboardingModel.WalletType type) {
        return new AccountOnboardingRequest(
            name,
            name,
            type,
            List.of()
        );
    }

    public static PortfolioOnboardingRequest createPortfolio(CreatePortfolioDto createPortfolioDto, List<Grant> grants) {
        return new PortfolioOnboardingRequest(
            createPortfolioDto.name(),
            createPortfolioDto.name(),
            createPortfolioDto.portfolioCurrency(),
            PortfolioType.valueOf(createPortfolioDto.portfolioType().name()),
            map(createPortfolioDto.tags()),
            grants
        );
    }

    public static PortfolioOnboardingRequest createVostroPortfolioOnboardingRequest(List<Grant> grants) {
        CreatePortfolioDto createPortfolioDto = PortfolioFactory.randomVostroUSDPortfolio();

        return new PortfolioOnboardingRequest(
            createPortfolioDto.name(),
            createPortfolioDto.name(),
            createPortfolioDto.portfolioCurrency(),
            PortfolioType.valueOf(createPortfolioDto.portfolioType().name()),
            map(createPortfolioDto.tags()),
            grants
        );
    }

    public static PortfolioOnboardingRequest createNostroPortfolioOnboardingRequest(List<Grant> grants) {
        CreatePortfolioDto createPortfolioDto = PortfolioFactory.randomNostroUSDPortfolio();

        return new PortfolioOnboardingRequest(
            createPortfolioDto.name(),
            createPortfolioDto.name(),
            createPortfolioDto.portfolioCurrency(),
            PortfolioType.valueOf(createPortfolioDto.portfolioType().name()),
            map(createPortfolioDto.tags()),
            grants
        );
    }

    public static DepositReservationRequest createReservationDepositRequest(
        Currency currency,
        String portfolio,
        String accountId,
        String feePortfolioId,
        String feeAccountId) {

        String reservationRef = Randomizer.uuid();
        BigDecimal amount = AccountingFactory.produceAmount();

        return new DepositReservationRequest(
            reservationRef,
            TransactionType.DEPOSIT,
            Time.currentTimeMillis(),
            currency.name(),
            amount,
            portfolio,
            accountId,
            feePortfolioId,
            feeAccountId,
            null
        );
    }

    public static Deposit createDepositTransactionRequest(
        Currency currency,
        String portfolio,
        String accountName,
        String feeAccountName,
        String feePortfolio) {

        return getDeposit(null, currency, portfolio, accountName, feeAccountName, feePortfolio);
    }

    public static Deposit createDepositTransactionRequestWithNoFees(
        Currency currency,
        String portfolio,
        String accountName) {

        return getDeposit(null, currency, portfolio, accountName, null, null);
    }

    public static Deposit createDepositTransactionRequestWithAmount(
        BigDecimal amount,
        Currency currency,
        String portfolio,
        String accountName,
        String feeAccountName,
        String feePortfolio) {

        return getDeposit(amount, currency, portfolio, accountName, feeAccountName, feePortfolio);
    }

    public static Deposit createDepositTransactionRequestWithNoFeesWithAmount(
        BigDecimal amount,
        Currency currency,
        String portfolio,
        String accountName) {

        return getDeposit(amount, currency, portfolio, accountName, null, null);
    }

    private static Deposit getDeposit(BigDecimal amount, Currency currency, String portfolio, String accountName, String feeAccountName, String feePortfolio) {
        String reservationRef = Randomizer.uuid();
        String executionId = Randomizer.uuid();
        String id = Randomizer.uuid();
        ImmutableCollection<TransactionFee> fees = null;
        if (feePortfolio != null && feeAccountName != null) {
            TransactionFee transactionFee = AccountingFactory.produceTransactionReservationFee(DEFAULT_CURRENCY);
            fees = ImmutableList.of(transactionFee);
        }
        return new Deposit(
            id,
            reservationRef,
            System.currentTimeMillis(),
            executionId,
            null,
            TransactionType.DEPOSIT,
            null,
            null,
            currency.name(),
            amount == null ? AccountingFactory.produceAmount() : amount,
            portfolio,
            accountName,
            feePortfolio,
            feeAccountName,
            null,
            fees);
    }

    public static Withdrawal createWithdrawalTransactionRequestWithAmount(
        BigDecimal amount,
        Currency currency,
        String portfolio,
        String accountName,
        String feeAccountName,
        String feePortfolio) {
        return getWithdrawal(amount, currency, portfolio, accountName, feeAccountName, feePortfolio);
    }

    public static Withdrawal createWithdrawalTransactionRequestWithAmountWithNoFees(
        BigDecimal amount,
        Currency currency,
        String portfolio,
        String accountName) {
        return getWithdrawal(amount, currency, portfolio, accountName, null, null);
    }

    public static Withdrawal createWithdrawalTransactionRequest(
        Currency currency,
        String portfolio,
        String accountName,
        String feeAccountName,
        String feePortfolio) {
        return getWithdrawal(null, currency, portfolio, accountName, feeAccountName, feePortfolio);
    }

    private static Withdrawal getWithdrawal(BigDecimal amount, Currency currency, String portfolio, String accountName, String feeAccountName, String feePortfolio) {
        String reservationRef = Randomizer.uuid();
        String id = Randomizer.uuid();
        String executionId = Randomizer.uuid();
        ImmutableCollection<TransactionFee> fees = null;
        if (feePortfolio != null && feeAccountName != null) {
            TransactionFee transactionFee = AccountingFactory.produceTransactionReservationFee(DEFAULT_CURRENCY);
            fees = ImmutableList.of(transactionFee);
        }
        BigDecimal finalAmount = (amount == null) ? AccountingFactory.produceAmount() : amount;
        return new Withdrawal(
            id,
            reservationRef,
            System.currentTimeMillis(),
            executionId,
            null,
            TransactionType.WITHDRAWAL,
            null,
            null,
            currency.name(),
            finalAmount,
            portfolio,
            accountName,
            feePortfolio,
            feeAccountName,
            null,
            fees);
    }

    public static PortfolioCashTransferRequest createPortfolioCashTransferRequest(Currency currency, String sourcePortfolio, String targetPortfolio) {
        String reservationRef = Randomizer.uuid();
        String id = Randomizer.uuid();
        String executionId = Randomizer.uuid();
        TransactionFee transactionFee = AccountingFactory.produceTransactionReservationFee(currency);
        ImmutableCollection<TransactionFee> fees = ImmutableList.of(transactionFee);
        BigDecimal finalAmount = AccountingFactory.produceAmount();

        return new PortfolioCashTransferRequest(
            reservationRef,
            System.currentTimeMillis(),
            executionId,
            null,
            TransactionType.PORTFOLIO_CASH_TRANSFER,
            currency.name(),
            finalAmount,
            null,
            sourcePortfolio,
            targetPortfolio,
            fees);
    }

    public static AccountCashTransferRequest createAccountCashTransferRequest(Currency currency, String sourceAccount, String targetAccount, String feePortfolio, String feeAccount) {
        String reservationRef = Randomizer.uuid();
        String id = Randomizer.uuid();
        String executionId = Randomizer.uuid();
        TransactionFee transactionFee = AccountingFactory.produceTransactionReservationFee(currency);
        ImmutableCollection<TransactionFee> fees = ImmutableList.of(transactionFee);
        BigDecimal finalAmount = AccountingFactory.produceAmount();

        return new AccountCashTransferRequest(
            reservationRef,
            System.currentTimeMillis(),
            executionId,
            null,
            TransactionType.ACCOUNT_CASH_TRANSFER,
            currency.name(),
            finalAmount,
            null,
            sourceAccount,
            targetAccount,
            feePortfolio,
            feeAccount,
            fees
        );
    }

    public static AccountCashTransferRequest createAccountCashTransferRequestWithNoFees(Currency currency, String sourceAccount, String targetAccount) {
        String reservationRef = Randomizer.uuid();
        String executionId = Randomizer.uuid();
        BigDecimal finalAmount = AccountingFactory.produceAmount();

        return new AccountCashTransferRequest(
            reservationRef,
            System.currentTimeMillis(),
            executionId,
            null,
            TransactionType.ACCOUNT_CASH_TRANSFER,
            currency.name(),
            finalAmount,
            null,
            sourceAccount,
            targetAccount,
            null,
            null,
            null
        );
    }

    public static ClientCashTradeRequest createClientCashTradeTransactionRequest(Currency baseCurrency, Currency quoteCurrency, String portfolio, String counterPortfolio) {
        return new ClientCashTradeRequest(
            Randomizer.uuid(),
            System.currentTimeMillis(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            CLIENT_CASH_TRADE,
            baseCurrency.name(),
            quoteCurrency.name(),
            DEFAULT_QUANTITY,
            null,
            DEFAULT_PRICE,
            portfolio,
            counterPortfolio,
            Randomizer.uuid(),
            null,
            true,
            System.currentTimeMillis()
        );
    }

    public static StreetCashTradeRequest createStreetCashTradeTransactionRequest(Currency baseCurrency, Currency quoteCurrency, String portfolio, String accountId) {
        return getStreetCashTrade(
            baseCurrency,
            quoteCurrency,
            portfolio,
            accountId,
            null
        );
    }

    public static StreetCashTradeRequest createStreetCashTradeTransactionRequestWithPrice(Currency baseCurrency, Currency quoteCurrency, String portfolio, String accountId, BigDecimal price) {
        return getStreetCashTrade(
            baseCurrency,
            quoteCurrency,
            portfolio,
            accountId,
            price
        );
    }

    public static StreetCashTradeRequest getStreetCashTrade(Currency baseCurrency, Currency quoteCurrency, String portfolio, String accountId, BigDecimal price) {
        return new StreetCashTradeRequest(
            Randomizer.uuid(),
            System.currentTimeMillis(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            Randomizer.uuid(),
            TransactionType.STREET_CASH_TRADE,
            baseCurrency.name(),
            quoteCurrency.name(),
            DEFAULT_QUANTITY,
            null,
            (price == null) ? DEFAULT_PRICE : price,
            portfolio,
            accountId,
            Randomizer.uuid(),
            null,
            null,
            null
        );
    }

    public static PortfolioCashTransferReservationRequest createPortfolioCashTransferReservationRequest(Currency currency, String sourcePortfolio, String targetPortfolio) {
        String reservationRef = Randomizer.uuid();
        BigDecimal finalAmount = AccountingFactory.produceAmount();
        return new PortfolioCashTransferReservationRequest(
            reservationRef,
            TransactionType.PORTFOLIO_CASH_TRANSFER,
            System.currentTimeMillis(),
            currency.name(),
            finalAmount,
            sourcePortfolio,
            targetPortfolio,
            null
        );
    }

    public static PortfolioCashTransferReservationRequest createPortfolioCashTransferReservationRequestWithAmount(BigDecimal finalAmount, Currency currency, String sourcePortfolio, String targetPortfolio) {
        String reservationRef = Randomizer.uuid();
        return new PortfolioCashTransferReservationRequest(
            reservationRef,
            TransactionType.PORTFOLIO_CASH_TRANSFER,
            System.currentTimeMillis(),
            currency.name(),
            finalAmount,
            sourcePortfolio,
            targetPortfolio,
            null
        );
    }

    public static AccountCashTransferReservationRequest createAccountCashTransferReservationRequest(Currency currency, String sourceAccount, String targetAccount, String feePortfolio, String feeAccount) {
        String reservationRef = Randomizer.uuid();
        BigDecimal finalAmount = AccountingFactory.produceAmount();
        return new AccountCashTransferReservationRequest(
            reservationRef,
            TransactionType.ACCOUNT_CASH_TRANSFER,
            System.currentTimeMillis(),
            currency.name(),
            finalAmount,
            sourceAccount,
            targetAccount,
            feeAccount,
            feePortfolio,
            null
        );
    }

    public static AccountCashTransferReservationRequest createAccountCashTransferReservationRequestWithAmount(BigDecimal finalAmount, Currency currency, String sourceAccount, String targetAccount, String feePortfolio, String feeAccount) {
        String reservationRef = Randomizer.uuid();
        return new AccountCashTransferReservationRequest(
            reservationRef,
            TransactionType.ACCOUNT_CASH_TRANSFER,
            System.currentTimeMillis(),
            currency.name(),
            finalAmount,
            sourceAccount,
            targetAccount,
            feeAccount,
            feePortfolio,
            null
        );
    }

    public static ClientCashTradeReservationRequest createClientCashTradeReservationRequest(Currency baseCurrency, Currency quoteCurrency, String portfolio, String counterPortfolio) {
        String reservationRef = Randomizer.uuid();
        return new ClientCashTradeReservationRequest(
            reservationRef,
            CLIENT_CASH_TRADE,
            System.currentTimeMillis(),
            quoteCurrency.name(),
            baseCurrency.name(),
            DEFAULT_QUANTITY,
            DEFAULT_PRICE,
            null,
            portfolio,
            counterPortfolio,
            null
        );
    }

    public static ClientCashTradeReservationRequest createClientCashTradeReservationRequestWithPrice(BigDecimal price, Currency baseCurrency, Currency quoteCurrency, String portfolio, String counterPortfolio) {
        String reservationRef = Randomizer.uuid();
        return new ClientCashTradeReservationRequest(
            reservationRef,
            CLIENT_CASH_TRADE,
            System.currentTimeMillis(),
            quoteCurrency.name(),
            baseCurrency.name(),
            DEFAULT_QUANTITY,
            price,
            null,
            portfolio,
            counterPortfolio,
            null
        );
    }

    public static StreetCashTradeReservationRequest createStreetCashTradeReservationRequest(Currency baseCurrency, Currency quoteCurrency, String portfolio, String account) {
        String reservationRef = Randomizer.uuid();
        return new StreetCashTradeReservationRequest(
            reservationRef,
            TransactionType.STREET_CASH_TRADE,
            System.currentTimeMillis(),
            quoteCurrency.name(),
            baseCurrency.name(),
            DEFAULT_QUANTITY,
            DEFAULT_PRICE,
            null,
            portfolio,
            account,
            null
        );
    }

    public static StreetCashTradeReservationRequest createStreetCashTradeReservationRequestWithPrice(BigDecimal price, Currency baseCurrency, Currency quoteCurrency, String portfolio, String account) {
        String reservationRef = Randomizer.uuid();
        return new StreetCashTradeReservationRequest(
            reservationRef,
            TransactionType.STREET_CASH_TRADE,
            System.currentTimeMillis(),
            quoteCurrency.name(),
            baseCurrency.name(),
            DEFAULT_QUANTITY,
            price,
            null,
            portfolio,
            account,
            null
        );
    }

    public static WithdrawalReservationRequest createReservationWithdrawalRequestWithAmount(
        BigDecimal amount,
        Currency currency,
        String portfolio,
        String accountName,
        String feePortfolio,
        String feeAccountName) {
        return getWithdrawalReservation(amount, currency, portfolio, accountName, feeAccountName, feePortfolio);
    }

    public static WithdrawalReservationRequest createReservationWithdrawalRequest(
        Currency currency,
        String portfolio,
        String accountName,
        String feePortfolio,
        String feeAccountName) {
        return getWithdrawalReservation(null, currency, portfolio, accountName, feeAccountName, feePortfolio);
    }

    private static WithdrawalReservationRequest getWithdrawalReservation(
        BigDecimal amount,
        Currency currency,
        String portfolio,
        String accountName,
        String feeAccountName,
        String feePortfolio) {

        String reservationRef = Randomizer.uuid();
        BigDecimal finalAmount = (amount == null) ? AccountingFactory.produceAmount() : amount;
        return new WithdrawalReservationRequest(
            reservationRef,
            TransactionType.WITHDRAWAL,
            Time.currentTimeMillis(),
            currency.name(),
            finalAmount,
            portfolio,
            accountName,
            feePortfolio,
            feeAccountName,
            null
        );
    }

    private static List<Tag> map(List<TagDto> tags) {
        return tags.stream()
            .map(t -> new Tag(t.key(), t.value()))
            .collect(Collectors.toList());
    }

}
