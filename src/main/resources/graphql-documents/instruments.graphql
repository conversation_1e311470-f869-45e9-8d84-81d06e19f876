query ($venueNames: [String!]) {
    instruments(venueNames: $venueNames) {
        baseInstrument {
            venueName
            assetClass
            description
            quoteCurrency
            inverseContract
            venueType
            symbol
        }
        instrumentIdentifiers {
            adapterTicker
            tradingViewId
            venueTradingViewId
            instrumentId
        }
        tradingConstraints {
            minQty
            maxQty
            minQuoteQty
            maxQuoteQty
            qtyIncr
            quoteQtyIncr
            minPrice
            maxPrice
            priceIncr
            minNotional
            contractSize
            tradeable
        }
        forexSpotProperties {
            baseCurrency
        }
        archivedAt
    }
}