package io.wyden.brokerconfig.application;

import io.wyden.brokerconfig.domain.reset.ResetConfigurationInput;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.PricingConfig;
import org.springframework.stereotype.Service;

@Service
public class ResetConfigurationService {

    private final PortfolioStorageService portfolioStorageService;
    private final PortfolioGroupStorageService portfolioGroupStorageService;

    public ResetConfigurationService(PortfolioStorageService portfolioStorageService, PortfolioGroupStorageService portfolioGroupStorageService) {
        this.portfolioStorageService = portfolioStorageService;
        this.portfolioGroupStorageService = portfolioGroupStorageService;
    }

    public String reset(ResetConfigurationInput resetConfigurationInput) {
        switch (resetConfigurationInput.configurationLevel()) {
            case PORTFOLIO -> {
                switch (resetConfigurationInput.configurationType()) {
                    case PRICING -> portfolioStorageService.save(resetConfigurationInput.resourceId(), emptyPricingConfig());
                    case EXECUTION -> portfolioStorageService.save(resetConfigurationInput.resourceId(), emptyExecutionConfig());
                    case HEDGING -> portfolioStorageService.save(resetConfigurationInput.resourceId(), emptyHedgingConfig());
                }
            }
            case PORTFOLIO_GROUP -> {
                switch (resetConfigurationInput.configurationType()) {
                    case PRICING -> portfolioGroupStorageService.save(resetConfigurationInput.resourceId(), emptyPricingConfig());
                    case EXECUTION -> portfolioGroupStorageService.save(resetConfigurationInput.resourceId(), emptyExecutionConfig());
                    case HEDGING -> portfolioGroupStorageService.save(resetConfigurationInput.resourceId(), emptyHedgingConfig());
                }
            }
            case PORTFOLIO_INSTRUMENT ->
                portfolioStorageService.resetConfigurationForInstrument(resetConfigurationInput.configurationType(), resetConfigurationInput.resourceId(), resetConfigurationInput.instrumentId());
            case PORTFOLIO_GROUP_INSTRUMENT ->
                portfolioGroupStorageService.resetConfigurationForInstrument(resetConfigurationInput.configurationType(), resetConfigurationInput.resourceId(), resetConfigurationInput.instrumentId());
        }

        return resetConfigurationInput.resourceId();
    }

    private PricingConfig emptyPricingConfig() {
        return PricingConfig.newBuilder().build();
    }

    private ExecutionConfig emptyExecutionConfig() {
        return ExecutionConfig.newBuilder().build();
    }

    private HedgingConfig emptyHedgingConfig() {
        return HedgingConfig.newBuilder().build();
    }
}
