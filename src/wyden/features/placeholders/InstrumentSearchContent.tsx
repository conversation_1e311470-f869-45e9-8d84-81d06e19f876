import { styled } from '@ui/styled';
import { InstrumentSearch } from '@wyden/features/instrument-search/InstrumentSearch';
import { PlaceholderWidgetContent } from './KpisPlaceholder';
import { useTradingInstrument } from '@wyden/hooks/useTradingInstrument';
import { useWorkspaces } from '../useWorkspaces';
import { LoadingDataContainer } from '../LoadingDataContainer';
import { useSimpleOrderFormStore } from '../order-form/useSimpleOrderFormStore';
import { InstrumentResponse } from '@wyden/services/graphql/generated/graphql';
import { useInstrumentSearch } from '../instrument-search/useInstrumentSearch';
import { logger } from '@wyden/utils/logger';

const CONTAINER_NAME = 'instrument-search-widget';

export const InstrumentSearchContent = ({ label }: { label?: string }) => {
  const { instrument, changeInstrument, loading } = useTradingInstrument();
  const { currentWorkspace } = useWorkspaces();
  const updateOrderEntryValues = useSimpleOrderFormStore((state) => state.updateOrderEntryValues);
  const { instruments } = useInstrumentSearch();

  const handleInstrumentChange = (instrument: InstrumentResponse) => {
    changeInstrument(instrument);
    if (!currentWorkspace) {
      logger.warn('There is no saved workspace, the instrument cannot be saved');
      return;
    }
    updateOrderEntryValues(currentWorkspace.id, {
      limitPrice: undefined,
      quantity: '1',
      instrument,
    });
  };

  return (
    <LoadingDataContainer loading={loading} data={instruments}>
      <PlaceholderWidgetContent>
        <FullWidthDiv>
          <InstrumentSearch
            label={label}
            clearable={false}
            className={CONTAINER_NAME}
            onInstrumentChange={handleInstrumentChange}
            instrument={instrument || null}
          />
        </FullWidthDiv>
      </PlaceholderWidgetContent>
    </LoadingDataContainer>
  );
};
const FullWidthDiv = styled('div')`
  width: 100%;
`;
