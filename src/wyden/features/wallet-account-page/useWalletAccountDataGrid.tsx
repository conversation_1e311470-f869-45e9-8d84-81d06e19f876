import { ERRORS, INFINITE_SCROLL_PAGE_SIZE } from '@wyden/constants';
import { useNetworkStore } from '@wyden/features/error-indicators/network-indicators/useNetworkStore';
import { useWalletAccountSearchLazyQuery } from '@wyden/services/graphql/generated/graphql';
import { GridApi, GridReadyEvent, IDatasource } from 'ag-grid-community';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

export const useWalletAccountDataGrid = () => {
  const searchValRef = useRef<string | null>('');
  const [searchVal, setSearchVal] = useState<string>('');
  const [searchInputVal, setSearchInputVal] = useState<string>('');
  const [lazyQuery, { error }] = useWalletAccountSearchLazyQuery();
  const [initialLoading, setInitialLoading] = useState(true);
  const [reloading, setReloading] = useState(false);
  const { upsertRequest } = useNetworkStore();
  const endCursor = useRef<string | null>();
  const sortingOrder = useRef<string | null>();
  const api = useRef<GridApi>();
  const [showNoRows, setShowNoRows] = useState(false);
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearchVal(e.target.value);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedHandleSearch = useCallback(
    debounce((e: React.ChangeEvent<HTMLInputElement>) => {
      handleSearch(e);
    }, 300),
    [],
  );

  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInputVal(e.target.value);
    debouncedHandleSearch(e);
  };

  useEffect(() => {
    setReloading(true);
    searchValRef.current = searchVal;

    // Preventing ugly rows flickering, we want to make sure overlay is rendered before operations
    setTimeout(() => {
      endCursor.current = undefined;
      api.current?.setRowCount(0);
      api.current?.purgeInfiniteCache();
    }, 0);
  }, [searchVal]);

  const datasource: IDatasource = useMemo(() => {
    return {
      getRows(params) {
        lazyQuery({
          variables: {
            search: {
              first: INFINITE_SCROLL_PAGE_SIZE,
              after: endCursor.current,
              name: searchValRef.current,
            },
          },
          fetchPolicy: 'network-only',
        }).then((res) => {
          if (res.error) {
            upsertRequest('walletAccountSearch', {
              pending: false,
              error: ERRORS.CLIENT_ERROR,
            });
          } else {
            const dataToLoad =
              res?.data?.walletAccountSearch?.edges?.map((edge) => edge.node) ?? [];
            const lastRow = res?.data?.walletAccountSearch?.pageInfo?.hasNextPage
              ? -1
              : params.startRow + dataToLoad.length;

            endCursor.current = res?.data?.walletAccountSearch?.pageInfo?.endCursor;

            if (params.startRow === 0 && dataToLoad.length === 0) {
              setShowNoRows(true);
            } else {
              setShowNoRows(false);
            }

            params.successCallback(dataToLoad, lastRow);

            setTimeout(() => {
              setInitialLoading(false);
              setReloading(false);
            }, 0);
          }
        });
      },
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const refresh = () => {
    setReloading(true);
    // Preventing ugly rows flickering, we want to make sure overlay is rendered before operations
    setTimeout(() => {
      sortingOrder.current = undefined;
      endCursor.current = undefined;
      api.current?.setRowCount(0);
      api.current?.purgeInfiniteCache();
    }, 0);
  };

  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      if (params.api) {
        params.api.setDatasource(datasource);
        api.current = params.api;
      }
    },
    [datasource],
  );

  return {
    onGridReady,
    refresh,
    initialLoading,
    error,
    showNoRows,
    reloading,
    searchVal: searchInputVal,
    handleSearch: handleSearchInput,
  };
};
