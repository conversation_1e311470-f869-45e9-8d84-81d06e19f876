package io.wyden.settlement.server.settlement.settlementconfiguration;

import io.wyden.settlement.client.settlementconfiguration.SettlementAccountConfiguration;
import io.wyden.settlement.client.settlementconfiguration.SettlementAccountConfigurationInput;
import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationInput;
import io.wyden.settlement.client.settlementconfiguration.SettlementConfigurationResponse;
import io.wyden.settlement.server.settlement.MutationSubmittedResponse;
import io.wyden.settlement.server.settlement.settlementconfiguration.schedule.SchedulerMessageCreator;
import io.wyden.settlement.server.settlement.settlementconfiguration.schedule.SettlementRunScheduler;

import jakarta.transaction.Transactional;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.Collection;
import java.util.List;

@Service
public class SettlementConfigurationService {

    private final SettlementConfigurationRepository configurationRepository;
    private final SettlementConfigurationValidator validator;

    private final SettlementRunScheduler scheduler;

    public SettlementConfigurationService(SettlementConfigurationRepository configurationRepository,
                                          SettlementConfigurationValidator validator,
                                          SettlementRunScheduler scheduler) {
        this.configurationRepository = configurationRepository;
        this.validator = validator;
        this.scheduler = scheduler;
    }

    public Collection<SettlementConfigurationResponse> getSettlementConfigurations(String targetZoneIdStr) {

        ZoneId targetZoneId;
        if (targetZoneIdStr != null) {
            SettlementConfigurationValidator.ValidationResult validationResult = validator.validateScheduleTimeZoneId(targetZoneIdStr);
            if (validationResult.status().equals(SettlementConfigurationValidator.ValidationStatus.FAILURE)) {
                throw new SettlementConfigurationException(String.join(", ", validationResult.errorMessages()));
            }
            targetZoneId = ZoneId.of(targetZoneIdStr);
        } else {
            targetZoneId = null;
        }

        return configurationRepository.findAll().stream()
            .map(entity -> SettlementConfigurationTimeZoneMapper.map(entity, targetZoneId))
            .map(entity -> new SettlementConfigurationResponse(entity.accountId(), entity.configuration()))
            .toList();
    }

    @Transactional
    public MutationSubmittedResponse settlementConfigurationUpdate(List<SettlementConfigurationInput> configurations) {
        validateAllConfigurations(configurations);

        scheduler.cancelAll();

        configurations.forEach(cfg -> {
            if (shouldBeDeleted(cfg)) {
                configurationRepository.deleteById(cfg.accountId());
            } else {
                SettlementAccountConfiguration mappedCfg = SettlementAccountConfigurationMapper.map(cfg.config());
                configurationRepository.save(new SettlementAccountConfigurationEntity(cfg.accountId(), mappedCfg));
            }
        });

        scheduler.scheduleAll();
        return new MutationSubmittedResponse("Settlement run configuration scheduled and saved");
    }

    private boolean shouldBeDeleted(SettlementConfigurationInput cfg) {
        return cfg.config() == null
            || CollectionUtils.isEmpty(cfg.config().schedule());
    }

    private void validateAllConfigurations(List<SettlementConfigurationInput> configurations) {
        configurations.forEach(input -> {
            SettlementConfigurationValidator.ValidationResult validationResult = validator.validate(input);
            if (validationResult.status().equals(SettlementConfigurationValidator.ValidationStatus.FAILURE)) {
                throw new SettlementConfigurationException(String.join(", ", validationResult.errorMessages()));
            }
        });
    }

}