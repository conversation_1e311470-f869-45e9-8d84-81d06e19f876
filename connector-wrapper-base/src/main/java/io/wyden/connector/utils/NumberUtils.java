package io.wyden.connector.utils;

import java.math.BigDecimal;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.EMPTY;

public final class NumberUtils {

    private NumberUtils() {
        // Emptry
    }

    public static String toString(BigDecimal decimal) {
        return Objects.nonNull(decimal) ? decimal.toString() : EMPTY;
    }

    public static String toString(Integer value) {
        return Objects.nonNull(value) ? value.toString() : EMPTY;
    }

    public static String toString(Double value) {
        return Objects.nonNull(value) ? value.toString() : EMPTY;
    }
}
