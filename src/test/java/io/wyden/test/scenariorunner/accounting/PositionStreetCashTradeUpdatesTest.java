package io.wyden.test.scenariorunner.accounting;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.booking.BookingModel.PositionResponse;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.test.scenariorunner.accounting.base.StreetAccountingBase;
import io.wyden.test.scenariorunner.assertion.accounting.PositionSoftAssert;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.refdata.Currency;
import io.wyden.test.scenariorunner.data.refdata.InstrumentId;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.integration.service.RateServiceClient;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.MathUtil;
import io.wyden.test.scenariorunner.util.MessageQueue;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

import static io.wyden.published.client.ClientSide.BUY;
import static io.wyden.published.client.ClientSide.SELL;
import static io.wyden.test.scenariorunner.assertion.accounting.PositionPredicates.pendingQtyIs;
import static io.wyden.test.scenariorunner.assertion.accounting.PositionPredicates.quantityIs;
import static io.wyden.test.scenariorunner.data.ErrorMsg.INSUFFICIENT_FUNDS_REJECT_MSG;
import static io.wyden.test.scenariorunner.data.infra.Epics.ACCOUNTING;
import static io.wyden.test.scenariorunner.integration.gqlclient.searchinput.PositionSearchInputs.bySymbolAndPortfolio;
import static io.wyden.test.scenariorunner.util.WaitUtils.awaitMsgFromQueue;
import static io.wyden.test.scenariorunner.util.WaitUtils.awaitNoMoreMsgFromQueue;

@Epic(ACCOUNTING)
@Fix
public class PositionStreetCashTradeUpdatesTest extends StreetAccountingBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(PositionStreetCashTradeUpdatesTest.class);

    private static final String BASE_CURRENCY = Currency.BASE.name();
    private static final String QUOTE_CURRENCY = Currency.USD.name();
    private static final String SYMBOL = InstrumentId.BASE_USD_FOREX_WYDENMOCK.getName();
    private final RateServiceClient rateServiceClient = new RateServiceClient();

    @Test
    void whenBankTraderPlaceBuyAndSellOrdersWithFeeThatAreFilledInSeveralFills_thenEachFillProduceBaseCurrencyPositionChange(ClientSession bank, ConnectorMockSession bankConn) {

        BigDecimal quantity = BigDecimal.valueOf(0.8);
        BigDecimal partialFillQuantity = BigDecimal.valueOf(0.5);
        BigDecimal buyFillPrice = BigDecimal.valueOf(30_000);
        BigDecimal buyFee = BigDecimal.valueOf(10.5);
        BigDecimal sellFillPrice = BigDecimal.valueOf(40_000);
        BigDecimal sellFee = BigDecimal.valueOf(12);

        MessageQueue<PositionResponse> positions = gqlActor.booking()
            .subscribePositionUpdatesWithQueue(bySymbolAndPortfolio(BASE_CURRENCY, portfolio));

        rateServiceClient.updatePrice(BASE_CURRENCY, QUOTE_CURRENCY, buyFillPrice);

        // give time for subscription to happen to overcome https://algotrader.atlassian.net/browse/AC-3208
        WaitUtils.justWait(Timeouts.JUST_WAIT);

        bank.sendDefaultLimitOrder(quantity.doubleValue(), SYMBOL, buyFillPrice.toString());
        bankConn.acceptNewOrder();
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // initial zero position may come first
        verifyBaseCurrencyReservation(awaitMsgFromQueue(positions, pendingQtyIs(quantity)), quantity);

        bankConn.fillPart(partialFillQuantity.doubleValue(), buyFillPrice.doubleValue(), buyFillPrice.doubleValue(), buyFee.doubleValue(), QUOTE_CURRENCY);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);

        verifyBaseCurrencyPositionAfterFill(awaitMsgFromQueue(positions, quantityIs(partialFillQuantity)), quantity, partialFillQuantity, buyFillPrice, buyFee);

        bankConn.fillFull(quantity.doubleValue(), quantity.subtract(partialFillQuantity).doubleValue(), buyFillPrice.doubleValue(), buyFillPrice.doubleValue());
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);

        verifyBaseCurrencyPositionAfterFill(awaitMsgFromQueue(positions, quantityIs(quantity)), quantity, quantity, buyFillPrice, buyFee);

        rateServiceClient.updatePrice(BASE_CURRENCY, QUOTE_CURRENCY, sellFillPrice);
        bank.sendMarketOrder(SELL, quantity.doubleValue(), SYMBOL, portfolio);
        bankConn.acceptNewOrder();
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        verifyBaseCurrencyPositionAfterPriceChange(awaitMsgFromQueue(positions, quantityIs(quantity)), quantity, buyFillPrice, buyFee, sellFillPrice);

        bankConn.fillPart(partialFillQuantity.doubleValue(), sellFillPrice.doubleValue(), sellFillPrice.doubleValue(), sellFee.doubleValue(), QUOTE_CURRENCY);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);

        BigDecimal firstPartialSellFillPositionQty = quantity.subtract(partialFillQuantity);
        verifyBaseCurrencyPositionAfterFirstSellPartialFill(awaitMsgFromQueue(positions, quantityIs(firstPartialSellFillPositionQty)), quantity, partialFillQuantity, buyFillPrice, sellFillPrice);

        bankConn.fillFull(quantity.doubleValue(), quantity.subtract(partialFillQuantity).doubleValue(), sellFillPrice.doubleValue(), sellFillPrice.doubleValue());
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);

        verifyBaseCurrencyPositionAfterSellFully(awaitMsgFromQueue(positions, quantityIs(BigDecimal.ZERO)));
    }

    @Step
    private void verifyBaseCurrencyReservation(PositionResponse position, BigDecimal reservationQty) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        LOGGER.info("Expected pendingQty: " + reservationQty);
        PositionSoftAssert.assertThat(position)
            .symbolIs(BASE_CURRENCY)
            .currencyIs(BASE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(reservationQty)
            .quantityIs(BigDecimal.ZERO)
            .notionalQuantityIs(BigDecimal.ZERO)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .grossRealizedPnlIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .grossUnrealizedPnlIs(BigDecimal.ZERO)
            .netCostIs(BigDecimal.ZERO)
            .grossCostIs(BigDecimal.ZERO)
            .marketValueIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ZERO)
            .grossAveragePriceIs(BigDecimal.ZERO)
            .assertAll();
    }

    @Step
    private void verifyBaseCurrencyPositionAfterFill(PositionResponse position, BigDecimal orderQty, BigDecimal fillQty, BigDecimal fillPrice, BigDecimal fee) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        LOGGER.info("Expected qty: " + fillQty);
        BigDecimal marketValue = fillQty.multiply(fillPrice);
        BigDecimal netCost = marketValue.add(fee);
        BigDecimal netAveragePrice = MathUtil.divide(netCost, fillQty);

        PositionSoftAssert.assertThat(position)
            .symbolIs(BASE_CURRENCY)
            .currencyIs(BASE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(QUOTE_CURRENCY)
            .pendingQuantityIs(orderQty)
            .quantityIs(fillQty)
            .notionalQuantityIs(fillQty)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .grossRealizedPnlIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(fee.negate())
            .grossUnrealizedPnlIs(BigDecimal.ZERO)
            .netCostIs(netCost)
            .grossCostIs(marketValue)
            .marketValueIs(marketValue)
            .netAveragePriceIs(netAveragePrice)
            .grossAveragePriceIs(fillPrice)
            .assertAll();
    }

    @Step
    private void verifyBaseCurrencyPositionAfterPriceChange(PositionResponse position, BigDecimal quantity, BigDecimal buyFillPrice, BigDecimal buyFee, BigDecimal sellFillPrice) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        LOGGER.info("Expected qty: " + quantity);
        BigDecimal marketValue = quantity.multiply(sellFillPrice);
        BigDecimal grossCost = quantity.multiply(buyFillPrice);
        BigDecimal netCost = grossCost.add(buyFee);
        BigDecimal netAveragePrice = MathUtil.divide(netCost, quantity);
        BigDecimal netUnrealizedPnl = marketValue.subtract(netCost);
        BigDecimal grossUnrealizedPnl = marketValue.subtract(grossCost);

        PositionSoftAssert.assertThat(position)
            .symbolIs(BASE_CURRENCY)
            .currencyIs(BASE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(BigDecimal.ZERO)
            .quantityIs(quantity)
            .notionalQuantityIs(quantity)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .grossRealizedPnlIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(netUnrealizedPnl)
            .grossUnrealizedPnlIs(grossUnrealizedPnl)
            .netCostIs(netCost)
            .grossCostIs(grossCost)
            .marketValueIs(marketValue)
            .netAveragePriceIs(netAveragePrice)
            .grossAveragePriceIs(buyFillPrice)
            .assertAll();
    }

    @Step
    private void verifyBaseCurrencyPositionAfterFirstSellPartialFill(PositionResponse position, BigDecimal quantity, BigDecimal partialFillQuantity, BigDecimal buyFillPrice, BigDecimal sellFillPrice) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        BigDecimal baseQuantity = quantity.subtract(partialFillQuantity);
        BigDecimal marketValue = baseQuantity.multiply(sellFillPrice);
        BigDecimal grossRealizedPnl = sellFillPrice.subtract(buyFillPrice).multiply(partialFillQuantity);
        BigDecimal grossCost = baseQuantity.multiply(buyFillPrice);
        BigDecimal netRealizedPnl = BigDecimal.valueOf(4981.4375);
        BigDecimal netCost = BigDecimal.valueOf(9003.9375);
        BigDecimal netUnrealizedPnl = BigDecimal.valueOf(2996.0625);
        BigDecimal grossUnrealizedPnl = marketValue.subtract(grossCost);
        BigDecimal netAveragePrice = BigDecimal.valueOf(30013.125);

        PositionSoftAssert.assertThat(position)
            .symbolIs(BASE_CURRENCY)
            .currencyIs(BASE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(BigDecimal.ZERO)
            .quantityIs(baseQuantity)
            .notionalQuantityIs(baseQuantity)
            .netRealizedPnlIs(netRealizedPnl)
            .grossRealizedPnlIs(grossRealizedPnl)
            .netUnrealizedPnlIs(netUnrealizedPnl)
            .grossUnrealizedPnlIs(grossUnrealizedPnl)
            .netCostIs(netCost)
            .grossCostIs(grossCost)
            .marketValueIs(marketValue)
            .netAveragePriceIs(netAveragePrice)
            .grossAveragePriceIs(buyFillPrice)
            .assertAll();
    }

    @Step
    private void verifyBaseCurrencyPositionAfterSellFully(PositionResponse position) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        BigDecimal netRealizedPnl = BigDecimal.valueOf(7977.5);
        BigDecimal grossRealizedPnl = BigDecimal.valueOf(8000.0);
        LOGGER.info("Expected netRealizedPnl: " + netRealizedPnl);
        LOGGER.info("Expected grossRealizedPnl: " + grossRealizedPnl);

        PositionSoftAssert.assertThat(position)
            .symbolIs(BASE_CURRENCY)
            .currencyIs(BASE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(BigDecimal.ZERO)
            .quantityIs(BigDecimal.ZERO)
            .notionalQuantityIs(BigDecimal.ZERO)
            .netRealizedPnlIs(netRealizedPnl)
            .grossRealizedPnlIs(grossRealizedPnl)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .grossUnrealizedPnlIs(BigDecimal.ZERO)
            .netCostIs(BigDecimal.ZERO)
            .grossCostIs(BigDecimal.ZERO)
            .marketValueIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ZERO)
            .grossAveragePriceIs(BigDecimal.ZERO)
            .assertAll();
    }

    @Test
    void whenBankTraderPlaceBuyAndSellOrdersWithFeeThatAreFilledInSeveralFills_thenEachFillProduceQuoteCurrencyPositionChange(ClientSession bank, ConnectorMockSession bankConn) {
        BigDecimal baseQuantity = BigDecimal.valueOf(0.8);
        BigDecimal partialFillQuantity = BigDecimal.valueOf(0.5);
        BigDecimal buyFillPrice = BigDecimal.valueOf(30_000);
        BigDecimal buyFee = BigDecimal.valueOf(10.5);
        BigDecimal sellFillPrice = BigDecimal.valueOf(40_000);
        BigDecimal sellFee = BigDecimal.valueOf(12);

        MessageQueue<PositionResponse> positions = gqlActor.booking()
            .subscribePositionUpdatesWithQueue(bySymbolAndPortfolio(QUOTE_CURRENCY, portfolio));

        // give time for subscription to happen to overcome https://algotrader.atlassian.net/browse/AC-3208
        WaitUtils.justWait(Timeouts.JUST_WAIT);

        rateServiceClient.updatePrice(BASE_CURRENCY, QUOTE_CURRENCY, buyFillPrice);

        bank.sendDefaultLimitOrder(baseQuantity.doubleValue(), SYMBOL, buyFillPrice.toString());
        bankConn.acceptNewOrder();
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // initial zero position may come first
        BigDecimal expectedPendingQty = buyFillPrice.multiply(baseQuantity).negate();
        verifyQuoteCurrencyReservation(awaitMsgFromQueue(positions, pendingQtyIs(expectedPendingQty)), expectedPendingQty);

        bankConn.fillPart(partialFillQuantity.doubleValue(), buyFillPrice.doubleValue(), buyFillPrice.doubleValue(), buyFee.doubleValue(), QUOTE_CURRENCY);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);

        BigDecimal expectedQtyAfterFirstBuyFill = buyFillPrice.multiply(partialFillQuantity).add(buyFee).negate();
        BigDecimal expectedPendingQtyAfterFirstBuyFill = baseQuantity.multiply(buyFillPrice).add(buyFee).negate();
        verifyQuoteCurrencyPositionAfterFirstBuyPartialFillWithFee(awaitMsgFromQueue(positions, quantityIs(expectedQtyAfterFirstBuyFill)), expectedQtyAfterFirstBuyFill, expectedPendingQtyAfterFirstBuyFill);

        bankConn.fillFull(baseQuantity.doubleValue(), baseQuantity.subtract(partialFillQuantity).doubleValue(), buyFillPrice.doubleValue(), buyFillPrice.doubleValue());
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);

        verifyQuoteCurrencyPositionAfterFullBuyFill(awaitMsgFromQueue(positions, quantityIs(expectedPendingQtyAfterFirstBuyFill)), expectedPendingQtyAfterFirstBuyFill, expectedPendingQtyAfterFirstBuyFill);

        bank.sendMarketOrder(SELL, baseQuantity.doubleValue(), SYMBOL, portfolio);
        bankConn.acceptNewOrder();
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        rateServiceClient.updatePrice(BASE_CURRENCY, QUOTE_CURRENCY, sellFillPrice);

        BigDecimal expectedPendingQtyAfterPriceChange = buyFee.negate();
        verifyQuoteCurrencyPositionAfterPriceChange(awaitMsgFromQueue(positions, pendingQtyIs(expectedPendingQtyAfterPriceChange)), expectedPendingQtyAfterFirstBuyFill, expectedPendingQtyAfterPriceChange);

        bankConn.fillPart(partialFillQuantity.doubleValue(), sellFillPrice.doubleValue(), sellFillPrice.doubleValue(), sellFee.doubleValue(), QUOTE_CURRENCY);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PARTIALLY_FILLED);

        BigDecimal spentOnBuy = baseQuantity.multiply(buyFillPrice);
        BigDecimal receivedOnSellPart = partialFillQuantity.multiply(sellFillPrice);
        BigDecimal expectedQtyAfterFirstSellPartialFill = spentOnBuy.subtract(receivedOnSellPart).add(buyFee).add(sellFee).negate();
        BigDecimal expectedPendingQtyAfterFirstSellPartialFill = buyFee.add(sellFee).negate();
        verifyQuoteCurrencyPositionAfterFirstSellPartialFill(awaitMsgFromQueue(positions, quantityIs(expectedQtyAfterFirstSellPartialFill)), expectedQtyAfterFirstSellPartialFill, expectedPendingQtyAfterFirstSellPartialFill);

        bankConn.fillFull(baseQuantity.doubleValue(), baseQuantity.subtract(partialFillQuantity).doubleValue(), sellFillPrice.doubleValue(), sellFillPrice.doubleValue());
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.FILLED);

        BigDecimal receivedOnSellFull = baseQuantity.multiply(sellFillPrice);
        BigDecimal expectedQtyAfterSellFillFully = receivedOnSellFull.subtract(spentOnBuy).subtract(buyFee).subtract(sellFee);
        verifyQuoteCurrencyPositionAfterSellFully(awaitMsgFromQueue(positions, quantityIs(expectedQtyAfterSellFillFully)), expectedQtyAfterSellFillFully);
    }

    @Step
    private void verifyQuoteCurrencyReservation(PositionResponse position, BigDecimal expectedPendingQty) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        LOGGER.info("Expected pendingQty: " + expectedPendingQty);
        PositionSoftAssert.assertThat(position)
            .symbolIs(QUOTE_CURRENCY)
            .currencyIs(QUOTE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(expectedPendingQty)
            .quantityIs(BigDecimal.ZERO)
            .notionalQuantityIs(BigDecimal.ZERO)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .grossRealizedPnlIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .grossUnrealizedPnlIs(BigDecimal.ZERO)
            .netCostIs(BigDecimal.ZERO)
            .grossCostIs(BigDecimal.ZERO)
            .marketValueIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ZERO)
            .grossAveragePriceIs(BigDecimal.ZERO)
            .assertAll();
    }

    @Step
    private void verifyInitialQuoteCurrencyPosition(PositionResponse position) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        PositionSoftAssert.assertThat(position)
            .symbolIs(QUOTE_CURRENCY)
            .currencyIs(QUOTE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(BigDecimal.ZERO)
            .quantityIs(BigDecimal.ZERO)
            .notionalQuantityIs(BigDecimal.ZERO)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .grossRealizedPnlIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .grossUnrealizedPnlIs(BigDecimal.ZERO)
            .netCostIs(BigDecimal.ZERO)
            .grossCostIs(BigDecimal.ZERO)
            .marketValueIs(BigDecimal.ZERO)
            .netAveragePriceIs(BigDecimal.ZERO)
            .grossAveragePriceIs(BigDecimal.ZERO)
            .assertAll();
    }

    @Step
    private void verifyQuoteCurrencyPositionAfterFirstBuyPartialFillWithFee(PositionResponse position, BigDecimal expectedQty, BigDecimal expectedPendingQty) {
        verifyQuoteCurrencyPosition(position, expectedQty, expectedPendingQty);
    }

    @Step
    private void verifyQuoteCurrencyPositionAfterFullBuyFill(PositionResponse position, BigDecimal expectedQty, BigDecimal expectedPendingQty) {
        verifyQuoteCurrencyPosition(position, expectedQty, expectedPendingQty);
    }

    @Step
    private void verifyQuoteCurrencyPositionAfterPriceChange(PositionResponse position, BigDecimal expectedQty, BigDecimal expectedPendingQty) {
        verifyQuoteCurrencyPositionAfterFullBuyFill(position, expectedQty, expectedPendingQty);
    }

    @Step
    private void verifyQuoteCurrencyPositionAfterFirstSellPartialFill(PositionResponse position, BigDecimal expectedQty, BigDecimal expectedPendingQty) {
        verifyQuoteCurrencyPosition(position, expectedQty, expectedPendingQty);
    }

    @Step
    private void verifyQuoteCurrencyPositionAfterSellFully(PositionResponse position, BigDecimal expectedQty) {
        verifyQuoteCurrencyPosition(position, expectedQty, expectedQty);
    }

    @Step
    private void verifyQuoteCurrencyPosition(PositionResponse position, BigDecimal expectedQty, BigDecimal expectedPendingQty) {
        LOGGER.info("Verify position with updatedAt: " + position.updatedAt());
        LOGGER.info("Expected quote qty: " + expectedQty);
        LOGGER.info("Expected quote pendingQty: " + expectedPendingQty);
        PositionSoftAssert.assertThat(position)
            .symbolIs(QUOTE_CURRENCY)
            .currencyIs(QUOTE_CURRENCY)
            .portfolioIdIs(portfolio)
            .bookingCurrencyIs(PortfolioFactory.DEFAULT_BOOKING_CURRENCY)
            .pendingQuantityIs(expectedPendingQty)
            .quantityIs(expectedQty)
            .notionalQuantityIs(expectedQty)
            .netRealizedPnlIs(BigDecimal.ZERO)
            .grossRealizedPnlIs(BigDecimal.ZERO)
            .netUnrealizedPnlIs(BigDecimal.ZERO)
            .grossUnrealizedPnlIs(BigDecimal.ZERO)
            .netCostIs(expectedQty)
            .grossCostIs(expectedQty)
            .marketValueIs(expectedQty)
            .netAveragePriceIs(BigDecimal.ONE)
            .grossAveragePriceIs(BigDecimal.ONE)
            .assertAll();
    }

    @Test
    void whenBankTraderPlaceOrderThatIsCancelledOrRejectedWithoutFills_thenOrdersNotAffectBaseCurrencyPosition(ClientSession bank, ConnectorMockSession bankConn) {

        BigDecimal quantity = BigDecimal.valueOf(0.8);

        MessageQueue<PositionResponse> positions = gqlActor.booking()
            .subscribePositionUpdatesWithQueue(bySymbolAndPortfolio(BASE_CURRENCY, portfolio));

        // give time for subscription to happen to overcome https://algotrader.atlassian.net/browse/AC-3208
        WaitUtils.justWait(Timeouts.JUST_WAIT);

        bank.sendMarketOrder(BUY, quantity.doubleValue(), SYMBOL, portfolio);
        bankConn.acceptNewOrder();
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // initial zero position may come first
        PositionResponse baseCurrencyReservationNewOrderToBeCancelled = awaitMsgFromQueue(positions, pendingQtyIs(quantity));
        verifyBaseCurrencyReservation(baseCurrencyReservationNewOrderToBeCancelled, quantity);

        bank.sendCancel();
        bankConn.acceptCancel(0);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_CANCELED, ClientOrderStatus.CANCELED);

        PositionResponse baseCurrencyReservationReleasedAfterOrderCancel = awaitMsgFromQueue(positions, pendingQtyIs(BigDecimal.ZERO));
        verifyBaseCurrencyReservation(baseCurrencyReservationReleasedAfterOrderCancel, BigDecimal.ZERO);

        bank.sendMarketOrder(BUY, quantity.doubleValue(), SYMBOL, portfolio);
        bankConn.rejectNewOrder(INSUFFICIENT_FUNDS_REJECT_MSG);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REJECTED, ClientOrderStatus.REJECTED);

        PositionResponse baseCurrencyReservationNewOrderToBeRejected = awaitMsgFromQueue(positions, pendingQtyIs(quantity));
        verifyBaseCurrencyReservation(baseCurrencyReservationNewOrderToBeRejected, quantity);

        PositionResponse baseCurrencyReservationReleaseAfterOrderReject = awaitMsgFromQueue(positions, pendingQtyIs(BigDecimal.ZERO));
        verifyBaseCurrencyReservation(baseCurrencyReservationReleaseAfterOrderReject, BigDecimal.ZERO);

        awaitNoMoreMsgFromQueue(positions);
    }

    @Test
    void whenBankTraderPlaceOrderThatIsCancelledOrRejectedWithoutFills_thenOrdersNotAffectQuoteCurrencyPosition(ClientSession bank, ConnectorMockSession bankConn) {
        BigDecimal quantity = BigDecimal.valueOf(0.8);
        BigDecimal buyFillPrice = BigDecimal.valueOf(30_000);

        MessageQueue<PositionResponse> positions = gqlActor.booking()
            .subscribePositionUpdatesWithQueue(bySymbolAndPortfolio(QUOTE_CURRENCY, portfolio));

        rateServiceClient.updatePrice(BASE_CURRENCY, QUOTE_CURRENCY, buyFillPrice);

        // give time for subscription to happen to overcome https://algotrader.atlassian.net/browse/AC-3208
        WaitUtils.justWait(Timeouts.JUST_WAIT);

        bank.sendMarketOrder(BUY, quantity.doubleValue(), SYMBOL, portfolio);
        bankConn.acceptNewOrder();
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        BigDecimal expectedPendingQty = buyFillPrice.multiply(quantity).negate();
        // initial zero position may come first
        PositionResponse quoteReservationNewOrderToBeCancelled = awaitMsgFromQueue(positions, pendingQtyIs(expectedPendingQty));
        verifyQuoteCurrencyReservation(quoteReservationNewOrderToBeCancelled, expectedPendingQty);

        bank.sendCancel();
        bankConn.acceptCancel(0);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_CANCELED, ClientOrderStatus.CANCELED);

        PositionResponse quoteReservationReleaseOrderCancelled = awaitMsgFromQueue(positions, pendingQtyIs(BigDecimal.ZERO));
        verifyInitialQuoteCurrencyPosition(quoteReservationReleaseOrderCancelled);

        bank.sendMarketOrder(BUY, quantity.doubleValue(), SYMBOL, portfolio);
        bankConn.rejectNewOrder(INSUFFICIENT_FUNDS_REJECT_MSG);
        bank.receiveExecutionReport();
        bank.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_REJECTED, ClientOrderStatus.REJECTED);

        PositionResponse quoteReservationNewOrderToBeRejected = awaitMsgFromQueue(positions, pendingQtyIs(expectedPendingQty));
        verifyQuoteCurrencyReservation(quoteReservationNewOrderToBeRejected, expectedPendingQty);

        PositionResponse quoteReservationReleaseAfterOrderRejected = awaitMsgFromQueue(positions, pendingQtyIs(BigDecimal.ZERO));
        verifyInitialQuoteCurrencyPosition(quoteReservationReleaseAfterOrderRejected);

        awaitNoMoreMsgFromQueue(positions);
    }
}
