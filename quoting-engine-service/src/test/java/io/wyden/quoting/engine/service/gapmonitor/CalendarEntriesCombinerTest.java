package io.wyden.quoting.engine.service.gapmonitor;

import io.wyden.published.brokerdesk.CalendarEntry;
import io.wyden.published.brokerdesk.QuotingSourceAccountConfig;
import io.wyden.published.common.DayOfTheWeek;
import io.wyden.published.common.TimeInAWeek;
import io.wyden.published.common.TimeUnit;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class CalendarEntriesCombinerTest {

    @Test
    public void testEmptySources() {
        List<QuotingSourceAccountConfig> sources = Collections.emptyList();
        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(sources);
        assertTrue(result.isEmpty(), "Result should be empty when no sources are provided");
    }

    @Test
    public void testNullSources() {
        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(null);
        assertTrue(result.isEmpty(), "Result should be empty when null sources are provided");
    }

    @Test
    public void testSingleSourceNoEntries() {
        QuotingSourceAccountConfig source = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, Collections.emptyList());

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Collections.singletonList(source));

        assertEquals(1, result.size(), "Should create a single entry covering the whole week");
        CalendarEntry entry = result.get(0);
        assertEquals(DayOfTheWeek.MONDAY, entry.getFrom().getDay());
        assertEquals("00:00", entry.getFrom().getTime());
        assertEquals(DayOfTheWeek.SUNDAY, entry.getTo().getDay());
        assertEquals("23:59", entry.getTo().getTime());
        assertEquals(60, entry.getQuoteTtl());
        assertEquals(TimeUnit.MINUTES, entry.getQuoteTtlUnit());
    }

    @Test
    public void testMultipleSourcesNoEntries() {
        QuotingSourceAccountConfig source1 = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, Collections.emptyList());
        QuotingSourceAccountConfig source2 = createSourceConfig(
            "source2", true, 30, TimeUnit.SECONDS, Collections.emptyList());

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Arrays.asList(source1, source2));

        assertEquals(1, result.size(), "Should create a single entry covering the whole week");
        CalendarEntry entry = result.get(0);
        assertEquals(30, entry.getQuoteTtl());
        assertEquals(TimeUnit.SECONDS, entry.getQuoteTtlUnit());
    }

    @Test
    public void testSingleSourceWithEntries() {
        List<CalendarEntry> entries = new ArrayList<>();
        entries.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "17:00",
            30, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Collections.singletonList(source));

        assertEquals(3, result.size(), "Should create 3 entries (before, during, after business hours)");

        // First entry (Monday 00:00 to Monday 09:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(0).getFrom().getDay());
        assertEquals("00:00", result.get(0).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(0).getTo().getDay());
        assertEquals("09:00", result.get(0).getTo().getTime());
        assertEquals(60, result.get(0).getQuoteTtl());
        assertEquals(TimeUnit.MINUTES, result.get(0).getQuoteTtlUnit());

        // Second entry (Monday 09:00 to Monday 17:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getFrom().getDay());
        assertEquals("09:00", result.get(1).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getTo().getDay());
        assertEquals("17:00", result.get(1).getTo().getTime());
        assertEquals(30, result.get(1).getQuoteTtl());
        assertEquals(TimeUnit.SECONDS, result.get(1).getQuoteTtlUnit());

        // Third entry (Monday 17:00 to Sunday 23:59)
        assertEquals(DayOfTheWeek.MONDAY, result.get(2).getFrom().getDay());
        assertEquals("17:00", result.get(2).getFrom().getTime());
        assertEquals(DayOfTheWeek.SUNDAY, result.get(2).getTo().getDay());
        assertEquals("23:59", result.get(2).getTo().getTime());
        assertEquals(60, result.get(2).getQuoteTtl());
        assertEquals(TimeUnit.MINUTES, result.get(2).getQuoteTtlUnit());
    }

    @Test
    public void testMultipleSourcesWithOverlappingEntries() {
        // Source 1 entries
        List<CalendarEntry> entries1 = new ArrayList<>();
        entries1.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "17:00",
            30, TimeUnit.SECONDS));

        // Source 2 entries
        List<CalendarEntry> entries2 = new ArrayList<>();
        entries2.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "12:00",
            DayOfTheWeek.MONDAY, "20:00",
            15, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source1 = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries1);
        QuotingSourceAccountConfig source2 = createSourceConfig(
            "source2", true, 45, TimeUnit.MINUTES, entries2);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Arrays.asList(source1, source2));

        assertEquals(4, result.size(), "Should create 4 entries");

        // First entry (Monday 00:00 to Monday 09:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(0).getFrom().getDay());
        assertEquals("00:00", result.get(0).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(0).getTo().getDay());
        assertEquals("09:00", result.get(0).getTo().getTime());
        assertEquals(45, result.get(0).getQuoteTtl());
        assertEquals(TimeUnit.MINUTES, result.get(0).getQuoteTtlUnit());

        // Second entry (Monday 09:00 to Monday 12:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getFrom().getDay());
        assertEquals("09:00", result.get(1).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getTo().getDay());
        assertEquals("12:00", result.get(1).getTo().getTime());
        assertEquals(30, result.get(1).getQuoteTtl());
        assertEquals(TimeUnit.SECONDS, result.get(1).getQuoteTtlUnit());

        // Third entry (Monday 12:00 to Monday 17:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(2).getFrom().getDay());
        assertEquals("12:00", result.get(2).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(2).getTo().getDay());
        assertEquals("20:00", result.get(2).getTo().getTime());
        assertEquals(15, result.get(2).getQuoteTtl());
        assertEquals(TimeUnit.SECONDS, result.get(2).getQuoteTtlUnit());

        // Fourth entry (Monday 17:00 to Sunday 23:59)
        assertEquals(DayOfTheWeek.MONDAY, result.get(3).getFrom().getDay());
        assertEquals("20:00", result.get(3).getFrom().getTime());
        assertEquals(DayOfTheWeek.SUNDAY, result.get(3).getTo().getDay());
        assertEquals("23:59", result.get(3).getTo().getTime());
        assertEquals(45, result.get(3).getQuoteTtl());
        assertEquals(TimeUnit.MINUTES, result.get(3).getQuoteTtlUnit());
    }

    @Test
    public void testMultipleDisjointEntries() {
        // Source 1 entries
        List<CalendarEntry> entries1 = new ArrayList<>();
        entries1.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "17:00",
            30, TimeUnit.SECONDS));

        entries1.add(createCalendarEntry(
            DayOfTheWeek.TUESDAY, "09:00",
            DayOfTheWeek.TUESDAY, "17:00",
            30, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries1);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Collections.singletonList(source));

        assertEquals(5, result.size(), "Should create 5 entries");

        // First entry (Monday 00:00 to Monday 09:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(0).getFrom().getDay());
        assertEquals("00:00", result.get(0).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(0).getTo().getDay());
        assertEquals("09:00", result.get(0).getTo().getTime());

        // Second entry (Monday 09:00 to Monday 17:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getFrom().getDay());
        assertEquals("09:00", result.get(1).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getTo().getDay());
        assertEquals("17:00", result.get(1).getTo().getTime());

        // Third entry (Monday 17:00 to Tuesday 09:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(2).getFrom().getDay());
        assertEquals("17:00", result.get(2).getFrom().getTime());
        assertEquals(DayOfTheWeek.TUESDAY, result.get(2).getTo().getDay());
        assertEquals("09:00", result.get(2).getTo().getTime());
    }

    @Test
    public void testMergingAdjacentIdenticalEntries() {
        // Source entries with same TTL but in adjacent time slots
        List<CalendarEntry> entries = new ArrayList<>();
        entries.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "12:00",
            30, TimeUnit.SECONDS));

        entries.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "12:00",
            DayOfTheWeek.MONDAY, "17:00",
            30, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Collections.singletonList(source));

        assertEquals(3, result.size(), "Should merge adjacent identical entries");

        // Second entry should be merged (Monday 09:00 to Monday 17:00)
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getFrom().getDay());
        assertEquals("09:00", result.get(1).getFrom().getTime());
        assertEquals(DayOfTheWeek.MONDAY, result.get(1).getTo().getDay());
        assertEquals("17:00", result.get(1).getTo().getTime());
        assertEquals(30, result.get(1).getQuoteTtl());
        assertEquals(TimeUnit.SECONDS, result.get(1).getQuoteTtlUnit());
    }

    @Test
    public void testNoMergingDifferentTTL() {
        // Source entries with different TTL in adjacent time slots
        List<CalendarEntry> entries = new ArrayList<>();
        entries.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "12:00",
            30, TimeUnit.SECONDS));

        entries.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "12:00",
            DayOfTheWeek.MONDAY, "17:00",
            20, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Collections.singletonList(source));

        assertEquals(4, result.size(), "Should not merge entries with different TTL");
    }

    @Test
    public void testComplexScenario() {
        // Source 1 entries
        List<CalendarEntry> entries1 = new ArrayList<>();
        entries1.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "17:00",
            15, TimeUnit.SECONDS));

        entries1.add(createCalendarEntry(
            DayOfTheWeek.FRIDAY, "09:00",
            DayOfTheWeek.FRIDAY, "17:00",
            15, TimeUnit.SECONDS));

        // Source 2 entries
        List<CalendarEntry> entries2 = new ArrayList<>();
        entries2.add(createCalendarEntry(
            DayOfTheWeek.WEDNESDAY, "09:00",
            DayOfTheWeek.WEDNESDAY, "17:00",
            20, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source1 = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries1);
        QuotingSourceAccountConfig source2 = createSourceConfig(
            "source2", true, 30, TimeUnit.MINUTES, entries2);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Arrays.asList(source1, source2));

        // We expect gaps to be filled with the smaller default TTL
        assertEquals(7, result.size(), "Should have 7 distinct time periods");

        // Verify that the appropriate default TTL is used for gaps
        for (CalendarEntry entry : result) {
            if (isDuringWorkingHours(entry)) {
                // During working hours should have specific TTLs
                assertNotEquals(30, entry.getQuoteTtl());
                assertNotEquals(TimeUnit.MINUTES, entry.getQuoteTtlUnit());
            } else {
                // Outside working hours should have the smaller default TTL
                assertEquals(30, entry.getQuoteTtl());
                assertEquals(TimeUnit.MINUTES, entry.getQuoteTtlUnit());
            }
        }
    }

    @Test
    public void testDifferentUnits() {
        // Source 1 entries
        List<CalendarEntry> entries1 = new ArrayList<>();
        entries1.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "09:00",
            DayOfTheWeek.MONDAY, "17:00",
            2, TimeUnit.MINUTES));

        // Source 2 entries
        List<CalendarEntry> entries2 = new ArrayList<>();
        entries2.add(createCalendarEntry(
            DayOfTheWeek.MONDAY, "12:00",
            DayOfTheWeek.MONDAY, "14:00",
            100, TimeUnit.SECONDS));

        QuotingSourceAccountConfig source1 = createSourceConfig(
            "source1", true, 60, TimeUnit.MINUTES, entries1);
        QuotingSourceAccountConfig source2 = createSourceConfig(
            "source2", true, 30, TimeUnit.MINUTES, entries2);

        List<CalendarEntry> result = CalendarEntriesCombiner.combineCalendarEntries(Arrays.asList(source1, source2));

        // Find the entry for Monday 12:00-14:00
        CalendarEntry overlap = null;
        for (CalendarEntry entry : result) {
            if (entry.getFrom().getDay() == DayOfTheWeek.MONDAY &&
                entry.getFrom().getTime().equals("12:00") &&
                entry.getTo().getDay() == DayOfTheWeek.MONDAY &&
                entry.getTo().getTime().equals("14:00")) {
                overlap = entry;
                break;
            }
        }

        assertNotNull(overlap, "Should have an entry for Monday 12:00-14:00");
        assertEquals(100, overlap.getQuoteTtl());
        assertEquals(TimeUnit.SECONDS, overlap.getQuoteTtlUnit());
    }

    // Helper methods to create test objects

    private QuotingSourceAccountConfig createSourceConfig(
        String venueAccountId,
        boolean droppingQuotesOnDisconnection,
        int defaultQuoteTtl,
        TimeUnit defaultQuoteTtlUnit,
        List<CalendarEntry> calendarEntries) {

        return QuotingSourceAccountConfig.newBuilder()
            .setVenueAccountId(venueAccountId)
            .setIsDroppingQuotesOnDisconnection(droppingQuotesOnDisconnection)
            .setDefaultQuoteTtl(defaultQuoteTtl)
            .setDefaultQuoteTtlUnit(defaultQuoteTtlUnit)
            .addAllCalendarEntries(calendarEntries)
            .build();
    }

    private CalendarEntry createCalendarEntry(
        DayOfTheWeek fromDay, String fromTime,
        DayOfTheWeek toDay, String toTime,
        int quoteTtl, TimeUnit quoteTtlUnit) {

        TimeInAWeek from = TimeInAWeek.newBuilder()
            .setDay(fromDay)
            .setTime(fromTime)
            .build();

        TimeInAWeek to = TimeInAWeek.newBuilder()
            .setDay(toDay)
            .setTime(toTime)
            .build();

        return CalendarEntry.newBuilder()
            .setFrom(from)
            .setTo(to)
            .setQuoteTtl(quoteTtl)
            .setQuoteTtlUnit(quoteTtlUnit)
            .build();
    }

    private boolean isDuringWorkingHours(CalendarEntry entry) {
        DayOfTheWeek day = entry.getFrom().getDay();
        String time = entry.getFrom().getTime();

        if (day == DayOfTheWeek.MONDAY &&
            ("09:00".equals(time) || "12:00".equals(time))) {
            return true;
        }

        if (day == DayOfTheWeek.WEDNESDAY && "09:00".equals(time)) {
            return true;
        }

        if (day == DayOfTheWeek.FRIDAY && "09:00".equals(time)) {
            return true;
        }

        return false;
    }
}