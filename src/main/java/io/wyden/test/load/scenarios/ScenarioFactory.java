package io.wyden.test.load.scenarios;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.test.load.AlarmClock;
import io.wyden.test.load.ExecutionStore;
import io.wyden.test.load.ScenarioRunner.ScenarioRunnerConfig;
import io.wyden.test.load.actors.cloud.ConfigurationService;
import io.wyden.test.load.scenarios.marketdata.StreetSideL1Stream;
import io.wyden.test.load.scenarios.marketdata.StreetSideL2Stream;
import io.wyden.test.load.scenarios.trade.ClientSideMarketOrder;
import io.wyden.test.load.scenarios.trade.ClobClientLimitOrder;
import io.wyden.test.load.scenarios.trade.ClobClientMarketOrder;
import io.wyden.test.load.scenarios.trade.StreetSideLimitOrder;
import io.wyden.test.load.scenarios.trade.StreetSideMarketOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.ParametersAreNonnullByDefault;

@ParametersAreNonnullByDefault
public class ScenarioFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(ScenarioFactory.class);

    private final ExecutionStore executionStore;
    private final AlarmClock alarmClock;
    private final MeterRegistry meterRegistry;
    private final ConfigurationService configService;

    public ScenarioFactory(ExecutionStore executionStore, AlarmClock alarmClock, MeterRegistry meterRegistry, ConfigurationService configService) {
        this.executionStore = executionStore;
        this.alarmClock = alarmClock;
        this.meterRegistry = meterRegistry;
        this.configService = configService;
    }

    public Scenario create(ScenarioRunnerConfig config) {
        String scenarioName = config.name();
        LOGGER.info("Creating scenario scenarioName={}", scenarioName);
        switch (scenarioName) {
            case "StreetSideMarketOrder":
                return new StreetSideMarketOrder(scenarioName, executionStore, alarmClock, meterRegistry);
            case "ClientSideMarketOrder":
                return new ClientSideMarketOrder(scenarioName, executionStore, alarmClock, meterRegistry);
            case "StreetSideLimitOrder":
                return new StreetSideLimitOrder(scenarioName, executionStore, alarmClock, meterRegistry);
            case "StreetSideL1Stream":
                return new StreetSideL1Stream(scenarioName, executionStore, alarmClock, meterRegistry);
            case "StreetSideL2Stream":
                return new StreetSideL2Stream(scenarioName, executionStore, alarmClock, meterRegistry);
            case "ClobClientMarketOrder":
                return new ClobClientMarketOrder(config, executionStore, alarmClock, meterRegistry, configService);
            case "ClobClientLimitOrder":
                return new ClobClientLimitOrder(config, executionStore, alarmClock, meterRegistry, configService);
        }
        LOGGER.error("Unknown scenario name {}", scenarioName);
        return null;
    }
}
