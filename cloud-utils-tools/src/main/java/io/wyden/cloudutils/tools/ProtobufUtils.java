package io.wyden.cloudutils.tools;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.TextFormat;
import com.google.protobuf.util.JsonFormat;

import java.math.BigDecimal;

public final class ProtobufUtils {

    private ProtobufUtils() {
        // Empty
    }

    public static String shortDebugString(MessageOrBuilder message) {
        return message != null ? TextFormat.shortDebugString(message) : "null";
    }

    public static String toProtoString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return null;
        }

        return bigDecimal.stripTrailingZeros().toPlainString();
    }

    public static String messageToString(Message message) {
        try {
            return JsonFormat.printer()
                .preservingProtoFieldNames()
                .sortingMapKeys()
                .print(message);
        } catch (InvalidProtocolBufferException ex) {
            throw new RuntimeException("Unable to convert protobuf to string", ex);
        }
    }

    public static Message stringToMessage(Message message, String updateJson) {
        try {
            Message.Builder builder = message.toBuilder();
            JsonFormat.parser()
                .ignoringUnknownFields()
                .merge(updateJson, builder);
            return builder.build();
        } catch (InvalidProtocolBufferException ex) {
            throw new RuntimeException("Unable to convert string to protobuf", ex);
        }
    }
}
