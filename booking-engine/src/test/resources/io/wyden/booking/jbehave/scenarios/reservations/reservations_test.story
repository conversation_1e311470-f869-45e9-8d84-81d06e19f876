Meta: Implements "Wyden Cloud - Accounting.xlsx", tab "Reservations"

Scenario: deposit EUR 10'000

Given market data prices:
| instrument | price     |
| EUR        | 1 EUR     |
| EUR        | 1 USD     |
| ETH        | 1_700 EUR |
| ETH        | 1_700 USD |

When Client 1 makes a reservation DEPOSIT-1 for a deposit of 10_000 EUR into Bitfinex account
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | RESERVATION     | EUR        | 10_000 | 1     | -   | -             | -            |
| -           | Bitfinex  | RESERVATION     | EUR        | 10_000 | 1     | -   | -             | -            |

And position net state change to:
| portfolioId | instrument | qty | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | -   | 10_000     | -                      | -                         | -    | -           | -           | -             | -            |
| Client 1    | ETH        | -   | -          | -                      | -                         | -    | -           | -           | -             |              |

When Client 1 deposit DEPOSIT-1 of 10_000 EUR into Bitfinex account is executed
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType     | instrument | qty     | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | DEPOSIT             | EUR        | 10_000  | 1     | -   | -             | 10_000       |
| Client 1    | -         | RESERVATION_RELEASE | EUR        | -10_000 | 1     | -   | 10_000        | 10_000       |
| -           | Bitfinex  | DEPOSIT             | EUR        | 10_000  | 1     | -   | -             | 10_000       |
| -           | Bitfinex  | RESERVATION_RELEASE | EUR        | -10_000 | 1     | -   | 10_000        | 10_000       |

And position net state change to:
| portfolioId | instrument | qty    | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | 10_000 | 10_000     | 10_000                 | 10_000                    | 10_000 | 10_000      | -           | -             | 1            |
| Client 1    | ETH        | -      | -          | -                      | -                         | -      | -           | -           | -             |              |

Scenario: trade 1'706 EUR
Given market data prices:
| instrument | price     |
| ETH        | 1_706 EUR |
| ETH        | 1_706 USD |

When Client 1 makes a reservation TRADE-A for a trade of 1 ETH/EUR on Bitfinex account
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | RESERVATION     | ETH        | 1      | 1_706 | -   | -             | -            |
| Client 1    | -         | RESERVATION     | EUR        | -1_706 | 1     | -   | 10_000        | 10_000       |
| -           | Bitfinex  | RESERVATION     | ETH        | 1      | 1_706 | -   | -             | -            |
| -           | Bitfinex  | RESERVATION     | EUR        | -1_706 | 1     | -   | 10_000        | 10_000       |

And position net state change to:
| portfolioId | instrument | qty    | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost   | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | 10_000 | 8_294      | 8_294                  | 8_294                     | 10_000 | 10_000      | -           | -             | 1            |
| Client 1    | ETH        | -      | 1          | -                      | -                         | -      | -           | -           | -             |              |

Given market data prices:
| instrument | price     |
| ETH        | 1_700 EUR |
| ETH        | 1_700 USD |

When Client 1 trade TRADE-A of 1 ETH/EUR with 1.7 EUR fee on Bitfinex account is executed
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType               | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | CASH_TRADE_CREDIT             | ETH        | 1      | 1_700 | 1.7 | -             | 1            |
| Client 1    | -         | RESERVATION_RELEASE           | ETH        | -1     | 1_700 | -   | 1             | 1            |
| Client 1    | -         | CASH_TRADE_DEBIT              | EUR        | -1_700 | 1     | -   | 10_000        | 8_300        |
| Client 1    | -         | TRADING_FEE                   | EUR        | -1.7   | 1     | -   | 8_300         | 8_298.3      |
| Client 1    | -         | RESERVATION_RELEASE           | EUR        | 1_700  | 1     | -   | 8_298.3       | 8_298.3      |
| Client 1    | -         | RESERVATION_RELEASE_REMAINING | EUR        | 6      | 1     | -   | 8_298.3       | 8_298.3      |
| -           | Bitfinex  | CASH_TRADE_CREDIT             | ETH        | 1      | 1_700 | 1.7 | -             | 1            |
| -           | Bitfinex  | RESERVATION_RELEASE           | ETH        | -1     | 1_700 | -   | 1             | 1            |
| -           | Bitfinex  | CASH_TRADE_DEBIT              | EUR        | -1_700 | 1     | -   | 10_000        | 8_300        |
| -           | Bitfinex  | TRADING_FEE                   | EUR        | -1.7   | 1     | -   | 8_300         | 8_298.3      |
| -           | Bitfinex  | RESERVATION_RELEASE           | EUR        | 1_700  | 1     | -   | 8_298.3       | 8_298.3      |
| -           | Bitfinex  | RESERVATION_RELEASE_REMAINING | EUR        | 6      | 1     | -   | 8_298.3       | 8_298.3      |

And position net state change to:
| portfolioId | instrument | qty     | pendingQty | availableForTradingQty | availableForWithdrawalQty | cost    | marketValue | realizedPnL | unrealizedPnL | averagePrice |
| Client 1    | EUR        | 8_298.3 | 8_298.3    | 8_298.3                | 8_298.3                   | 8_298.3 | 8_298.3     | -           | -             | 1            |
| Client 1    | ETH        | 1       | 1          | 1                      | -                         | 1_701.7 | 1_700       | -           | -1.70         | 1_701.7      |

Scenario: FIAT settlement


Scenario: withdraw 5'000 EUR
When Client 1 makes a reservation WITHDRAWAL-1 for a withdrawal of 5_000 EUR from Bitfinex account
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType        | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | WITHDRAWAL_RESERVATION | EUR        | -5_000 | 1     | -   | 8_298.3       | 8_298.3      |
| -           | Bitfinex  | WITHDRAWAL_RESERVATION | EUR        | -5_000 | 1     | -   | 8_298.3       | 8_298.3      |

When Client 1 withdrawal WITHDRAWAL-1 of 5_000 EUR from Bitfinex account is executed
Then ledger entries are created:
| portfolioId | accountId | ledgerEntryType     | instrument | qty    | price | fee | balanceBefore | balanceAfter |
| Client 1    | -         | WITHDRAWAL          | EUR        | -5_000 | 1     | -   | 8_298.3       | 3_298.3      |
| Client 1    | -         | RESERVATION_RELEASE | EUR        | 5_000  | 1     | -   | 3_298.3       | 3_298.3      |
| -           | Bitfinex  | WITHDRAWAL          | EUR        | -5_000 | 1     | -   | 8_298.3       | 3_298.3      |
| -           | Bitfinex  | RESERVATION_RELEASE | EUR        | 5_000  | 1     | -   | 3_298.3       | 3_298.3      |
