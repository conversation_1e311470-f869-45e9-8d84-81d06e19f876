package io.wyden.cloudutils.tools;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.assertj.core.api.Assertions.assertThat;

class MarkupUtilsTest {

    @ParameterizedTest
    @CsvSource({
        //high precision
        "0.0001, 100, 100.01, 6",
        "0.02, 189.123123123, 192.905586, 6",
        "0.03324, 24.789, 25.612987, 6",
        "0.266, 398.51, 504.51366, 6",
        "0.89, 1678.666, 3172.67874, 6",

        //low precision
        "0.0001, 100, 100.01, 2",
        "0.02, 189.123123123, 192.91, 2",
        "0.03324, 24.789, 25.62, 2",
        "0.266, 398.51, 504.52, 2",
        "0.89, 1678.666, 3172.68, 2",

        //0.5 precision
        "0.0001, 100, 100.5, 0.5",
        "0.02, 189.123123123, 193.0, 0.5"
    })
    void askWithMarkup(String markup, String price, String expected, BigDecimal precision) {
        BigDecimal adjustedPrice = MarkupUtils.askWithMarkup(bd(price), bd(markup), precision);
        assertThat(adjustedPrice).isEqualByComparingTo(bd(expected));
    }

    @ParameterizedTest
    @CsvSource({
        //high precision
        "0.0001, 100, 99.99, 6",
        "0.02, 189.123123123, 185.340660, 6",
        "0.03324, 24.789, 23.965013, 6",
        "0.266, 398.51, 292.506340, 6",
        "0.89, 1678.666, 184.653260, 6",

        //low precision
        "0.0001, 100, 99.99, 2",
        "0.02, 189.123123123, 185.34, 2",
        "0.03324, 24.789, 23.96, 2",
        "0.266, 398.51, 292.50, 2",
        "0.89, 1678.666, 184.65, 2",

        //0.5 precision
        "0.0001, 100, 99.5, 0.5",
        "0.02, 189.123123123, 185.0, 0.5"
    })
    void bidWithMarkupRemoved(String markup, String price, String expected, BigDecimal precision) {
        BigDecimal adjustedPrice = MarkupUtils.bidWithMarkupRemoved(bd(price), bd(markup), precision);
        assertThat(adjustedPrice).isEqualByComparingTo(bd(expected));
    }

    @ParameterizedTest
    @CsvSource({
        //high precision
        "0.0001, 100, 99.99, 6",
        "0.02, 189.123123123, 185.414826, 6",
        "0.03324, 24.789, 23.991521, 6",
        "0.266, 398.51, 314.778830, 6",
        "0.89, 1678.666, 888.183068, 6",

        //low precision
        "0.0001, 100, 99.99, 2",
        "0.02, 189.123123123, 185.41, 2",
        "0.03324, 24.789, 23.99, 2",
        "0.266, 398.51, 314.77, 2",
        "0.89, 1678.666, 888.18, 2",

        //0.5 precision
        "0.0001, 100, 99.5, 0.5",
        "0.02, 189.123123123, 185.0, 0.5"
    })
    void askWithMarkupRemoved(String markup, String price, String expected, BigDecimal precision) {
        BigDecimal adjustedPrice = MarkupUtils.askWithMarkupRemoved(bd(price), bd(markup), precision);
        assertThat(adjustedPrice).isEqualByComparingTo(bd(expected));
    }

    @ParameterizedTest
    @CsvSource({
        //high precision
        "0.0001, 100, 100.010002, 6",
        "0.02, 189.123123123, 192.982779, 6",
        "0.03324, 24.789, 25.641318, 6",
        "0.266, 398.51, 542.929156, 6",
        "0.89, 1678.666, 15260.600000, 6",

        //low precision
        "0.0001, 100, 100.02, 2",
        "0.02, 189.123123123, 192.99, 2",
        "0.03324, 24.789, 25.65, 2",
        "0.266, 398.51, 542.93, 2",
        "0.89, 1678.666, 15260.60, 2",

        //0.5 precision
        "0.0001, 100, 100.5, 0.5",
        "0.02, 189.123123123, 193.0, 0.5"
    })
    void bidWithMarkup(String markup, String price, String expected, BigDecimal precision) {
        BigDecimal adjustedPrice = MarkupUtils.bidWithMarkup(bd(price), bd(markup), precision);
        assertThat(adjustedPrice).isEqualByComparingTo(bd(expected));
    }
}