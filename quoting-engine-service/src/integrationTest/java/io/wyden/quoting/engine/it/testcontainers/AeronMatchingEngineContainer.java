package io.wyden.quoting.engine.it.testcontainers;

import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.InternetProtocol;
import org.testcontainers.containers.wait.strategy.LogMessageWaitStrategy;

import java.net.DatagramSocket;
import java.util.Map;

public class AeronMatchingEngineContainer extends GenericContainer<AeronMatchingEngineContainer> {

    public final int localPort = findFreeUdpPort();

    public AeronMatchingEngineContainer(String dockerImageName) {
        super(dockerImageName);
    }

    @Override
    protected void configure() {
        super.configure();
        setHostAccessible(true);
        addEnv("NODE_ID", "0");
        addEnv("CLUSTER_ADDRESSES", "0.0.0.0");
        addFixedExposedPort(localPort, 9002, InternetProtocol.UDP);
        setTmpFsMapping(Map.of("/dev/shm", "rw"));
        setWaitStrategy(new LogMessageWaitStrategy().withRegEx(".*Cluster Node is in role LEADER.*"));
    }

    public static int findFreeUdpPort() {
        try (DatagramSocket socket = new DatagramSocket(0)) { // 0 means that the OS will allocate an available port
            return socket.getLocalPort();
        } catch (Exception e) {
            throw new RuntimeException("Unable to find a free UDP port", e);
        }
    }
}
