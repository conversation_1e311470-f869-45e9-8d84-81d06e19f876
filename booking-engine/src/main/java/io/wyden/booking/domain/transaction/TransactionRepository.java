package io.wyden.booking.domain.transaction;

import io.wyden.booking.domain.common.JpaRepository;
import io.wyden.booking.domain.settlement.Settlement;
import io.wyden.booking.interfaces.rest.RequestModel;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    long countByProperties(RequestModel.AuthorizedTransactionSearch request);

    long countByProperties(RequestModel.AuthorizedTransactionSearch request, String after);

    Collection<Transaction> findByProperties(RequestModel.AuthorizedTransactionSearch request);

    void deleteByProperties(RequestModel.AuthorizedTransactionSearch request);

    Optional<Transaction> findByUuid(String transactionUuid);

    default Optional<Transaction> findByExecutionId(String executionId) {
        return findByExecutionIdIn(List.of(executionId)).stream()
            .findFirst();
    }

    Collection<Transaction> findByExecutionIdIn(Collection<String> executionIds);

    long countSettlements(RequestModel.SettlementSearch request);

    long countSettlements(RequestModel.SettlementSearch request, String after);

    Collection<Settlement> findSettlements(RequestModel.SettlementSearch request);
}
