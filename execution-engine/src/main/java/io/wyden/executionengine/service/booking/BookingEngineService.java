package io.wyden.executionengine.service.booking;

import io.wyden.executionengine.domain.ClientSideInstrument;
import io.wyden.executionengine.domain.InitialExposure;
import io.wyden.published.common.CursorConnection;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;

@Service
public class BookingEngineService {

    private static final Logger LOGGER = getLogger(BookingEngineService.class);

    private final BookingEngineHttpClient bookingEngineHttpClient;

    public BookingEngineService(BookingEngineHttpClient bookingEngineHttpClient) {
        this.bookingEngineHttpClient = bookingEngineHttpClient;
    }

    public InitialExposure getInitialExposure(ClientSideInstrument clientSideInstrument, String bankPortfolioId) {

        Instant start = Instant.now();
        LOGGER.info("Calling getInitialExposure for client-side instrument=({}) and portfolio=({})", clientSideInstrument.getInstrumentId(), bankPortfolioId);

        BookingModel.SearchInput searchInput = new BookingModel.SearchInput(
                List.of(clientSideInstrument.getBaseCurrency(), clientSideInstrument.getQuoteCurrency()),
                null,
                List.of(bankPortfolioId),
                null,
                null);

        CursorConnection positionConnection = bookingEngineHttpClient.getPositionsFlat(searchInput);
        BigDecimal baseCurrencyExposure = getCurrencyExposure(positionConnection, clientSideInstrument.getBaseCurrency());
        BigDecimal quoteCurrencyExposure = getCurrencyExposure(positionConnection, clientSideInstrument.getQuoteCurrency());
        InitialExposure exposure = new InitialExposure(baseCurrencyExposure, quoteCurrencyExposure);
        LOGGER.info("Initial exposure for clientSideInstrument: {}, baseCurrencyExposure: {}, quoteCurrencyExposure: {}", clientSideInstrument.getInstrumentId(), baseCurrencyExposure, quoteCurrencyExposure);

        Instant finish = Instant.now();
        long seconds = Duration.between(start, finish).toSeconds();
        LOGGER.info("Got getInitialExposure=({}, {}) for client-side instrument=({}) and portfolio=({}) in ({})s",
                exposure.baseCurrencyExposure(), exposure.quoteCurrencyExposure(), clientSideInstrument.getInstrumentId(), bankPortfolioId, seconds);

        return exposure;
    }

    public InitialExposure getInitialExposure(String asset, String bankPortfolioId) {
        Instant start = Instant.now();
        LOGGER.info("Calling getInitialExposure for asset=({}) and portfolio=({})", asset, bankPortfolioId);

        BookingModel.SearchInput searchInput = new BookingModel.SearchInput(
                List.of(asset),
                null,
                List.of(bankPortfolioId),
                null,
                null);

        CursorConnection positionConnection = bookingEngineHttpClient.getPositionsFlat(searchInput);
        BigDecimal baseCurrencyExposure = getCurrencyExposure(positionConnection, asset);
        BigDecimal quoteCurrencyExposure = BigDecimal.ZERO;
        InitialExposure exposure = new InitialExposure(baseCurrencyExposure, quoteCurrencyExposure);
        LOGGER.info("Initial exposure for asset={}, exposure={}", asset, baseCurrencyExposure);

        Instant finish = Instant.now();
        long seconds = Duration.between(start, finish).toSeconds();
        LOGGER.info("Got getInitialExposure=({}, {}) for asset=({}) and portfolio=({}) in ({})s",
                exposure.baseCurrencyExposure(), exposure.quoteCurrencyExposure(), asset, bankPortfolioId, seconds);

        return exposure;
    }

    @NotNull
    private static BigDecimal getCurrencyExposure(CursorConnection positionConnection, String currency) {
        return positionConnection.getEdgesList().stream()
                .filter(positionEdge -> positionEdge.getNode().getPosition().getSymbol().equals(currency))
                .map(matchingPositionEdge -> new BigDecimal(matchingPositionEdge.getNode().getPosition().getQuantity()))
                .findFirst().orElse(BigDecimal.ZERO);
    }
}
