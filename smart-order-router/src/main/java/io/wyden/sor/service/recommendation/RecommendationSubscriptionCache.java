package io.wyden.sor.service.recommendation;

import io.wyden.sor.service.fsm.OrderState;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Consumer;

@Component
public class RecommendationSubscriptionCache {

    private Set<RecommendationSubscription> recommendationSubscriptions = new HashSet<>();

    private Map<String, OrderState> recommendationSubscriptionIdToOrderStateMap = new HashMap<>();

    private Consumer<OrderState> orderStateUpdater;

    public synchronized void addNewSubscription(RecommendationSubscription recommendationSubscription) {
        recommendationSubscriptions.add(recommendationSubscription);
    }
    public synchronized void removeSubscriptionForId(String subscriptionId) {
        Optional<RecommendationSubscription> subscriptionForId = getSubscriptionForId(subscriptionId);
        subscriptionForId.ifPresent(subscription ->  {
            subscription.cancelSubscriptionTasks();
            recommendationSubscriptions.remove(subscription);
        });
    }

    public void refreshBestExecutionRequestForId(String subscriptionId, ScheduledFuture bestExecutionRequestRefresh) {
        Optional<RecommendationSubscription> subscriptionForId = getSubscriptionForId(subscriptionId);
        subscriptionForId.ifPresent(subscription -> subscription.refreshBestExecutionRequestRefresh(bestExecutionRequestRefresh));
    }

    public synchronized Optional<RecommendationSubscription> getSubscriptionForId(String subscriptionId) {
        return recommendationSubscriptions.stream().filter(subscription -> subscription.getRecommendationSubscriptionId().equals(subscriptionId)).findFirst();
    }

    public void updateRecommendationSubscriptionId(String oldRecommendationSubscriptionId, String newRecommendationSubscriptionId) {
        OrderState orderState = recommendationSubscriptionIdToOrderStateMap.remove(oldRecommendationSubscriptionId);

        if (orderState != null) {
            addNewRecommendationSubscriptionIdForOrderState(newRecommendationSubscriptionId, orderState);
        }
    }

    public void addNewRecommendationSubscriptionIdForOrderState(String recommendationSubscriptionId, OrderState orderState, Consumer<OrderState> orderStateUpdater) {
        this.orderStateUpdater = orderStateUpdater;
        addNewRecommendationSubscriptionIdForOrderState(recommendationSubscriptionId, orderState);
    }

    private void addNewRecommendationSubscriptionIdForOrderState(String recommendationSubscriptionId, OrderState orderState) {
        recommendationSubscriptionIdToOrderStateMap.put(recommendationSubscriptionId, orderState);
        orderState.setRecommendationSubscriptionId(recommendationSubscriptionId);
        orderStateUpdater.accept(orderState);
    }
}