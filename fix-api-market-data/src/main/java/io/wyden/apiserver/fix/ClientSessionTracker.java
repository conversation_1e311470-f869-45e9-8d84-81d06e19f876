package io.wyden.apiserver.fix;

import io.micrometer.core.instrument.Tags;
import io.wyden.apiserver.fix.common.fix.FixSessionWrapper;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.springframework.stereotype.Service;

@Service
// TODO mpd common?
public class ClientSessionTracker {

    public ClientSessionTracker(FixSessionWrapper fixSessionWrapper, Telemetry telemetry) {
        telemetry.getMeterRegistry().gauge("wyden.sessions.user", Tags.of("protocol", "fix", "server", "marketdata"), fixSessionWrapper, w -> w.getLoggedOnSessions().size());
    }
}
