package io.wyden.apiserver.fix.domain;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import jakarta.annotation.Nullable;
import org.apache.commons.lang3.tuple.Pair;
import quickfix.SessionID;
import quickfix.fix44.MarketDataRequest;

public class MarketDataSubscriptionsMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "fix-api-custom-ohlc-request-map_v0.1";

    public static IMap<String, Pair<MarketDataRequest, SessionID>> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
    }

    @Override
    protected void addMapConfig(Config config, @Nullable MapStoreConfig mapStoreConfig) {
        super.addMapConfig(config, mapStoreConfig);
    }
}
