import SyncIcon from '@mui/icons-material/Sync';
import { IconButton, Tooltip } from '@mui/material';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { FullScreenButton } from '@wyden/components/FullScreenButton';
import { TutorialButton } from '@wyden/features/tutorial/TutorialButton';
import { positionsQuerySteps } from '@wyden/features/tutorial/tutorialData';
import { Widget, WidgetBody } from '@wyden/features/widgets-renderer/widget/Widget';
import { WidgetHeader } from '@wyden/features/widgets-renderer/widget/WidgetHeader';
import { useAnchor } from '@wyden/hooks/useAnchor';
import { PositionResponseSchema } from '@wyden/services/graphql/generated/graphql';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { getBrowserLocale } from '../../helpers/locale';
import { ColumnManagerProvider } from '../grid/ColumnManager';
import { ColumnSettings } from '../grid/ColumnSettings';
import { InfiniteScrollGridWrapper } from '../grid/InfiniteScrollGridWrapper';
import { WIDGETS_NAMES } from '../widgets-renderer/widget-names';
import { PositionsFilters } from './PositionsFilters';
import { PositionsGrid } from './PositionsGrid';
import {
  actionsKey,
  defaultPositionsVisibleFields,
  positionsQueryDefinitions,
} from './positionsColumnDefinitions';
import { PositionsProvider, useLazyPositions } from './useLazyPositions';
import { usePositionFiltersActions } from './usePositionsFilters';
import { useSystemCurrency } from '@wyden/hooks/useSystemCurrency';
import { selectFocus, useFocusStore } from '../focus/useFocusStore';

export function PositionsQuery() {
  const { t } = useTranslation();
  const locale = getBrowserLocale();
  const CONTAINER_NAME = 'positions-query-widget';
  const { systemCurrency } = useSystemCurrency();
  const { portfolio, account } = useFocusStore(selectFocus);

  const { setInitialWidgetState } = usePositionFiltersActions();
  useEffect(() => {
    const portfolios = portfolio ? [portfolio] : [];
    const accounts = account?.venue ? [account] : []; // If account has a venue, it's an account
    const wallets = account?.venue === '' ? [account] : []; // If account has no venue, it's a wallet
    setInitialWidgetState(portfolios, accounts, wallets);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { onGridReady, refresh, initialLoading, error, showNoRows, reloading } = useLazyPositions({
    fetchPolicy: 'network-only',
  });

  const [loadedTime, setLoadedTime] = useState(new Date().toLocaleTimeString(locale));
  const columnSettingsModalProps = useAnchor('column-settings');

  return (
    <ColumnManagerProvider
      type={WIDGETS_NAMES.POSITIONS_QUERY}
      defaultVisibleFields={defaultPositionsVisibleFields}
      validationSchema={PositionResponseSchema().extend({
        [actionsKey]: z.string(),
      })}
      columnDefinitions={positionsQueryDefinitions(systemCurrency)}
    >
      <Widget actionsVisible={columnSettingsModalProps.open}>
        <PositionsProvider>
          <WidgetHeader>
            <WidgetHeaderSection>
              <Tooltip
                data-testid="loaded-section"
                arrow
                title={t('widget.loaded', {
                  time: loadedTime,
                })}
              >
                <IconButton
                  data-testid="sync-positions-data"
                  onClick={() => {
                    refresh();
                    setLoadedTime(new Date().toLocaleTimeString(locale));
                  }}
                >
                  <StyledSyncIcon />
                </IconButton>
              </Tooltip>
              <ColumnSettings {...columnSettingsModalProps} />
              <TutorialButton steps={positionsQuerySteps} />
              <FullScreenButton />
            </WidgetHeaderSection>
          </WidgetHeader>
          <StyledWidgetBody className={CONTAINER_NAME}>
            <PositionsFilters />
            <InfiniteScrollGridWrapper
              error={error}
              initialLoading={initialLoading}
              reloading={reloading}
              showNoRows={showNoRows}
              refresh={refresh}
              noRowsInfo={t('positions.noPositions')}
            >
              <PositionsGrid
                positionsSource={{ source: 'QUERY', datasource: undefined }}
                onGridReady={onGridReady}
                isDataLoading={!initialLoading && reloading}
              />
            </InfiniteScrollGridWrapper>
          </StyledWidgetBody>
        </PositionsProvider>
      </Widget>
    </ColumnManagerProvider>
  );
}

const StyledWidgetBody = styled(WidgetBody)`
  flex-direction: column;
`;
const WidgetHeaderSection = styled('div')`
  display: flex;
  align-items: center;
`;

const StyledSyncIcon = styled(SyncIcon)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;
