package io.wyden.quotingorderservice.service.fsm;

import io.wyden.published.oems.OemsOrderStatus;

/**
 * Terminal state, doesn't accept any messages, never transits to another state.
 * Emits CancelReject on cancel requests.
 * Order moves to Rejected state when order in PendingNew state
 * receives trade REJECTED ExecutionReport.
 */
class StatusRejected extends OrderStatus {

    private static final OrderStatus instance = new StatusRejected();

    static OrderStatus create() {
        return instance;
    }

    @Override
    Precedence getPrecedence() {
        return Precedence.REJECTED;
    }

    @Override
    boolean isTerminal() {
        return true;
    }

    @Override
    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.STATUS_REJECTED;
    }
}
