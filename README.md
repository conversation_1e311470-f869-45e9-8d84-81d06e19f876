
# Development

## Update bootstrap styles

To update bootstrap styles, perform:

```bash
cd bootstrap
npm install
grunt sass
```

Commit modified css files.

## Bootstrap dashboard development

To update bootstrap styles after each modification, perform:

```bash
cd bootstrap
npm install
grunt watch
```

# Design

## Basic Autohedger

Orders are placed only when:

 * threshold is reached,
 * pendingQty coming from PnL service is equal to zero,
 * no pending street side orders are present in EE orderbook.

This is the state, when both services are in sync regarding
bank exposure and should be good enough, provided street side orders execute quickly. If this won't be
the case, more aggressive method would be needed.
