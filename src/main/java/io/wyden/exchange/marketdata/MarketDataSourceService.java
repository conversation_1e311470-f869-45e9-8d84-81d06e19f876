package io.wyden.exchange.marketdata;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;

import io.wyden.exchange.providers.MarketDataProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

@Service
class MarketDataSourceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataSourceService.class);

    private final List<MarketDataProvider> marketDataProviders = new ArrayList<>();

    MarketDataSourceService(List<MarketDataProvider> marketDataProviders) {
        this.marketDataProviders.addAll(marketDataProviders);
    }

    public void subscribe(String symbol, MarketDataDepth marketDataDepth) {
        for (MarketDataProvider marketDataProvider : marketDataProviders) {
            if (marketDataProvider.isAvailable(symbol)) {
                marketDataProvider.subscribe(symbol, marketDataDepth);
                return;
            }
        }
        LOGGER.warn("Symbol {} not found.", symbol);
    }

    public void unsubscribe(String symbol, MarketDataDepth marketDataDepth) {
        for (MarketDataProvider marketDataProvider : marketDataProviders) {
            if (marketDataProvider.isAvailable(symbol)) {
                marketDataProvider.unsubscribe(symbol, marketDataDepth);
                return;
            }
        }
        LOGGER.warn("Symbol {} not found.", symbol);
    }

    public Flux<MarketDataEventDTO> getFlux(String symbol) {
        LOGGER.debug("Requested flux from {} for ticker {}", this.getClass().getSimpleName(), symbol);
        for (MarketDataProvider marketDataProvider : marketDataProviders) {
            if (marketDataProvider.isAvailable(symbol)) {
                return marketDataProvider.getFlux(symbol);
            }
        }
        LOGGER.warn("Symbol {} not found.", symbol);
        return Flux.empty();
    }

    public void reset() {
        marketDataProviders.forEach(MarketDataProvider::reset);
    }
}
