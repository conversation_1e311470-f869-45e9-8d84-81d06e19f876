---
status: ACCEPTED
---

# Use Keycloak as Authentication and Access Gateway as Authorization Service

## Context and Problem Statement

In previous version of Algotrader we used Keycloak as both Authorization and Authentication service, this produced multiple 
problems with syncing those two worlds. 

## Considered Options

* Keycloak Only
* HashiCorp Vault
* Access Gateway - new service to handle both responsibilities 
* Mixed solution

## Decision Outcome

In new Wyden platform we decided to use Keycloak for Authentication purpose only 
because it provides multiple needed features such as for example User Federation, which would require a lot of implementation. 

For Authorization purpose we decided to implement our own solution. 

To build full user context we will match those two worlds using username and groups from Keycloak's token and internal database fields
from Access Gateway microservice. 

To store permission mapping we decided to use PostgresSQL, as this mapping is strictly based on relations, and we 
needed database with reactive java driver. 

We also agreed on keeping sensitive data inside HashiCorp vault, as it seems it is the safest opensource solution for now. 