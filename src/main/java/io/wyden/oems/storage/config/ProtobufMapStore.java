package io.wyden.oems.storage.config;

import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.jdbc.core.JdbcTemplate;

public abstract class ProtobufMapStore<K, V extends Message> extends BaseMapStore<K, V> {

    private final Parser<V> parser;

    protected ProtobufMapStore(JdbcTemplate jdbc, Parser<V> parser, MeterRegistry meterRegistry) {
        super(jdbc, meterRegistry);
        this.parser = parser;
    }

    protected abstract String loadQuery();

    @Override
    protected V loadRecord(K key) {
        return jdbc.query(loadQuery(), new ProtobufRowMapper<>(parser), key).stream()
            .findFirst()
            .orElse(null);
    }
}
