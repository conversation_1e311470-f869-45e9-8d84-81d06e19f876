package io.wyden.test.scenariorunner.clob.trading;

import ch.algotrader.api.connector.marketdata.domain.OrderBookLevelDTO;

import io.qameta.allure.Epic;
import io.qameta.allure.Feature;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.venue.VenueRequest;
import io.wyden.test.scenariorunner.assertion.trading.ClientResponseSoftAssert;
import io.wyden.test.scenariorunner.assertion.trading.VenueRequestSoftAssert;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.trading.OrderBookPriceProvider;
import io.wyden.test.scenariorunner.data.trading.SimpleOrderFactory;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.eachtest.BrokerDeskConfigurationExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorConnectorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClobQuotingConfigExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClobRelatedInstrumentsExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ConnectorMockSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.FixActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.PortfolioExtension;
import io.wyden.test.scenariorunner.extension.eachtest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.VenueAccountExtension;
import io.wyden.test.scenariorunner.integration.fixclient.FixTradingActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.model.marketdata.L2Event;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import reactor.core.Disposable;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto.NOSTRO;
import static io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto.VOSTRO;
import static io.wyden.cloud.utils.test.TestUtils.bd;
import static io.wyden.test.scenariorunner.assertion.trading.ClientResponseSoftAssert.assertThat;
import static io.wyden.test.scenariorunner.data.ErrorMsg.INSUFFICIENT_FUNDS_REJECT_MSG;
import static io.wyden.test.scenariorunner.data.ErrorMsg.UNSOLICITED_CANCEL_CANNOT_HEDGE;
import static io.wyden.test.scenariorunner.data.brokerdesk.ExecutionConfigurationFactory.simpleAgencyExecutionConfigOnlyExchangeFee;
import static io.wyden.test.scenariorunner.data.brokerdesk.PricingConfigurationFactory.pricingConfigNoMarkup;
import static io.wyden.test.scenariorunner.data.quoting.QuotingConfigurationFactory.enabledConfigNoMarkup;
import static io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants.MOCK_VENUE;
import static io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants.WYDEN_EXCHANGE_ACCOUNT;
import static io.wyden.test.scenariorunner.data.refdata.VenueAccountFactory.randomVenueAccountName;
import static io.wyden.test.scenariorunner.data.infra.Epics.CLOB;
import static io.wyden.test.scenariorunner.data.infra.Features.EXTERNAL_MATCHING;
import static io.wyden.test.scenariorunner.data.infra.TestTags.GARANTI;
import static io.wyden.test.scenariorunner.extension.eachtest.ClobRelatedInstrumentsExtension.triangulated;
import static io.wyden.test.scenariorunner.tool.TestGarbageRemover.e2ePrefix;
import static io.wyden.test.scenariorunner.util.Randomizer.uniqueResourceName;

/**
 * Notes:
 * --
 * Instability risk: running same tests on one env w/o cleanup of instruments may lead to garbage order books in case same instrument will be picked occasionally.
 * --
 * Predefined order books are inserted into connector-wrapper-mock before each test.
 * --
 * Street instruments used for CLOB are hard-coded in connector-wrapper-mock MockReferenceDataController class
 * Client and Wyden Exchange instruments are created on the fly based on picked street instruments currencies see {@link ClobRelatedInstrumentsExtension}
 * --
 * Client CLOB CLOBnFXCONVn instrument constraints (same as Wyden Exchange instrument constraints):
 * [minQty=0.00001, maxQty=999999999, minQuoteQty=0.00001, maxQuoteQty=999999999, qtyIncr=0.000001, quoteQtyIncr=0.000001, minPrice=0.00001, maxPrice=999999999, priceIncr=0.00000001, minNotional=0.00001, contractSize=10, tradeable=true]
 * Street primary CLOBnFXPRIMn instrument constraints:
 * [minQty=0, maxQty=9999999, minQuoteQty=, maxQuoteQty=, qtyIncr=1E-8, quoteQtyIncr=, minPrice=1, maxPrice=9999999, priceIncr=0.01, minNotional=, contractSize=1, tradeable=true]
 */
@Epic(CLOB)
@Feature(EXTERNAL_MATCHING)
@Tag(GARANTI)
@ExtendWith(
    SecurityIntegratorExtension.class
)
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.BOOKING_ENGINE,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.RISK_ENGINE,
    Service.STORAGE,
    Service.BROKER_CONFIG_SERVICE,
    Service.AGENCY_TRADING_SERVICE,
    Service.PRICING_SERVICE,
    Service.QUOTING_ENGINE,
    Service.CLOB_GATEWAY
})
public class ClobTradingExternalMatchTriangulationTest {

    private static final String CLIENT_VENUE = e2ePrefix("client_venue");
    private static final String STREET_VENUE = MOCK_VENUE;

    private final String CLIENT_VENUE_ACCOUNT = randomVenueAccountName("bank-account");
    private final String STREET_VENUE_ACCOUNT = uniqueResourceName("street-account");

    private final String clientPortfolio = uniqueResourceName("client_portfolio");
    private final String bankPortfolio = uniqueResourceName("bank_portfolio");
    private final String clobPortfolio = uniqueResourceName("clob_portfolio");

    @RegisterExtension
    @Order(0)
    FixActorExtension clientActorExtension = new FixActorExtension();
    @RegisterExtension
    @Order(1)
    PortfolioExtension vostroPortfolioExtension = new PortfolioExtension(clientActorExtension.clientId(), VOSTRO, clientPortfolio);
    @RegisterExtension
    @Order(2)
    PortfolioExtension nostroPortfolioExtension = new PortfolioExtension(clientActorExtension.clientId(), NOSTRO, bankPortfolio);
    @RegisterExtension
    @Order(3)
    PortfolioExtension clobPortfolioExtension = new PortfolioExtension(clientActorExtension.clientId(), NOSTRO, clobPortfolio);
    @RegisterExtension
    @Order(4)
    VenueAccountExtension streetAccountExtension = new VenueAccountExtension(
        clientActorExtension.clientId(),
        Map.of(STREET_VENUE, List.of(STREET_VENUE_ACCOUNT))
    );
    @RegisterExtension
    @Order(5)
    VenueAccountExtension clientAccountExtension = new VenueAccountExtension(
        clientActorExtension.clientId(),
        Map.of(CLIENT_VENUE, List.of(CLIENT_VENUE_ACCOUNT))
    );
    @RegisterExtension
    @Order(6)
    ConnectorMockSessionExtension streetConnectorSessionExtension = new ConnectorMockSessionExtension(STREET_VENUE_ACCOUNT);
    @RegisterExtension
    @Order(7)
    ClientActorConnectorExtension clientActorConnectorExtension = new ClientActorConnectorExtension(clientActorExtension.clientId(), true);
    @RegisterExtension
    @Order(8)
    BrokerDeskConfigurationExtension configurationExtension = new BrokerDeskConfigurationExtension(clientActorExtension.clientId(), clientPortfolio,
        simpleAgencyExecutionConfigOnlyExchangeFee(bankPortfolio, WYDEN_EXCHANGE_ACCOUNT),
        pricingConfigNoMarkup(List.of(WYDEN_EXCHANGE_ACCOUNT)));
    @RegisterExtension
    @Order(9)
    ClobRelatedInstrumentsExtension clobInstrumentExtension =
        triangulated(clientActorExtension.clientId(), clientActorExtension.clientId(),
            CLIENT_VENUE, STREET_VENUE, STREET_VENUE);
    @RegisterExtension
    @Order(10)
    ClobQuotingConfigExtension clobQuotingConfigExtension =
        new ClobQuotingConfigExtension(clientActorExtension.clientId(), enabledConfigNoMarkup(clobPortfolio, List.of(STREET_VENUE_ACCOUNT)));

    FixTradingActor clientActor;

    private SimpleOrderFactory orderFactory;
    private final OrderBookPriceProvider priceProvider = new OrderBookPriceProvider();

    private final List<L2Event> clientOrderBooks = new ArrayList<>();

    private Disposable orderBookSubscription;

    private final BigDecimal topPrimaryBidPrice = bd(10000);
    private final BigDecimal topPrimaryAskPrice = bd(10100);
    private final BigDecimal topConversionBidPrice = bd(10);
    private final BigDecimal topConversionAskPrice = BigDecimal.valueOf(10.1);

    @BeforeEach
    void setupTriangulatedOrderBook(InstrumentResponseDto streetPrimaryInstrument, InstrumentResponseDto streetConversionInstrument, InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {
        this.clientActor = clientActorExtension.actor();

        this.orderFactory = new SimpleOrderFactory(clientActorExtension.clientId(), clientPortfolio, priceProvider);
        this.orderBookSubscription = clientActor.marketData().getClientSideL2Flux(clientPortfolio, clientInstrument.instrumentIdentifiers().instrumentId())
            .doOnNext(priceProvider)
            .subscribe(clientOrderBooks::add);

        mock.sendBidAskQuote(streetConversionInstrument.instrumentIdentifiers().adapterTicker(),
                topConversionBidPrice.doubleValue(), 1, topConversionAskPrice.doubleValue(), 1);
        // to ensure clob quotes generation, we need to have conversion quote before receiving street primary instrument order book
        WaitUtils.justWait(Timeouts.JUST_WAIT);

        mock.sendOrderBook(streetPrimaryInstrument.instrumentIdentifiers().adapterTicker(),
            //bids
            Map.of(
                topPrimaryBidPrice, new OrderBookLevelDTO(topPrimaryBidPrice, bd(1), 1),
                bd(9950), new OrderBookLevelDTO(bd(9950), bd(2), 1)),
            //asks
            Map.of(
                topPrimaryAskPrice, new OrderBookLevelDTO(topPrimaryAskPrice, bd(1), 1),
                bd(10150), new OrderBookLevelDTO(bd(10150), bd(2), 1)));

        WaitUtils.waitUntil("clob client order book appears", () ->
                clientOrderBooks.stream().anyMatch(e -> !e.asks().isEmpty() && !e.bids().isEmpty()),
            Timeouts.WAIT_FOR_CONDITION_M);
        // resulting client order book:
        // bids={100000.0=OrderBookLevelDTO[price=100000.0, amount=1.0, count=1], 99500.0=OrderBookLevelDTO[price=99500.0, amount=2.0, count=1]}, asks={102515.0=OrderBookLevelDTO[price=102515.0, amount=2.0, count=1], 102010.0=OrderBookLevelDTO[price=102010.0, amount=1.0, count=1]},
        // topBid=OrderBookLevelDTO[price=100000.0, amount=1.0, count=1], topAsk=OrderBookLevelDTO[price=102010.0, amount=1.0, count=1]]
    }

    @AfterEach
    void dispose() {
        if (orderBookSubscription != null) orderBookSubscription.dispose();
        clientActor.cancelAllOpenOrders();
    }

    @Test
    void clientFOKOrderCancelledAsCannotBeHedgedFully(InstrumentResponseDto clientInstrument) {

        List<OrderTypeAndSide> testParams = List.of(
            new OrderTypeAndSide(ClientOrderType.MARKET, ClientSide.BUY),
            new OrderTypeAndSide(ClientOrderType.LIMIT, ClientSide.SELL)
        );

        for (OrderTypeAndSide param : testParams) {
            fokCancelledCannotBeHedged(param.orderType(), param.side(), clientInstrument);
        }
    }

    @Step
    private void fokCancelledCannotBeHedged(ClientOrderType orderType, ClientSide side, InstrumentResponseDto clientInstrument) {
        // whole order book size = 3, so 4 doesn't fit in
        double quantityMoreThenBestOrderInBook = 4;
        ClientRequest request = orderFactory.defaultOrder(orderType, side, quantityMoreThenBestOrderInBook,
            clientInstrument.instrumentIdentifiers().instrumentId(), ClientTIF.FOK, clientPortfolio);
        clientActor.trading().sendOrder(request);

        ClientResponse clientERNew = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERNew)
            .isTypicalNewEROriginTo(request)
            .assertAll();

        ClientResponse clientERCancelled = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERCancelled)
            .isTypicalCancelledWithoutFillsEROriginTo(request)
            .assertAll();
    }

    @Test
    void clientIOCOrderPartiallyFilledAndCancelledAsAcceptedByExchangeButCannotBeHedgedFully(InstrumentResponseDto clientInstrument, ConnectorMockSession mock, InstrumentResponseDto streetPrimaryInstrument) {

        List<OrderTypeAndSide> testParams = List.of(
            new OrderTypeAndSide(ClientOrderType.MARKET, ClientSide.SELL),
            new OrderTypeAndSide(ClientOrderType.LIMIT, ClientSide.BUY)
        );

        for (OrderTypeAndSide param : testParams) {
            iocPartiallyFilledAndCancelled(param.orderType(), param.side(), clientInstrument, mock, streetPrimaryInstrument);
        }

    }

    private double getExpectedFillPrice(ClientSide side) {
        return side == ClientSide.BUY ?
            topPrimaryAskPrice.multiply(topConversionAskPrice).doubleValue() :
            topPrimaryBidPrice.multiply(topConversionBidPrice).doubleValue();
    }

    @Step
    private void iocPartiallyFilledAndCancelled(ClientOrderType orderType, ClientSide side, InstrumentResponseDto clientInstrument, ConnectorMockSession mock, InstrumentResponseDto streetPrimaryInstrument) {
        // best book order size = 1, so 2 doesn't fit in
        double qtyMoreThenBestOrderInBook = 2;
        ClientRequest request = orderFactory.orderToBeFilledImmediately(orderType, side, qtyMoreThenBestOrderInBook,
            clientInstrument.instrumentIdentifiers().instrumentId(), ClientTIF.IOC);
        clientActor.trading().sendOrder(request);

        mock.acceptNewOrder();
        VenueRequest hedgingOrder = mock.getOrder();
        VenueRequestSoftAssert.assertThat(hedgingOrder)
            .adapterTickerIs(streetPrimaryInstrument.instrumentIdentifiers().adapterTicker())
            .typicalClobHedgingOrder()
            .sideIs(side)
            .priceIs(ClientSide.SELL.equals(side) ? BigDecimal.valueOf(9_950) : BigDecimal.valueOf(10_100)) // should be best primary instrument price?
            .quantityIs(ClientSide.SELL.equals(side) ? BigDecimal.valueOf(0.922337) : BigDecimal.valueOf(0.904163))
            .assertAll();
        mock.fillFull(Double.parseDouble(hedgingOrder.getQuantity()), Double.parseDouble(hedgingOrder.getPrice()));

        ClientResponse clientERNew = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERNew)
            .isTypicalNewEROriginTo(request)
            .assertAll();

        double expectedFillPrice = getExpectedFillPrice(side);

        ClientResponse clientERPartiallyFilled = clientActor.awaitReceiveExecutionReport();
        double expectedPartialFillQty = Double.parseDouble(clientERPartiallyFilled.getLastQty());
        assertThat(clientERPartiallyFilled)
            .isTypicalFirstPartialFillEROriginTo(request, expectedFillPrice, expectedPartialFillQty, clientInstrument)
            .assertAll();

        ClientResponse clientERCancelled = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERCancelled)
            .isTypicalCancelledWithFilledQtyEROriginTo(request, expectedFillPrice, expectedPartialFillQty)
            .assertAll();
    }

    @Test
    void clientOrderCancelledWithHedgeFailedAsHedgingOrderRejectedFullyByExchangeAndOriginalOrderCannotBeRestated(InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {

        List<OrderTypeAndSideAndTif> testParams = List.of(
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.BUY, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.SELL, ClientTIF.FOK),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.GTC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.GTD)
        );

        for (OrderTypeAndSideAndTif param : testParams) {
            hedgeFailed(param.orderType(), param.side(), param.tif(), clientInstrument, mock);
        }
    }

    @Step
    private void hedgeFailed(ClientOrderType orderType, ClientSide side, ClientTIF tif, InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {
        double qtyCannotBeRestated = 0.001;
        ClientRequest request = orderFactory.orderToBeFilledImmediately(orderType, side, qtyCannotBeRestated,
            clientInstrument.instrumentIdentifiers().instrumentId(), tif);

        hedgeFailed(request, mock);
    }

    private void hedgeFailed(ClientRequest request, ConnectorMockSession mock) {
        clientActor.trading().sendOrder(request);

        mock.rejectNewOrder(INSUFFICIENT_FUNDS_REJECT_MSG);

        ClientResponse clientERNew = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERNew)
            .isTypicalNewEROriginTo(request)
            .assertAll();
        ClientResponse clientERCancelled = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERCancelled)
            .isTypicalCancelledWithoutFillsEROriginTo(request)
            .textIs(UNSOLICITED_CANCEL_CANNOT_HEDGE)
            .assertAll();
    }

    @Test
    void clientOrderFilledInOneFillAsExchangeProvidesSuitableOrderBookToHedge(InstrumentResponseDto clientInstrument, ConnectorMockSession mock, InstrumentResponseDto streetPrimaryInstrument) {

        List<OrderTypeAndSideAndTif> testParams = List.of(
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.SELL, ClientTIF.FOK),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.FOK),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.GTC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.GTD)
        );

        for (OrderTypeAndSideAndTif param : testParams) {
            filledInOneFill(param.orderType(), param.side(), param.tif(), clientInstrument, mock, streetPrimaryInstrument);
        }
    }

    @Step
    private void filledInOneFill(ClientOrderType orderType, ClientSide side, ClientTIF tif, InstrumentResponseDto clientInstrument, ConnectorMockSession mock, InstrumentResponseDto streetPrimaryInstrument) {
        // less than 1 which is size of best order in book
        double qtyToBeFilledImmediately = 0.1;
        ClientRequest request = orderFactory.orderToBeFilledImmediately(orderType, side, qtyToBeFilledImmediately,
            clientInstrument.instrumentIdentifiers().instrumentId(), tif);

        filledInOneFill(request, mock, qtyToBeFilledImmediately, streetPrimaryInstrument);
    }

    private void filledInOneFill(ClientRequest request, ConnectorMockSession mock, double expectedHedgingOrderQty, InstrumentResponseDto streetPrimaryInstrument) {
        clientActor.trading().sendOrder(request);

        mock.acceptNewOrder();
        VenueRequest hedgingOrder = mock.getOrder();
        VenueRequestSoftAssert.assertThat(hedgingOrder)
            .adapterTickerIs(streetPrimaryInstrument.instrumentIdentifiers().adapterTicker())
            .typicalClobHedgingOrder()
            .sideIs(request.getSide())
            .orderTypeIs(ClientOrderType.LIMIT)
            .quantityIs(BigDecimal.valueOf(expectedHedgingOrderQty))
            .priceIs(ClientSide.SELL.equals(request.getSide()) ? BigDecimal.valueOf(9_950) : BigDecimal.valueOf(10_100)) // should be best primary instrument price?
            .assertAll();
        mock.fillFull(Double.parseDouble(hedgingOrder.getQuantity()), Double.parseDouble(hedgingOrder.getPrice()));

        ClientResponse clientERNew = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERNew)
            .isTypicalNewEROriginTo(request)
            .assertAll();

        double expectedFillPrice = getExpectedFillPrice(request.getSide());

        ClientResponse clientFilledFullyER = clientActor.awaitReceiveExecutionReport();
        ClientResponseSoftAssert.assertThat(clientFilledFullyER)
            .isTypicalFullyFilledInOneFillEROriginTo(request, expectedFillPrice)
            .assertAll();
    }

    @Test
    void clientCashOrderFilledInOneFillAsExchangeProvidesSuitableOrderBookToHedge(InstrumentResponseDto clientInstrument, ConnectorMockSession mock, InstrumentResponseDto streetPrimaryInstrument) {

        List<OrderTypeAndSideAndTif> testParams = List.of(
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.SELL, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.GTC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.GTD),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.BUY, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.GTC)
        );

        for (OrderTypeAndSideAndTif param : testParams) {
            cashOrderFilledInOneFill(param.orderType(), param.side(), param.tif(), clientInstrument, mock, streetPrimaryInstrument);
        }
    }

    @Step
    private void cashOrderFilledInOneFill(ClientOrderType orderType, ClientSide side, ClientTIF tif, InstrumentResponseDto clientInstrument, ConnectorMockSession mock, InstrumentResponseDto streetPrimaryInstrument) {
        // SELL cash qty 1000 / 100000  = 0.001 resulting hedging qty
        // BUY cash qty 1020.1 / 102010 = 0.001 resulting hedging qty
        double qtyToBeFilledImmediately = ClientSide.SELL.equals(side) ? 1000 : 1020.1;
        double expectedHedgingQty = 0.01;
        ClientRequest request = orderFactory.cashOrderToBeFilledImmediately(orderType, side, qtyToBeFilledImmediately,
            clientInstrument.instrumentIdentifiers().instrumentId(), tif, clientInstrument.baseInstrument().quoteCurrency());

        filledInOneFill(request, mock, expectedHedgingQty, streetPrimaryInstrument);
    }

    @Test
    void clientCashOrderCancelledWithHedgeFailedAsHedgingOrderRejectedFullyByExchangeAndOriginalOrderCannotBeRestated(InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {

        List<OrderTypeAndSideAndTif> testParams = List.of(
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.SELL, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.GTC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.SELL, ClientTIF.GTD)
        );

        for (OrderTypeAndSideAndTif param : testParams) {
            cashOrderHedgeFailed(param.orderType(), param.side(), param.tif(), clientInstrument, mock);
        }
    }

    private void cashOrderHedgeFailed(ClientOrderType orderType, ClientSide side, ClientTIF tif, InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {
        // qty 10 * ~ 10000 price
        double nonRestatableQty = 10;
        ClientRequest request = orderFactory.cashOrderToBeFilledImmediately(orderType, side, nonRestatableQty,
            clientInstrument.instrumentIdentifiers().instrumentId(), tif, clientInstrument.baseInstrument().quoteCurrency());

        hedgeFailed(request, mock);
    }

    @Test
    void clientCashOrderRestatedOnQtyIncrAndFilledInOneFill(InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {

        List<OrderTypeAndSideAndTif> testParams = List.of(
            new OrderTypeAndSideAndTif(ClientOrderType.MARKET, ClientSide.BUY, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.IOC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.GTC),
            new OrderTypeAndSideAndTif(ClientOrderType.LIMIT, ClientSide.BUY, ClientTIF.GTD)
        );

        for (OrderTypeAndSideAndTif param : testParams) {
            cashCashOrderRestatedOnQtyIncrAndFilledInOneFill(param.orderType(), param.side(), param.tif(), clientInstrument, mock);
        }
    }

    @Step
    private void cashCashOrderRestatedOnQtyIncrAndFilledInOneFill(ClientOrderType orderType, ClientSide side, ClientTIF tif, InstrumentResponseDto clientInstrument, ConnectorMockSession mock) {
        // 102010 best available price for 1 base to buy
        // 1000 / 102010 = 0.0098029604940692 of base that we need to buy
        // order qty should be reduced to 0.009802 (hedging order qty) according to client instrument constraint 0.000001
        // 0.009802 * 102010 = 999.90202 expected cash order quantity
        double orderQtyToBeRestated = 1000;
        double expectedHedgingOrderQty = 0.009802;
        double expectedRestatedQty = 999.90202;
        String expectedText = "Unsolicited reduce, CLOB qty increment";

        ClientRequest request = orderFactory.cashOrderToBeFilledImmediately(orderType, side, orderQtyToBeRestated,
            clientInstrument.instrumentIdentifiers().instrumentId(), tif, clientInstrument.baseInstrument().quoteCurrency());

        clientActor.trading().sendOrder(request);

        mock.acceptNewOrder();
        VenueRequest venueOrder = mock.getOrder();
        VenueRequestSoftAssert.assertThat(venueOrder)
            .typicalClobHedgingOrder()
            .sideIs(request.getSide())
            .orderTypeIs(ClientOrderType.LIMIT)
            .quantityIs(BigDecimal.valueOf(expectedHedgingOrderQty))
            .priceIs(topPrimaryAskPrice) // best primary ask price
            .assertAll();
        mock.fillFull(Double.parseDouble(venueOrder.getQuantity()), Double.parseDouble(venueOrder.getPrice()));

        ClientResponse clientERNew = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERNew)
            .isTypicalNewEROriginTo(request)
            .assertAll();

        ClientResponse clientERRestated = clientActor.awaitReceiveExecutionReport();
        assertThat(clientERRestated)
            .isTypicalRestatedNewER(request, expectedRestatedQty, expectedText)
            .assertAll();

        ClientResponse clientERFilled = clientActor.awaitReceiveExecutionReport();
        double expectedFillPrice = getExpectedFillPrice(request.getSide());
        assertThat(clientERFilled)
            .isTypicalRestatedFullyFilledInOneFillER(request, expectedRestatedQty, expectedFillPrice)
            .assertAll();
    }

    private record OrderTypeAndSide(ClientOrderType orderType, ClientSide side) {

    }

    private record OrderTypeAndSideAndTif(ClientOrderType orderType, ClientSide side, ClientTIF tif) {

    }

}
