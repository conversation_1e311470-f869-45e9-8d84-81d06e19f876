spring.application.name=execution-engine
spring.profiles.active=prod

server.port=8097

# Hazelcast setup:

# comma-separated list of hz member hosts
hz.addressList = localhost
hz.outboundPortDefinition =

# RabbitMQ setup:

rabbitmq.username = execution-engine
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
rabbitmq.port = 5672

# RabbitMQ destinations - inbound requests:

rabbitmq.trading-execution-engine-queue = execution-engine-queue.trading.ALL

rabbitmq.booking-engine-execution-engine-queue.position-snapshot = execution-engine-queue.booking.%s.POSITION_SNAPSHOT

rabbitmq.reference-data-execution-engine-queue.instrument-change-event = execution-engine-queue.reference-data.INSTRUMENT-CHANGE-EVENT

#Used for setting up the bank portfolio id and attaching Rabbit consumer for position snapshots on this portfolio
# Direct dependencies:
booking.engine.host = http://localhost:8100
booking.engine.positions.url = ${booking.engine.host}/positions
reference.data.host = http://localhost:8098
reference.data.get.portfolios.by.id.url = ${reference.data.host}/portfolios

# Docs:

springdoc.packagesToScan=io.wyden.executionengine.service.web
springdoc.pathsToMatch=/v3, /api/**
springdoc.swagger-ui.enabled=false

tracing.collector.endpoint=http://localhost:4317
management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=execution-engine

quoting.bank-venue-account = Bank
quoting.allowedVenues = {BitMEX:'BITMEX-testnet1',Simulator:'simulator',WydenMock:'WydenMock_Account_FirstSirian'}
quoting.marketdata.client.queuePrefix = execution-engine-queue.market-data.EVENT
