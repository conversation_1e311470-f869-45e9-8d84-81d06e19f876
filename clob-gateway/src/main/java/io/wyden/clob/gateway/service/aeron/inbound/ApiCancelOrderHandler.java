package io.wyden.clob.gateway.service.aeron.inbound;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.fsm.OrderService;
import io.wyden.clob.gateway.utils.SbeUtils;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.sbe.ApiCancelOrderDecoder;
import io.wyden.sbe.CommandResultCode;
import io.wyden.sbe.MessageHeaderDecoder;
import org.agrona.DirectBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static io.wyden.clob.gateway.infrastructure.telemetry.TelemetryConfiguration.COUNT_INBOUND_AERON_EVENT;
import static io.wyden.clob.gateway.infrastructure.telemetry.TelemetryConfiguration.MATCHING_ENGINE_SYMBOL;

@Component
class ApiCancelOrderHandler implements AeronMessageHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiCancelOrderHandler.class);

    private final ApiCancelOrderDecoder cancelOrder = new ApiCancelOrderDecoder();

    private final OrderService orderService;
    private final MeterRegistry meterRegistry;

    public ApiCancelOrderHandler(@Lazy OrderService orderService,
                                 Telemetry telemetry) {
        this.orderService = orderService;
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public int templateId() {
        return cancelOrder.sbeTemplateId();
    }

    @Override
    public void handle(DirectBuffer buffer, int offset, MessageHeaderDecoder headerDecoder) {
        cancelOrder.wrapAndApplyHeader(buffer, offset, headerDecoder);
        updateMetrics(cancelOrder);
        String orderUuid = SbeUtils.toUuidString(cancelOrder.orderUuid());
        LOGGER.info("Received ApiCancelOrder {}, orderUuid={}", cancelOrder, orderUuid);

        boolean isSuccess = cancelOrder.result() == CommandResultCode.SUCCESS;
        orderService.onCancelOrder(new MatchingEngineEvent.CancelOrder(
            cancelOrder.orderId(),
            orderUuid,
            isSuccess,
            Boolean.TRUE.equals(SbeUtils.toBoolean(cancelOrder.orderExpired())),
            cancelOrder.result().name()
        ));
    }

    private void updateMetrics(ApiCancelOrderDecoder apiCancelOrderDecoder) {
        try {
            this.meterRegistry.counter(COUNT_INBOUND_AERON_EVENT,
                MATCHING_ENGINE_SYMBOL, String.valueOf(apiCancelOrderDecoder.symbol()),
                "execType", "cancel"
            ).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}