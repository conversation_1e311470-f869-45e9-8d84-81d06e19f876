mutation ($id: ID!, $request: ExecutionConfigurationInput!) {
    updatePortfolioExecutionConfiguration(
        portfolioId: $id
        request: $request
    ) {
        resultsPerInstrument {
            instrumentId
            isValid
            preventsTrading
            preventsMarketData
            errors {
                errorMessage
                preventsTrading
                preventsMarketData
            }
        }
    }
}
