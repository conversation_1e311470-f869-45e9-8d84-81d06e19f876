package io.wyden.agencytrading.web;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.agencytrading.domain.map.AgencyTradingOrderStateMapConfig;
import io.wyden.agencytrading.model.AgencyTradingOrderState;
import org.springframework.stereotype.Service;

@Service
public class HazelcastAgencyTradingOrderStateMapService extends AbstractHazelcastMapService<String, AgencyTradingOrderState> {

    public HazelcastAgencyTradingOrderStateMapService(HazelcastInstance hazelcastInstance) {
        super(AgencyTradingOrderStateMapConfig.getMap(hazelcastInstance));
    }
}
