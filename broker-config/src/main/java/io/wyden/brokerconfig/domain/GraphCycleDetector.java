package io.wyden.brokerconfig.domain;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Component
public class GraphCycleDetector<T> {
    private final Map<T, Set<T>> graph = new HashMap<>();

    public Optional<List<T>> createsCycle(T from, List<T> toList) {
        Map<T, Set<T>> tempGraph = copyGraphWithEdge(from, toList);
        return findCycle(tempGraph);
    }

    public void addToGraph(T from, List<T> toList) {
        for (T to : toList) {
            if (!to.equals(from)) {
                graph.computeIfAbsent(from, k -> new HashSet<>()).add(to);
            }
        }
    }

    private Map<T, Set<T>> copyGraphWithEdge(T from, List<T> toList) {
        Map<T, Set<T>> tempGraph = new HashMap<>();
        for (Map.Entry<T, Set<T>> entry : graph.entrySet()) {
            tempGraph.put(entry.getKey(), new HashSet<>(entry.getValue()));
        }
        for (T to : toList) {
            if (!to.equals(from)) {
                tempGraph.computeIfAbsent(from, k -> new HashSet<>()).add(to);
            }
        }
        return tempGraph;
    }

    private Optional<List<T>> findCycle(Map<T, Set<T>> graph) {
        Set<T> visited = new HashSet<>();
        Set<T> recStack = new HashSet<>();

        for (T node : graph.keySet()) {
            List<T> path = new ArrayList<>();
            Optional<List<T>> result = dfs(node, graph, visited, recStack, path);
            if (result.isPresent()) {
                return result;
            }
        }
        return Optional.empty();
    }

    private Optional<List<T>> dfs(T node,
                                              Map<T, Set<T>> graph,
                                              Set<T> visited,
                                              Set<T> recStack,
                                              List<T> path) {
        if (recStack.contains(node)) {
            int cycleStartIndex = path.indexOf(node);
            return Optional.of(new ArrayList<>(path.subList(cycleStartIndex, path.size())));
        }

        if (visited.contains(node)) {
            return Optional.empty();
        }

        visited.add(node);
        recStack.add(node);
        path.add(node);

        for (T neighbor : graph.getOrDefault(node, Collections.emptySet())) {
            Optional<List<T>> result = dfs(neighbor, graph, visited, recStack, path);
            if (result.isPresent()) {
                return result;
            }
        }

        recStack.remove(node);
        path.remove(path.size() - 1);

        return Optional.empty();
    }
}
