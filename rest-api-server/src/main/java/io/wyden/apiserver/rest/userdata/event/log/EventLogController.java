package io.wyden.apiserver.rest.userdata.event.log;

import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import org.reactivestreams.Publisher;
import org.springframework.graphql.data.method.annotation.SubscriptionMapping;
import org.springframework.stereotype.Controller;

import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Controller
public class EventLogController {

    private final EventLogService eventLogService;

    public EventLogController(EventLogService eventLogService) {
        this.eventLogService = eventLogService;
    }

    @SubscriptionMapping("eventsLogs")
    public Publisher<EventLogResponseDto> subscribeEventLogEvents(WydenAuthenticationToken token) {
        return eventLogService.subscribeEventLogEvents(user(token));
    }
}
