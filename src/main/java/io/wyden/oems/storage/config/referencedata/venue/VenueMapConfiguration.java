package io.wyden.oems.storage.config.referencedata.venue;

import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.config.StoredMapConfigBean;
import io.wyden.oems.storage.web.StringProtobufMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import io.wyden.published.referencedata.Venue;
import io.wyden.referencedata.domain.VenueMapConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static io.wyden.oems.storage.config.MapStores.defaultEagerStoreConfig;

@Configuration
public class VenueMapConfiguration {

    @Bean
    HazelcastMapConfigBean venueMapConfig(VenueMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new VenueMapConfig();
        MapStoreConfig mapStoreConfig = defaultEagerStoreConfig(mapStore, 0, true);
        return new StoredMapConfigBean(hazelcastMapConfig, mapStoreConfig);
    }

    @Bean
    WebAccess venueMapWebAccess(HazelcastInstance hazelcast, VenueMapStore mapStore) {
        HazelcastMapConfig hazelcastMapConfig = new VenueMapConfig();
        return new StringProtobufMapWebAccess<>(hazelcast, hazelcastMapConfig, Venue.getDefaultInstance(), mapStore);
    }
}
