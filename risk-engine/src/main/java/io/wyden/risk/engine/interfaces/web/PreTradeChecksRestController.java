package io.wyden.risk.engine.interfaces.web;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeChecksList;
import io.wyden.risk.engine.pretradecheck.configuration.PreTradeCheckConfigurationService;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
@RequestMapping("/pretradechecks")
public class PreTradeChecksRestController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreTradeChecksRestController.class);

    private final PreTradeCheckConfigurationService preTradeCheckConfigurationService;

    public PreTradeChecksRestController(PreTradeCheckConfigurationService preTradeCheckConfigurationService) {
        this.preTradeCheckConfigurationService = preTradeCheckConfigurationService;
    }

    @GetMapping(produces = APPLICATION_PROTOBUF_VALUE)
    public PreTradeChecksList getPreTradeChecks() {
        Collection<PreTradeCheck> checks = preTradeCheckConfigurationService.getPreTradeChecks();
        PreTradeChecksList.Builder listBuilder = PreTradeChecksList.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build());
        for (PreTradeCheck check : checks) {
            listBuilder.addItems(check);
        }
        return listBuilder.build();
    }

    @PostMapping(consumes = APPLICATION_PROTOBUF_VALUE, produces = APPLICATION_PROTOBUF_VALUE)
    public PreTradeCheck savePreTradeCheck(@RequestBody PreTradeCheck check) {
        PreTradeCheck saved = preTradeCheckConfigurationService.savePreTradeCheck(check);
        return saved;
    }

    @DeleteMapping("/{id}")
    public void deletePreTradeCheck(@PathVariable String id) {
        preTradeCheckConfigurationService.deletePreTradeCheck(id);
    }

    @ExceptionHandler({ValidationException.class})
    public ResponseEntity<String> validationExceptionHandler(ValidationException exception) {
        LOGGER.warn("New ValidationException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException exception) {
        LOGGER.warn("New IllegalArgumentException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<String> exceptionHandler(Exception exception) {
        LOGGER.warn("New {} {}", exception.getClass().getName(), exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exception.getMessage());
    }
}
