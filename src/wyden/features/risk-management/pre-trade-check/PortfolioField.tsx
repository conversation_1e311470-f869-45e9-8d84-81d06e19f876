import { FormControl, FormHelperText } from '@mui/material';
import { useTsController } from '@ts-react/form';
import { useTranslation } from 'react-i18next';
import { PortfolioSelect } from '../../PortfolioSelect';
import { PortfolioResponse } from '@wyden/services/graphql/generated/graphql';

export type ObjectOption = { label: string; id: string };
export type AutocompleteOption = string | string[] | ObjectOption | ObjectOption[];

export const PortfolioField = () => {
  const { field, error } = useTsController();
  const errorMessageKey = error?.errorMessage || '';

  const { t } = useTranslation();

  return (
    <FormControl fullWidth error={!!error}>
      <PortfolioSelect
        multiple={true}
        value={field.value as PortfolioResponse[] | null}
        onChange={(value) => field.onChange(value)}
      />
      {error && <FormHelperText error={!!error}>{t(errorMessageKey)}</FormHelperText>}
    </FormControl>
  );
};
