import ClearIcon from '@mui/icons-material/Clear';
import { Autocomplete } from '@ui/Autocomplete';
import { FullWidthPopper } from '@ui/Popper';
import { TextField } from '@ui/TextField';
import { archivedFilter } from '@wyden/helpers/archivedFilter';
import { differenceWith, isEqual } from 'lodash';
import { SyntheticEvent } from 'react';
import { useTranslation } from 'react-i18next';
import {
  getInstrumentSymbol,
  getInstrumentReadableIdentifier,
  getInstrumentId,
  getInstrumentVenueName,
} from '../../helpers/instrument';
import { InstrumentData } from '../app-header/useInstrumentStore';
import { InstrumentRow } from './InstrumentRow';
import { useInstrumentSearch } from './useInstrumentSearch';
import { InstrumentResponse, VenueType } from '@wyden/services/graphql/generated/graphql';
import { logger } from '@wyden/utils/logger';

interface InstrumentSearchProps {
  instrument: InstrumentResponse | null;
  onInstrumentChange: (instrument: InstrumentData) => void;
  excludedInstruments?: InstrumentData[];
  className?: string;
  clearable?: boolean;
  label?: string;
  venueType?: VenueType;
  venueNames?: string[];
  error?: boolean;
  disabled?: boolean;
  required?: boolean;
  warning?: boolean;
  skipOptionalLabel?: boolean;
  autoFocus?: boolean;
  id?: string;
}

export const INSTRUMENT_SEARCH_ROW_TEST_ID = 'instrument-search-row';
export const INSTRUMENT_SEARCH_CLEAN_TEST_ID = 'clear-instrument-search-icon';
export const INSTRUMENT_SEARCH_AUTOCOMPLETE_TEST_ID = 'instrument-search-autocomplete';

export const InstrumentSearch = (props: InstrumentSearchProps) => {
  const { instrument, onInstrumentChange, excludedInstruments, className } = props;
  const { t } = useTranslation();
  const { instruments, instrumentsLoading, updateSearchQuery } = useInstrumentSearch({
    venueType: props.venueType,
    venueNames: props.venueNames,
  });
  const filteredInstruments = instruments.filter((s) => Boolean(s)).filter(archivedFilter);
  const instrumentsToDisplay = excludedInstruments
    ? differenceWith(filteredInstruments, excludedInstruments, isEqual)
    : filteredInstruments;

  const handleInputChange = async (_event: SyntheticEvent<Element, Event>, newQuery: string) => {
    const isSelectingInstrument = newQuery.includes('@');

    if (isSelectingInstrument) {
      updateSearchQuery('');
    } else {
      updateSearchQuery(newQuery);
    }
  };

  return (
    <Autocomplete
      fullWidth
      disableClearable={!props.clearable}
      id={props.id || 'instrument-search'}
      clearIcon={<ClearIcon data-testid={INSTRUMENT_SEARCH_CLEAN_TEST_ID} fontSize="small" />}
      className={className}
      disabled={props.disabled}
      loading={instrumentsLoading}
      onKeyDown={(e) => {
        e.stopPropagation();
      }}
      PopperComponent={FullWidthPopper}
      renderInput={(params) => (
        <TextField
          {...params}
          autoFocus={props.autoFocus}
          required={props.required}
          error={props.error}
          warning={props.warning}
          skipOptionalLabel={props.skipOptionalLabel}
          label={props.label !== undefined ? props.label : t('instrumentSearch.label')}
        />
      )}
      getOptionLabel={(instrument) => getInstrumentReadableIdentifier(instrument)}
      renderOption={(props, instrument) => {
        const symbol = getInstrumentSymbol(instrument) || getInstrumentId(instrument);
        const venueName = getInstrumentVenueName(instrument);

        if (symbol && venueName) {
          return (
            <InstrumentRow
              {...props}
              symbol={symbol}
              assetClass={instrument?.baseInstrument?.assetClass || ''}
              baseCurrency={instrument?.forexSpotProperties?.baseCurrency || ''}
              venueName={venueName}
              data-testid={INSTRUMENT_SEARCH_ROW_TEST_ID}
            />
          );
        } else {
          logger.warn(
            `${t('instrumentSearch.symbolOrVenueNotFound')} ${JSON.stringify(instrument)}`,
          );
        }
      }}
      options={instrumentsToDisplay.sort(
        (a, b) => getInstrumentId(a)?.localeCompare(getInstrumentId(b) ?? '') || 0,
      )}
      isOptionEqualToValue={(option, value) =>
        option?.instrumentIdentifiers?.instrumentId === value?.instrumentIdentifiers?.instrumentId
      }
      value={instrument}
      onChange={(_event, value) => {
        onInstrumentChange(value as InstrumentData);
      }}
      filterOptions={(x) => x}
      onInputChange={handleInputChange}
    />
  );
};
