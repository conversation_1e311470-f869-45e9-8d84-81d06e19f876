package io.wyden.clob.gateway.service.calculator;

import io.wyden.clob.gateway.service.tracking.FailureNonRecoverableException;
import io.wyden.clob.gateway.utils.MathUtils;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import io.wyden.published.oems.OemsSide;
import io.wyden.published.referencedata.Instrument;
import jakarta.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;

import static io.wyden.clob.gateway.utils.BigDecimalUtils.bd;
import static io.wyden.clob.gateway.utils.BigDecimalUtils.isCreatable;
import static io.wyden.clob.gateway.utils.QuotingConfigUtils.findEffectiveHedgingMargin;

public class HedgingCalculator {

    private static final Logger LOGGER = LoggerFactory.getLogger(HedgingCalculator.class);

    public static BigDecimal roundToPriceIncrement(BigDecimal limitPrice, Instrument hedgingInstrument) {
        if (isCreatable(hedgingInstrument.getTradingConstraints().getPriceIncr())) {
            BigDecimal priceIncr = bd(hedgingInstrument.getTradingConstraints().getPriceIncr());
            return roundToIncrement(limitPrice, priceIncr, RoundingMode.HALF_UP);
        }
        return limitPrice;
    }

    public static boolean needsQuantityAdjustment(BigDecimal quantity, Instrument hedgingInstrument) {
        if (isCreatable(hedgingInstrument.getTradingConstraints().getQtyIncr())) {
            BigDecimal qtyIncr = new BigDecimal(hedgingInstrument.getTradingConstraints().getQtyIncr());
            return quantity.remainder(qtyIncr).compareTo(BigDecimal.ZERO) != 0;
        }
        return false;
    }

    public static BigDecimal roundToQtyIncrement(BigDecimal quantity, Instrument hedgingInstrument) {
        if (isCreatable(hedgingInstrument.getTradingConstraints().getQtyIncr())) {
            BigDecimal qtyIncr = bd(hedgingInstrument.getTradingConstraints().getQtyIncr());
            return roundToIncrement(quantity, qtyIncr, RoundingMode.DOWN);
        }
        return quantity;
    }

    private static BigDecimal roundToIncrement(BigDecimal value, BigDecimal increment, RoundingMode roundingMode) {
        return value.divide(increment, 0, roundingMode).multiply(increment);
    }

    public static BigDecimal calculateTriangulatedHedgingPrice(BigDecimal quantity, OemsSide side, MarketDataEvent marketDataEvent) {

        return switch (side) {
            case BUY -> {
                if (marketDataEvent.hasOrderBook()) {
                    yield getAveragePrice(getSortedOrderBookLevels(marketDataEvent.getOrderBook(), side), quantity);
                }
                if (marketDataEvent.hasBidAskQuote()) {
                    yield bd(marketDataEvent.getBidAskQuote().getAskPrice());
                }
                if (marketDataEvent.hasAsk()) {
                    yield bd(marketDataEvent.getAsk().getPrice());
                }
                throw new FailureNonRecoverableException("Unable to calculate hedging price");
            }
            case SELL -> {
                if (marketDataEvent.hasOrderBook()) {
                    yield getAveragePrice(getSortedOrderBookLevels(marketDataEvent.getOrderBook(), side), quantity);
                }
                if (marketDataEvent.hasBidAskQuote()) {
                    yield bd(marketDataEvent.getBidAskQuote().getBidPrice());
                }
                if (marketDataEvent.hasBid()) {
                    yield bd(marketDataEvent.getBid().getPrice());
                }
                throw new FailureNonRecoverableException("Unable to calculate hedging price");
            }
            case UNRECOGNIZED, SIDE_UNDETERMINED, REDUCE_SHORT, SELL_SHORT -> throw new FailureNonRecoverableException("Unable to calculate hedging price for side %s".formatted(side));
        };
    }

    private static List<OrderBookLevel> getSortedOrderBookLevels(OrderBook orderBook, OemsSide side) {
        if (side == OemsSide.BUY) {
            return orderBook.getAsksMap().values().stream()
                .sorted(Comparator.comparing(OrderBookLevel::getPrice))
                .toList();
        }

        if (side == OemsSide.SELL) {
            return orderBook.getBidsMap().values().stream()
                .sorted(Comparator.comparing(OrderBookLevel::getPrice).reversed())
                .toList();
        }

        throw new FailureNonRecoverableException("Unable to get sorted order book for side %s".formatted(side));
    }

    private static BigDecimal getAveragePrice(List<OrderBookLevel> levels, BigDecimal quantity) {
        BigDecimal cumTakenQty = BigDecimal.ZERO;
        BigDecimal cumTakenAmount = BigDecimal.ZERO;
        for (OrderBookLevel level : levels) {
            BigDecimal remQty = quantity.subtract(cumTakenQty);
            BigDecimal levelPrice = bd(level.getPrice());
            BigDecimal takenQty = bd(level.getAmount()).min(remQty);
            BigDecimal takenAmount = takenQty.multiply(levelPrice);

            cumTakenQty = cumTakenQty.add(takenQty);
            cumTakenAmount = cumTakenAmount.add(takenAmount);

            if (cumTakenQty.compareTo(quantity) == 0) {
                break;
            }
        }
        return cumTakenAmount.divide(cumTakenQty, 20, RoundingMode.HALF_DOWN);
    }
}
