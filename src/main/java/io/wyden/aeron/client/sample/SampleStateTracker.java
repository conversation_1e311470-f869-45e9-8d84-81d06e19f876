package io.wyden.aeron.client.sample;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SampleStateTracker {

    private static final Logger LOGGER = LoggerFactory.getLogger(SampleStateTracker.class);

    private State state = State.INIT;

    public State getState() {
        return state;
    }

    public void setState(State state) {
        if (this.state == State.READY) {
            LOGGER.info("ignoring state adjustment to {}", state);
        } else {
            this.state = state;
            LOGGER.info("adjusted state to {}", state);
        }
    }

    public static enum State {

        INIT,
        ORDERS_REQUESTED,
        ORDERS_INITIALIZED,
        SYMBOLS_REQUESTED,
        SYMBOLS_EMPTY,
        READY;
    }
}
