plugins {
    id 'java'
    id 'idea'
    id "jaco<PERSON>"
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

dependencies {
    implementation project(':quoting-engine-domain')

    implementation dependencyCatalog.reference.data.client
    implementation dependencyCatalog.cloud.utils.rabbitmq
    implementation dependencyCatalog.cloud.utils.hazelcast
    implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
    implementation dependencyCatalog.cloud.utils.telemetry
    implementation dependencyCatalog.cloud.utils.spring
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.clob.gateway.domain

    implementation dependencyCatalog.sbe
    implementation dependencyCatalog.aeron.all

    implementation dependencyCatalog.spring.boot.starter.webflux
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.hazelcast
    implementation dependencyCatalog.hazelcast.jet.protobuf
    implementation dependencyCatalog.vavr.vavr

    testImplementation dependencyCatalog.reactor.test
    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.cloud.utils.test
    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
    testImplementation dependencyCatalog.awaitility

    annotationProcessor dependencyCatalog.spring.boot.configuration.processor

    testRuntimeOnly dependencyCatalog.junit.jupiter.engine
}

bootJar {
    manifest {
        attributes(
                "Implementation-Version": "${version}",
        )
    }
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = [
            "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9071",
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
            "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
            "--add-exports=java.base/jdk.internal.ref=ALL-UNNAMED",
            "--add-exports=java.base/jdk.internal.util=ALL-UNNAMED"
    ]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

sonarqube {
    properties {
        property "sonar.projectKey", "quoting-engine"
        property "sonar.projectName", "Quoting Engine"
    }
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project
                implementation dependencyCatalog.reference.data.client
                implementation dependencyCatalog.cloud.utils.rabbitmq
                implementation dependencyCatalog.cloud.utils.rabbitmq.destinations
                implementation dependencyCatalog.cloud.utils.telemetry
                implementation dependencyCatalog.cloud.utils.test
                implementation dependencyCatalog.published.language.oems
                implementation dependencyCatalog.hazelcast
                implementation(dependencyCatalog.hazelcast) { artifact { classifier = 'tests'} }
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.spring.boot.starter.webflux
                implementation dependencyCatalog.testcontainers
                implementation dependencyCatalog.testcontainers.junit.jupiter
                implementation dependencyCatalog.testcontainers.rabbitmq
                implementation dependencyCatalog.snakeyaml
                implementation dependencyCatalog.javafaker
                implementation dependencyCatalog.aeron.all
                implementation dependencyCatalog.reactor.test
                implementation dependencyCatalog.sbe
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                        jvmArgs('--add-opens', 'java.base/sun.nio.ch=ALL-UNNAMED')
                        jvmArgs('--add-opens', 'java.base/java.lang.reflect=ALL-UNNAMED')
                        jvmArgs('--add-exports', 'java.base/jdk.internal.ref=ALL-UNNAMED')
                        jvmArgs('--add-exports', 'java.base/jdk.internal.util=ALL-UNNAMED')
                    }
                }
            }
        }
    }
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
