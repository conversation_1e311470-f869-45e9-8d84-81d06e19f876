Feature: Feature #5: Street Cash Trade Booking
  As a trader
  I want to process street cash trades
  So that I can see changes in my positions


  Scenario: Deposit USD
    And Bank_5 deposits 20000.0 USD into Bitfinex_5 account

    Then position qty state change to:
      | portfolioId | accountId  | instrument | qty    |
      | Bank_5      | -          | USD        | 20_000 |
      | Bank_5      | -          | BTC        | -      |
      | -           | Bitfinex_5 | USD        | 20_000 |
      | -           | Bitfinex_5 | BTC        | -      |


  Scenario: Process Street Cash Trade BUY BTC/USD
    When Bank_5 trades 1.0 BTC/USD at 10500.0 USD with 10.0 USD transaction fee against Bitfinex_5 account

    Then position qty state change to:
      | portfolioId | accountId  | instrument | qty   |
      | Bank_5      | -          | USD        | 9_490 |
      | Bank_5      | -          | BTC        | 1     |
      | -           | Bitfinex_5 | USD        | 9_490 |
      | -           | Bitfinex_5 | BTC        | 1     |


  Scenario: Process Street Cash Trade SELL BTC/USD
    When Bank_5 trades -1.0 BTC/USD at 11000.0 USD with 10.0 USD transaction fee against Bitfinex_5 account

    Then position qty state change to:
      | portfolioId | accountId  | instrument | qty    |
      | Bank_5      | -          | USD        | 20_480 |
      | Bank_5      | -          | BTC        | -      |
      | -           | Bitfinex_5 | USD        | 20_480 |
      | -           | Bitfinex_5 | BTC        | -      |


  Scenario: Withdraw USD
    When Bank_5 withdraws all his USD from Bitfinex_5 account

    Then position qty state change to:
      | portfolioId | accountId  | instrument | qty |
      | Bank_5      | -          | USD        | -   |
      | Bank_5      | -          | BTC        | -   |
      | -           | Bitfinex_5 | USD        | -   |
      | -           | Bitfinex_5 | BTC        | -   |
