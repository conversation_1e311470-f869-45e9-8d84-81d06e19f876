package io.wyden.apiserver.fix.common.marketdata.model;

import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.time.Duration;
import javax.annotation.Nullable;

public record BarEvent(MarketDataIdentifierDTO identifier,
                       Duration barSize,
                       @Nullable BigDecimal tradeOpen,
                       @Nullable BigDecimal tradeHi,
                       @Nullable BigDecimal tradeLow,
                       @Nullable BigDecimal tradeClose,
                       @Nullable BigDecimal tradeVol,
                       @Nullable BigDecimal midOpen,
                       @Nullable BigDecimal midHi,
                       @Nullable BigDecimal midLow,
                       @Nullable BigDecimal midClose) implements MarketDataEvent {

    @Override
    public MarketDataTypeDTO marketDataType() {
        return MarketDataTypeDTO.BAR;
    }

    @Override
    public boolean isValid() {
        return ObjectUtils.allNotNull(tradeOpen, tradeHi, tradeLow, tradeClose) || ObjectUtils.allNotNull(midOpen, midHi, midLow, midClose);
    }
}
