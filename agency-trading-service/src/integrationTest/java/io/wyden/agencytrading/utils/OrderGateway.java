package io.wyden.agencytrading.utils;

import com.google.protobuf.Message;
import io.wyden.agencytrading.infrastructure.rabbit.RabbitDestinations;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTargetType;
import org.assertj.core.api.AssertionsForClassTypes;

import java.util.Map;
import java.util.function.Predicate;

import static io.wyden.agencytrading.service.oems.outbound.OemsResponseFactory.ORDER_GATEWAY_TARGET;

public class OrderGateway {

    private final RabbitDestinations rabbitDestinations;

    private final ExchangeObserver<Message> tradingRouterExchange;
    private final ExchangeObserver<Message> ingressExchange;

    public OrderGateway(RabbitDestinations destinations, RabbitIntegrator rabbitIntegrator) {
        this.rabbitDestinations = destinations;

        tradingRouterExchange = ExchangeObserver.newBuilder(rabbitIntegrator, destinations.getTradingRouterExchange(), TradingMessageParser.parser(), "oems-request-queue")
            .withHeaders(Map.of(
                OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName()
            ))
            .build();
        ingressExchange = ExchangeObserver.newBuilder(rabbitIntegrator, destinations.getTradingIngressExchange(), TradingMessageParser.parser(), "oems-response-queue")
            .withHeaders(Map.of(
                OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName()
            ))
            .build();
        tradingRouterExchange.attach();
        ingressExchange.attach();
    }

    public void cleanup() {
        tradingRouterExchange.detach();
    }

    public OemsRequest emitsOemsRequest(OemsRequest request) {
        RabbitExchange<Message> destination = rabbitDestinations.getTradingIngressExchange();
        Map<String, String> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.SOURCE.getHeaderName(), ORDER_GATEWAY_TARGET,
            OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.BROKER_DESK.name(),
            OemsHeader.TARGET.getHeaderName(), request.getMetadata().getTarget(),
            OemsHeader.PTC.getHeaderName(), OemsRequest.OemsPTCStatus.APPROVED.name()
        );
        destination.publishWithHeaders(request, headers);
        return request;
    }

    public OemsResponse awaitExecutionReportWithOrderStatusReceived(String orderId, OemsOrderStatus orderStatus) {
        OemsResponse statusER = awaitOemsResponse(response -> orderId.equals(response.getOrderId()), OemsResponse.OemsResponseType.EXECUTION_REPORT);

        AssertionsForClassTypes.assertThat(statusER.getOrderStatus())
            .as("Agency should register execution report and share Order status as " + orderStatus)
            .isEqualTo(orderStatus);

        return statusER;
    }

    public OemsResponse awaitExecutionReportWithExecTypeReceived(String orderId, OemsExecType execType) {
        OemsResponse statusER = awaitOemsResponse(response -> orderId.equals(response.getOrderId()), OemsResponse.OemsResponseType.EXECUTION_REPORT);

        AssertionsForClassTypes.assertThat(statusER.getExecType())
            .as("Agency should register execution report and share Order status as " + execType)
            .isEqualTo(execType);

        return statusER;
    }

    public OemsResponse awaitCancelRejectWithOrderStatusReceived(String orderId, OemsOrderStatus orderStatus) {
        OemsResponse statusER = awaitOemsResponse(response -> orderId.equals(response.getOrderId()), OemsResponse.OemsResponseType.CANCEL_REJECT);

        AssertionsForClassTypes.assertThat(statusER.getOrderStatus())
            .as("Agency should register cancel reject and share Order status as " + orderStatus)
            .isEqualTo(orderStatus);

        return statusER;
    }

    private OemsResponse awaitOemsResponse(Predicate<OemsResponse> filter, OemsResponse.OemsResponseType oemsResponseType) {
        Predicate<OemsResponse> isExecutionReport = m -> m != null && m.getResponseType() == oemsResponseType;
        Predicate<OemsResponse> isFilteredExecutionReport = filter != null ? isExecutionReport.and(filter) : isExecutionReport;
        Predicate<Message> predicate = m -> m instanceof OemsResponse oemsResponse && isFilteredExecutionReport.test(oemsResponse);
        return (OemsResponse) ingressExchange.awaitMessage(predicate);
    }
}
