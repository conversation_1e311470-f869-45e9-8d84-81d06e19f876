package io.wyden.rate.domain.map;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.CompoundKeyUtils;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.rate.RateSubscription;
import io.wyden.published.rate.RateSubscriptionKey;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class RateSubscriptionMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "rate-service-rate-subscription_0.1";

    public static IMap<String, RateSubscription> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(RateSubscription.class, RateSubscription.parser()));
    }

    public static String toKey(String baseCurrency, String quoteCurrency) {
        return CompoundKeyUtils.toKey(2, baseCurrency, quoteCurrency);
    }

    public static RateSubscriptionKey fromKey(String key) {
        String[] parts = CompoundKeyUtils.fromKey(2, key);
        return RateSubscriptionKey.newBuilder()
            .setBaseCurrency(parts[0])
            .setQuoteCurrency(parts[1])
            .build();
    }
}
