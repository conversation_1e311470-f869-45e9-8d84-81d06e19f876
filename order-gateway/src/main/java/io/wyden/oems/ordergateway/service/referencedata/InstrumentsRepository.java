package io.wyden.oems.ordergateway.service.referencedata;

import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Repository;

@Repository
public class InstrumentsRepository {

    private final Tracing otlTracing;

    private final InstrumentsCacheFacade instrumentsCacheFacade;

    public InstrumentsRepository(Tracing otlTracing, InstrumentsCacheFacade instrumentsCacheFacade) {
        this.instrumentsCacheFacade = instrumentsCacheFacade;
        this.otlTracing = otlTracing;
    }

    @Nullable
    public Instrument find(String instrumentId) {
        try (var ignored = otlTracing.createSpan("instrumentsrepository.find", SpanKind.CLIENT)) {
            return instrumentsCacheFacade.find(instrumentId)
                    .orElse(null);
        }
    }

    @NotNull
    public static Instrument buildInstrument(String baseCurrency, String quoteCurrency, String venueName) {
        return Instrument.newBuilder()
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency(baseCurrency)
                .build())
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setQuoteCurrency(quoteCurrency)
                .setVenueName(venueName)
                .build())
            .build();
    }
}
