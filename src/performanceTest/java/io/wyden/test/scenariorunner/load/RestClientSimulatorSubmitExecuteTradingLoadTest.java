package io.wyden.test.scenariorunner.load;

import java.time.Duration;

public class RestClientSimulatorSubmitExecuteTradingLoadTest extends LoadTest {

    @Override
    int users() {
        return 1;
    }

    @Override
    String actorType() {
        return "WydenCloudRest";
    }

    @Override
    String scenario() {
        return "ClientSideMarketOrder";
    }

    @Override
    double targetLoad() {
        return 20;
    }

    @Override
    Duration rampUpDuration() {
        return Duration.ofMinutes(20);
    }

    @Override
    Duration consistentLoadDuration() {
        return Duration.ofHours(2);
    }

}
