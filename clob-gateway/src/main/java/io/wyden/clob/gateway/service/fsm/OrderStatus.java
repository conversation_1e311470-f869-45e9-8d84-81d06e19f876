package io.wyden.clob.gateway.service.fsm;

import io.wyden.clob.gateway.model.ClobOrderState;
import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.oems.Trade;
import io.wyden.clob.gateway.service.oems.outbound.OemsResponseFactory;
import io.wyden.published.oems.OemsLiquidityIndicator;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Instrument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;
import java.util.stream.Collectors;

import static io.wyden.published.oems.OemsOrderStatus.STATUS_CANCELED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_FILLED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PARTIALLY_FILLED_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_CANCEL_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_NEW_VALUE;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED_VALUE;

/**
 * OrderStatus descendants represent order state in state machine. Each order may exist in more than one order state,
 * the value with the highest precedence is used to handle events. State transitions are described below:
 *
 *              +-------------------------------> Rejected
 *              | +--------+---------+-------------v
 * Start -> PendingNew -> New -> PartialFill   -> Filled
 *               |         |          v ^
 *               +---------+---> PendingCancel -> Cancelled
 */
@SuppressWarnings("squid:S1172") // Remove unused method parameters
abstract class OrderStatus implements Comparable<OrderStatus> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStatus.class);
    private static final String DUPLICATE_ORDER_ID = "Duplicate orderId";

    enum Precedence {
        START(0),
        PENDING_NEW(2),
        NEW(2),
        REJECTED(2),
        PARTIAL_FILL(3),
        CANCELLED(4),
        EXPIRED(4),
        FILLED(7),
        PENDING_CANCEL(11),
        UNRECOGNIZED(100);

        private final int value;

        Precedence(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    abstract Precedence getPrecedence();

    void onNewOrder(OrderContext context, OemsRequest request, PlaceOrderCommandFactory placeOrderCommandFactory) {
        OrderState orderState = context.getOrderState();
        OemsResponse reject = OemsResponseFactory.createRejectAsDuplicate(orderState, DUPLICATE_ORDER_ID);
        context.emit(reject);
    }

    void onCancel(OrderContext context, OemsRequest request) {
        String inResponseToRequestId = request.getMetadata().getRequestId();
        String inResponseToRequesterId = request.getMetadata().getRequesterId();
        OrderState orderState = context.getOrderState();
        context.emit(OemsResponseFactory.createCancelRejectWrongState(inResponseToRequestId, inResponseToRequesterId, orderState));
    }

    void onCancelReject(OrderContext context, long matchingEngineOrderId, String reason) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Ignoring CancelReject for force cancelled order. Current OrderState: {}", context.getOrderState());
            return;
        }
        LOGGER.warn("Received CancelReject inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onPlaceOrder(OrderContext context, MatchingEngineEvent.PlaceOrder placeOrder, Instrument instrument) {
        LOGGER.warn("Received ExecType.NEW inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onRejected(OrderContext context, MatchingEngineEvent.PlaceOrder placeOrder) {
        LOGGER.warn("Received ExecType.REJECTED inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onTrade(OrderContext context, Trade trade) {
        LOGGER.info("Received Trade {} inconsistent with OrderState. Current OrderState: {}", trade, context.getOrderState());
    }

    void onCancelled(OrderContext context, long matchingEngineOrderId) {
        LOGGER.info("Received CANCELLED inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    public void onExpired(OrderContext context) {
        LOGGER.info("Received EXPIRED inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onExternalMatch(OrderContext context, MatchingEngineEvent.TakerTradeEvent takerTradeEvent, MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade, Instrument instrument, OemsLiquidityIndicator clientLiquidityIndicator) {
        LOGGER.info("Received external match in wrong state. Current state: {}", context.getOrderState());
    }

    void onHedgingOrderRejected(OrderContext context, Trade trade) {
        LOGGER.info("Received hedging order rejected inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    void onHedgingOrderRestated(OrderContext context, Trade trade) {
        LOGGER.info("Received hedging order restated inconsistent with OrderState. Current OrderState: {}", context.getOrderState());
    }

    boolean isTerminal() {
        return false;
    }

    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
    }

    static Set<OrderStatus> toOrderStates(ClobOrderState clobOrderState) {
        return clobOrderState.getCurrentStatusValueList().stream()
            .map(OrderStatus::toOrderStatus)
            .collect(Collectors.toSet());
    }

    static OrderStatus toOrderStatus(int venueOrderStatus) {
        return switch (venueOrderStatus) {
            case STATUS_PENDING_NEW_VALUE -> StatusPendingNew.create();
            case STATUS_NEW_VALUE -> StatusNew.create();
            case STATUS_PARTIALLY_FILLED_VALUE -> StatusPartialFill.create();
            case STATUS_FILLED_VALUE -> StatusFilled.create();
            case STATUS_PENDING_CANCEL_VALUE -> StatusPendingCancel.create();
            case STATUS_CANCELED_VALUE -> StatusCancelled.create();
            case STATUS_REJECTED_VALUE -> StatusRejected.create();
            default -> StatusUnrecognized.create(venueOrderStatus);
        };
    }

    @Override
    public boolean equals(Object o) {
        return o != null && getClass() == o.getClass();
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    @Override
    public String toString() {
        return "%s.%d".formatted(this.getClass().getSimpleName(), this.getPrecedence().getValue());
    }

    @Override
    public int compareTo(OrderStatus o) {
        return this.getPrecedence().getValue() - o.getPrecedence().getValue();
    }
}
