package io.wyden.websocketserver.it;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.Metadata;
import okhttp3.mockwebserver.MockResponse;
import okio.Buffer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

import static org.assertj.core.api.Assertions.assertThat;

public class PositionTest extends WebSocketIntegrationTestBase {
    public static final MockResponse MOCK_POSITIONS_SEARCH_RESPONSE = new MockResponse()
        .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_PROTOBUF_VALUE)
        .setResponseCode(200)
        .setBody(new Buffer().write(
            CursorConnection.newBuilder()
                .addEdges(CursorEdge.newBuilder().setNode(CursorNode.newBuilder().setPosition(PositionSnapshot.newBuilder().setQuantity("4").setAccount("account4").setPortfolio("portfolio4"))))
                .addEdges(CursorEdge.newBuilder().setNode(CursorNode.newBuilder().setPosition(PositionSnapshot.newBuilder().setQuantity("5").setAccount("account5").setPortfolio("portfolio5"))))
                .addEdges(CursorEdge.newBuilder().setNode(CursorNode.newBuilder().setPosition(PositionSnapshot.newBuilder().setQuantity("6").setAccount("account6").setPortfolio("portfolio6"))))
                .build().toByteArray()));

    private RabbitExchange<PositionSnapshot> positionSnapshotExchange;

    @BeforeEach
    void setUp() {
        webSocketClient.connect();
        webSocketClient.login(apiKey, apiSecret);
        positionSnapshotExchange = OemsExchange.Booking.declarePositionUpdatedExchange(rabbitIntegrator);
    }

    @Test
    void shouldStreamPositionsAndBeAbleToUnsubscribe() {
        ZonedDateTime timestamp = ZonedDateTime.of(2024, 11, 4, 15, 0, 0, 0, ZoneId.of("UTC"));

        String subscribeCommandId = webSocketClient.send("subscribe", "position", Map.of());
        webSocketClient.awaitSuccess(subscribeCommandId);

        emitPositionSnapshot(timestamp.plusSeconds(0), "account1", "portfolio1");
        emitPositionSnapshot(timestamp.plusSeconds(1), "notpermittedaccount2", "portfolio1"); // filtered out by account permission
        emitPositionSnapshot(timestamp.plusSeconds(2), "account1", "notpermittedportfolio1"); // filtered out by portfolio permission
        emitPositionSnapshot(timestamp.plusSeconds(3), "account3", "portfolio1");

        webSocketClient.awaitMessageSequence(
            """
                {"event":"position","payload":{"updatedAt":*************,"currency":"","symbol":"","portfolioId":"portfolio1","accountId":"account1","quantity":1,"pendingQuantity":null,"availableForTradingQuantity":null,"availableForWithdrawalQuantity":null,"settledQuantity":null,"unsettledQuantity":null,"notionalQuantity":null,"marketValue":null,"netRealizedPnl":null,"grossRealizedPnl":null,"netUnrealizedPnl":null,"grossUnrealizedPnl":null,"netCost":null,"grossCost":null,"netAveragePrice":null,"grossAveragePrice":null}}""",
            """
                {"event":"position","payload":{"updatedAt":*************,"currency":"","symbol":"","portfolioId":"portfolio1","accountId":"account3","quantity":1,"pendingQuantity":null,"availableForTradingQuantity":null,"availableForWithdrawalQuantity":null,"settledQuantity":null,"unsettledQuantity":null,"notionalQuantity":null,"marketValue":null,"netRealizedPnl":null,"grossRealizedPnl":null,"netUnrealizedPnl":null,"grossUnrealizedPnl":null,"netCost":null,"grossCost":null,"netAveragePrice":null,"grossAveragePrice":null}}""");

        String unsubscribeCommandId = webSocketClient.send("unsubscribe", "position", Map.of());
        webSocketClient.awaitSuccess(unsubscribeCommandId);

        emitPositionSnapshot(timestamp.plusSeconds(10), "account1", "portfolio1");

        LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(500));
        assertThat(webSocketClient.getMessages()).hasSize(5); // login, subscribe, 2 events, unsubscribe
    }

    @Test
    void shouldSendPositionsSnapshotIfRequested() throws Exception {
        String subscribeCommandId = webSocketClient.send("subscribe", "position", Map.of("snapshot", true));
        webSocketClient.awaitSuccess(subscribeCommandId);

        webSocketClient.awaitMessageSequence(
            """
                {"event":"position","payload":{"updatedAt":null,"currency":"","symbol":"","portfolioId":"portfolio4","accountId":"account4","quantity":4,"pendingQuantity":null,"availableForTradingQuantity":null,"availableForWithdrawalQuantity":null,"settledQuantity":null,"unsettledQuantity":null,"notionalQuantity":null,"marketValue":null,"netRealizedPnl":null,"grossRealizedPnl":null,"netUnrealizedPnl":null,"grossUnrealizedPnl":null,"netCost":null,"grossCost":null,"netAveragePrice":null,"grossAveragePrice":null}}""",
            """
                {"event":"position","payload":{"updatedAt":null,"currency":"","symbol":"","portfolioId":"portfolio5","accountId":"account5","quantity":5,"pendingQuantity":null,"availableForTradingQuantity":null,"availableForWithdrawalQuantity":null,"settledQuantity":null,"unsettledQuantity":null,"notionalQuantity":null,"marketValue":null,"netRealizedPnl":null,"grossRealizedPnl":null,"netUnrealizedPnl":null,"grossUnrealizedPnl":null,"netCost":null,"grossCost":null,"netAveragePrice":null,"grossAveragePrice":null}}""",
            """
                {"event":"position","payload":{"updatedAt":null,"currency":"","symbol":"","portfolioId":"portfolio6","accountId":"account6","quantity":6,"pendingQuantity":null,"availableForTradingQuantity":null,"availableForWithdrawalQuantity":null,"settledQuantity":null,"unsettledQuantity":null,"notionalQuantity":null,"marketValue":null,"netRealizedPnl":null,"grossRealizedPnl":null,"netUnrealizedPnl":null,"grossUnrealizedPnl":null,"netCost":null,"grossCost":null,"netAveragePrice":null,"grossAveragePrice":null}}"""
        );
    }

    public PositionSnapshot emitPositionSnapshot(ZonedDateTime timestamp, String accountIdName, String portfolioIdName) {
        PositionSnapshot positionSnapshot = PositionSnapshot.newBuilder()
            .setAccount(accountIdName)
            .setPortfolio(portfolioIdName)
            .setQuantity("1")
            .setMetadata(Metadata.newBuilder()
                .setUpdatedAt(DateUtils.toIsoUtcTime(timestamp))
                .build())
            .build();
        try {
            positionSnapshotExchange.publishWithHeaders(positionSnapshot, Map.of()).get(1, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return positionSnapshot;
    }
}
