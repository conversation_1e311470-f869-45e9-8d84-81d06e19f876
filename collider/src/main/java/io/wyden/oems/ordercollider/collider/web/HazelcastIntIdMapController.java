package io.wyden.oems.ordercollider.collider.web;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.ParametersAreNonnullByDefault;

@Tag(name="Hazelcast int-id map")
@RestController
@RequestMapping("/hz/venue-int-id/map")
@ParametersAreNonnullByDefault
class HazelcastIntIdMapController extends AbstractHazelcastController<String> {

    public HazelcastIntIdMapController(HazelcastVenueIntIdIndexMapService mapService) {
        super(mapService);
    }

    @Override
    String stringToProto(String value) {
        return value;
    }
}
