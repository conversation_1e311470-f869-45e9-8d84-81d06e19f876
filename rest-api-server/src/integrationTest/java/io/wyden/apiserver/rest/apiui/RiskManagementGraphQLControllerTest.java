package io.wyden.apiserver.rest.apiui;

import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioTypeDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.TagDto;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.risk.RiskManagementService;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckInputDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertySchemaDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckResponseDto;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckSchemaDto;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeCheckLevel;
import io.wyden.published.risk.PreTradeCheckPropertySchema;
import io.wyden.published.risk.PreTradeCheckPropertyType;
import io.wyden.published.risk.PreTradeCheckPropertyValue;
import io.wyden.published.risk.PreTradeCheckSchema;
import io.wyden.published.risk.PreTradeCheckSchemaList;
import io.wyden.published.risk.PreTradeChecksList;
import io.wyden.published.risk.RequestChannel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.graphql.test.tester.GraphQlTester;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckChannel.API;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckChannel.UI;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckLevel.WARN;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyType.NUMBER;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyType.STRING;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyType.STRING_LIST;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class RiskManagementGraphQLControllerTest extends PermissionsIntegrationTestNoDb {

    @Autowired
    private RiskManagementService riskManagementService;

    @Autowired
    private PortfolioRepository portfolioRepository;

    private static PreTradeCheckInputDto ptc1() {
        return new PreTradeCheckInputDto(
            "ptc1",
            "MaxPosition",
            WARN,
            List.of("Swiss Bank", "German Bank"),
            List.of(new TagDto("region", "EMEA")),
            List.of(API, UI),
            List.of(
                new PreTradeCheckPropertyDto(STRING, "maxValueCurrency", List.of("USD")),
                new PreTradeCheckPropertyDto(NUMBER, "maxValueWarn", List.of("100"))
            )
        );
    }

    private static PreTradeCheckResponseDto ptc1Response() {
        return new PreTradeCheckResponseDto(
            "ptc1",
            "MaxPosition",
            WARN,
            List.of(swissBank(), germanBank()),
            List.of(new TagDto("region", "EMEA")),
            List.of(API, UI),
            List.of(
                new PreTradeCheckPropertyDto(STRING, "maxValueCurrency", List.of("USD")),
                new PreTradeCheckPropertyDto(NUMBER, "maxValueWarn", List.of("100"))
            )
        );
    }

    private static PreTradeCheckResponseDto ptc2Response() {
        return new PreTradeCheckResponseDto(
            "ptc2",
            "OrderType",
            PreTradeCheckModel.PreTradeCheckLevel.BLOCK,
            List.of(swissBank(), germanBank()),
            List.of(new TagDto("region", "EMEA")),
            List.of(API, UI),
            List.of(
                new PreTradeCheckPropertyDto(STRING_LIST, "allowlist", List.of("LIMIT", "STOP_LIMIT"))
            )
        );
    }

    private static PortfolioResponseDto swissBank() {
        return new PortfolioResponseDto("Swiss Bank",
            "Swiss Bank",
            "2024-03-06T15:00:30.373617Z",
            null,
            PortfolioTypeDto.VOSTRO,
            null,
            null,
            null,
            null);
    }

    private static Portfolio swissBankProto() {
        return Portfolio.newBuilder()
            .setId("Swiss Bank")
            .setName("Swiss Bank")
            .setCreatedAt("2024-03-06T15:00:30.373617Z")
            .setPortfolioType(PortfolioType.VOSTRO)
            .build();
    }

    private static PortfolioResponseDto germanBank() {
        return new PortfolioResponseDto("German Bank",
            "German Bank",
            "2024-03-06T15:00:30.373617Z",
            null,
            PortfolioTypeDto.VOSTRO,
            null,
            null,
            null,
            null);
    }

    private static Portfolio germanBankProto() {
        return Portfolio.newBuilder()
            .setId("German Bank")
            .setName("German Bank")
            .setCreatedAt("2024-03-06T15:00:30.373617Z")
            .setPortfolioType(PortfolioType.VOSTRO)
            .build();
    }

    @Test
    @WithMockCustomUser
    void shouldReturnPreTradeCheckSchema() {
        grant("risk", "manage");
        when(riskManagementService.getPreTradeCheckSchema())
            .thenReturn(Mono.just(PreTradeCheckSchemaList.newBuilder()
                .addItems(orderTypeSchemaProto())
                .addItems(maxPositionSchemaProto())
                .build()));

        GraphQlTester.Response response = executeFile("pre-trade-check-form-schema", Map.of());
        response.path("$.errors").pathDoesNotExist();
        List<PreTradeCheckSchemaDto> returnedChecks = response.path("preTradeCheckFormSchema").entityList(PreTradeCheckSchemaDto.class).get();
        assertThat(returnedChecks)
            .containsOnly(
                orderTypeSchema(),
                maxPositionSchema()
            );
    }

    @Test
    @WithMockCustomUser
    void shouldReturnAccessDenied_whenSavingPTCWithoutRiskManage() {
        grant("risk", "read");
        AssertionError exception = assertThrows(AssertionError.class, () -> executeAndVerifyFile("save-pre-trade-check", Map.of("request", ptc1())));
        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @Test
    @WithMockCustomUser
    void shouldDeletePreTradeCheck() {
        grant("risk", "manage");
        String preTradeCheckId = UUID.randomUUID().toString();

        when(riskManagementService.deletePreTradeCheck(preTradeCheckId))
            .thenReturn(Mono.empty());

        assertDoesNotThrow(() -> executeAndVerifyFile("delete-pre-trade-check", Map.of("preTradeCheckId", preTradeCheckId)));

        verify(riskManagementService).deletePreTradeCheck(preTradeCheckId);
    }

    @Test
    @WithMockCustomUser
    void shouldReturnAccessDenied_whenDeletingPTCWithoutRiskManage() {
        grant("risk", "read");
        AssertionError exception = assertThrows(AssertionError.class, () -> executeAndVerifyFile("delete-pre-trade-check", Map.of("preTradeCheckId", UUID.randomUUID().toString())));
        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @Test
    @WithMockCustomUser
    void shouldReturnAccessDenied_whenGettingPTCsWithoutRiskRead() {
        grant("risk", "manage");
        AssertionError exception = assertThrows(AssertionError.class, () -> executeAndVerifyFile("pre-trade-checks"));
        assertThat(exception).hasMessageContaining("Access Denied");
    }

    @Test
    @WithMockCustomUser
    void shouldReturnPreTradeChecks() {
        grant("risk", "read");
        when(riskManagementService.getPreTradeChecks())
            .thenReturn(Mono.just(PreTradeChecksList.newBuilder()
                .addItems(ptc1Proto())
                .addItems(ptc2Proto())
                .build()));

        when(portfolioRepository.find(swissBank().name())).thenReturn(Optional.of(swissBankProto()));
        when(portfolioRepository.find(germanBank().name())).thenReturn(Optional.of(germanBankProto()));

        GraphQlTester.Response response = executeFile("pre-trade-checks", Map.of());
        response.path("$.errors").pathDoesNotExist();
        List<PreTradeCheckResponseDto> returnedChecks = response.path("preTradeChecks").entityList(PreTradeCheckResponseDto.class).get();
        assertThat(returnedChecks)
            .containsOnly(
                ptc1Response(),
                ptc2Response()
            );
    }

    @Test
    @WithMockCustomUser
    void shouldSavePreTradeCheck() {
        grant("risk", "manage");
        when(riskManagementService.savePreTradeCheck(any(PreTradeCheck.class)))
            .thenAnswer(invocation -> Mono.just(invocation.getArgument(0)));

        PreTradeCheckInputDto request = ptc1();
        assertDoesNotThrow(() -> executeAndVerifyFile("save-pre-trade-check", Map.of("request", request)));

        verify(riskManagementService).savePreTradeCheck(ptc1Proto());
    }

    private static PreTradeCheck ptc1Proto() {
        return PreTradeCheck.newBuilder()
            .setId("ptc1")
            .setType("MaxPosition")
            .setLevel(PreTradeCheckLevel.WARN)
            .setEnabled(true)
            .addPortfolios("Swiss Bank")
            .addPortfolios("German Bank")
            .putPortfolioTags("region", "EMEA")
            .addRequestChannels(RequestChannel.API)
            .addRequestChannels(RequestChannel.UI)
            .putProperties("maxValueCurrency", PreTradeCheckPropertyValue.newBuilder().setStringValue("USD").build())
            .putProperties("maxValueWarn", PreTradeCheckPropertyValue.newBuilder().setDecimalValue("100").build())
            .build();
    }

    private static PreTradeCheck ptc2Proto() {
        return PreTradeCheck.newBuilder()
            .setId("ptc2")
            .setType("OrderType")
            .setLevel(PreTradeCheckLevel.BLOCK)
            .setEnabled(true)
            .addPortfolios("Swiss Bank")
            .addPortfolios("German Bank")
            .putPortfolioTags("region", "EMEA")
            .addRequestChannels(RequestChannel.API)
            .addRequestChannels(RequestChannel.UI)
            .putProperties("allowlist", PreTradeCheckPropertyValue.newBuilder().setStringArray(PreTradeCheckPropertyValue.StringArray.newBuilder().addAllStringValue(List.of("LIMIT", "STOP_LIMIT"))).build())
            .build();
    }

    private static PreTradeCheckSchemaDto orderTypeSchema() {
        return new PreTradeCheckSchemaDto(
            "OrderType",
            List.of(
                new PreTradeCheckPropertySchemaDto("allowlist", STRING_LIST, false, true, List.of("LIMIT", "MARKET", "STOP", "STOP_LIMIT")),
                new PreTradeCheckPropertySchemaDto("blocklist", STRING_LIST, false, true, List.of("LIMIT", "MARKET", "STOP", "STOP_LIMIT"))
            )
        );
    }

    private static PreTradeCheckSchemaDto maxPositionSchema() {
        return new PreTradeCheckSchemaDto(
            "MaxPosition",
            List.of(
                new PreTradeCheckPropertySchemaDto("symbol", STRING, true, false, null),
                new PreTradeCheckPropertySchemaDto("maxValueWarn", NUMBER, false, false, null),
                new PreTradeCheckPropertySchemaDto("maxValueCurrency", STRING, false, false, null)
            )
        );
    }

    private static PreTradeCheckSchema orderTypeSchemaProto() {
        return PreTradeCheckSchema.newBuilder()
            .setType("OrderType")
            .putProperties("allowlist", PreTradeCheckPropertySchema.newBuilder()
                .setType(PreTradeCheckPropertyType.STRING_ARRAY)
                .setRequired(false)
                .addAllEnumValues(List.of("LIMIT", "MARKET", "STOP", "STOP_LIMIT"))
                .build())
            .putProperties("blocklist", PreTradeCheckPropertySchema.newBuilder()
                .setType(PreTradeCheckPropertyType.STRING_ARRAY)
                .setRequired(false)
                .addAllEnumValues(List.of("LIMIT", "MARKET", "STOP", "STOP_LIMIT"))
                .build())
            .build();
    }

    private static PreTradeCheckSchema maxPositionSchemaProto() {
        return PreTradeCheckSchema.newBuilder()
            .setType("MaxPosition")
            .putProperties("symbol", PreTradeCheckPropertySchema.newBuilder()
                .setType(PreTradeCheckPropertyType.STRING)
                .setRequired(true)
                .build())
            .putProperties("maxValueWarn", PreTradeCheckPropertySchema.newBuilder()
                .setType(PreTradeCheckPropertyType.DECIMAL)
                .setRequired(false)
                .build())
            .putProperties("maxValueCurrency", PreTradeCheckPropertySchema.newBuilder()
                .setType(PreTradeCheckPropertyType.STRING)
                .setRequired(false)
                .build())
            .build();
    }
}
