package io.wyden.apiserver.rest.apiui.mocks.custody;


import io.wyden.apiserver.rest.trading.model.MutationSubmittedResponse;

import org.springframework.graphql.data.method.annotation.Argument;
import org.springframework.graphql.data.method.annotation.MutationMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

@Controller
public class CustodyTransferGraphQLController {

    @MutationMapping("transfer")
    public Mono<MutationSubmittedResponse> transfer(@Argument TransferRequest input) {
        return Mono.just(new MutationSubmittedResponse("ok"));
    }
}
