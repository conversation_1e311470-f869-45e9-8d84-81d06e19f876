package io.wyden.aeron.snapshot;

import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.agrona.concurrent.AgentRunner;
import org.agrona.concurrent.ShutdownSignalBarrier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.StringJoiner;
import java.util.concurrent.atomic.AtomicInteger;

import io.aeron.cluster.client.AeronCluster;
import io.aeron.cluster.client.EgressListener;
import io.aeron.cluster.codecs.AdminRequestType;
import io.aeron.cluster.codecs.AdminResponseCode;
import io.aeron.driver.MediaDriver;
import io.aeron.driver.ThreadingMode;
import io.aeron.logbuffer.Header;
import io.aeron.samples.cluster.ClusterConfig;

@Service
public class AeronSnapshotService implements DisposableBean, CommandLineRunner {

    private static final Logger LOGGER = LoggerFactory.getLogger(AeronSnapshotService.class);

    private final AtomicInteger exitCode = new AtomicInteger(0);
    private final ShutdownSignalBarrier barrier = new ShutdownSignalBarrier();

    private final ApplicationContext context;
    private final MediaDriver mediaDriver;

    private AeronCluster aeronCluster;
    private AgentRunner agentRunner;

    public AeronSnapshotService(
            final ApplicationContext context,
            @Value("${aeron.cluster.hostnames}") final String[] hostnames,
            @Value("${aeron.cluster.port.base}") final int portBase,
            @Value("${aeron.cluster.client.egress.channel}") final String egressChannel,
            @Value("${aeron.cluster.client.ingress.channel}") final String ingressChannel,
            @Value("${aeron.cluster.client.message.timeoutNs}") final long messageTimeoutNs) throws Exception {

        this.context = context;

        final EgressListener listener = new EgressListener() {

            @Override
            public void onAdminResponse(long clusterSessionId, long correlationId, AdminRequestType requestType, AdminResponseCode responseCode, String message, DirectBuffer payload, int payloadOffset, int payloadLength) {
                if (responseCode == AdminResponseCode.OK) {
                    LOGGER.info("Received AdminResponse clusterSessionId: {} AdminRequestType: {} AdminResponseCode: {}", clusterSessionId, requestType, responseCode);
                    barrier.signal();
                } else {
                    LOGGER.error("Received AdminResponse clusterSessionId: {} AdminRequestType: {} AdminResponseCode: {} Message: {}", clusterSessionId, requestType, responseCode, message);
                    exitCode.set(1);
                    barrier.signal();
                }
            }

            @Override
            public void onMessage(long clusterSessionId, long timestamp, DirectBuffer buffer, int offset, int length, Header header) {
            }
        };

        final Agent agent = new Agent() {

            @Override
            public int doWork() throws Exception {
                return aeronCluster.pollEgress();
            }

            @Override
            public String roleName() {
                return "snapshot-client";
            }
        };

        mediaDriver = MediaDriver.launchEmbedded(new MediaDriver.Context()
            .threadingMode(ThreadingMode.SHARED)
            .dirDeleteOnStart(true)
            .dirDeleteOnShutdown(true));

        AeronCluster.Context clusterContext = new AeronCluster.Context()
                .messageTimeoutNs(messageTimeoutNs)
                .egressListener(listener)
                .egressChannel(egressChannel)
                .aeronDirectoryName(mediaDriver.aeronDirectoryName())
                .ingressChannel(ingressChannel)
                .errorHandler(throwable -> LOGGER.error("AeronCluster Error", throwable))
                .ingressEndpoints(ingressEndpoints(hostnames, portBase));

        try {
            aeronCluster = AeronCluster.connect(clusterContext);
        } catch (Exception e) {
            LOGGER.error("Error connecting to cluster", e);
            mediaDriver.close();
            exitCode.set(5);
            SpringApplication.exit(context, exitCode::get);
        }

        agentRunner = new AgentRunner(
                aeronCluster.context().idleStrategy(),
                Throwable::printStackTrace,
                null,
                agent);

        AgentRunner.startOnThread(agentRunner);
    }

    @Override
    public void run(String... args) throws Exception {

        if (aeronCluster.sendAdminRequestToTakeASnapshot(0)) {
            barrier.await();
        } else {
            LOGGER.error("Failed sending AdminRequestToTakeASnapshot");
            exitCode.set(1);
        }

        SpringApplication.exit(context, exitCode::get);
    }

    @Override
    public void destroy() throws Exception {
        agentRunner.close();
        aeronCluster.close();
        mediaDriver.close();
    }

    private static String ingressEndpoints(final String[] hostnames, final int portBase) {
        final StringJoiner endpointsBuilder = new StringJoiner(",");
        for (int nodeId = 0; nodeId < hostnames.length; nodeId++) {
            final int port = ClusterConfig.calculatePort(nodeId, portBase, ClusterConfig.CLIENT_FACING_PORT_OFFSET);
            endpointsBuilder.add(nodeId + "=" + hostnames[nodeId] + ":" + port);
        }
        return endpointsBuilder.toString();
    }
}
