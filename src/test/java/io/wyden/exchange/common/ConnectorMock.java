package io.wyden.exchange.common;

import ch.algotrader.api.connector.application.Connector;
import ch.algotrader.api.connector.ops.diagnostics.DiagnosticController;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Flux;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

public class ConnectorMock {

    @Configuration
    public static class ConnectorTestConfiguration {
        @Bean
        Connector connector() {
            Connector connectorMock = mock(Connector.class);

            DiagnosticController diagnosticController = mock(DiagnosticController.class);
            doReturn(Flux.empty()).when(diagnosticController).getEventFlux();
            doReturn(diagnosticController).when(connectorMock).getDiagnosticController();
            return connectorMock;
        }
    }


}