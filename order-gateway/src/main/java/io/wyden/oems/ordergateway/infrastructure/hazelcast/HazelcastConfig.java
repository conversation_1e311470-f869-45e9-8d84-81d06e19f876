package io.wyden.oems.ordergateway.infrastructure.hazelcast;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.client.config.ClientNetworkConfig;
import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.oems.ordergateway.domain.map.ClientOrderStateMapConfig;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.ReferenceDataProvider;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.InstrumentMapConfig;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

import static io.wyden.cloudutils.hazelcast.NearCaches.defaultEvictionConfig;
import static io.wyden.cloudutils.hazelcast.NearCaches.defaultNearCacheConfig;

@Configuration
public class HazelcastConfig {

    @Bean
    ReferenceDataProvider referenceDataProvider() {
        return new ReferenceDataProvider();
    }

    @Bean
    public InstrumentsCacheFacade instrumentsCacheFacade(HazelcastInstance hazelcast,
                                                         ReferenceDataProvider referenceDataProvider,
                                                         Tracing otlTracing) {
        return referenceDataProvider.getInstrumentsCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public VenueAccountCacheFacade venueAccountCacheFacade(HazelcastInstance hazelcast,
                                                           ReferenceDataProvider referenceDataProvider,
                                                           Tracing otlTracing) {
        return referenceDataProvider.getVenueAccountCacheFacade(hazelcast, otlTracing);
    }

    @Bean
    public ClientOrderStateMapConfig clientOrderStateMapConfig() {
        return new ClientOrderStateMapConfig();
    }

    @Bean
    public InstrumentMapConfig instrumentMapConfig() {
        return new InstrumentMapConfig();
    }

    @Bean
    public VenueAccountMapConfig venueAccountMapConfig() {
        return new VenueAccountMapConfig();
    }

    @Bean
    public ClientConfig createClientConfig(@Value("${hz.addressList}") String addressList,
                                           @Value("${hz.outboundPortDefinition}") String outboundPortDefinition,
                                           List<HazelcastMapConfig> hazelcastMaps,
                                           InstrumentMapConfig instrumentMapConfig,
                                           VenueAccountMapConfig venueAccountMapConfig) {
        ClientConfig clientConfig = new ClientConfig();
        clientConfig.getConnectionStrategyConfig().getConnectionRetryConfig().setMaxBackoffMillis(5000);

        ClientNetworkConfig networkConfig = clientConfig.getNetworkConfig();
        Arrays.stream(addressList.split(",")).forEach(networkConfig::addAddress);
        networkConfig.setSmartRouting(true);

        if (!outboundPortDefinition.isBlank()) {
            networkConfig.addOutboundPortDefinition(outboundPortDefinition);
        }

        networkConfig.setRedoOperation(true);
        networkConfig.setConnectionTimeout(5000);

        hazelcastMaps.forEach(m -> m.applyConfig(clientConfig));

        instrumentMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));
        venueAccountMapConfig.addNearCache(clientConfig, defaultNearCacheConfig(defaultEvictionConfig()));

        return clientConfig;
    }

    @Bean("hazelcast")
    public HazelcastInstance createHazelcastInstance(ClientConfig clientConfig, Telemetry telemetry, List<HazelcastMapConfig> hazelcastMaps) {
        HazelcastInstance hz = HazelcastClient.newHazelcastClient(clientConfig);
        hazelcastMaps.forEach(m -> m.setupMemberInstance(hz));
        hazelcastMaps.forEach(m -> m.setupMetrics(hz, telemetry.getMeterRegistry()));
        return hz;
    }

}
