package io.wyden.test.scenariorunner.ui;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus;
import io.wyden.apiserver.rest.apiui.SharedModel.OrderType;
import io.wyden.apiserver.rest.apiui.SharedModel.Side;
import io.wyden.apiserver.rest.apiui.SharedModel.TIF;
import io.wyden.apiserver.rest.orderhistory.model.OrderStateResponse;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.ui.pages.tradingpage.ActiveOrdersWidget.ActiveOrdersRow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.HashMap;
import java.util.stream.Stream;

import static io.wyden.test.scenariorunner.assertion.trading.OrderStatePredicate.clientIdAndStatusAre;
import static io.wyden.test.scenariorunner.data.infra.Epics.UI;
import static io.wyden.test.scenariorunner.ui.TestDefaults.DEFAULT_LIMIT_PRICE;
import static io.wyden.test.scenariorunner.ui.TestDefaults.DEFAULT_QUANTITY;
import static io.wyden.test.scenariorunner.ui.TestDefaults.DEFAULT_STOP_PRICE;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(UI)
@GraphQL
public class UITradingTest extends UIBaseTest {

    @BeforeEach
    void setUp() {
        uiActor = uiActorExtension.uiActor();
        uiActor.tradingPage().orderEntryFormWidget().venueDropdown()
            .chooseVenue(VenueAccountConstants.MOCK_VENUE);
        uiActor.tradingPage().orderEntryFormWidget()
            .chooseVenueAccount(connectorSessionExtension.venueAccountName());
    }

    static Stream<Arguments> traderExecutesStreetSideOrder() {
        return Stream.of(
            Arguments.of(OrderType.MARKET, Side.BUY),
            Arguments.of(OrderType.LIMIT, Side.BUY),
            Arguments.of(OrderType.STOP, Side.BUY),
            Arguments.of(OrderType.STOP_LIMIT, Side.BUY),
            Arguments.of(OrderType.MARKET, Side.SELL),
            Arguments.of(OrderType.LIMIT, Side.SELL),
            Arguments.of(OrderType.STOP, Side.SELL),
            Arguments.of(OrderType.STOP_LIMIT, Side.SELL)
        );
    }

    @ParameterizedTest
    @MethodSource
    void traderExecutesStreetSideOrder(OrderType orderType, Side orderSide, ConnectorMockSession connectorMockSession) {
        GraphQLActor graphQLActor = (GraphQLActor) clientActorExtension.actor();
        uiActor.tradingPage().orderEntryFormWidget()
            .chooseOrderType(orderType)
            .enterDefaultOrderFor(orderType)
            .chooseTIF(TIF.GTC)
            .submitOrderWithSide(orderSide);

        connectorMockSession.acceptNewOrder();
        connectorMockSession.fillFull(DEFAULT_QUANTITY, DEFAULT_LIMIT_PRICE);

        OrderStateResponse orderStateResponse =
            graphQLActor.orderState().await(clientIdAndStatusAre(graphQLActor, OrderStatus.FILLED));

        HashMap<ActiveOrdersRow, String> orderRow = uiActor
            .tradingPage()
            .activeOrdersWidget()
            .getOrderByOrderId(orderStateResponse.getOrderId());

        assertThat(orderRow.get(ActiveOrdersRow.ORDER_STATUS))
            .isEqualTo("Filled");
        assertThat(orderRow.get(ActiveOrdersRow.SIDE))
            .isEqualTo(orderSide.toString());
        verifyOrderPrices(orderRow, orderType);
    }

    public Double normalize(String value) {
        if (value == null || value.trim().equals("-")) {
            return null;
        }
        return Double.parseDouble(value.replace(",", "").trim());
    }

    @Step("Verify order prices")
    private void verifyOrderPrices(HashMap<ActiveOrdersRow, String> orderRow, OrderType orderType) {
        switch (orderType) {
            case MARKET:
                assertThat(normalize(orderRow.get(ActiveOrdersRow.AVG_PRICE)))
                    .isEqualTo(DEFAULT_LIMIT_PRICE);
                assertThat(normalize(orderRow.get(ActiveOrdersRow.LIMIT_PRICE)))
                    .isNull();
                assertThat(normalize(orderRow.get(ActiveOrdersRow.STOP_PRICE)))
                    .isNull();
                break;
            case LIMIT:
                assertThat(normalize(orderRow.get(ActiveOrdersRow.AVG_PRICE)))
                    .isEqualTo(DEFAULT_LIMIT_PRICE);
                assertThat(normalize(orderRow.get(ActiveOrdersRow.LIMIT_PRICE)))
                    .isEqualTo(DEFAULT_LIMIT_PRICE);
                assertThat(normalize(orderRow.get(ActiveOrdersRow.STOP_PRICE)))
                    .isNull();
                break;
            case STOP:
                assertThat(normalize(orderRow.get(ActiveOrdersRow.AVG_PRICE)))
                    .isEqualTo(DEFAULT_LIMIT_PRICE);
                assertThat(normalize(orderRow.get(ActiveOrdersRow.LIMIT_PRICE)))
                    .isNull();
                assertThat(normalize(orderRow.get(ActiveOrdersRow.STOP_PRICE)))
                    .isEqualTo(DEFAULT_STOP_PRICE);
                break;
            case STOP_LIMIT:
                assertThat(normalize(orderRow.get(ActiveOrdersRow.AVG_PRICE)))
                    .isEqualTo(DEFAULT_LIMIT_PRICE);
                assertThat(normalize(orderRow.get(ActiveOrdersRow.LIMIT_PRICE)))
                    .isEqualTo(DEFAULT_LIMIT_PRICE);
                assertThat(normalize(orderRow.get(ActiveOrdersRow.STOP_PRICE)))
                    .isEqualTo(DEFAULT_STOP_PRICE);
                break;
        }
    }
}
