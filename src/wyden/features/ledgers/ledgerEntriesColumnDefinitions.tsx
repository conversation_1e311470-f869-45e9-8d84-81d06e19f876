import {
  buildObjectKeyValidator,
  getCurrencyBasedMoneyField,
  getDateColumnName,
  getGenericField,
} from '@wyden/features/grid/utils';
import { LedgerEntryResponse } from '@wyden/services/graphql/generated/graphql';
import { ColDef } from 'ag-grid-community';
import { DateRendererForMsTimestamp } from '../grid/DateRenderer';
import { ActionsRenderer } from './LedgersActionsRenderer';
import i18n from '@wyden/i18n';
import { formatLedgerType } from '@wyden/hooks/useEnumFormatters';

const validLedgerKey = buildObjectKeyValidator<LedgerEntryResponse>();

export const ledgerEntryAccountNameKey = validLedgerKey('accountName');
export const ledgerEntryCurrencyKey = validLedgerKey('currency');
export const ledgerEntryIdKey = validLedgerKey('id');
export const ledgerEntryOrderIdKey = validLedgerKey('orderId');
export const ledgerEntryPortfolioIdKey = validLedgerKey('portfolioId');
export const ledgerEntryPortfolioNameKey = validLedgerKey('portfolioName');
export const ledgerEntryPriceKey = validLedgerKey('price');
export const ledgerEntryQuantityKey = validLedgerKey('quantity');
export const ledgerEntrySymbolKey = validLedgerKey('symbol');
export const ledgerEntryTransactionIdKey = validLedgerKey('transactionId');
export const ledgerEntryTypeKey = validLedgerKey('type');
export const ledgerEntryUpdatedAtKey = validLedgerKey('updatedAt');
export const actionsKey = 'actions';
export const ledgerBalanceBeforeKey = validLedgerKey('balanceBefore');
export const ledgerBalanceAfterKey = validLedgerKey('balanceAfter');

export const ledgerEntriesDefinitions: ColDef<LedgerEntryResponse>[] = [
  getGenericField(ledgerEntrySymbolKey, { sortable: false }),
  {
    ...getGenericField(ledgerEntryUpdatedAtKey),
    resizable: true,
    cellRenderer: DateRendererForMsTimestamp('updatedAt'),
    sort: 'desc' as const,
    headerName: getDateColumnName('updatedAt'),
  },
  getGenericField(ledgerEntryIdKey, { sortable: false }),
  getGenericField(ledgerEntryOrderIdKey, { sortable: false }),
  getGenericField(ledgerEntryQuantityKey, { sortable: false }),
  { ...getCurrencyBasedMoneyField(ledgerEntryPriceKey, ledgerEntryCurrencyKey), sortable: false },
  getGenericField(ledgerEntryTypeKey, {
    sortable: false,
    valueGetter: (params) => formatLedgerType(i18n.t, params.data?.type),
  }),
  getGenericField(ledgerEntryCurrencyKey, { sortable: false }),
  getGenericField(ledgerEntryPortfolioIdKey, { sortable: false }),
  getGenericField(ledgerEntryPortfolioNameKey, { sortable: false }),
  getGenericField(ledgerEntryTransactionIdKey, { sortable: false }),
  getGenericField(ledgerEntryAccountNameKey, { sortable: false }),
  getGenericField(actionsKey, {
    headerName: '',
    width: 40,
    minWidth: 40,
    maxWidth: 40,
    cellRenderer: ActionsRenderer,
    pinned: 'right',
    lockPinned: true,
    sortable: false,
  }),
  {
    ...getCurrencyBasedMoneyField(ledgerBalanceBeforeKey, ledgerEntryCurrencyKey),
    sortable: false,
  },
  { ...getCurrencyBasedMoneyField(ledgerBalanceAfterKey, ledgerEntryCurrencyKey), sortable: false },
];

export const defaultLedgerEntriesVisibleFields = [
  ledgerEntryIdKey,
  ledgerEntryTypeKey,
  ledgerEntryPortfolioNameKey,
  ledgerEntryAccountNameKey,
  ledgerEntrySymbolKey,
  ledgerEntryQuantityKey,
  ledgerEntryCurrencyKey,
  ledgerEntryTransactionIdKey,
  ledgerEntryUpdatedAtKey,
  actionsKey,
  ledgerBalanceBeforeKey,
  ledgerBalanceAfterKey,
];
