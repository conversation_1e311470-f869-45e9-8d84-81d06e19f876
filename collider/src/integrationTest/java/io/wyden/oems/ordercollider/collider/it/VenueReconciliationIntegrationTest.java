package io.wyden.oems.ordercollider.collider.it;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.micrometer.common.util.StringUtils;
import io.wyden.oems.ordercollider.domain.map.VenueOpenOrdersMapConfig;
import io.wyden.oems.ordercollider.domain.map.VenueOrderStateMapConfig;
import io.wyden.oems.ordercollider.model.VenueOrderState;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.venue.VenueReconciliationRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class VenueReconciliationIntegrationTest extends ColliderIntegrationTestBase {

    @Autowired
    HazelcastInstance hazelcast;

    @Test
    void givenPendingOrdersWhenReconciliationCapabilityBecomesAliveReconciliationRequestIsSent() {
        IMap<String, VenueOrderState> venueOrderStateMap = VenueOrderStateMapConfig.getMap(hazelcast);

        client.emitsOemsRequest(testingData.clientSingleOrderRequest(OemsOrderType.MARKET, OemsRequest.OemsPTCStatus.APPROVED));
        connector.awaitVenueRequest();
        connector.emits(testingData.correlationReportOfOrderIdIntId().build());

        Awaitility.await()
            .until(() -> StringUtils.isNotBlank(venueOrderStateMap.get(testingData.orderId).getIntId()));

        VenueOrderState state = venueOrderStateMap.get(testingData.orderId);
        VenueOpenOrdersMapConfig.getMap(hazelcast).put(testingData.venueAccount, List.of(state));

        client.emitReconciliationStateChangedEvent();

        VenueReconciliationRequest request = client.awaitReconciliationRequest();
        assertThat(request.getVenueOrdersCount()).isEqualTo(1);
        assertThat(request.getVenueOrders(0).getIntId()).isEqualTo(testingData.intId);
    }

    @Test
    void givenPendingOrdersWhenTradingCapabilityBecomesAliveAndReconciliationIsAliveThenReconciliationRequestIsSent() {
        IMap<String, VenueOrderState> venueOrderStateMap = VenueOrderStateMapConfig.getMap(hazelcast);

        client.emitsOemsRequest(testingData.clientSingleOrderRequest(OemsOrderType.MARKET, OemsRequest.OemsPTCStatus.APPROVED));
        connector.awaitVenueRequest();
        connector.emits(testingData.correlationReportOfOrderIdIntId().build());

        Awaitility.await()
            .until(() -> StringUtils.isNotBlank(venueOrderStateMap.get(testingData.orderId).getIntId()));

        VenueOrderState state = venueOrderStateMap.get(testingData.orderId);
        VenueOpenOrdersMapConfig.getMap(hazelcast).put(testingData.venueAccount, List.of(state));

        client.emitTradingStateChangedEvent();

        VenueReconciliationRequest request = client.awaitReconciliationRequest();
        assertThat(request.getVenueOrdersCount()).isEqualTo(1);
        assertThat(request.getVenueOrders(0).getIntId()).isEqualTo(testingData.intId);
    }
}
