package io.wyden.connector.mock.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.CorsRegistry;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.WebFluxConfigurer;

@Configuration
@EnableWebFlux
public class CorsGlobalConfiguration implements WebFluxConfigurer {

    private static final String ALL = "*";
    private static final String ALL_PATHS = "/**";

    @Override
    public void addCorsMappings(CorsRegistry corsRegistry) {
        corsRegistry.addMapping(ALL_PATHS)
            .allowedOrigins(ALL)
            .allowedMethods(ALL)
            .allowedHeaders(ALL);
    }
}
