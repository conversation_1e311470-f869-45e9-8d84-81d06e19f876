package io.wyden.connector.rate.io;

import com.google.protobuf.Message;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.ProtobufUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.BidAskQuote;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.Trade;
import io.wyden.published.rate.Rate;
import io.wyden.published.rate.RateEvent;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static io.wyden.connector.infra.Meters.RATE_EVENT_OUTGOING_COUNT;

public class RateEventEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(RateEventEmitter.class);
    private static final String RATE_SERVICE_HEADER = "rate-service";

    private final RabbitExchange<RateEvent> rateEventExchange;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public RateEventEmitter(RabbitIntegrator rabbitIntegrator, Telemetry telemetry) {
        this.rateEventExchange = OemsExchange.Rate.declareRateEventExchange(rabbitIntegrator);
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    public void emit(String baseCurrency, String quoteCurrency, Message message) {
        try (var ignored = otlTracing.createSpan("rateevent.emit", SpanKind.PRODUCER)) {
            emitInner(baseCurrency, quoteCurrency, message);
        }
    }

    private void emitInner(String baseCurrency, String quoteCurrency, Message message) {
        LOGGER.trace("Emitting new Message: {}", message);
        RateEvent rateEvent;

        if (message instanceof BidAskQuote bidAskQuote) {
            rateEvent = convertBidAskQuote(bidAskQuote);
        } else if (message instanceof Trade trade) {
            rateEvent = convertTrade(trade);
        } else if (message instanceof OrderBook orderBook) {
            rateEvent = convertOrderBook(orderBook);
        } else if (message instanceof Rate conversionRate) {
            rateEvent = convertConversionRate(conversionRate);
        } else {
            LOGGER.warn("Unknown message type: {}, message: {}", message.getClass().getSimpleName(), ProtobufUtils.shortDebugString(message));
            return;
        }

        rateEvent = rateEvent.toBuilder()
            .setMetadata(Metadata.newBuilder()
                .setResponseId(UUID.randomUUID().toString())
                .setCreatedAt(ZonedDateTime.now().toString())
                .build())
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .build();

        updateMetrics(rateEvent);
        emit(rateEvent, rateEventExchange);
    }

    private RateEvent convertBidAskQuote(BidAskQuote bidAskQuote) {
        return RateEvent.newBuilder()
            .setIdentifier(bidAskQuote.getIdentifier())
            .setValue(calculateAvgRate(bidAskQuote.getBidPrice(), bidAskQuote.getAskPrice()))
            .build();
    }

    private RateEvent convertTrade(Trade trade) {
        return RateEvent.newBuilder()
            .setIdentifier(trade.getIdentifier())
            .setValue(trade.getLastPrice())
            .build();
    }

    private RateEvent convertOrderBook(OrderBook orderBook) {
        return RateEvent.newBuilder()
            .setIdentifier(orderBook.getIdentifier())
            .setValue(calculateAvgRate(orderBook.getTopBid().getPrice(), orderBook.getTopAsk().getPrice()))
            .build();
    }

    private RateEvent convertConversionRate(Rate conversionRate) {
        return RateEvent.newBuilder()
            .setValue(conversionRate.getValue())
            .build();
    }

    @NotNull
    private <T extends Message> void emit(T message, RabbitExchange<T> exchange) {
        try {
            Map<String, String> headers = Map.of(
                OemsHeader.SERVICE.getHeaderName(), RATE_SERVICE_HEADER
            );

            LOGGER.trace("Emitting rate event: {}", ProtobufUtils.shortDebugString(message));
            exchange.tryPublishWithHeaders(message, headers);
        } catch (Exception ex) {
            LOGGER.error("Failed to emit rate event", ex);
        }
    }

    private static String calculateAvgRate(String lowerValue, String higherValue) {
        return bd(lowerValue).add(bd(higherValue)).divide(bd(2), RoundingMode.HALF_UP).toPlainString();
    }

    private void updateMetrics(RateEvent rateEvent) {
        try {
            this.meterRegistry.counter(RATE_EVENT_OUTGOING_COUNT,
                "baseCurrency", rateEvent.getBaseCurrency(),
                "quoteCurrency", rateEvent.getQuoteCurrency()).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}