package io.wyden.booking.application.reservation;

import io.wyden.booking.application.SimpleJpaRepository;
import io.wyden.booking.application.querylimit.QueryLimitValidator;
import io.wyden.booking.domain.instrument.Currency_;
import io.wyden.booking.domain.position.AccountReference_;
import io.wyden.booking.domain.position.PortfolioReference_;
import io.wyden.booking.domain.reservation.Reservation;
import io.wyden.booking.domain.reservation.ReservationRepository;
import io.wyden.booking.domain.reservation.Reservation_;
import io.wyden.booking.domain.reservation.trade.CashTradeReservation_;
import io.wyden.booking.domain.reservation.trade.ClientCashTradeReservation_;
import io.wyden.booking.domain.reservation.trade.StreetCashTradeReservation_;
import io.wyden.booking.domain.reservation.trade.TradeReservation_;
import io.wyden.booking.infrastructure.telemetry.Meters;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.interfaces.rest.RequestModel.ReservationSearch;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.DateUtils;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.metamodel.SingularAttribute;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import static io.wyden.booking.application.transaction.TransactionPredicates.matchTransactionType;
import static io.wyden.booking.domain.position.ReferenceTypesMapper.map;
import static io.wyden.booking.infrastructure.telemetry.Meters.ReservationQueryType.COUNT_BY_PROPERTIES;
import static io.wyden.booking.infrastructure.telemetry.Meters.ReservationQueryType.DELETE_BY_PROPERTIES;
import static io.wyden.booking.infrastructure.telemetry.Meters.ReservationQueryType.FIND_BY_PROPERTIES;
import static io.wyden.booking.infrastructure.telemetry.Meters.ReservationQueryType.FIND_BY_RESERVATION_REF;
import static io.wyden.booking.infrastructure.telemetry.Meters.ReservationQueryType.SAVE;
import static io.wyden.booking.infrastructure.telemetry.Meters.bookingReservationQueryLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.springframework.transaction.annotation.Propagation.REQUIRED;

@Repository
public class JpaReservationRepository extends SimpleJpaRepository<Reservation, Long> implements ReservationRepository {

    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;
    private final QueryLimitValidator queryLimitValidator;

    public JpaReservationRepository(EntityManager entityManager,
                                    Telemetry telemetry,
                                    QueryLimitValidator queryLimitValidator) {
        super(entityManager, Reservation.class);
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.queryLimitValidator = queryLimitValidator;
    }

    @Override
    @Transactional(propagation = REQUIRED)
    public <S extends Reservation> S save(S entity) {
        try (var ignored = otlTracing.createSpan("booking.save.reservation", SpanKind.CLIENT)) {
            return recordLatencyIn(latencyTimer(SAVE)).of(() -> super.save(entity));
        }
    }

    @Override
    public List<Reservation> findByProperties(ReservationSearch request) {
        try (var ignored = otlTracing.createSpan("booking.lookup.reservation.findbyproperties", SpanKind.CLIENT)) {
            return recordLatencyIn(latencyTimer(FIND_BY_PROPERTIES)).of(() -> doFindByProperties(request));
        }
    }

    @Override
    public long countByProperties(ReservationSearch search) {
        return recordLatencyIn(latencyTimer(COUNT_BY_PROPERTIES)).of(() -> doCountByProperties(search, EMPTY));
    }

    @Override
    public long countByProperties(ReservationSearch search, String after) {
        return recordLatencyIn(latencyTimer(COUNT_BY_PROPERTIES)).of(() -> doCountByProperties(search, after));
    }

    private long doCountByProperties(ReservationSearch search, String after) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
        Root<Reservation> reservation = countQuery.from(Reservation.class);
        List<Predicate> predicates = getPredicates(search, builder, reservation);

        if (StringUtils.isNotBlank(after)) {
            long cursor = Long.parseLong(search.after());
            predicates.add(builder.greaterThan(reservation.get(Reservation_.id), cursor));
        }

        countQuery
            .select(builder.count(reservation.get(Reservation_.id)))
            .where(predicates.toArray(Predicate[]::new));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    @Override
    public void deleteByProperties(ReservationSearch request) {
        recordLatencyIn(latencyTimer(DELETE_BY_PROPERTIES)).of(() ->
            findByProperties(request)
                .forEach(entityManager::remove));
    }

    @Override
    public Optional<Reservation> findByReservationRef(String reservationRef) {
        return recordLatencyIn(latencyTimer(FIND_BY_RESERVATION_REF)).of(() -> doFindByReservationRef(reservationRef));
    }

    private Optional<Reservation> doFindByReservationRef(String reservationRef) {
        try (var ignored = otlTracing.createSpan("booking.lookup.reservation.findbyreservationref", SpanKind.CLIENT)) {
            CriteriaBuilder builder = entityManager.getCriteriaBuilder();
            CriteriaQuery<Reservation> searchQuery = builder.createQuery(Reservation.class);
            Root<Reservation> reservation = searchQuery.from(Reservation.class);

            Predicate reservationRefPredicate = builder.equal(reservation.get(Reservation_.reservationRef), reservationRef);

            searchQuery
                .select(reservation)
                .where(reservationRefPredicate);

            return entityManager
                .createQuery(searchQuery)
                .getResultList()
                .stream().findFirst();
        }
    }

    private List<Reservation> doFindByProperties(ReservationSearch search) {
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Reservation> searchQuery = builder.createQuery(Reservation.class);
        Root<Reservation> reservations = searchQuery.from(Reservation.class);
        List<Predicate> predicates = getPredicates(search, builder, reservations);

        if (StringUtils.isNotBlank(search.after())) {
            long cursor = Long.parseLong(search.after());
            predicates.add(builder.greaterThan(reservations.get(Reservation_.id), cursor));
        }

        searchQuery
            .select(reservations)
            .where(predicates.toArray(Predicate[]::new));

        if (search.sortingOrder() != null) {
            if (search.sortingOrder() == RequestModel.SortingOrder.ASC) {
                searchQuery.orderBy(
                    builder.asc(reservations.get(Reservation_.dateTime)),
                    builder.asc(reservations.get(Reservation_.id)));
            } else {
                searchQuery.orderBy(
                    builder.desc(reservations.get(Reservation_.dateTime)),
                    builder.desc(reservations.get(Reservation_.id)));
            }
        }

        TypedQuery<Reservation> typedQuery = entityManager.createQuery(searchQuery);

        if (search.first() != null) {
            typedQuery.setMaxResults(search.first());
        }

        return typedQuery.getResultList();
    }

    private List<Predicate> getPredicates(ReservationSearch request,
                                          CriteriaBuilder builder,
                                          Root<Reservation> reservation) {

        queryLimitValidator.validate(request);

        // SingularAttribute does not provide type parameters <>, because SingularAttribute need concrete implementation class
        // to be compatible with Root<Reservation>. In other words, we would need Trade_ for currency, AssetTrade_ for security, etc.
        // which is too restrictive and does not work well with SINGLE_TABLE inheritance in JPA

        SingularAttribute currencyAttribute = TradeReservation_.currency;
        SingularAttribute baseCurrencyAttribute = CashTradeReservation_.baseCurrency;
        SingularAttribute portfolioAttribute = ClientCashTradeReservation_.portfolio;
        SingularAttribute counterPortfolioAttribute = ClientCashTradeReservation_.counterPortfolio;

        Join portfolio = reservation.join(portfolioAttribute, JoinType.LEFT);
        Join counterPortfolio = reservation.join(counterPortfolioAttribute, JoinType.LEFT);
        SingularAttribute accountAttribute = StreetCashTradeReservation_.account;
        Join account = reservation.join(accountAttribute, JoinType.LEFT);

        List<Predicate> predicates = new LinkedList<>();

        if (!request.symbol().isEmpty()) {
            Predicate currencyPredicate = reservation.get(currencyAttribute).get(Currency_.currency).in(request.symbol());
            Predicate baseCurrencyPredicate = reservation.get(baseCurrencyAttribute).get(Currency_.currency).in(request.symbol());
            predicates.add(builder.or(currencyPredicate, baseCurrencyPredicate));
        }

        predicates.add(builder.or(
            createPortfolioPredicates(portfolio, counterPortfolio, request, builder, reservation),
            createAccountPredicates(account, request, builder, reservation)
        ));

        if (!request.portfolio().isEmpty()) {
            predicates.add(builder.or(
                portfolio.get(PortfolioReference_.portfolioId).in(request.portfolio()),
                counterPortfolio.get(PortfolioReference_.portfolioId).in(request.portfolio())));
        }

        if (!request.accountId().isEmpty()) {
            predicates.add(account.get(AccountReference_.accountId).in(request.accountId()));
        }

        predicates.add(getPortfolioTypePredicate(portfolio, request, builder));
        predicates.add(getPortfolioTypePredicate(counterPortfolio, request, builder));
        predicates.add(getAccountTypePredicate(account, request, builder));

        if (isNotBlank(request.reservationRef())) {
            Predicate reservationRefPredicate = builder.equal(reservation.get(Reservation_.reservationRef), request.reservationRef());
            predicates.add(reservationRefPredicate);
        }

        if (!request.transactionType().isEmpty()) {
            Predicate[] transactionTypePredicates = request.transactionType().stream()
                .flatMap(transactionType -> matchTransactionType(transactionType).stream())
                .map(transactionType -> builder.equal(reservation.get(Reservation_.transactionType), transactionType))
                .toArray(Predicate[]::new);
            predicates.add(builder.or(transactionTypePredicates));
        }

        if (isNotBlank(request.from())) {
            ZonedDateTime after = DateUtils.epochMillisToZonedDateTime(request.from());
            Predicate fromPredicate = builder.greaterThanOrEqualTo(reservation.get(Reservation_.dateTime), after);
            predicates.add(fromPredicate);
        }

        if (isNotBlank(request.to())) {
            ZonedDateTime before = DateUtils.epochMillisToZonedDateTime(request.to());
            Predicate toPredicate = builder.lessThan(reservation.get(Reservation_.dateTime), before);
            predicates.add(toPredicate);
        }

        return predicates;
    }

    private Predicate createAccountPredicates(Join account, ReservationSearch request, CriteriaBuilder builder, Root<Reservation> root) {

        List<Predicate> predicates = new LinkedList<>();

        if (RequestModel.AccountType.ALL.equals(request.accountType())) {

            predicates.add(account.get(AccountReference_.accountId).isNotNull());

        } else if (RequestModel.AccountType.NONE.equals(request.accountType())) {

            //predicates.add(account.get(AccountReference_.accountId).isNull());
            predicates.add(builder.disjunction());

        } else if (RequestModel.AccountType.WALLET.equals(request.accountType())) {
            Predicate accountTypeEmptyPredicate = account.get(AccountReference_.accountType).isNull();
            Predicate accountTypePredicate = account.get(AccountReference_.accountType).in(request.accountType().name());

            if (RequestModel.WalletType.NOSTRO.equals(request.walletType()) || RequestModel.WalletType.VOSTRO.equals(request.walletType())) {
                Predicate walletTypePredicate = account.get(AccountReference_.walletType).in(map(request.walletType()).name());
                Predicate emptywalletTypePredicate = account.get(AccountReference_.walletType).isNull();

                predicates.add(builder.and(
                    builder.or(accountTypeEmptyPredicate, accountTypePredicate),
                    builder.or(emptywalletTypePredicate, walletTypePredicate)));
            } else {
                predicates.add(builder.or(accountTypeEmptyPredicate, accountTypePredicate));
            }
        } else {
            predicates.add(account.get(AccountReference_.accountId).in(request.accountId()));
        }

        return builder.and(predicates.toArray(Predicate[]::new));
    }

    private Predicate createPortfolioPredicates(Join portfolio, Join counterPortfolio, ReservationSearch request, CriteriaBuilder builder, Root<Reservation> root) {

        List<Predicate> predicates = new LinkedList<>();

        if (RequestModel.PortfolioType.ALL.equals(request.portfolioType())) {
            predicates.add(builder.or(
                portfolio.get(PortfolioReference_.portfolioId).isNotNull(),
                counterPortfolio.get(PortfolioReference_.portfolioId).isNotNull()));
        } else if (RequestModel.PortfolioType.NONE.equals(request.portfolioType())) {
            predicates.add(builder.disjunction());
        } else if (RequestModel.PortfolioType.VOSTRO.equals(request.portfolioType()) || RequestModel.PortfolioType.NOSTRO.equals(request.portfolioType())) {
            Predicate portfolioTypeEmptyPredicate = portfolio.get(PortfolioReference_.portfolioType).isNull();
            Predicate portfolioTypePredicate = portfolio.get(PortfolioReference_.portfolioType).in(map(request.portfolioType()).name());
            Predicate isPortfolio = portfolio.get(PortfolioReference_.portfolioId).isNotNull();

            Predicate counterPortfolioTypeEmptyPredicate = portfolio.get(PortfolioReference_.portfolioType).isNull();
            Predicate counterPortfolioTypePredicate = portfolio.get(PortfolioReference_.portfolioType).in(map(request.portfolioType()).name());
            Predicate isCounterPortfolio = portfolio.get(PortfolioReference_.portfolioId).isNotNull();

            predicates.add(builder.and(
                builder.or(isPortfolio, isCounterPortfolio),
                builder.or(portfolioTypeEmptyPredicate, portfolioTypePredicate),
                builder.or(counterPortfolioTypeEmptyPredicate, counterPortfolioTypePredicate)));
        } else {
            predicates.add(builder.or(
                portfolio.get(PortfolioReference_.portfolioId).in(request.portfolio()),
                counterPortfolio.get(PortfolioReference_.portfolioId).in(request.portfolio())));
        }

        return builder.and(predicates.toArray(Predicate[]::new));
    }

    private Timer latencyTimer(Meters.ReservationQueryType queryType) {
        try {
            return bookingReservationQueryLatencyTimer(meterRegistry, queryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }

    private static Predicate getPortfolioTypePredicate(Join portfolio, ReservationSearch search, CriteriaBuilder builder) {

        if (RequestModel.PortfolioType.NONE.equals(search.portfolioType())) {
            return portfolio.get(PortfolioReference_.portfolioId).isNull();
        }

        if (RequestModel.PortfolioType.VOSTRO.equals(search.portfolioType()) || RequestModel.PortfolioType.NOSTRO.equals(search.portfolioType())) {
            Predicate portfolioTypeEmptyPredicate = portfolio.get(PortfolioReference_.portfolioType).isNull();
            Predicate portfolioTypePredicate = portfolio.get(PortfolioReference_.portfolioType).in(map(search.portfolioType()).name());
            Predicate inAuthorizedPredicate = portfolio.get(PortfolioReference_.portfolioId).in(search.portfolio());

            return builder.or(
                portfolioTypeEmptyPredicate,
                portfolioTypePredicate,
                inAuthorizedPredicate
            );
        }
        return builder.conjunction();
    }

    private static Predicate getAccountTypePredicate(Join account, ReservationSearch search, CriteriaBuilder builder) {
        if (RequestModel.AccountType.NONE.equals(search.accountType())) {
            return account.get(AccountReference_.accountId).isNull();
        }

        if (RequestModel.AccountType.WALLET.equals(search.accountType())) {

            Predicate accountTypeEmptyPredicate = account.get(AccountReference_.accountType).isNull();
            Predicate accountTypePredicate = account.get(AccountReference_.accountType).in(map(search.accountType()).name());
            Predicate inAuthorizedPredicate = account.get(AccountReference_.accountId).in(search.accountId());

            if (RequestModel.WalletType.NOSTRO.equals(search.walletType()) || RequestModel.WalletType.VOSTRO.equals(search.walletType())) {
                Predicate walletTypePredicate = account.get(AccountReference_.walletType).in(map(search.walletType()).name());
                Predicate emptywalletTypePredicate = account.get(AccountReference_.walletType).isNull();

                return builder.and(
                    builder.or(accountTypeEmptyPredicate, accountTypePredicate, inAuthorizedPredicate),
                    builder.or(emptywalletTypePredicate, walletTypePredicate, inAuthorizedPredicate));
            } else {
                return builder.or(accountTypeEmptyPredicate, accountTypePredicate, inAuthorizedPredicate);
            }
        }
        return builder.conjunction();
    }

}
