package io.wyden.aeron.node;

import io.aeron.Publication;
import io.aeron.cluster.service.ClientSession;
import io.aeron.cluster.service.Cluster;
import io.aeron.cluster.service.Cluster.Role;
import io.wyden.sbe.ClientRegistrationDecoder;
import org.agrona.DirectBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class AeronSessionContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(AeronSessionContext.class);
    private static final long RETRY_COUNT = 10;

    private final AeronClientSessions clientSessions;

    private Cluster cluster;
    private ClientSession session;
    private Cluster.Role role = Role.FOLLOWER;

    public AeronSessionContext(final AeronClientSessions clientSessions) {
        this.clientSessions = clientSessions;
    }

    public void setCluster(Cluster cluster) {
        this.cluster = cluster;
    }

    public void setClientSession(final ClientSession session) {
        this.session = session;
    }

    public void setRole(Role role) {
        this.role = role;

    }

    public boolean isLeader() {
        return Role.LEADER.equals(role);
    }

    /**
     * Replies to the sender of the current session message, with retry. Disconnects a client that failed to offer
     * @param buffer the buffer to read data from
     * @param offset the offset to read from
     * @param length the length to read
     */
    public void reply(final DirectBuffer buffer, final int offset, final int length) {
        if (session != null) {
            offerToSession(session, buffer, offset, length);
        }
    }

    /**
     * Broadcasts a message to all connected sessions. If the offer fails to any session after a number of retries,
     * then that session is disconnected.
     * @param buffer the buffer to read data from
     * @param offset the offset to read from
     * @param length the length to read
     */
    public void broadcast(final DirectBuffer buffer, final int offset, final int length) {
        for (ClientSession element : clientSessions.getAllSessions()) {
            offerToSession(element, buffer, offset, length);
        }
    }

    public void routeToGroupConsistent(final int templateId, final long eventHash, final DirectBuffer buffer, final int offset, final int length) {
        for (Integer groupId : clientSessions.getGroupsForTemplateId(templateId)) {
            List<ClientSession> groupSessions = clientSessions.getGroupSessions(groupId);
            int index = Math.toIntExact(Math.abs(eventHash) % groupSessions.size());
            ClientSession session = groupSessions.get(index);
            offerToSession(session, buffer, offset, length);
        }
    }

    /**
     * Offers a message to a session, with retry. Disconnects a client that failed to offer after RETRY_COUNT retries
     * @param targetSession the session to offer to
     * @param buffer the buffer to read data from
     * @param offset the offset to read from
     * @param length the length to read
     */
    private void offerToSession(final ClientSession targetSession, final DirectBuffer buffer, final int offset, final int length) {
        int retries = 0;
        do {
            final long result = targetSession.offer(buffer, offset, length);
            if (result > 0L) {
                return;
            } else if (result == Publication.ADMIN_ACTION) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("admin action on session offer");
                }
            } else if (result == Publication.BACK_PRESSURED) {
                LOGGER.warn("backpressure on session offer");
            } else if (result == Publication.MAX_POSITION_EXCEEDED) {
                LOGGER.error("max position exceeded: {}", result);
                return;
            } else if (result == Publication.NOT_CONNECTED) {
                // client session not connected. happens during log replay.
                return;
            }

            cluster.idleStrategy().idle();
            retries += 1;
        } while (retries < RETRY_COUNT);

        LOGGER.error("failed to send message within {} retries. ", RETRY_COUNT);
    }

    public void registerClient(ClientRegistrationDecoder clientRegistration) {
        if (clientRegistration.groupId() > 0) {
            ClientRegistrationDecoder.TemplateIdsDecoder templateIdsDecoder = clientRegistration.templateIds();
            int[] templateIds = new int[templateIdsDecoder.count()];
            for (int i = 0; i < templateIdsDecoder.count(); i++) {
                templateIds[i] = templateIdsDecoder.next().templateId();
            }
            clientSessions.addGroupClient(session, clientRegistration.groupId(), templateIds);
            LOGGER.info("Session {} registered as member of group {}, templateIds {}", session.id(), clientRegistration.groupId(), Arrays.toString(templateIds));
        }
    }
}