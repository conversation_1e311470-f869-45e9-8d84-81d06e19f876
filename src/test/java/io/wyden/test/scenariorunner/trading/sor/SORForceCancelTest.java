package io.wyden.test.scenariorunner.trading.sor;

import io.wyden.apiserver.rest.orderhistory.model.OrderStateResponse;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientSide;
import io.wyden.test.scenariorunner.data.refdata.InstrumentId;
import io.wyden.test.scenariorunner.data.source.SimpleClientOrderTypesSource;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.common.ConnectorMockSessions;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.util.Randomizer;
import io.wyden.test.scenariorunner.util.WaitUtils;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;

import java.time.Duration;
import java.util.List;

import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.CANCELED;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderStatus.NEW;
import static org.assertj.core.api.Assertions.assertThat;

@GraphQL
class SORForceCancelTest extends SORTradingTestBase {

    private static final String SYMBOL = "DOGEUSD";
    private static final InstrumentId TARGET_INSTRUMENT = InstrumentId.DOGE_USD_FOREX_WYDENMOCK;

    @Disabled
    @ParameterizedTest(name = "sorForceCancelOrderAfterAcceptingOrder type {0}")
    @SimpleClientOrderTypesSource
    void sorForceCancelOrderAfterAcceptingOrder(ClientOrderType orderType, GraphQLActor actor, ConnectorMockSessions connSessions, ClientSession client) {
        // given
        List<OrderStateResponse> orderStates = actor.orderState().subscribeCurrentOrderStates();
        prepareAcceptedSorOrder(orderType, connSessions, client);

        // when
        client.sendForceCancel();

        //then
        WaitUtils.waitUntilAsserted(
            () -> assertThat(orderStates)
                .extracting(OrderStateResponse::getOrderStatus)
                    .containsSubsequence(NEW, NEW, CANCELED, CANCELED));
    }

    @Disabled
    @Test
    void sorCancelOrderBeforeAcceptingOrder(GraphQLActor actor, ConnectorMockSessions connSessions, ClientSession client) {

        // given
        List<OrderStateResponse> orderStates = actor.orderState().subscribeCurrentOrderStates();
        ConnectorMockSession bestPriceCandidate = connSessions.getSession(bestCandidate);

        double bestPrice = Randomizer.randomDouble(DEFAULT_PRICE_BOUND);
        bestPriceCandidate.sendOrderBook(TARGET_INSTRUMENT.getTicker().toString(), bestPrice);
        connSessions.getSession(secondBestCandidate).sendOrderBook(TARGET_INSTRUMENT.getTicker().toString(), bestPrice + 1);

        // when
        client.sendOrder(smartOrderFactory.order(ClientOrderType.LIMIT, ClientSide.BUY, REQUESTED_QTY, String.valueOf(bestPrice), "", SYMBOL));
        WaitUtils.justWait(Duration.ofSeconds(1));
        client.sendForceCancel();
        WaitUtils.justWait(Duration.ofSeconds(1));
        bestPriceCandidate.acceptNewOrder();
        WaitUtils.justWait(Duration.ofSeconds(1));
        bestPriceCandidate.acceptCancel(0);

        //then
        WaitUtils.waitUntilAsserted(
            () -> assertThat(orderStates)
                .extracting(OrderStateResponse::getOrderStatus)
                .containsSubsequence(NEW, CANCELED, CANCELED));
    }

}