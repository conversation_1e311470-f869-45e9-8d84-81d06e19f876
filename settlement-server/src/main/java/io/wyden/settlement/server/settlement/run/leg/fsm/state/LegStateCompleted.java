package io.wyden.settlement.server.settlement.run.leg.fsm.state;

import io.wyden.settlement.server.settlement.run.SettlementRunEntity;
import io.wyden.settlement.server.settlement.run.leg.SettlementLegEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LegStateCompleted extends LegState {
    private Logger LOGGER = LoggerFactory.getLogger(LegStateCompleted.class);

    public LegStateCompleted(SettlementRunEntity run, Long legId) {
        super(run, legId);
    }
}