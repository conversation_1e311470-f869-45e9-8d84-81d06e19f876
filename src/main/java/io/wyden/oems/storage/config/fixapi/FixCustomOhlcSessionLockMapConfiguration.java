package io.wyden.oems.storage.config.fixapi;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.apiserver.fix.domain.FixCustomOhlcSessionLockMapConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.EphemeralMapConfigBean;
import io.wyden.oems.storage.config.HazelcastMapConfigBean;
import io.wyden.oems.storage.web.StringStringMapWebAccess;
import io.wyden.oems.storage.web.WebAccess;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FixCustomOhlcSessionLockMapConfiguration {

    @Bean
    HazelcastMapConfigBean fixCustomOhlcSessionLockMapConfig() {
        HazelcastMapConfig hazelcastMapConfig = new FixCustomOhlcSessionLockMapConfig();
        return new EphemeralMapConfigBean(hazelcastMapConfig);
    }

    @Bean
    WebAccess fixCustomOhlcSessionLockMapWebAccess(HazelcastInstance hazelcast) {
        HazelcastMapConfig hazelcastMapConfig = new FixCustomOhlcSessionLockMapConfig();
        return new StringStringMapWebAccess(hazelcast, hazelcastMapConfig, null);
    }
}
