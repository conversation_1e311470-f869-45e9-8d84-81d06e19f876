import { WIDGETS_NAMES } from '@wyden/features/widgets-renderer/widget-names';
import { useWidget } from '@wyden/features/widgets-renderer/widget/hooks/useWidget';
import {
  getInstrumentId,
  getInstrumentSymbol,
  getInstrumentVenueName,
} from '@wyden/helpers/instrument';
import {
  InstrumentResponse,
  InstrumentResponseSchema,
  OrderStateResponse,
} from '@wyden/services/graphql/generated/graphql';
import { z } from 'zod';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ComparatorValue } from '../chip-filters/filters/Comparator';

export interface OrdersFilter {
  instruments: InstrumentResponse[];
  orderCategory: string[];
  orderId: string;
  extOrderId: string;
  clientOrderId: string;
  target: string;
  orderQty: ComparatorValue;
}

export const OrderFiltersSchema = z.object({
  instruments: z.array(InstrumentResponseSchema()),
  orderId: z.string(),
  extOrderId: z.string(),
  clientOrderId: z.string(),
  target: z.string(),
  orderQty: z
    .object({
      comparator: z.union([
        z.literal('lessThan'),
        z.literal('greaterThan'),
        z.literal('equal'),
        z.literal('between'),
      ]),
      values: z
        .tuple([z.number()])
        .or(z.tuple([z.number(), z.number().optional()]))
        .optional(),
    })
    .optional(),
});

export enum OrdersFilterLists {
  COLUMN_LIST = 'columnList',
  INSTRUMENT_LIST = 'instruments',
  TARGET = 'target',
  ORDER_QTY = 'orderQty',
  ORDER_CATEGORY_LIST = 'orderCategory',
  ORDER_ID = 'orderId',
  EXT_ORDER_ID = 'extOrderId',
  CLIENT_ORDER_ID = 'clientOrderId',
}

interface OrdersFilterStoreActions {
  setSelectedList: (selectedWidget: string) => (list: OrdersFilterLists) => void;
  setFilter: (selectedWidget: string) => (filters: Partial<OrdersFilter>) => void;
  setOrderId: (selectedWidget: string) => (orderId: string) => void;
  setExtOrderId: (selectedWidget: string) => (extOrderId: string) => void;
  setClientOrderId: (selectedWidget: string) => (clientOrderId: string) => void;
  setInstruments: (selectedWidget: string) => (instruments: InstrumentResponse[]) => void;
  setTarget: (selectedWidget: string) => (target: string) => void;
  setOrderQty: (selectedWidget: string) => (orderQty?: ComparatorValue) => void;
  setOrderCategory: (selectedWidget: string) => (orderCategory: string[]) => void;
  columnListSelected: (selectedWidget: string) => () => void;
  clear: (selectedWidget: string) => () => void;
  clearFilter: (selectedWidget: string) => (filter: keyof OrdersFilter) => void;
  setInitialState: (selectedWidget: string) => () => void;
  reset: () => void;
}

type ExtractedActions = {
  [key in keyof OrdersFilterStoreActions]: OrdersFilterStoreActions[key] extends (
    selectedWidget: string,
  ) => infer R
    ? R
    : never;
};

interface OrdersFiltersStore extends OrdersFilterStoreActions {
  widgetStates: Record<
    string,
    {
      selectedList: OrdersFilterLists;
      filters: Partial<OrdersFilter>;
    }
  >;
  widgetIds: string[];
}

enum OrderFilterActions {
  SET_SELECTED_LIST = 'setSelectedList',
  SET_FILTER = 'setFilter',
  SET_ORDER_QTY = 'setOrderQty',
  SET_ORDER_CATEGORY = 'setOrderCategory',
  SET_INSTRUMENTS = 'setInstruments',
  SET_ORDER_ID = 'setOrderId',
  SET_EXT_ORDER_ID = 'setExtOrderId',
  SET_CLIENT_ORDER_ID = 'setClientOrderId',
  SET_TARGET = 'setTarget',
  COLUMN_LIST_SELECTED = 'columnListSelected',
  CLEAR = 'clear',
  CLEAR_FILTER = 'clearFilter',
  SET_INITIAL_STATE = 'setInitialState',
}

const initialValues = {
  instruments: [],
  orderCategory: [],
  orderId: '',
  extOrderId: '',
  clientOrderId: '',
  target: '',
  orderQty: undefined,
};

export const useOrdersFilters = create<OrdersFiltersStore>()(
  devtools(
    (set) => ({
      widgetStates: {},
      widgetIds: [] as string[],
      setSelectedList: (selectedWidget: string) => (list) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  selectedList: list,
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_SELECTED_LIST,
        ),
      columnListSelected: (selectedWidget: string) => () =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  selectedList: OrdersFilterLists.COLUMN_LIST,
                },
              },
            };
          },
          false,
          OrderFilterActions.COLUMN_LIST_SELECTED,
        ),
      setFilter: (selectedWidget: string) => (filters) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    ...filters,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_FILTER,
        ),
      setInstruments: (selectedWidget: string) => (instruments) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    instruments,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_INSTRUMENTS,
        ),
      setTarget: (selectedWidget: string) => (target) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    target,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_TARGET,
        ),
      setOrderQty: (selectedWidget: string) => (orderQty) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    orderQty,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_ORDER_QTY,
        ),
      setOrderCategory: (selectedWidget: string) => (orderCategory) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    orderCategory,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_ORDER_CATEGORY,
        ),
      setOrderId: (selectedWidget: string) => (orderId) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    orderId,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_ORDER_ID,
        ),
      setExtOrderId: (selectedWidget: string) => (extOrderId) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    extOrderId,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_EXT_ORDER_ID,
        ),
      setClientOrderId: (selectedWidget: string) => (clientOrderId) =>
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    clientOrderId,
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_CLIENT_ORDER_ID,
        ),
      clear: (selectedWidget: string) => () => {
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: initialValues,
                },
              },
            };
          },
          false,
          OrderFilterActions.CLEAR,
        );
      },
      clearFilter: (selectedWidget: string) => (filter) => {
        set(
          (state) => {
            if (selectedWidget === null || selectedWidget === undefined) return state;
            return {
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  ...state.widgetStates[selectedWidget],
                  filters: {
                    ...state.widgetStates[selectedWidget].filters,
                    [filter]: initialValues[filter],
                  },
                },
              },
            };
          },
          false,
          OrderFilterActions.CLEAR_FILTER,
        );
      },
      setInitialState: (selectedWidget) => () => {
        set(
          (state) => {
            if (
              selectedWidget === null ||
              selectedWidget === undefined ||
              state.widgetIds.some((v) => v === selectedWidget)
            )
              return state;

            return {
              widgetIds: [...state.widgetIds, selectedWidget],
              widgetStates: {
                ...state.widgetStates,
                [selectedWidget]: {
                  selectedList: OrdersFilterLists.COLUMN_LIST,
                  filters: initialValues,
                },
              },
            };
          },
          false,
          OrderFilterActions.SET_INITIAL_STATE,
        );
      },
      reset: () => set({ widgetStates: {}, widgetIds: [] }),
    }),
    { name: 'OrdersFilters' },
  ),
);
const selectExtractedFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (
      selectedWidget === null ||
      selectedWidget === undefined ||
      !(selectedWidget in state.widgetStates)
    )
      return initialValues;
    return state.widgetStates[selectedWidget].filters;
  };

const selectInstrumentsFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (
      selectedWidget === null ||
      selectedWidget === undefined ||
      !(selectedWidget in state.widgetStates)
    )
      return initialValues.instruments;
    return state.widgetStates[selectedWidget].filters.instruments;
  };

const selectOrderQtyFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (selectedWidget === null || selectedWidget === undefined) return undefined;
    return state.widgetStates[selectedWidget].filters.orderQty;
  };

const selectOrderCategoryFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (
      selectedWidget === null ||
      selectedWidget === undefined ||
      !(selectedWidget in state.widgetStates)
    )
      return initialValues.orderCategory;
    return state.widgetStates[selectedWidget].filters.orderCategory;
  };

const selectOrderIdFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (selectedWidget === null || selectedWidget === undefined) return '';
    return state.widgetStates[selectedWidget].filters.orderId;
  };

const selectTargetFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (selectedWidget === null || selectedWidget === undefined) return '';
    return state.widgetStates[selectedWidget].filters.target;
  };

const selectIfAnyFilterIsApplied =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (selectedWidget === null || selectedWidget === undefined) return false;

    return Boolean(
      state.widgetStates[selectedWidget]?.filters?.target ||
        state.widgetStates[selectedWidget]?.filters?.orderId ||
        state.widgetStates[selectedWidget]?.filters?.extOrderId ||
        state.widgetStates[selectedWidget]?.filters?.clientOrderId ||
        state.widgetStates[selectedWidget]?.filters?.orderQty ||
        state.widgetStates[selectedWidget]?.filters?.orderCategory?.length ||
        state.widgetStates[selectedWidget]?.filters?.instruments?.length,
    );
  };

const selectOrderFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (
      selectedWidget === null ||
      selectedWidget === undefined ||
      !(selectedWidget in state.widgetStates)
    )
      return {
        selectedList: OrdersFilterLists.COLUMN_LIST,
        filters: [],
      };
    const currentFilters = state.widgetStates[selectedWidget].filters;
    const currentSelectedList = state.widgetStates[selectedWidget].selectedList;
    const filters = Object?.entries(currentFilters)?.map(([key, value]) => {
      if (
        key === 'orderQty' &&
        typeof value === 'object' &&
        'comparator' in value &&
        'values' in value
      ) {
        return {
          key,
          values: value ? [value] : [],
        };
      }
      if (key === 'instruments' && Array.isArray(value)) {
        return {
          key,
          values: value?.map(
            (instrument) =>
              getInstrumentSymbol(instrument as InstrumentResponse) +
              '@' +
              getInstrumentVenueName(instrument as InstrumentResponse),
          ),
        };
      }
      if (key === 'orderCategory') {
        return {
          key,
          values: value,
        };
      }
      return { key, values: value ? [value] : [] };
    });
    return {
      selectedList: currentSelectedList,
      filters: (filters as { key: string; values: string[] }[]).filter(
        ({ values }) => values?.length > 0,
      ),
    };
  };

const selectExtOrderIdFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (selectedWidget === null || selectedWidget === undefined) return '';
    return state.widgetStates[selectedWidget].filters.extOrderId;
  };

const selectClientOrderIdFilters =
  (selectedWidget: string | null | undefined) => (state: OrdersFiltersStore) => {
    if (selectedWidget === null || selectedWidget === undefined) return '';
    return state.widgetStates[selectedWidget].filters.clientOrderId;
  };

export const useOrdersSelectors = () => {
  const { widgetData } = useWidget(WIDGETS_NAMES.ORDERS);
  const id = widgetData.id;

  return {
    selectExtractedFilters: selectExtractedFilters(id),
    selectInstrumentsFilters: selectInstrumentsFilters(id),
    selectOrderFilters: selectOrderFilters(id),
    selectOrderCategoryFilters: selectOrderCategoryFilters(id),
    selectOrderIdFilters: selectOrderIdFilters(id),
    selectExtOrderIdFilters: selectExtOrderIdFilters(id),
    selectClientOrderIdFilters: selectClientOrderIdFilters(id),
    selectTargetFilters: selectTargetFilters(id),
    selectOrderQtyFilters: selectOrderQtyFilters(id),
    selectIfAnyFilterIsApplied: selectIfAnyFilterIsApplied(id),
  };
};

export const useOrdersFiltersActions = () => {
  const { widgetData } = useWidget(WIDGETS_NAMES.ORDERS);

  const actions = useOrdersFilters(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ({ widgetStates, widgetIds, reset, ...actions }) => actions,
  );

  const withWidgetContext = Object.fromEntries(
    Object.entries(actions).map(([key, value]) => {
      return [key, value(widgetData.id)];
    }),
  );

  // typescript is not able to infer the type of withWidgetContext, so we need to cast it
  return withWidgetContext as unknown as ExtractedActions;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const doesExternalFilterPass = (
  data: OrderStateResponse | undefined,
  filters: Partial<OrdersFilter>,
) => {
  if (!data) {
    return false;
  }
  if (filters.orderId && !data.orderId.toLowerCase().includes(filters.orderId.toLowerCase())) {
    return false;
  } else if (
    filters.extOrderId &&
    !data.extOrderId?.toLowerCase().includes(filters.extOrderId.toLowerCase())
  ) {
    return false;
  } else if (
    filters.clientOrderId &&
    !data.clOrderId?.toLowerCase().includes(filters.clientOrderId.toLowerCase())
  ) {
    return false;
  } else if (
    filters.target &&
    !data?.venueAccountDescs?.find(
      (account) => account.id.toLowerCase() === filters.target?.toLowerCase(),
    )
  ) {
    return false;
  } else if (
    filters.orderQty?.comparator === 'lessThan' &&
    data.orderQty >= (filters.orderQty?.values?.[0] || 0)
  ) {
    return false;
  } else if (
    filters.orderQty?.comparator === 'greaterThan' &&
    data.orderQty <= (filters.orderQty?.values?.[0] || 0)
  ) {
    return false;
  } else if (
    filters.orderQty?.comparator === 'equal' &&
    data.orderQty !== (filters.orderQty?.values?.[0] || 0)
  ) {
    return false;
  } else if (
    filters.orderQty?.comparator === 'between' &&
    (data.orderQty < (filters.orderQty?.values?.[0] || 0) ||
      data.orderQty > (filters.orderQty?.values?.[1] || 0))
  ) {
    return false;
  } else if (filters.instruments?.length) {
    const instrumentIds = filters.instruments.map(getInstrumentId);
    if (!instrumentIds.includes(getInstrumentId(data.instrument))) {
      return false;
    }
  } else if (filters.orderCategory?.length) {
    if (!filters.orderCategory.includes(data.orderCategory as string)) {
      return false;
    }
  }

  return true;
};
