package io.wyden.test.scenariorunner.util;

import io.qameta.allure.Allure;
import org.slf4j.Logger;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Predicate;

import static org.slf4j.LoggerFactory.getLogger;

public class MessageQueue<T> implements Consumer<T> {

    private static final Logger LOGGER = getLogger(MessageQueue.class);

    private final BlockingQueue<T> queue = new LinkedBlockingQueue<>();
    private Disposable subscription;
    private final AtomicBoolean connected = new AtomicBoolean(false);

    private final String msgName;

    public MessageQueue() {
        this.msgName = "";
    }

    public MessageQueue(String msgName) {
        this.msgName = msgName;
    }

    public T next(Duration timeout) {
        return next(timeout, t -> true);
    }

    public T next(Duration timeout, Predicate<T> predicate) {
        long endTime = System.currentTimeMillis() + timeout.toMillis();

        while (System.currentTimeMillis() < endTime) {
            try {
                T message = queue.poll(timeout.getSeconds(), TimeUnit.SECONDS);
                if (message != null) {
                    if (predicate.test(message)) {
                        Allure.step("Msg: " + message);
                        return message;
                    } else {
                        LOGGER.trace("Dropped unwanted message: " + message);
                    }
                } else {
                    LOGGER.trace("Timeout when waiting for next message");
                }
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
                return null;
            }
        }

        return null; // Return null if timeout is reached without satisfying the predicate
    }

    public void connect(Flux<T> flux) {
        subscription = flux
            .subscribeOn(Schedulers.boundedElastic())
            .doOnSubscribe(s -> connected.set(true))
            .doOnTerminate(() -> connected.set(false))
            .subscribe(this);
    }

    public void disconnect() {
        if (subscription != null) {
            subscription.dispose();
        }
        subscription = null;
    }

    public String msgName() {
        return msgName;
    }

    @Override
    public void accept(T t) {
        queue.add(t);
    }

    public AtomicBoolean isConnected() {
        return connected;
    }

    public List<T> asList() {
        return new ArrayList<>(queue);
    }

}
