package io.wyden.rest.management.orderhistory;

import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.portfolio.AccessGatewayMockClient;
import io.wyden.rest.management.portfolio.PortfolioRepository;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static io.wyden.rest.management.orderhistory.OrderHistorySearchInput.SimplePredicateInput;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OrderHistoryPermissionValidatorTest {
    Set<PermissionDto> permissions = Set.of(
        new PermissionDto("venue.account", "read", "simulator"),
        new PermissionDto("venue.account", "read", "bitmex-testnet1"),
        new PermissionDto("venue.account", "read", "simulator2"),
        new PermissionDto("venue.account", "read", "bitmex-testnet2"),
        new PermissionDto("venue.account", "read", "simulator3"),
        new PermissionDto("venue.account", "read", "bitmex-testnet3"),
        new PermissionDto("portfolio", "read", "Client 1"),
        new PermissionDto("portfolio", "read", "Client 2"),
        new PermissionDto("portfolio", "read", "Client 3"),
        new PermissionDto("portfolio", "read", "Client 4"),
        new PermissionDto("portfolio", "read", "Client 5"),
        new PermissionDto("portfolio", "read", "Bank"),
        new PermissionDto("portfolio", "read", "e2e_BANK_Portfolio"),
        new PermissionDto("portfolio", "read", "e2e_RETAIL_portfolio")
    );

    Set<String> vostroIds = Set.of("Client 1", "Client 2", "Client 3");

    Collection<String> venueAccountsIdsInPermissions = getVenueIdsFromPermissions();
    Collection<String> portfolioIdsInPermissions = getPortfolioIdsFromPermissions();
    Collection<String> portfolioVostroIdsInPermissions = getPortfolioVostroIdsFromPermissions();
    Collection<String> portfolioNostroIdsInPermissions = getPortfolioNostroIdsFromPermissions();

    PortfolioRepository portfolioRepository = mock(PortfolioRepository.class);
    VenueAccountRepository venueAccountRepository = mock(VenueAccountRepository.class);

    VenueAccountCacheFacade venueAccountCacheFacade = mock(VenueAccountCacheFacade.class);
    PortfoliosCacheFacade portfoliosCacheFacade = mock(PortfoliosCacheFacade.class);

    AccessGatewayMockClient accessGatewayClient = new AccessGatewayMockClient(permissions);

    OrderHistoryPermissionValidator sut = new OrderHistoryPermissionValidator(accessGatewayClient, venueAccountCacheFacade, portfoliosCacheFacade);

    @BeforeEach
    public void setup() {
        accessGatewayClient.grantPermissions(permissions);

        when(venueAccountCacheFacade.venueAccountDetails()).thenReturn(permissions.stream()
            .filter(p -> p.getResource().equals("venue.account"))
            .map(p -> VenueAccount.newBuilder().setId(p.getResourceId()).setVenueAccountName(p.getResourceId()).build())
            .toList());

        when(portfoliosCacheFacade.findAll()).thenReturn(permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .map(p -> Portfolio.newBuilder()
                .setId(p.getResourceId())
                .setName(p.getResourceId())
                .setPortfolioType(vostroIds.contains(p.getResourceId()) ? PortfolioType.VOSTRO : PortfolioType.NOSTRO)
                .build())
            .collect(Collectors.toSet()));
    }

    @Test
    void emptyRequestWithoutPermissions_authorizedRequestShouldBeNull() {
        accessGatewayClient.revokePermissions();

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNull();
    }

    @Test
    void emptyRequestWithOnlyDynamicPermissions_shouldContainsAllResourcesWithDynamicPermission() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(authorized.collectionPredicates()).isNotEmpty();

    }

    @Test
    void emptyRequestWithStaticReadPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(authorized.collectionPredicates()).isEmpty();
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void emptyRequestWithStaticReadVostroNostroPermissions_shouldReturnEmptyRequest() {
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(authorized.collectionPredicates()).isEmpty();
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void emptyRequestWithPortfolioStaticReadPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsAll(venueAccountsIdsInPermissions);
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).isEmpty();

    }

    @Test
    void emptyRequestWithVenueStaticReadPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).isEmpty();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsAll(portfolioIdsInPermissions);

    }

    @Test
    void emptyRequestWithVenueStaticReadPermissionsAndPortfolioVostroPermissions_shouldReturnRequestWithPredicateForPortfolioType() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).isEmpty();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsAll(portfolioVostroIdsInPermissions);
        assertThat(authorized.simplePredicates()).hasSize(1);
        assertThat(authorized.simplePredicates().stream()
            .filter(p -> p.field().equals(SimplePredicateInput.Field.PORTFOLIO_TYPE))
            .map(SimplePredicateInput::value)
            .toList()
        ).contains("VOSTRO");
    }

    @Test
    void emptyRequestWithVenueStaticReadPermissionsAndPortfolioNostroPermissions_shouldReturnRequestWithPredicateForPortfolioType() {
        accessGatewayClient.grantStaticVenueAccountPermission("read");
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest();
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).isEmpty();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsAll(portfolioNostroIdsInPermissions);
        assertThat(authorized.simplePredicates()).hasSize(1);
        assertThat(authorized.simplePredicates().stream()
            .filter(p -> p.field().equals(SimplePredicateInput.Field.PORTFOLIO_TYPE))
            .map(SimplePredicateInput::value)
            .toList()
        ).contains("NOSTRO");
    }

    @Test
    void requestedAccountsWithOnlyDynamicPermissions_shouldContainsRequestedResourcesWithDynamicPermission() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, "simulator")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsOnly("simulator");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithOnlyDynamicPermissions_shouldContainsRequestedResourcesWithDynamicPermission() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedAccountsWithStaticPermissions_shouldContainsOnlyRequestedResources() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, "simulator")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsOnly("simulator");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithOnlyDynamicPermissions_shouldContainsOnlyRequestedResources() {
        accessGatewayClient.grantStaticPortfolioPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioIdWithoutDynamicAccessWithStaticAccountAndStaticVostroNostro_shouldContainsOnlyRequestedResources() {
        accessGatewayClient.revokePermissions();
        accessGatewayClient.grantStaticPortfolioNostroPermission("read");
        accessGatewayClient.grantStaticPortfolioVostroPermission("read");
        accessGatewayClient.grantStaticVenueAccountPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedAccountsWithPortfolioStaticPermissions_shouldReturnRequestWithVenueIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new OrderHistorySearchInput.SimplePredicateInput(OrderHistorySearchInput.SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, "simulator")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToAccountIds(authorized.collectionPredicates())).containsOnly("simulator");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedPortfolioWithPortfolioStaticPermissions_shouldReturnRequestWithPortfolioIds() {
        accessGatewayClient.grantStaticPortfolioPermission("read");

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new OrderHistorySearchInput.SimplePredicateInput(OrderHistorySearchInput.SimplePredicateInput.PredicateType.EQUAL, OrderHistorySearchInput.SimplePredicateInput.Field.PORTFOLIO_ID, "Client 1")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());

        assertThat(authorized).isNotNull();
        assertThat(collectionPredicatesToPortfolioIds(authorized.collectionPredicates())).containsOnly("Client 1");
        assertThat(authorized.simplePredicates()).isEmpty();
    }

    @Test
    void requestedSimpleContainsPredicateOtherThatPortfolio_shouldNotThrow() {

        OrderHistorySearchInput orderHistorySearchInput = emptyRequest()
            .withSimplePredicates(List.of(new OrderHistorySearchInput.SimplePredicateInput(OrderHistorySearchInput.SimplePredicateInput.PredicateType.CONTAINS, OrderHistorySearchInput.SimplePredicateInput.Field.ORDER_ID, "xxx")));
        OrderHistorySearchInput authorized = sut.authorizeSearchRequest(orderHistorySearchInput, token());
        assertThat(authorized).isNotNull();
    }


    private static @NotNull OrderHistorySearchInput emptyRequest() {
        return new OrderHistorySearchInput(List.of(), List.of(), List.of(), null, null, null);
    }

    private Collection<String> getPortfolioIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    private Collection<String> getPortfolioVostroIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .filter(p -> this.vostroIds.contains(p.getResourceId()))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    private Collection<String> getPortfolioNostroIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("portfolio"))
            .filter(p -> !this.vostroIds.contains(p.getResourceId()))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    private Collection<String> getVenueIdsFromPermissions() {
        return permissions.stream()
            .filter(p -> p.getResource().equals("venue.account"))
            .map(PermissionDto::getResourceId)
            .collect(Collectors.toSet());
    }

    Collection<String> collectionPredicatesToPortfolioIds(Collection<OrderHistorySearchInput.CollectionPredicateInput> predicates) {
        return predicates.stream()
            .filter(p -> p.field().equals(OrderHistorySearchInput.CollectionPredicateInput.Field.PORTFOLIO_ID))
            .map(OrderHistorySearchInput.CollectionPredicateInput::value)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
    }

    Collection<String> collectionPredicatesToAccountIds(Collection<OrderHistorySearchInput.CollectionPredicateInput> predicates) {
        return predicates.stream()
            .filter(p -> p.field().equals(OrderHistorySearchInput.CollectionPredicateInput.Field.VENUE_ACCOUNT_ID))
            .map(OrderHistorySearchInput.CollectionPredicateInput::value)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());
    }

    private WydenAuthenticationToken token() {
        return new WydenAuthenticationToken(null, "", "", "TESTER", Set.of(), Set.of(), portfolioRepository, venueAccountRepository);
    }


}
