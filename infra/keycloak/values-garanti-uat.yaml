auth:
  adminUser: admin
  adminPassword: "s!.rzY/I*%5fl09fD!bYk"

extraEnvVars:
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"

replicaCount: 2

cache:
  enabled: false

ingress:
  enabled: true
  ingressClassName: nginx
  tls: true
  hostname: "keycloak-garanti-uat.wyden.io"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"

externalDatabase:
  host: wyden-cloud-garanti-uat.postgres.database.azure.com
  user: wyden
  password: 2q#uTkH!Sc9Lf4Hf
  database: keycloak

nodeSelector:
  environment: garanti-uat
tolerations:
  - key: "garanti-uat"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "kubernetes.azure.com/scalesetpriority"
    operator: "Equal"
    value: "spot"
    effect: "NoSchedule"

resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi
