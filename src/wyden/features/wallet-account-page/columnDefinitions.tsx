import i18n from '@wyden/i18n';
import { WalletAccountResponse } from '@wyden/services/graphql/generated/graphql';
import { ColDef } from 'ag-grid-community';
import { caseInsensitiveSorter } from '../../helpers/gridHelpers';
import { DateRendererToGoWithValueGetter, ISODateValueGetter } from '../grid/DateRenderer';
import { buildObjectKeyValidator, getDateColumnName, getGenericField } from '../grid/utils';

const validKey = buildObjectKeyValidator<WalletAccountResponse>();

export const nameKey = validKey('name');
export const idKey = validKey('id');
export const walletTypeKey = validKey('walletType');
export const createdAtKey = validKey('createdAt');

export const walletAccountDataColumnDefinitions: ColDef<WalletAccountResponse>[] = [
  getGenericField(nameK<PERSON>, {
    sortable: false,
  }),
  getGenericField(idKey, {
    headerName: i18n.t('grid.columnHeader.id') as string,
    comparator: caseInsensitiveSorter,
  }),
  getGenericField(walletTypeKey),
  getGenericField(createdAtKey, {
    valueGetter: ISODateValueGetter('createdAt'),
    cellRenderer: DateRendererToGoWithValueGetter(),
    headerName: getDateColumnName('createdAt'),
  }),
];
