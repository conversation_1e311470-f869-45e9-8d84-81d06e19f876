import SearchIcon from '@mui/icons-material/Search';
import { IconButton, Tooltip } from '@mui/material';
import { OrderStateResponse } from '@wyden/services/graphql/generated/graphql';
import { ICellRendererParams } from 'ag-grid-community';
import { useTranslation } from 'react-i18next';

import { useTransactionHistoryFilters } from '@wyden/features/transactions-history/useTransactionHistoryFilters';

export const TransactionSearch = ({ data }: ICellRendererParams<OrderStateResponse>) => {
  const { setOrderId } = useTransactionHistoryFilters();
  const { t } = useTranslation();

  return (
    <Tooltip title={t('orders.searchTransactions')} arrow>
      <span>
        {' '}
        {/* button wrapper needed here - when disabled, it doesn't trigger the tooltip event */}
        <IconButton onClick={() => setOrderId(data?.orderId || '')}>
          <SearchIcon fontSize="small" />
        </IconButton>
      </span>
    </Tooltip>
  );
};
