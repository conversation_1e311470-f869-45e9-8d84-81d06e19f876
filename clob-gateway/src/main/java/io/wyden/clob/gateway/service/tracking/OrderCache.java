package io.wyden.clob.gateway.service.tracking;

import com.hazelcast.core.HazelcastInstance;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.clob.gateway.domain.map.ClobOrderStateMapConfig;
import io.wyden.clob.gateway.infrastructure.telemetry.Meters;
import io.wyden.clob.gateway.model.ClobOrderState;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;

@Component
public class OrderCache extends OrderStateLookup {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCache.class);
    private final long completedOrderTtlSeconds;
    private final boolean canRemove;

    public OrderCache(HazelcastInstance hazelcast,
                      Tracing otlTracing,
                      MeterRegistry meterRegistry,
                      @Value("${hz.completedOrderTtlSeconds:60}") long completedOrderTtlSeconds,
                      @Value("${hz.remove:false}") boolean canRemove) {
        super(ClobOrderStateMapConfig.getMap(hazelcast), otlTracing, meterRegistry);
        this.completedOrderTtlSeconds = completedOrderTtlSeconds;
        this.canRemove = canRemove;
    }

    public void add(ClobOrderState orderState) {
        try (var ignored = otlTracing.createSpan("ordercache.add", SpanKind.CLIENT)) {
            recordLatencyIn(Meters.storageQueryLatencyTimer(meterRegistry, Meters.QueryType.CLOB_ORDER_STATE_ADD))
                .of(() -> addInner(orderState));
        }
    }

    private void addInner(ClobOrderState orderState) {
        String smartOrderId = orderState.getRequest().getOrderId();
        uniqueConstraintCheck(orderState);

        var ttlTime = orderState.getClosed() ? completedOrderTtlSeconds : 0;
        LOGGER.info("Registering a new ClobOrderState in the cache TTL={}\n{}", ttlTime, orderState);
        clobOrderStateMap.put(smartOrderId, orderState, ttlTime, TimeUnit.SECONDS);
    }

    public void update(ClobOrderState updatedState) {
        try (var ignored = otlTracing.createSpan("ordercache.update", SpanKind.CLIENT)) {
            recordLatencyIn(Meters.storageQueryLatencyTimer(meterRegistry, Meters.QueryType.CLOB_ORDER_STATE_UPDATE))
                .of(() -> updateInner(updatedState));
        }
    }

    private void updateInner(ClobOrderState updatedState) {
        if (canRemove && updatedState.getClosed()) {
            clobOrderStateMap.remove(updatedState.getRequest().getOrderId());
        } else {
            // TODO - We will likely need a state change history for audit reasons. Apart from updating current state here,
            //  we should be dumping incoming states into a separate IMap<String, Deque<ClobOrderState> history
            var ttlTime = updatedState.getClosed() ? completedOrderTtlSeconds : 0;
            LOGGER.trace("Updating ClobOrderState in the cache TTL={}\n{}", ttlTime, updatedState);
            clobOrderStateMap.put(updatedState.getRequest().getOrderId(), updatedState, ttlTime, TimeUnit.SECONDS);
        }
    }

    private void uniqueConstraintCheck(ClobOrderState orderState) {
        String orderId = orderState.getRequest().getOrderId();
        if (clobOrderStateMap.containsKey(orderId)) {
            throw new DuplicateOrderState(
                "Unique constraint fail - Order with orderId %s already present!".formatted(orderId));
        }
    }

    public static class DuplicateOrderState extends FailureNonRecoverableException {
        public DuplicateOrderState(String message) {
            super(message);
        }
    }
}
