package io.wyden.marketdata.model;

import io.micrometer.core.instrument.MeterRegistry;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

public class MarketDataSubscriptionMap {

    private final Map<MarketDataSubscriptionKey, MarketDataSubscription> marketDataSubscriptionMap = new ConcurrentHashMap<>();
    private final Map<String, MarketDataSubscription> marketDataSubscriptionsMapById = new ConcurrentHashMap<>();

    public MarketDataSubscriptionMap(MeterRegistry meterRegistry) {
        meterRegistry.gaugeMapSize("wyden.market-data.subscriptions", List.of(), marketDataSubscriptionMap);
    }

    public MarketDataSubscription put(MarketDataSubscriptionKey marketDataSubscriptionKey, MarketDataSubscription marketDataSubscription) {
        marketDataSubscriptionsMapById.put(marketDataSubscription.subscriptionId(), marketDataSubscription);
        return marketDataSubscriptionMap.put(marketDataSubscriptionKey, marketDataSubscription);
    }

    public MarketDataSubscription computeIfAbsent(MarketDataSubscriptionKey key, Function<? super MarketDataSubscriptionKey, ? extends MarketDataSubscription> function) {
        MarketDataSubscription subscription = marketDataSubscriptionMap.computeIfAbsent(key, function);
        marketDataSubscriptionsMapById.put(subscription.subscriptionId(), subscription);
        return subscription;
    }

    public MarketDataSubscription remove(MarketDataSubscriptionKey subscriptionKey) {
        MarketDataSubscription removed = marketDataSubscriptionMap.remove(subscriptionKey);
        if (Objects.nonNull(removed)) {
            marketDataSubscriptionsMapById.remove(removed.subscriptionId());
        }
        return removed;
    }

    public Map<MarketDataSubscriptionKey, MarketDataSubscription> getSubscriptionMap() {
        return Collections.unmodifiableMap(marketDataSubscriptionMap);
    }

    public Map<String, MarketDataSubscription> getSubscriptionsMapById() {
        return Collections.unmodifiableMap(marketDataSubscriptionsMapById);
    }

    public MarketDataSubscription get(MarketDataSubscriptionKey subscriptionKey) {
        return marketDataSubscriptionMap.get(subscriptionKey);
    }

    public MarketDataSubscription get(String subscriptionId) {
        return marketDataSubscriptionsMapById.get(subscriptionId);
    }
}
