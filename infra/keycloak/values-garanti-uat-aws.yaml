auth:
  adminUser: admin
  adminPassword: "s!.rzY/I*%5fl09fD!bYk"

extraEnvVars:
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"

cache:
  enabled: false

ingress:
  enabled: true
  ingressClassName: nginx
  tls: true
  hostname: "keycloak-garanti-uat.wyden.io"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-wyden"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"

externalDatabase:
  host: garanti-garantiuat.cluster-cx2cemkicqia.eu-central-1.rds.amazonaws.com
  user: wyden
  password: 2q#uTkH!Sc9Lf4Hf
  database: keycloak

nodeSelector:
  environment: garantiuat
tolerations:
  - key: "garantiuat"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  - key: "client"
    operator: "Equal"
    value: "garanti"
    effect: "NoSchedule"

resources:
  requests:
    cpu: 250m
    memory: 512Mi
  limits:
    cpu: 1000m
    memory: 2Gi
