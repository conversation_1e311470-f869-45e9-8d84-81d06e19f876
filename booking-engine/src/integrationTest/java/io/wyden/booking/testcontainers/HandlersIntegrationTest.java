package io.wyden.booking.testcontainers;

import io.wyden.booking.application.MessageHistoryService;
import io.wyden.booking.testcontainers.infra.OracleSetupExtension;
import io.wyden.booking.testcontainers.infra.PostgreSQLSetupExtension;
import io.wyden.published.booking.PositionSnapshot;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import static org.assertj.core.api.Assertions.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

abstract class HandlersIntegrationTest extends RabbitIntegrationBase {

    @Autowired
    private MessageHistoryService messageHistoryService;

    @Test
    void shouldCallTransactionHandlersOnlyOnce() {
        OemsResponse oemsResponse = responseFromBrokerDesk()
            .build();

        publishResponseAndAwaitProcessed(List.of(oemsResponse));

        await("until message is processed")
            .atMost(Duration.ofSeconds(10))
            .pollInterval(Duration.ofSeconds(1))
            .until(() -> messageHistoryService.isCompleted(oemsResponse.getMetadata().getResponseId()));
    }

    @Test
    void shouldCallPositionHandlersOnlyOncePerPosition() throws InterruptedException {
        Collection<PositionSnapshot> modifiedPositions = declareQueueAndBindTo(positionChangeExchange, PositionSnapshot.parser());

        OemsResponse oemsResponse = responseFromBrokerDesk()
            .build();

        publishResponseAndAwaitProcessed(List.of(oemsResponse));

        await("until message is processed")
            .atMost(Duration.ofSeconds(10))
            .pollInterval(Duration.ofSeconds(1))
            .until(() -> messageHistoryService.isCompleted(oemsResponse.getMetadata().getResponseId()));

        await("position mutations are published")
            .atMost(Duration.ofSeconds(10))
            .pollInterval(Duration.ofSeconds(1))
            .until(() -> modifiedPositions.size() == 4);

        // positions should be unique by portfolio and currency
        Set<PositionSnapshot> uniquePositions = new TreeSet<>(Comparator.comparing(msg -> msg.getSymbol() + " " + msg.getPortfolio() + " " + msg.getAccount()));
        uniquePositions.addAll(modifiedPositions);

        assertThat(uniquePositions).hasSize(4);
    }
}

@ExtendWith(PostgreSQLSetupExtension.class)
class PostgreSqlHandlersIntegrationTest extends HandlersIntegrationTest {
}

@ExtendWith(OracleSetupExtension.class)
@Disabled
class OracleHandlersIntegrationTest extends HandlersIntegrationTest {
}
