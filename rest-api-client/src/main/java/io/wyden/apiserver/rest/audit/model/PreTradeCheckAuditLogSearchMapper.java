package io.wyden.apiserver.rest.audit.model;

public class PreTradeCheckAuditLogSearchMapper {

    private PreTradeCheckAuditLogSearchMapper() {
        //hide constructor
    }

    public static PreTradeCheckAuditLogSearch map(PreTradeCheckAuditLogSearchInput model) {
        if (model == null ) {
            return null;
        }

        return new PreTradeCheckAuditLogSearch(
            model.from(),
            model.to(),
            model.portfolioIds(),
            null,
            model.first(),
            model.after()
        );
    }
}
