package io.wyden.sor.infrastructure.rabbit;

import com.google.protobuf.Message;
import com.rabbitmq.client.AMQP;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.TradingMessageParser;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.smartrecommendationengine.SOROrderExpiredEvent;
import io.wyden.sor.service.fsm.OrderService;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class OrderExpiredEventConsumer implements MessageConsumer<SOROrderExpiredEvent> {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderExpiredEventConsumer.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitDestinations rabbitDestinations;
    private final OrderService orderService;
    private final String queueName;
    private final String consumerName;

    public OrderExpiredEventConsumer(RabbitIntegrator rabbitIntegrator,
                                     RabbitDestinations rabbitDestinations,
                                     OrderService orderService,
                                     @Value("${rabbitmq.trading-order-expired-queue}") String queueName,
                                     @Value("${spring.application.name}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.rabbitDestinations = rabbitDestinations;
        this.orderService = orderService;
        this.queueName = queueName;
        this.consumerName = consumerName;
    }

    @PostConstruct
    public void init() {
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(SOROrderExpiredEvent sorOrderExpiredEvent, AMQP.BasicProperties basicProperties) {
        try {
            orderService.onOrderExpiredEvent(sorOrderExpiredEvent);
            return ConsumptionResult.consumed();
        } catch (Exception e) {
            LOGGER.warn("Exception when processing {} - retrying", sorOrderExpiredEvent, e);
            return ConsumptionResult.failureNeedsRequeue();
        }
    }

    private void declareQueue() {
        RabbitQueue<SOROrderExpiredEvent> queue = new RabbitQueueBuilder<SOROrderExpiredEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();
        RabbitExchange<SOROrderExpiredEvent> exchange = rabbitDestinations.getOrderExpiredExchange();
        LOGGER.info("Binding exchange {} and queue {}", exchange.getName(), queue.getName());
        queue.bindWithHeaders(exchange, MatchingCondition.ANY, Map.of());
        queue.attachConsumer(SOROrderExpiredEvent.parser(), this);
    }
}
