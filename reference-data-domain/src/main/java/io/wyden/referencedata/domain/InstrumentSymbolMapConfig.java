package io.wyden.referencedata.domain;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.core.HazelcastJsonValue;
import com.hazelcast.map.IMap;
import com.hazelcast.sql.SqlStatement;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;

public class InstrumentSymbolMapConfig extends HazelcastMapConfig {

    public static final String MAP_NAME = "reference-data-instrument-symbols_0.1";

    public static IMap<String, HazelcastJsonValue> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
    }

    @Override
    public void setupClientInstance(HazelcastInstance hazelcastInstance) {
        createMapping(hazelcastInstance);
    }

    @Override
    public void setupMemberInstance(HazelcastInstance hazelcastInstance) {
        createMapping(hazelcastInstance);
    }

    private static void createMapping(HazelcastInstance hazelcastInstance) {
        hazelcastInstance.getSql().execute(new SqlStatement("""
            CREATE MAPPING IF NOT EXISTS "%s" (
                  __key varchar,
                  symbol varchar,
                  assetClass varchar,
                  venueName varchar
                )
            TYPE IMap
            OPTIONS (
              'keyFormat' = 'varchar',
              'valueFormat' = 'json-flat'
            )
            """.formatted(MAP_NAME)));
    }

    @Override
    protected HazelcastIndex[] getIndexes() {
        return IndexedField.values();
    }

    private enum Field {
        SYMBOL("symbol"),
        VENUE_NAME("venueName");

        private final String fieldCoordinate;

        Field(String fieldCoordinate) {
            this.fieldCoordinate = fieldCoordinate;
        }

        public String getFieldCoordinate() {
            return fieldCoordinate;
        }
    }

    private enum IndexedField implements HazelcastIndex {
        SYMBOL(Field.SYMBOL.fieldCoordinate, SORTED_INDEX),
        VENUE_NAME(Field.VENUE_NAME.fieldCoordinate, HASH_INDEX);

        private final String fieldCoordinate;
        private final String indexType;

        IndexedField(String fieldCoordinate, String indexType) {
            this.fieldCoordinate = fieldCoordinate;
            this.indexType = indexType;
        }

        @Override
        public String getFieldCoordinate() {
            return fieldCoordinate;
        }

        @Override
        public String getIndexType() {
            return indexType;
        }
    }
}
