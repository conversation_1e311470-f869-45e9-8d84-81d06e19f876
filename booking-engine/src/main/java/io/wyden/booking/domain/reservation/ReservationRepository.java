package io.wyden.booking.domain.reservation;

import io.wyden.booking.domain.common.JpaRepository;
import io.wyden.booking.interfaces.rest.RequestModel.ReservationSearch;

import java.util.List;
import java.util.Optional;

public interface ReservationRepository extends JpaRepository<Reservation, Long> {

    long countByProperties(ReservationSearch searchInput);

    long countByProperties(ReservationSearch searchInput, String after);

    List<Reservation> findByProperties(ReservationSearch request);

    void deleteByProperties(ReservationSearch request);

    Optional<Reservation> findByReservationRef(String reservationRef);
}
