package io.wyden.accessgateway.domain.permission;

import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.CompoundKeyUtils;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;

/*
 * This map is not storing value in the database, should be accessed only with containsKey(), set() and delete()
 *  - containsKey(toKey(...))     - returns true if user has permission to given resource
 *  - set(toKey(...), any-string) - grants permission to given resource
 *  - delete(toKey(...))          - revokes permission to given resource
 */
public class PermissionUserMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "permission-user_v0.1";

    public static IMap<String, String> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
    }

    public static String toKey(String aclId, String resource, String scope, String resourceId) {
        return CompoundKeyUtils.toKey(4, aclId, resource, scope, resourceId);
    }

    public static AccessControlEntry fromKey(String key) {
        String[] parts = CompoundKeyUtils.fromKey(4, key);
        return new AccessControlEntry(parts[0], parts[1], parts[2], parts[3]);
    }

    public record AccessControlEntry(String aclId, String resource, String scope, String resourceId) {}
}
