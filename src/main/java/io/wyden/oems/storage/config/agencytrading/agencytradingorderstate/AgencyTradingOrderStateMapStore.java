package io.wyden.oems.storage.config.agencytrading.agencytradingorderstate;

import io.wyden.agencytrading.model.AgencyTradingOrderState;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.oems.storage.config.ProtobufMapStore;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class AgencyTradingOrderStateMapStore extends ProtobufMapStore<String, AgencyTradingOrderState> {

    private static final String LOAD_QUERY = "select payload from agency_order_states where order_id=? order by sequence_num desc";
    private static final String STORE_QUERY = "insert into agency_order_states (order_id, sequence_num, closed, payload) values (?, ?, ?, ?)";

    public AgencyTradingOrderStateMapStore(JdbcTemplate jdbc, Telemetry telemetry) {
        super(jdbc, AgencyTradingOrderState.parser(), telemetry.getMeterRegistry());
    }

    @Override
    public String loadQuery() {
        return LOAD_QUERY;
    }

    @Override
    public void storeRecord(String key, AgencyTradingOrderState value) {
        jdbc.update(STORE_QUERY,
            value.getRequest().getOrderId(),
            value.getSequenceNumber(),
            value.getClosed() ? 1 : 0,
            value.toByteArray());
    }
}
