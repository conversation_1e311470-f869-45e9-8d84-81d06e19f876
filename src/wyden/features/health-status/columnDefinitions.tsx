import { buildObjectKeyValidator, getGenericField } from '@wyden/features/grid/utils';
import i18n from '@wyden/i18n';
import { ColDef, ICellRendererParams, ValueGetterParams } from 'ag-grid-community';
import { camelCase } from 'lodash';
import { Capability } from '../../services/graphql/generated/graphql';
import { TargetStatesResponseMapRecord } from '../target-states/useTargetStatesStore';
import { HealthStatusRenderer } from '../venue-account/AccountWithHealthStatusRenderer';
import { VenueIcon } from '../venue-account/VenueIcon';
import { useVenueAccounts, VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import { HealthStatusDetailsInfoRenderer } from './HealthStatusDetailsInfoRenderer';
import { CAPABILITIES_STATUSES_FOR_DISPLAY } from '@wyden/constants';

const validKey = buildObjectKeyValidator<TargetStatesResponseMapRecord>();

const actionKey = 'actions';
const targetKey = validKey('key');
const targetNameKey = validKey('targetName');
const venueKey = validKey('venue');
const allCapabilityKeys = Object.values(Capability);

export const VenueRenderer = (data: ICellRendererParams<TargetStatesResponseMapRecord>) => {
  const { getAllAccounts } = useVenueAccounts();
  const venues = getAllAccounts();
  const venue = venues.find(
    (venue: VenueAccountWithVenue) => venue.venueAccountName === data?.data?.targetName,
  );

  return <VenueIcon venue={venue?.venue || ''} />;
};

const capabilityKeys = allCapabilityKeys.filter((name) =>
  CAPABILITIES_STATUSES_FOR_DISPLAY.includes(name as Capability),
);

const healthStatusGetter =
  (capability: string) => (v: ValueGetterParams<TargetStatesResponseMapRecord>) =>
    v.data?.value?.find((capabilityStatus) => capabilityStatus?.capability === capability)
      ?.healthStatus;

export const healthStatusColumnDefinitions: ColDef<TargetStatesResponseMapRecord>[] = [
  getGenericField(actionKey, {
    headerName: '',
    minWidth: 32,
    width: 32,
    maxWidth: 32,
    cellRenderer: HealthStatusDetailsInfoRenderer,
    pinned: 'left',
    lockPinned: true,
    sortable: false,
  }),
  getGenericField(targetNameKey, {
    headerName: i18n.t('grid.columnHeader.accountName') as string,
    minWidth: 200,
  }),
  getGenericField(targetKey, {
    headerName: i18n.t('grid.columnHeader.id') as string,
    minWidth: 150,
  }),
  getGenericField(venueKey, {
    minWidth: 140,
    cellRenderer: VenueRenderer,
  }),
  ...capabilityKeys.map((capabilityKey) =>
    getGenericField(capabilityKey, {
      headerName: i18n.t(`grid.columnHeader.${camelCase(capabilityKey)}`) as string,
      valueGetter: healthStatusGetter(capabilityKey),
      headerClass: 'health-status-header',
      cellRenderer: ({ data }: ICellRendererParams<TargetStatesResponseMapRecord>) =>
        HealthStatusRenderer(capabilityKey, data),
    }),
  ),
];
