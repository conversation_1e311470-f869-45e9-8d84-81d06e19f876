package io.wyden.test.scenariorunner.extension.common;

import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveUserPermissionsRequestDto;
import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public class PermissionSharer {

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionSharer.class);

    private final String ownerClientId;
    private final String targetClientId;
    private final Set<AuthorityDto> permissionsToShare;

    public PermissionSharer(String ownerClientId, String targetClientId, Set<AuthorityDto> permissionsToShare) {
        this.ownerClientId = ownerClientId;
        this.targetClientId = targetClientId;
        this.permissionsToShare = permissionsToShare;
        LOGGER.debug("Initialized with ownerClientId={}, targetClientId={}, permissionsToShare={}", ownerClientId, targetClientId, permissionsToShare);
    }

    public void sharePermissions(ExtensionContext context) {

        GraphQLActor actor = ExtensionActorUtils.getConfigurationGraphQLActor(context, ownerClientId);
        AddOrRemoveUserPermissionsRequestDto addOrRemoveUserPermissionsRequestDto = new AddOrRemoveUserPermissionsRequestDto(
            targetClientId, permissionsToShare
        );
        actor.permission().addUserPermissionsAndWait(addOrRemoveUserPermissionsRequestDto);
    }
}
