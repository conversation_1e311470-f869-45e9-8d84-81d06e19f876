import { getColorBasedOnTheme } from '@wyden/utils/styles';
import { styled } from './styled';
import { color } from './theme/colors';

export const StyledMenuItem = styled('div')`
  display: flex;
  gap: ${({ theme }) => theme.spacing(1)};
  align-items: center;
  &:hover {
    background-color: ${getColorBasedOnTheme(
      color['light'].fillsElementsFillHover,
      color['dark'].fillsElementsFillHover,
    )};
    cursor: pointer;
  }
`;
