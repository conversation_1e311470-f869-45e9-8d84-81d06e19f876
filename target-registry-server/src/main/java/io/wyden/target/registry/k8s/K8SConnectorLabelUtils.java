package io.wyden.target.registry.k8s;

import java.util.Map;

class K8SConnectorLabelUtils {

    private static final String WYDEN_CONNECTOR_VENUE_ACCOUNT_LABEL = "wyden.connector/venue.account";

    private K8SConnectorLabelUtils(){}

    static Map<String, String> getLabelsForVenueAccount(String venueAccount) {
        return Map.of(WYDEN_CONNECTOR_VENUE_ACCOUNT_LABEL, venueAccount);
    }

    static String getLabelSelectorForVenueAccount(String venueAccount) {
        return WYDEN_CONNECTOR_VENUE_ACCOUNT_LABEL + "=" + venueAccount;
    }
}
