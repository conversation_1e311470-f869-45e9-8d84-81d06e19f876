package io.wyden.exchange.marketdata.override;

import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import com.google.common.base.MoreObjects;

import java.time.Duration;

public abstract class EventConfig {

    private String tickerId;
    private long fixedDelayInMillis;


    protected EventConfig(String tickerId, int perMinute, long fixedDelayInMillis) {
        this.tickerId = tickerId;
        if (fixedDelayInMillis == 0 && perMinute > 0) {
            this.fixedDelayInMillis = 60000L / perMinute;
        } else {
            this.fixedDelayInMillis = fixedDelayInMillis;
        }
    }

    public abstract MarketDataEventDTO convert();

    public Duration period() {
        return Duration.ofMillis(fixedDelayInMillis);
    }

    public String getTickerId() {
        return tickerId;
    }

    public void setTickerId(String tickerId) {
        this.tickerId = tickerId;
    }

    public long getFixedDelayInMillis() {
        return fixedDelayInMillis;
    }

    public void setFixedDelayInMillis(long fixedDelayInMillis) {
        this.fixedDelayInMillis = fixedDelayInMillis;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("tickerId", tickerId)
            .add("fixedDelayInMillis", fixedDelayInMillis)
            .toString();
    }
}
