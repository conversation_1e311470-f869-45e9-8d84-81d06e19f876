package io.wyden.accessgateway.testcontainers;

import com.hazelcast.config.AdvancedNetworkConfig;
import com.hazelcast.config.Config;
import com.hazelcast.config.JoinConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import dasniko.testcontainers.keycloak.KeycloakContainer;
import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import jakarta.annotation.PostConstruct;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.containers.wait.strategy.DockerHealthcheckWaitStrategy;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.vault.VaultContainer;

import java.util.UUID;

@Testcontainers
@ContextConfiguration(initializers = TestContainerTestBase.Initializer.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    properties = {"spring.main.allow-bean-definition-overriding=true", "logging.level.root=debug"})
@TestPropertySource(locations = "classpath:integration-test.properties")
@ExtendWith(SpringExtension.class)
public abstract class TestContainerTestBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestContainerTestBase.class);

    public static final String ADMIN_PASSWORD = "Z#c9YgatDgr\\,M}t";
    public static final String ADMIN_USERNAME = "admin";
    private static final String VAULT_TOKEN = "my-token";

    protected static final Network network = Network.newNetwork();
    protected static final RabbitMQContainer RABBIT_MQ = new RabbitMQContainer("rabbitmq:3.12-management")
        .withNetwork(network)
        .withReuse(true);

    protected static final KeycloakContainer keycloakContainer = new KeycloakContainer("quay.io/keycloak/keycloak:22.0.1")
        .withReuse(true)
        .withWorkingDirectory("/")
        .withAdminPassword(ADMIN_PASSWORD)
        .withAdminUsername(ADMIN_USERNAME)
        .withEnv("KEYCLOAK_ADMIN", ADMIN_USERNAME)
        .withEnv("KEYCLOAK_ADMIN_PASSWORD", ADMIN_PASSWORD)
        .waitingFor(new DockerHealthcheckWaitStrategy());

    protected static VaultContainer<?> vaultContainer = new VaultContainer<>("hashicorp/vault")
        .withReuse(true)
        .withVaultToken(VAULT_TOKEN);

    @BeforeAll
    public static void beforeAll() {
        if (!keycloakContainer.isRunning()) {
            keycloakContainer.start();
        }
        vaultContainer.start();
    }

    @DynamicPropertySource
    static void registerDynamicProperties(DynamicPropertyRegistry registry) {
        String keycloakUri = keycloakContainer.getAuthServerUrl() + "realms/Wyden";

        registry.add("spring.security.oauth2.client.provider.keycloak.issuer-uri", () -> keycloakUri);
        registry.add("spring.security.oauth2.resourceserver.jwt.issuer-uri", () -> keycloakUri);
        registry.add("keycloak.host", keycloakContainer::getAuthServerUrl);

        registry.add("spring.cloud.vault.port", () -> vaultContainer.getFirstMappedPort());
    }

    public static String TESTCONTAINER_AUTH_SERVER_URL;

    @LocalServerPort
    private int port;

    @PostConstruct
    public void init() {
        RestAssured.port = port;
        RestAssured.filters(new RequestLoggingFilter(), new ResponseLoggingFilter());
        TESTCONTAINER_AUTH_SERVER_URL = keycloakContainer.getAuthServerUrl() + "realms/Wyden/protocol/openid-connect/token";
    }

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {
            if (!RABBIT_MQ.isRunning()) {
                RABBIT_MQ.start();
            }
            var values = TestPropertyValues.of(
                "rabbitmq.reference-data-queue.venue-account-new=reference-data-queue.reference-data.VENUE-ACCOUNT-NEW",
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword(),
                "rabbitmq.virtualHost=/"
            );
            values.applyTo(applicationContext);
        }
    }

    @TestConfiguration
    public static class TestConfig {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            AdvancedNetworkConfig advancedNetworkConfig = config.getAdvancedNetworkConfig();
            JoinConfig join = advancedNetworkConfig.getJoin();
            join.getMulticastConfig().setEnabled(false);
            join.getAutoDetectionConfig().setEnabled(false);
            join.getKubernetesConfig().setEnabled(false);
            join.getTcpIpConfig().setEnabled(false);
            join.getDiscoveryConfig().setDiscoveryStrategyConfigs(null);
            config.setClusterName(UUID.randomUUID().toString());
            config.getJetConfig().setEnabled(true);
            config.getJetConfig().setBackupCount(0);
            return Hazelcast.newHazelcastInstance(config);
        }
    }
}
