package io.wyden.hedger.it;

import io.wyden.hedger.it.utils.OrderCollider;
import io.wyden.published.oems.OemsRequest;
import org.junit.jupiter.api.Named;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.test.annotation.DirtiesContext;

import java.util.function.BiConsumer;
import java.util.stream.Stream;

import static io.wyden.hedger.it.utils.HedgingRequestExpectations.expectHedgingRequest;
import static io.wyden.hedger.it.utils.TestingData.Instruments.ADAEUR_KRAKEN;
import static io.wyden.hedger.it.utils.TestingData.Instruments.BTCUSD_KRAKEN;
import static io.wyden.hedger.it.utils.TestingData.Instruments.DOGEEUR_KRAKEN;
import static io.wyden.hedger.it.utils.TestingData.Instruments.ETHUSD_KRAKEN;
import static io.wyden.hedger.it.utils.TestingData.Instruments.LTCEUR_KRAKEN;
import static io.wyden.hedger.it.utils.TestingData.Portfolios.BANK;
import static io.wyden.published.oems.OemsSide.BUY;
import static io.wyden.published.oems.OemsSide.SELL;
import static org.junit.jupiter.api.Named.named;
import static org.springframework.test.annotation.DirtiesContext.MethodMode.BEFORE_METHOD;

public class HedgingTest extends AutoHedgerIntegrationTestBase {
    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnHighThreshold_baseCcy() {
        booking.emitPositionSnapshot(11, "BTC", BANK);
        expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "10");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnLowThreshold_baseCcy() {
        booking.emitPositionSnapshot(-11, "BTC", BANK);
        expectHedgingRequest(collider, BTCUSD_KRAKEN, BUY, "12");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnHighThreshold_baseCcy_QtyIncr() {
        booking.emitPositionSnapshot(11.111, "ADA", BANK);
        expectHedgingRequest(collider, ADAEUR_KRAKEN, SELL, "10.1");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnLowThreshold_baseCcy_QtyIncr() {
        booking.emitPositionSnapshot(-11.111, "ADA", BANK);
        expectHedgingRequest(collider, ADAEUR_KRAKEN, BUY, "12.1");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnHighThreshold_baseCcy_BigQtyIncr() {
        booking.emitPositionSnapshot(112, "DOGE", BANK);
        expectHedgingRequest(collider, DOGEEUR_KRAKEN, SELL, "110");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnLowThreshold_baseCcy_BigQtyIncr() {
        booking.emitPositionSnapshot(-112, "DOGE", BANK);
        expectHedgingRequest(collider, DOGEEUR_KRAKEN, BUY, "110");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnHighThreshold_baseCcy_MaxQty() {
        booking.emitPositionSnapshot(250, "LTC", BANK);
        OemsRequest oemsRequest = expectHedgingRequest(collider, LTCEUR_KRAKEN, SELL, "100");
        booking.emitBookingCompleted(oemsRequest.getOrderId(), 150, "LTC", BANK);
        expectHedgingRequest(collider, LTCEUR_KRAKEN, SELL, "100");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnLowThreshold_baseCcy_MaxQty() {
        booking.emitPositionSnapshot(-250, "LTC", BANK);
        OemsRequest oemsRequest = expectHedgingRequest(collider, LTCEUR_KRAKEN, BUY, "100");
        booking.emitBookingCompleted(oemsRequest.getOrderId(),-150, "LTC", BANK);
        expectHedgingRequest(collider, LTCEUR_KRAKEN, BUY, "100");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnHighThreshold_baseCcy_MinQty() {
        booking.emitPositionSnapshot(15, "LTC", BANK);
        collider.ensureNoMoreRequests();
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnLowThreshold_baseCcy_MinQty() {
        booking.emitPositionSnapshot(-15, "LTC", BANK);
        collider.ensureNoMoreRequests();
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnHighThreshold_quoteCcy() {
        booking.emitPositionSnapshot(12_020, "USD", BANK);
        expectHedgingRequest(collider, ETHUSD_KRAKEN, BUY, "4.03");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldHedgeOnLowThreshold_quoteCcy() {
        booking.emitPositionSnapshot(-12_010, "USD", BANK);
        expectHedgingRequest(collider, ETHUSD_KRAKEN, SELL, "3.96");
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldNotAttemptNextHedgeOnRetryAndPositionChange() {
        booking.emitPositionSnapshot(11, "BTC", BANK);
        OemsRequest hedgingRequest = expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "10");
        collider.emitExecutionReportRejected(hedgingRequest);
        booking.emitPositionSnapshot(1, "BTC", BANK);
        collider.ensureNoMoreRequests();
    }

    @Test
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldAttemptNextHedgeOnRetryAndPositionChange() {
        booking.emitPositionSnapshot(11, "BTC", BANK);
        OemsRequest hedgingRequest = expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "10");
        collider.emitExecutionReportRejected(hedgingRequest);
        booking.emitPositionSnapshot(12, "BTC", BANK);
        expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "11");
    }

    @ParameterizedTest
    @MethodSource
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldNotAttemptNextHedge(BiConsumer<OrderCollider, OemsRequest> afterFirstHedging) {
        booking.emitPositionSnapshot(11, "BTC", BANK);
        OemsRequest hedgingRequest = expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "10");
        afterFirstHedging.accept(collider, hedgingRequest);

        booking.emitPositionSnapshot(11, "BTC", BANK);
        collider.ensureNoMoreRequests();
    }

    static Stream<Named<BiConsumer<OrderCollider, OemsRequest>>> shouldNotAttemptNextHedge() {
        return Stream.of(
            named("no ack", (collider, request) -> {
                // do nothing
            }),
            named("NEW", (collider, request) -> {
                collider.emitExecutionReportNew(request);
            }),
            named("NEW -> PARTIALLY_FILLED", (collider, request) -> {
                collider.emitExecutionReportNew(request);
                collider.emitExecutionReportPartiallyFilled(request, "1", "1", "100000", "9");
            })
        );
    }

    @ParameterizedTest
    @MethodSource
    @DirtiesContext(methodMode = BEFORE_METHOD)
    void shouldAttemptNextHedge(BiConsumer<OrderCollider, OemsRequest> afterFirstHedging) {
        booking.emitPositionSnapshot(11, "BTC", BANK);
        OemsRequest hedgingRequest = expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "10");
        afterFirstHedging.accept(collider, hedgingRequest);

        expectHedgingRequest(collider, BTCUSD_KRAKEN, SELL, "10");
    }

    static Stream<Named<BiConsumer<OrderCollider, OemsRequest>>> shouldAttemptNextHedge() {
        return Stream.of(
            named("REJECTED", (collider, request) -> {
                collider.emitExecutionReportRejected(request);
            }),
            named("CANCELED", (collider, request) -> {
                collider.emitExecutionReportCanceled(request);
            }),
            named("NEW -> CANCELED", (collider, request) -> {
                collider.emitExecutionReportNew(request);
                collider.emitExecutionReportCanceled(request);
            }),
            named("NEW -> PARTIALLY_FILLED -> CANCELED", (collider, request) -> {
                collider.emitExecutionReportNew(request);
                collider.emitExecutionReportPartiallyFilled(request, "1", "1", "100000", "9");
                collider.emitExecutionReportCanceled(request);
            })
        );
    }
}
