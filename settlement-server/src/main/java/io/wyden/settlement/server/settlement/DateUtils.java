package io.wyden.settlement.server.settlement;

import java.time.Instant;
import java.time.LocalDate;

public class DateUtils {

    private DateUtils() {
        // Private constructor to prevent instantiation
    }

    public static LocalDate convertEpochDate(String date) {
        return Instant.ofEpochMilli(Long.parseLong(date))
            .atZone(io.wyden.cloudutils.tools.DateUtils.UTC)
            .toLocalDate();
    }
}
