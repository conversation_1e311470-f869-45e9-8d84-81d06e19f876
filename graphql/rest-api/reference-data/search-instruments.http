### Search instruments

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("admin")}}

query {
    instrumentSearch(search: {first: 50, after: "XLMUPUSDT@FOREX@Generic", sortingOrder: DESC, venueNames: [], venueType: STREET}) {
        pageInfo {
            hasNextPage
            endCursor
        }
        edges {
            cursor
            node {
                baseInstrument {
                    symbol
                    assetClass
                    venueName
                    venueType
                }
                tradingConstraints {
                    tradeable
                }
            }
        }
    }
}
